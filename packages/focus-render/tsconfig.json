{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "types": ["webpack-env", "@types/node"], "typeRoots": ["node_modules/@types", "src/@types"], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "noImplicitAny": false, "declaration": true, "declarationDir": "./build-lib", "outDir": "./build-lib"}, "include": ["*.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx", "tests/**/*.d.ts", "src/service/web-base-core/Wa.js", "../activity-pages/src/views/preview/components/preview-tool.vue"], "exclude": ["node_modules"]}