<script setup lang="ts">
import { provide, ref } from 'vue'
import { UrlParser } from '../../utils/url'
console.log('focus-client......')
const locationHrefPared = new UrlParser(window.location.href)
const { pageid, aid, fid } = locationHrefPared.query
const focusFid = fid || pageid
const focusAid = aid
</script>

<template>
  <focus-render :focusFid="focusFid" :focusAid="focusAid" :needLogin="false"></focus-render>
</template>

<style lang="scss">
@import '../../styles/normalize.scss';

body {
  background-repeat: no-repeat;
  width: 100%;
  overflow-x: hidden;
}
.difalt-share {
  position: absolute;
  left: 0;
  top: -128px;
  visibility: hidden;
}
#app {
  width: 100%;
  text-align: center;
  overflow-x: hidden;
  line-height: 0;
  font-size: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
}
img {
  // 默认不可以长按触发扫一扫和下载
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -o-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}
</style>
