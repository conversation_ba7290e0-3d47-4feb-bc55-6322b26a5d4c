BUILD_TEST &&
  SHOW_VCONSOLE &&
  import(/* webpackChunkName: "vconsole" */ 'vconsole').then((cls) => {
    const Cls = cls.default
    return new Cls()
  })
console.log('focus-client-start')
import { createApp } from 'vue'
import App from './App.vue'
// 导入组件库-focusRender
import { focusRenderPlugin, focusCore } from '../../packages/focus-render/index'

import { createPinia } from 'pinia'
// if (BUILD_TEST) {
//   // TODO: 可以删除了
//   // 使用测试环境
//   focusCore.useTestMode()
// }
const app = createApp(App)
const pinia = createPinia()
app.use(pinia).use(focusRenderPlugin, { $pinia: pinia })

app.mount('#app')
