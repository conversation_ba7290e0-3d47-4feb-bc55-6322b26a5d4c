<template>
  <div class="loading" @touchmove.prevent v-show="showLoading">
    <p class="hide-text" v-show="false">{{ loadingContrlList }}</p>
    <div class="icon-wrap">
      <img src="../assets/img/common/penguin.png" alt="" />
      <div class="loading-pulse"></div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'focus-loading',
}
</script>
<script setup lang="ts">
import { toRefs } from 'vue'
import { focusStore } from '../packages/focus-render/index'
const { modalStore } = focusStore.stores
const { showLoading, loadingContrlList } = toRefs(modalStore)
</script>

<style lang="scss" scoped>
.loading {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  // background: rgba($color: #000000, $alpha: 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 9999;
  .icon-wrap {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 160px;
    height: 160px;
    background: rgba($color: #000000, $alpha: 0.3);
    border-radius: 12px;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
  }
  img {
    width: 80px;
    margin-bottom: 10px;
  }
}
$base-line-height: 30px;
$white: #ffffff;
$off-white: rgba($white, 0.5);
$spin-duration: 1s;

@keyframes pulse {
  50% {
    background: $white;
  }
}
.loading-pulse {
  position: relative;
  width: 15px;
  height: 15px;
  background: $off-white;
  animation: pulse 750ms infinite;
  animation-delay: 250ms;
  border-radius: 50%;
  // margin: 10px;
  // margin-left: -2px;
  transform: scale(0.6);
  box-sizing: content-box;
  &:before,
  &:after {
    content: '';
    position: absolute;
    display: block;
    height: 15px;

    width: 15px;
    background: $off-white;
    top: 50%;
    transform: translateY(-50%);
    animation: pulse 750ms infinite;
    border-radius: 50%;
  }
  &:before {
    left: -45px;
  }
  &:after {
    left: 45px;
    animation-delay: 500ms;
  }
}
</style>
