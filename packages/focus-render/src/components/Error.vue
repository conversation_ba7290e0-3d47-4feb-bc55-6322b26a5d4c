<template>
  <Mask class="error-mask" v-if="errorMaskMsgs.length" @touchmove.prevent :class="{ isdark: errorMaskFillDark }">
    <img class="cover" src="../assets/img/common/warning-v2.png" />
    <p class="text" v-for="(text, index) in errorMaskMsgs" :key="index">
      {{ text }}
    </p>
    <!-- <div class="btn" v-if="errorMaskShowBtn" @click="clickBtn">立即打开APP</div> -->
  </Mask>
</template>

<script setup lang="ts">
import Mask from './Mask.vue'
import { focusStore } from '../packages/focus-render/index'
import { storeToRefs } from 'pinia'
import { jumpService } from '../service'
const cdnPrefix = CDN_PREFIX_FOCUS_RENDER

const { modalStore } = focusStore.stores

const { errorMaskMsgs, errorMaskShowBtn, errorMaskFillDark } = storeToRefs(modalStore)

const clickBtn = () => {
  jumpService.commonUse.appHome()
}
</script>

<style lang="scss" scoped>
.error-mask {
  background: rgba($color: #000000, $alpha: 0.7);
  z-index: 9999;
  &.isdark {
    background: #7a7b7d;
  }
  .cover {
    width: 600px;
    margin-top: -200px;
  }
  .text {
    font-size: 32px;
    color: #fff;
  }
  .btn {
    width: fit-content;
    padding: 20px;
    background: #0089d3;
    color: #fff;
    font-size: 32px;
    border-radius: 16px;
    margin-top: 30px;
  }
}
</style>
