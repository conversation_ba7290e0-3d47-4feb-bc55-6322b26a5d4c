<template>
  <div
    class="mask"
    :class="transperentBg ? 'transperent' : ''"
    :style="{ justifyContent }"
    @touchmove="checkScroll"
    @touchend="endTouch"
    @touchstart="setStart"
  >
    <slot ref="curSlot" class="slotdom"></slot>
  </div>
</template>

<script setup lang="ts">
import { defineProps, useSlots, toRefs, onMounted, onUnmounted, ref, watchEffect, Ref, computed } from 'vue'
import { AnimateController } from '@/utils'
const props = defineProps({
  transperentBg: Boolean,
  justifyContent: {
    require: false,
    default: 'center',
  },
  canScroll: {
    require: false,
    default: '',
  },
})
const { transperentBg, justifyContent } = toRefs(props)

const slots = useSlots()
const curSlot = ref('')
const slotDom = ref<Element | null>(null)
const firstTouchY = ref(0)
const lastTouchY = ref(0)
const fingerIsDown = computed(() => {
  console.log('...起始位置', firstTouchY.value)
  console.log('...结束位置', lastTouchY.value)
  // const data = !!(lastTouchY.value - firstTouchY.value)
  if (firstTouchY.value < lastTouchY.value) {
    console.error('手指下滑')
  } else {
    console.error('手指上划')
  }
  return firstTouchY.value < lastTouchY.value
})

watchEffect(() => {
  if (slotDom.value) {
    if (slotDom.value.className.indexOf('mask-contain-wrap') < 0) {
      slotDom.value.className = slotDom.value.classList + ' mask-contain-wrap'
    }
  }
})
onMounted(() => {})

onUnmounted(() => {})
const isBindEvent = ref(false)
const wrapEvent = ref<Event | null>(null)

function setStart(e: Event) {
  console.log(e.currentTarget)
  console.log(e.target)
  firstTouchY.value = Math.floor((e as TouchEvent).touches[0].clientY)
  // console.log('🚀 ~ file: Mask.vue:48 ~ setStart ~  firstTouchY.value:', firstTouchY.value)
}

function setEnd(e: Event) {
  lastTouchY.value = Math.floor((e as TouchEvent).touches[0].clientY)
  // console.log('🚀 ~ file: Mask.vue:52 ~ setEnd ~ lastTouchY.value:', lastTouchY.value)
}

function checkScroll(e: any) {
  wrapEvent.value = e
  // console.log('🚀 ~ file: Mask.vue:41 ~ checkScroll ~ e:', e)
  if (props.canScroll) {
    setEnd(e)
    console.error('可以滚动！')
    if (isBindEvent.value && slotDom.value) {
      console.error('已经绑了事件了，算状态！')
      stopScroll(e)
      return
    }
    console.error('现在绑定！')
    slotDom.value = document.querySelector(props.canScroll)
    // 处理在顶部（底部）时，下拉（上滑）滚动穿透的问题
    if (slotDom.value) {
      isBindEvent.value = true
    }

    return
  }
  e.preventDefault()
}

function endTouch() {
  console.error('stop!!!!!')
  firstTouchY.value = 0
  lastTouchY.value = 0

  return true
}

function stopScroll(e: Event) {
  if (!slotDom.value || !wrapEvent.value) {
    return
  }
  const { scrollTop, scrollHeight, clientHeight } = slotDom.value
  // console.log('🚀 ~ clientHeight', clientHeight)
  // console.log('🚀 ~scrollHeight', scrollHeight)
  // console.log('🚀 ~ scrollTop', scrollTop)
  // const curTargetY = Math.floor((wrapEvent.value as TouchEvent).touches[0].clientY)
  // console.log('🚀 ~ file: Mask.vue:80 ~ stopScroll ~ curTargetY:', curTargetY)
  console.log('手指方向！！', fingerIsDown.value)
  if (scrollTop <= 0 && fingerIsDown.value) {
    // console.log('到顶啦！')
    // // 滚动到顶部下拉
    // console.log('手指下滑，不给滑动！')
    e.preventDefault()
    // slotDom.value.style.touchAction = 'none'
  } else if (scrollTop >= scrollHeight - clientHeight && !fingerIsDown.value) {
    // console.log('到底部啦！')
    // console.log('手指上滑，不给滑动！')

    // 滚动到底部上滑
    e.preventDefault()
    // slotDom.value.style.touchAction = 'none'
  } else {
    // slotDom.value.style.touchAction = 'auto'
  }
}
</script>

<style lang="scss">
.mask {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 8888;
  background: rgba($color: #000000, $alpha: 0.8);
  line-height: 1.5;
  touch-action: none;
  &.transperent {
    background: none;
  }
  .mask-contain-wrap {
    overflow-x: none;
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
  }
}
</style>
