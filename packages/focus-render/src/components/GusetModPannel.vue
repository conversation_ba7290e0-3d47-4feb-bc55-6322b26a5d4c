<template>
  <div class="login-pannel" v-if="showGuestModPannel" @touchmove.prevent>
    <div class="contain">
      <p>为了给您提供完整的服务体验，该页面需要获取您的昵称和头像，是否同意？</p>
      <div class="btn" @click="goLogin" data-reportid="guest_agree">同意</div>
      <div class="btn-cancel" @click="noLogin" data-reportid="guest_cancel">先看看</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject, isReactive, provide, toRefs, watch, readonly, getCurrentInstance, computed } from 'vue'
const props = defineProps({
  loginWx: Function,
})
const showGuestModPannel = ref(true)
const goLogin = function () {
  console.log('去登录啦')
  props.loginWx && props.loginWx()
}
const noLogin = function () {
  toogleGuestModPannel(false)
  document?.querySelector('body')?.addEventListener('click', stopAllClick, true)
}
function toogleGuestModPannel(status: boolean) {
  showGuestModPannel.value = status
}
function removeBodyEvent() {
  document?.querySelector('body')?.removeEventListener('click', stopAllClick, true)
}
function stopAllClick() {
  toogleGuestModPannel(true)
  removeBodyEvent()
}
</script>

<script></script>

<style lang="scss" scoped>
.login-pannel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  height: 100%;
  z-index: 222222;
  line-height: 1.5;
  .contain {
    background: #fff;
    width: 100%;
    height: 426px;
    font-size: 32px;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    p {
      font-weight: bold;
      width: 690px;
      margin-top: 60px;
      margin-bottom: 60px;
    }
    .btn {
      height: 88px;
      width: 480px;
      background: #f29661;
      border-radius: 44px;
      text-align: center;
      cursor: pointer;
      line-height: 88px;
      color: #fff;
      &:active {
        opacity: 0.6;
      }
    }
    .btn-cancel {
      color: #456ce6;
      font-size: 28px;
      margin-top: 20px;
      padding: 10px;
      cursor: pointer;
      &:active {
        opacity: 0.6;
      }
    }
  }
}
</style>
