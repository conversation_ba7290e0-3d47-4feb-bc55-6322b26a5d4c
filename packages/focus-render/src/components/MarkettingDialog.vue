<template>
  <div class="marketting_dialog">
    <dialog-mask :show="showDialog">
      <template #dialog_contain>
        <div class="marketting_dialog-contain">
          <div class="btn-close" @click="hide"></div>
          <img :src="popupData.bgImgUrl" alt="" @click="jump" />
        </div>
      </template>
    </dialog-mask>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, watchEffect, reactive, toRefs } from 'vue'
import DialogMask from './DialogMask.vue'
import { focusServices } from '../packages/focus-render'
const props = defineProps({
  popupData: {
    type: Object,
    default: {
      msgId: 0,
      bgImgUrl: '',
      testMsg: false,
      jumpConfig: {
        path: '',
        method: '',
      },
    },
  },
})

const showDialog = ref(false)

watchEffect(() => {
  showDialog.value = !!props.popupData.msgId
})

function hide() {
  showDialog.value = false
  readMsg()
}

function jump() {
  const { jumpService } = focusServices
  jumpService.jump(props.popupData.jumpConfig)
  setTimeout(() => {
    hide()
  }, 100)
}

function readMsg() {
  const { extendInstanceService } = focusServices
  const { msgId, testMsg } = toRefs(props.popupData)
  console.log('🚀 ~ file: MarkettingDialog.vue ~ line 47 ~ readMsg ~ props.popupData', props.popupData)
  console.log('🚀 ~ file: MarkettingDialog.vue ~ line 46 ~ readMsg ~ msgId,testMsg', msgId, testMsg)
  extendInstanceService.popup.read(msgId.value, testMsg.value)
}
</script>

<style lang="scss" scoped>
.marketting_dialog-contain {
  display: block;
  width: 580px;
  height: 630px;
  position: relative;
  margin-top: -200px;
  img {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  .btn-close {
    position: absolute;
    width: 60px;
    height: 60px;
    top: -86px;
    right: 0;
    background: url('../assets/img/common/btn-close.png') no-repeat;
    background-size: contain;
  }
}
</style>
