<template>
  <div
    class="btn-base"
    :class="{ isimg: !showText, noshadow: isTransText, noclick }"
    :style="curStyles"
    role="button"
    :aria-label="ariaLabelText"
    v-on:touchstart="iosTouch"
  >
    <wx-open-launch-weapp
      role="button"
      :aria-label="ariaLabelText"
      :id="`h5tomini_${moduleId}`"
      :username="clickEvent.miniUsername"
      :path="clickEvent.path"
      :style="`
        position: absolute;
        z-index: 100;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width:${curStyles.width};
        height: ${curStyles.height};
        overflow: hidden;`"
      v-if="!isInMini && isH5toMini"
    >
      <div v-is="'script'" type="text/wxtag-template">
        <div style="display: block; width: 500px; height: 500px"></div>
      </div>
    </wx-open-launch-weapp>

    <p v-if="showText">{{ text ? text : configData.text }}</p>

    <img v-else class="img" :src="img ? img : configData.imgUrl" />
  </div>
</template>

<script setup lang="ts">
/**
 * 基础的按钮组件，后续所有按钮都可以用这个
 *
 */
import focusCore from '@/service/focus-core'
import { toRefs, unref, computed, ref, onMounted, watchEffect } from 'vue'
import { useModuleDataResolve } from '../packages/hooks'

const props = defineProps<{
  moduleData: any
  customStyle?: any
  text?: string
  img?: string
  clickAction?: Function
  noclick?: Boolean
}>()
const { moduleData, customStyle } = toRefs(props)

const { configData, styleData, moduleType, moduleId } = useModuleDataResolve<{
  uiType: string
  imgUrl: string
  ariaLabel: string
  text: string
  clickEvent: any
}>(moduleData)

watchEffect(() => {
  // console.log('🚀 ~ file: BtnBase.vue ~ line 54 ~ configData', configData)
})
const clickEvent = configData.value.clickEvent || {}
const showText = computed<boolean>(() => {
  return configData.value.uiType === 'text'
})
const isInMini = ref(false)
const isH5toMini = computed(() => {
  const data = configData.value.clickEvent
  const { miniUsername, jumpType } = data
  return focusCore.env.isInWx && jumpType === 'mini'
})
const isTransText = ref(false)
const ariaLabelText = computed(() => {
  return showText.value ? configData.value.text : '跳转'
})
const curStyles = computed(() => {
  const _customStyle = (customStyle && customStyle.value) || {}
  const { boxSize, bg, margins, fontStyles, fontSize, position } = unref(styleData)

  const boxBg = {
    backgroundImage: showText.value ? bg && bg.backgroundImage : 'none',
    backgroundColor: showText.value ? bg && bg.backgroundColor : 'transperent',
    borderRadius: showText.value ? boxSize && boxSize.borderRadius : 'none',
  }

  let textStyles: any = {
    ...fontStyles,
    ...fontSize,
  }
  if (!showText.value) {
    textStyles = {}
  }
  const result = {
    ...boxSize,
    ...margins,
    ...boxBg,
    ...textStyles,
    ...position,
    ..._customStyle,
  }
  // console.log('🚀 ~ file: BtnBase.vue ~ line 91 ~ curStyles ~ result', result)

  if (bg.backgroundColor && showText.value) {
    const reg = /rgba\(.*0\)$/
    isTransText.value = reg.test(bg.backgroundColor)
  }

  // 图片模式移除bgcolor
  if (!showText.value) {
    result.backgroundColor = 'transperent'
  }

  return result
})

onMounted(() => {
  if (window.wx) {
    window.wx.miniProgram.getEnv((res: any) => {
      isInMini.value = res.miniprogram || false
    })
    // // 获取到小程序 wx-open-launch-weapp 标签对应的 dom
    const dom = document.getElementById(`h5tomini_${moduleId}`)

    dom &&
      dom.addEventListener('click', () => {
        console.log('success0', moduleId)
      })
    dom &&
      dom.addEventListener('launch', () => {
        console.log('success1', moduleId)

        // this.handleClick()
        if (props.clickAction) {
          props.clickAction()
        }
      })
    dom &&
      dom.addEventListener('error', function (e: any) {
        console.log('fail1', e.detail)
      })
  }
})

function iosTouch() {
  console.log('这只是用来处理ios的点击伪类生效 :active')
}
</script>

<style lang="scss" scoped>
.btn-base {
  flex: none;
  text-align: center;
  word-break: break-all;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: none;
  width: 100%;
  font-size: 32px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
  background-repeat: no-repeat;
  background-size: 100%;
  cursor: pointer;
  position: relative;
  .img {
    width: 100%;
    height: 100%;
  }
  &:active {
    opacity: 0.6;
  }
  &.isimg {
    background-color: none;
    box-shadow: none;
  }
  &.noshadow {
    box-shadow: none;
  }
  &.noclick {
    &:active {
      opacity: 1;
    }
  }
}
</style>
