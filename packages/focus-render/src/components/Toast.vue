<template>
  <Mask :transperentBg="true" v-if="toastMsg" class="toast">
    <div class="contain">{{ toastMsg }}</div>
  </Mask>
</template>

<script setup lang="ts">
import Mask from './Mask.vue'
import { toRefs } from 'vue'
import { focusStore } from '../packages/focus-render/index'
const { modalStore } = focusStore.stores
const { toastMsg } = toRefs(modalStore)
</script>

<style lang="scss" scoped>
.toast {
  z-index: 9999;
}
.contain {
  font-size: 32px;
  line-height: 1.5;
  background: rgba($color: #000000, $alpha: 0.5);
  padding: 10px 30px;
  border-radius: 10px;
  margin-top: -500px;
  color: #fff;
}
</style>
