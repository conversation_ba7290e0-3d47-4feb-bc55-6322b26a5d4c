<template>
  <div
    class="mask_wxshare"
    @touchmove.prevent
    v-if="showShareMesk"
    @click.stop="hideMask"
    style="color: #fff; text-align: center"
  >
    <div class="share_wrap">
      <div class="share_guide_wrap"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { focusStore } from '../packages/focus-render/index'
import { storeToRefs } from 'pinia'
const { modalStore } = focusStore.stores
const { showShareMesk } = storeToRefs(modalStore)

const hideMask = () => {
  modalStore.shareMaskContrl(false)
}
</script>

<style lang="scss" scoped>
.mask_wxshare {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 8889;
  background: rgba($color: #000000, $alpha: 0.4);
  /* share */
  .share_guide_wrap {
    position: absolute;
    top: 0;
    right: 0;
    width: 222px;
    height: 121px;
    background: url('../assets/img/common/share_new.png') no-repeat;
    background-size: 100% 100%;
  }
}
</style>
