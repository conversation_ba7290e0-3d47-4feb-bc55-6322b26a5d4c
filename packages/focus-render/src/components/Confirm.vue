<template>
  <Mask class="confirm-dialog" v-show="confirmDialog.show" @touchmove.prevent>
    <div class="contain">
      <sdiv class="wrap">
        <div class="title" v-show="confirmDialog.title">{{ confirmDialog.title }}</div>
        <div class="content" :style="{ textAlign: confirmDialog.contentAlign || 'center' }">
          <p v-for="(msg, index) in confirmDialog.contents" :key="index">
            {{ msg }}
          </p>
        </div>
      </sdiv>
      <div class="btns">
        <div class="cancel" @click="clickCancel" v-show="!confirmDialog.hideCancel">
          {{ confirmDialog.btnCancelText || '取消' }}
        </div>
        <div class="confirm" @click="clickConfrim" v-show="!confirmDialog.hideConfirm">
          {{ confirmDialog.btnConfirmText }}
        </div>
      </div>
    </div>
  </Mask>
</template>

<script setup lang="ts">
import Mask from './Mask.vue'
import { focusStore, focusCore } from '../packages/focus-render/index'
import { storeToRefs } from 'pinia'
import jumpService from '../service/jumpService'
const { modalStore } = focusStore.stores
console.log('🚀 ~ file: Confirm.vue ~ line 24 ~ modalStore', modalStore)
const { confirmDialog } = storeToRefs(modalStore)
const clickCancel = () => {
  modalStore.confirmContrl({ show: false, contents: [] })
  logClick('cancel')
}

function logClick(type: 'cancel' | 'confirm') {
  focusCore.eventLog(`click_dialog_btn`, type, {
    title: `${confirmDialog.value.title}`,
    contents: confirmDialog.value.contents,
    confirmText: confirmDialog.value.btnConfirmText,
  })
}

const clickConfrim = () => {
  const jumpConfig = confirmDialog.value.btnConfirmJumpConfig
  const alwaysShow = confirmDialog.value.alwaysShow
  logClick('confirm')

  if (!alwaysShow) {
    setTimeout(() => {
      clickCancel()
    }, 100)
  }
  if (jumpConfig) {
    jumpService.jump(jumpConfig)
    return
  }
  const confirmCb = confirmDialog.value.confirmCb

  if (confirmCb && typeof confirmCb === 'function') {
    confirmCb()
  }
}
</script>

<style lang="scss" scoped>
.confirm-dialog {
  background: rgba($color: #000000, $alpha: 0.4);

  .contain {
    width: 560px;
    background: #fff;
    color: #405080;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-direction: column;
    border-radius: 16px;
    margin-top: -40px;
    .wrap {
      width: 100%;
      min-height: 218px;
      box-sizing: border-box;
      padding: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
    .title {
      font-size: 36px;
      color: #37456e;
      text-align: center;
      line-height: 54px;
      font-weight: bold;
      padding-bottom: 8px;
    }
    .content {
      width: 480px;
      font-size: 32px;
      color: #37456e;
      letter-spacing: 0;
      text-align: center;
      line-height: 48px;
    }
    .btns {
      width: 100%;
      height: 102px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      border-top: 1px solid #efefef;
      > div {
        flex: auto;
        width: 100%;
        height: 100%;
        font-size: 36px;
        color: #456ce6;
        line-height: 52px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        &:nth-child(2) {
          border-left: 1px solid #efefef;
        }
        &.confirm {
          font-weight: 500;
        }
      }
    }
  }
}
</style>
