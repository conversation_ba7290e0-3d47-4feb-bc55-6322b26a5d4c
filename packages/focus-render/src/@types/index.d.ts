interface TFocusCoreInitConfig {
  noNeedLogin?: boolean
  focusFid?: number
  focusAid?: number
  ajaxConfig?: any
  commonConfig?: any
  useLocalModuleData?: any
}
interface TFocusInitResult {
  focusRenderCtr: any
  loginData: any
  nowTime: Dayjs
}

interface TFocusModule {
  moduleId: number
  moduleType: string
  moduleName: string
  pid: number
  moduleSubType?: string
  styles?: {
    bg?: string
    margin?: string
    time?: string
  }
  data: {} | any
}

interface TFocusDataResolve {
  shareConfig: {
    base?: any
    configList: Array<{ desc: string; imgUrl: string; mgmAid: string; path: string; title: string }>
  }
  popupConfig: any[]
  hideModuleIds: number[]
  allModulesData: TFocusModule[] // 所有配置模块
  popupConfig: any[]
  hideModuleIds: number[]
  getShareConfigByConfigId: (id: number | string) => TShareConfig
  needLogin: boolean
  changedModuleDataRef: Ref<any>
  changeModuleConfig: (
    moduleId: number | string,
    options?: {
      hide?: boolean
      remove?: boolean
      data?: any
      style?: any
    },
  ) => {
    render: () => void
  }
  isUseCommonGray: boolean
  pageVer: string
  moduleTree: TFocusModule[] // 可以展示的模块
  findModulesByType: (moduleType: string) => TFocusModule[]
}

interface TFocusCore {
  env: TypeEnv
  init(config?: TFocusCoreInitConfig): Promise<TFocusInitResult>
  _systemTimestamp: number
}
interface TFcousData {
  fid: Number
  apiList: Array<any>
  config: Array<any>
  shareConfig: any
  grayList: Array<TGrayIdItem>
  serviceConfig: any
  popupConfig: Array<any>
  pageVer?: string
}

interface TShareConfig {
  shareTitle?: string
  shareDesc?: string
  shareImg?: string
  shareUrl?: string
  shareMgmAid?: string
  appShareType?: '0' | '1' | '2' | '3' | '4' // 0:拉起朋友圈+好友；1:拉起好友；2:拉起朋友圈；3:直接分享好友；4:直接分享给好友一个小程序卡片
  miniUserName?: 'H5' | 'wxcb823d713276a10d-release' | 'wxcb823d713276a10d-preview' | 'wxdb1f857b4f658a6f-preview'
  mgmAidV2?: string
  shareImageBg?: string
  shareImageHeight?: string
  shareId?: string
}

interface TAppShareConfig {
  shareType: number
  type: string
  title: string
  description: string
  thumbImage: string
  webpageUrl: string
  path?: string
  hdImageData?: string
  userName?: string
  miniProgramType?: string
  shareImage?: string

  // 图片分享的字段
  shareImageHeight?: string
  shareImageWidth?: number
  shareURL?: string
  shareImageHeight?: string
}

interface TAcceptShareParam {
  m1Sid: string
  mgmAid: string
  function_type?: TAcceptShareFunctionType
}

interface TRelationShipItem {
  sid: string
  nick_name: string
  head_img_url: string
  gender: string
  user_name: string
  id: string
  time: string
}

type TTagIds = { id: string; hideModules: number[] }[]
interface TGrayIdItem {
  id: string
  subId: string
  isPage: '0' | '1'
  tagIds: TTagIds
  hideModules?: number[]
  useGrayType?: '0' | '1'
}

interface Product {
  product_period: string
  sale_status: string
  product_name: string
  product_code: string
  product_type: string
  templet_type: string
  rate_value: string
  earnings_rate_date?: string | number
  earnings_rate?: string
  max_earnings_rate?: string
  min_earnings_rate?: string
  show_start_buy_tag: string
  marketing_text: string
  risk_level: string
  extra_info: {
    bank_logo_url: string
    bank_short_name: string
    header_tags: string[]
    tag: string
    min_amount_desc: string
    rate_desc: string
    is_bank_finance: string
    min_amount_desc_recommend: string
    static_rate_tips: string
    ltp_rate_value?: string
  }
  productCanSale?: boolean
}

enum EnumErrorType {
  NOT_IN_PAGE_GRAY = 'not_in_page_gray',
  SAFE_LOCK = 'safe_lock',
}
