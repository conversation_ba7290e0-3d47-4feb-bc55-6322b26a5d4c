interface TModuleData {
  moduleId: number
  moduleName: string
  moduleType: string
  data: any
  styles: TModuleStyles
  moduleSubType?: string
  children?: TModuleData[]
  changeStyles?: {
    color?: string
  }
  changeData?: any
}

interface TModuleStyles {
  width?: number | string // 100/auto
  height?: number | string // 100/auto
  margin?: string // top,right,bottom,left
  /**
   * postion,top,left
   * 可选值
   * none
   * fixed-t
   * fixed-b
   * absolute,0,0
   */
  position?: string
  borderRadius?: number
  font?: string // size,weight,text-align,color
  bg?: string
  time?: string // off/on
}

interface TModuleStylesResolved {
  boxSize?: any
  bg?: any
  margins?: any
  fontStyles?: any
  fontSize?: any
  position?: any
  borderRadius?: any
}

namespace FocusConditionBox {
  type conditionList = {
    conditionId: number
    moduleIds: number[]
  }[]
}

enum allModuleTypes {
  MImage = 'MImage',
}

type targetModules = {
  MImage: NFocusImage.moduleData
}

type MapTModuleData<T> = TModuleData & targetMmodules[T]

type ObjectFromList<T extends ReadonlyArray<string>, V = string> = {
  [K in T extends ReadonlyArray<infer U> ? U : never]: V
}
type ObjectFromListArray<T extends ReadonlyArray<string>, V = string> = {
  [K in T extends ReadonlyArray<infer U> ? U : never]: V
}
/**
 * 具体某个组件的参数
 */

namespace NFocusImage {
  type moduleData = TModuleData & {
    moduleType: 'MImage'
    data: configData
  }

  type configData = {
    imgUrl: string
    imgCanScan: boolean
  }

  export const dynamicKey: ['imgUrl'] = ['imgUrl']
  type dynamicData = ObjectFromList<typeof dynamicKey>
}

namespace NFocusBox {
  type moduleData = TModuleData & {
    moduleType: 'MBox'
    data: configData
  }

  type configData = {
    // imgUrl: string
    flex: string
    boxh: string
  }
}

namespace NFocusActCoinConvertList {
  type moduleData = TModuleData & {
    moduleType: 'ActCoinConvertList'
    data: configData
  }

  type configData = {
    // imgUrl: string
    shareConfigId: string
    aid: string
    myRewardLink: string
  }
}

namespace NFocusActCoinHistory {
  type moduleData = TModuleData & {
    moduleType: 'ActCoinHistory'
    data: configData
  }

  type configData = {
    // imgUrl: string
    // aid: string
    types: string
  }
}

namespace NFocusText {
  type moduleData = TModuleData & {
    moduleType: 'MText'
    data: configData
  }

  type configData = {
    text: string
    colorMode: '0' | '1'
    grayTexts: string
  }

  export const dynamicKey: ['text'] = ['text']
  type dynamicData = ObjectFromList<typeof dynamicKey>
}

namespace NFocusSwiperBox {
  type moduleData = TModuleData & {
    moduleType: 'MSwiperBox'
    data: configData
  }

  type configData = {
    uiType: string
    cardWidth: string
    cardHeight: string
    dotColor: string
    cardImgs: string
    autoPlay: string
  }

  export const dynamicKey: ['text'] = ['text']
}

namespace NFocusRankList {
  type moduleData = TModuleData & {
    moduleType: 'RankList'
    data: configData
  }

  type configData = {
    apiKeyIndex: string //排行榜数据源ID
    showListLen: string //列表显示数量，排行榜总条数超出这个配置，则显示“更多”
    showListMax: string // 列表显示的最大数量，最多显示200条
    moreListLink: string //二级页面的跳转链接
    color1: string // 序号、昵称颜色
    color2: string // 分数颜色
    title3: string //第三列标题
  }

  export const dynamicKey: [
    'confRankIndex',
    'invited',
    'personalDsDate',
    'rankDsDate',
    'rankIndex',
    'rankList',
    'userScore'
  ] = ['confRankIndex', 'invited', 'personalDsDate', 'rankDsDate', 'rankIndex', 'rankList', 'userScore']

  type dynamicData = {
    confRankIndex: string
    invited: string
    personalDsDate: string
    rankDsDate: string
    rankIndex: string
    rankList: any[]
    userScore: string
  }
}

namespace NFocusBtnMgm {
  type moduleData = TModuleData & {
    moduleType: 'BtnMgm'
    data: configData
  }

  type configData = {
    fromAid: string
    configId: string
    imgUrl2: string
    imgUrl1: string
    imgUrl3: string
    joinAid: string
    noAccountCanShare: boolean
  }
}
namespace NFocusMgmList {
  type moduleData = TModuleData & {
    moduleType: 'MgmList'
    data: configData
  }

  type configData = {
    activedColor: string
    mgmAid: string
    titleBgColor: string
    titleFont: string
  }
}

namespace NFocusMgmListClassic {
  type moduleData = TModuleData & {
    moduleType: 'MgmList'
    data: configData
  }

  type configData = {
    mgmAids: string[]
    uiType: {
      type: '1' | '2' | '3' | '4'
      tabsText: {
        '0000': string
        '0001': string
        '0002': string
        '9999': string
      }
      headerColor: string
      headerBg: string
      headerActivedBg: string
      headerActivedColor: string
      old9999: '0' | '1'
    }
    minSize: string
    maxSize: string
    titleColor: string
    activedColor: string
    navBg: string
  }
}

namespace NFocusBtnAcceptShare {
  type moduleData = TModuleData & {
    moduleType: 'BtnAcceptShare'
    data: configData
  }

  type configData = {
    activedColor: string
    mgmAid: string
    titleBgColor: string
    titleFont: string
  }
}

namespace NFocusLottery {
  type moduleData = TModuleData & {
    moduleType: 'BtnAcceptShare'
    data: configData
  }

  type configData = {
    configId: string
    aids: string
    myRewardLink: string
    mechinBg: string
    priceSlectedBg: string
    btnUp: string
    btnDisabled: string
    btnColor: string
    light: string
    lightOff: string
    isShowLamp: boolean
  }
}

namespace NFocusBtnClick {
  type moduleData = TModuleData & {
    moduleType: 'BtnClick'
    data: configData
    moduleId: number
  }

  type configData = {
    uiType: string
    imgUrl: string
    ariaLabel: string
    text: string
    clickEvent: any
  }
}
namespace NFocusAnchorNav {
  type moduleData = TModuleData & {
    moduleType: 'AnchorNav'
    data: configData
  }

  type configData = {
    listData: string
    // "[{"taskId":"","imgUrl1":"https://www.webankwealthcdn.net/s/hjupload/hjAdmAdminPic/point_draw_smallbanner-***********.png","imgUrl2":""},{"taskId":"","imgUrl1":"https://www.webankwealthcdn.net/s/hjupload/hjAdmAdminPic/point_draw_smallbanner-***********.png","imgUrl2":""}]"
    topping: boolean
  }
}
namespace NFocusBtnAdding {
  type moduleData = TModuleData & {
    moduleType: 'BtnAdding'
    data: configData
  }

  type configData = {
    uiType: string
    imgUrl: string
    ariaLabel: string
    text: string
    clickEvent: any
    isJumpNow: '0' | '1'
    jumpType: 'joinNow' | 'noJoinNow'

    once: '1' | '0' // 0 每次都触发效果；1 只在首次参与，触发一次
    success: {
      bg: string
      font: string
      imgUrl: string
      text: string
    }
  }
}

namespace NFocusPlaceHolder {
  type moduleData = TModuleData & {
    moduleType: 'PlaceHolder'
    data: configData
  }

  type configData = {
    text: ''
    params: string[]
  }
}

namespace NMgmListCompany {
  type moduleData = TModuleData & {
    moduleType: 'MgmListCompany'
    data: configData
  }

  type configData = {
    apiKey: string
  }

  type listItemConfig = {
    nickName: string
    headImgUrl: string
    inviteTime: string
    registerTime: string
    certifiedTime: string
    certifiedCompanyName: string
    completeTime: string
    userStatus: string
  }

  export const dynamicKey: ['mgmListNew', 'mgmListOld'] = ['mgmListNew', 'mgmListOld']
  type dynamicData = ObjectFromListArray<typeof dynamicKey, listItemConfig[]>
}

namespace FamilyProductList {
  type moduleData = TModuleData & {
    moduleType: 'FamilyProductList'
    data: configData
  }

  type configData = {
    codes: string[]
    fromAid: string
    configId: string
  }
}

namespace NFocusBtnDownload {
  type moduleData = TModuleData & {
    moduleType: 'BtnClick'
    data: configData
  }

  type configData = {
    imgUrl: string
    androidLink: string
    iosLink: string
    links: string
  }
}

namespace NFocusMyRewardList {
  type moduleData = TModuleData & {
    moduleType: 'MyRewardList'
    data: configData
  }

  type configData = {
    aids: string
    listData: string // jsonStr
    // JSON.stringify([
    //   {
    //     cardId: '',
    //     title: '',
    //     imgUrl: '',
    //     desc: '',
    //     clickEvent: {
    //       ...defaultClickEvent
    //     }
    //   }
    // ])
  }
}

namespace NClickEvent {
  export interface item {
    path: string
    query?: any
    jumpType: string
    popid: number
    effect: EnumEffect | string
    miniUsername: string
    miniEnv: string
    //日历订阅
    caltitle?: string
    calnotes?: string
    calstartTime?: string
    calremindBefore?: string
    caltext?: string
    calimg?: string
    //跳转app签名链接
    jumpAppPath?: string
    copyStr?: string
  }
  export enum EnumEffect {
    Popup = 'popup',
    Jump = 'jump',
  }
}

namespace NFocusBtnAssistance {
  type moduleData = TModuleData & {
    moduleType: 'BtnAssistanceV2'
    data: configData
    moduleId: number
  }

  type configData = {
    imgUrl1: string
    imgUrl2: string
    jumpUrl: string
    jumpUrl2: string
    urlMgmAid: string
  }
}
namespace NFocusMVideo {
  type moduleData = TModuleData & {
    moduleType: 'MVideo'
    data: configData
    moduleId: number
  }

  type configData = {
    autoPlay: boolean
    imgUrl: string
    title: string
    videoUrl: string
  }
}

namespace NFocusCountDownTimer {
  type moduleData = TModuleData & {
    moduleType: 'CountDownTimer'
    data: configData
  }

  type configData = {
    endTime: string
    uiType: '1' | '2' | '3' // UI类型：1： 时分秒；2：天时分；3：天时分秒
  }
}

namespace NFocusInputShareCode {
  type moduleData = TModuleData & {
    moduleType: 'InputShareCode'
    data: configData
  }

  type configData = {
    colorInputBox: string
    colorPlaceHolder: string
    bgBtn: string // 支持渐变，用逗号隔开,仅限2个颜色
    colorBtnText: string
  }
}

namespace NFocusFundCoupon {
  type moduleData = TModuleData & {
    moduleType: 'FundCoupon'
    data: configData
  }

  type configData = {
    tip: string
    listData: string
    // listData: "[{"couponId":"123","btnBg1":"#e87b35","btnBg2":"#e87b35","btnText2":"去使用","clickEvent":{"effect":"jump","jumpType":"mini2mini","path":"","miniUsername":"gh_98abdef897c8","miniEnv":"release","query":{},"officialAccount":"微众银行Webank","appId":"wxcb823d713276a10d","jumpAppPath":"","copyStr":""}},{"couponId":"1232132","btnBg1":"#e87b35","btnBg2":"#e87b35","btnText2":"去使用","clickEvent":{"effect":"jump","jumpType":"custom","path":"/coupon_toast","query":{},"miniUsername":"gh_98abdef897c8","miniEnv":"release","officialAccount":"微众银行Webank","appId":"wxcb823d713276a10d","subAid":"","subTemps":"","subPlatformId":"","caltitle":"","calnotes":"","calstartTime":null,"calremindBefore":null,"caltext":"","calimg":"","popid":null,"equityCreditTaskId":"","jumpAppPath":"","copyStr":""}}]"
    claimBtnBg: string
    popupTip: string
  }
}
