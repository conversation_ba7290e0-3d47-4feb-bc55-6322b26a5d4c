import { dayjs } from '../utils/index'
import { focusCore, focusStore } from '../packages/focus-render/index'
import { EnumMethod } from './focus-core/plugins/Ajax'
import hjCoreIabPlus from '../service/focus-core/plugins/hjCoreIabPlus/hjCoreIabPlus'
import waLog from '../service/focus-core/waLog'

interface CheckActivityStatusResponse {
  isBeforeStartTime?: boolean
  isAfterEndTime?: boolean
  isReadyStatus?: boolean
  isSupportNoAuth?: boolean
  isUsedAndNotInGray?: boolean
  isUsedAndInGray?: boolean
  isNotUsedGray?: boolean
  isDuring?: boolean
}

interface DeviceInfo {
  DeviceID: string
  [key: string]: any
}

const cgis = {
  setWrInfo: {
    name: '参与活动',
    method: EnumMethod.POST,
    url: '/hjop/welfare/setwrinfo',
  },
  queryWrInfo: {
    name: '查询参与活动状态',
    url: '/hjop/welfare/querywrinfo',
    canSaveTimes: true,
    cacheWithQueryKeys: ['activity_id'],
  },
  queryConvertList: {
    name: '查询商品兑换列表',
    url: '/wm-htrserver/cop/hj/virtual_coin/goods/list',
  },
  convertGoodByActCoins: {
    name: '用代币兑换商品',
    url: '/wm-htrserver/cop/hj/virtual_coin/goods/exchange',
  },
  queryActCoinsHistory: {
    name: '代币消费记录',
    url: '/wm-htrserver/cop/hj/virtual_coin/detail/list',
  },
  activityInfoV2: {
    name: '查询活动信息-营销中台',
    url: '/op-fmfront/cop/hj/fm/activity/query-activity',
  },
  activityBrowsePreference: {
    name: '活动浏览偏好',
    url: '/wm-htrserver/cop/hj/save_user_activity_browse_preference',
    method: EnumMethod.POST,
  },
  checkActivityStatus: {
    name: '查询活动状态-已开户',
    url: '/op-fmfront/activity/hj/fm/activityAccountOpened',
  },
  checkActivityStatusNoAuth: {
    name: '查询活动状态-未开户',
    url: '/op-fmfront/activity/hj/fm/activityAccountNotOpened',
  },
}

export interface TActivityStatus {
  activityId: number // 活动id
  isDuring: boolean // 在活动期间
  isBeforeStartTime: boolean // 当前时间在活动开始前
  isAfterEndTime: boolean // 当前时间在活动开始后
  isReadyStatus: boolean // 活动是否已上架
  isSupportNoAuth: boolean // 未开户是否可参与
  isUsedAndNotInGray: boolean // 用了灰度且用户不在灰度名单
  isUsedAndInGray: boolean // 用了灰度且用户在灰度名单
  isNotUsedGray: boolean // 未使用灰度
  noNeedCheck?: boolean
}

export namespace NConvertItem {
  export interface itemGoods {
    cardId: string
    exchangeSuccUrl: string // 兑换成功的图
    goodsCode: string // 运营自定义的code
    name: string // 商品名
    price: Array<ItemPrice>
    haveInStock: number // 0 没库存 1 有库存
    showUrl: string // 商品图片
    status: number
    userLimit: number // 用户最高可兑换次数
    exchangedTimes: number // 用户已兑换次数
    userCanbuy: number // 用户还能兑换次数
  }

  export interface ItemPrice {
    coinType: string
    amount: number
    coinUrl: string
    coinName: string
  }
}

class ActivityService {
  constructor() {}

  // 活动浏览上报
  async activityBrowseMarkingReport(aid: number | string): Promise<boolean> {
    const activityId = Number(aid)
    let deviceId = ''
    let deviceInfo: any = {}
    try {
      deviceInfo = await hjCoreIabPlus.getDeviceInfo()
      console.log(
        '🐬 ~ file: activityService.ts:73 ~ ActivityService ~ activityBrowsePreference ~ deviceInfo:',
        deviceInfo,
      )
      deviceId = deviceInfo.DeviceID
    } catch (error) {
      console.log('🐬 ~ file: activityService.ts:83 ~ ActivityService ~ activityBrowsePreference ~ error:', error)
      return Promise.resolve(false)
    }

    waLog.eventLog('activity.browseMarkingReport', deviceId, { url: window.location.href, activityId, ...deviceInfo })
    if (!aid || !deviceId) {
      console.log('没有活动id或者没有设备id')
      return Promise.resolve(false)
    }
    return new Promise((resolve) => {
      focusCore
        .request(
          cgis.activityBrowsePreference,
          {},
          {
            activityId,
            deviceId: deviceId,
          },
        )
        .then((res: any) => {
          console.log('🐬 ~ file: activityService.ts:101 ~ ActivityService ~ .then ~ res:', res)
          waLog.eventLog('activity.browseLog', res.status, { url: window.location.href, activityId, ...deviceInfo })
          if (res.status) {
            console.log('未开户自动参与活动浏览打标成功')
            resolve(true)
          } else {
            console.log('未开户自动参与活动浏览打标失败')
            resolve(false)
          }
        })
        .catch((err: any) => {
          console.log('🐬 ~ file: activityService.ts:80 ~ ActivityService ~ returnnewPromise ~ err:', err)
          waLog.eventLog('activity.browseLog', 'error', {
            url: window.location.href,
            activityId,
            ...deviceInfo,
            err,
          })
          resolve(false)
        })
    })
  }

  /**
   * 查询活动是否可用
   * @param aids
   * @returns
   */
  checkAidsIsReady(aid: string | number): Promise<TActivityStatus> {
    console.log('🚀 ~ ActivityService ~ checkAidsIsReady ~ aid:', aid)
    const defaultData = {
      isBeforeStartTime: true,
      isAfterEndTime: false,
      isReadyStatus: true,
      isSupportNoAuth: true,
      isUsedAndNotInGray: false,
      isUsedAndInGray: false,
      isNotUsedGray: true,
      isDuring: false,
    }
    if (!aid) {
      return Promise.resolve({
        ...defaultData,
        activityId: 0,
        noNeedCheck: true,
      })
    }
    return new Promise((resolve, reject) => {
      const cgi = focusCore.hasAccount ? cgis.checkActivityStatus : cgis.checkActivityStatusNoAuth
      console.log('🚀 ~ ActivityService ~ returnnewPromise ~ cgi:', cgi)
      return focusCore
        .request(cgi, {
          activityId: aid.toString(),
        })
        .then(
          (res: {
            activityState: {
              activityId: string
              checkAccountStatus: string //1-未开户可参与；2-未开户不可参与
              checkGrayStatus: string // 1-在灰度；2-不在灰度
              checkGrayUsedStatus: string // 1-已用灰度；2-未用灰度
              activityRangeStatus: string //1-有效期前；2-有效期中；3-有效期后
              activityStatus: boolean // 活动上下架状态
              needCheck: boolean
            }
          }) => {
            const resStatus: { [k: string]: any } = {}
            const {
              activityRangeStatus,
              checkAccountStatus,
              checkGrayStatus,
              checkGrayUsedStatus,
              activityStatus,
              needCheck,
            } = res?.activityState || {}
            const data = {
              ...defaultData,
              activityId: Number(aid),
              isBeforeStartTime: activityRangeStatus === '1',
              isAfterEndTime: activityRangeStatus === '3' || !activityStatus,
              isDuring: activityRangeStatus === '2' && activityStatus,
              isReadyStatus: activityStatus,
              isSupportNoAuth: checkAccountStatus === '1' && checkGrayUsedStatus === '2',
              isUsedAndNotInGray: checkGrayStatus === '2' && checkGrayUsedStatus === '1',
              isUsedAndInGray: checkGrayStatus === '1' && checkGrayUsedStatus === '1',
              isNotUsedGray: checkGrayUsedStatus === '2',
              noNeedCheck: !needCheck,
            }

            const { activityStore } = focusStore.stores
            activityStore.updateAidsStatus(data)
            return resolve(data)
          },
        )
        .catch((err) => {
          console.log('🚀 ~ ActivityService ~ returnnewPromise ~ err:', err)
          const defaultData = {
            activityId: Number(aid),
            isBeforeStartTime: false,
            isAfterEndTime: false,
            isDuring: false,
            isReadyStatus: false,
            isSupportNoAuth: false,
            isUsedAndNotInGray: false,
            isUsedAndInGray: false,
            isNotUsedGray: false,
            noNeedCheck: true,
          }
          const { activityStore } = focusStore.stores
          activityStore.updateAidsStatus(defaultData)

          return resolve(defaultData)
        })
    })
  }

  /**
   * 查询参与状态
   * @param aid
   * @returns
   */
  checkWrInfoStatus(aid: string | number): Promise<number> {
    const { activityStore } = focusStore.stores
    const addingAids = activityStore.addingAids
    const _aid = Number(aid)
    if (!_aid) {
      console.error('没有活动id，不执行 querywrinfo')
      return Promise.resolve(0)
    }
    if (addingAids.indexOf(_aid) > -1) {
      return Promise.resolve(_aid)
    }
    return new Promise((resolve) => {
      focusCore
        .request(cgis.queryWrInfo, {
          activity_id: _aid,
        })
        .then((res: any) => {
          if (res.hasRecord) {
            activityStore.updatedAids(_aid)
            return resolve(_aid)
          }
          return resolve(0)
        })
        .catch((err: any) => {
          resolve(0)
        })
    })
  }

  /**
   * 是否是当天参与
   * @param aid
   */
  checkWrInfoIsToday(aid: string | number): Promise<{
    isT0orT1: boolean
    aid: number
    nowTime?: dayjs.Dayjs
    addingTime?: dayjs.Dayjs
  }> {
    const { activityStore } = focusStore.stores
    const addingAids = activityStore.addingAids
    const _aid = Number(aid)
    if (!_aid) {
      console.error('没有活动id，不执行 querywrinfo')
      return Promise.resolve({
        isT0orT1: false,
        aid: 0,
      })
    }
    return new Promise((resolve) => {
      focusCore.getNowTime().then((nowTime) => {
        focusCore
          .request(cgis.queryWrInfo, {
            activity_id: _aid,
          })
          .then((res: any) => {
            console.log('🚀 ~ file: activityService.ts:104 ~ ActivityService ~ .then ~ res:', res)
            if (res.hasRecord) {
              const time = res.createTime ? res.createTime * 1000 : ''
              const nextDay = dayjs(nowTime).add(1, 'd')

              activityStore.updatedAids(_aid)
              const isT0 = time
                ? dayjs(time).isSame(nowTime, 'day') &&
                  dayjs(time).isSame(nowTime, 'month') &&
                  dayjs(time).isSame(nowTime, 'year')
                : false
              const isT1 = time
                ? dayjs(time).isSame(nextDay, 'day') &&
                  dayjs(time).isSame(nextDay, 'month') &&
                  dayjs(time).isSame(nextDay, 'year')
                : false
              return resolve({
                isT0orT1: isT1 || isT0,
                aid: _aid,
                nowTime,
                addingTime: dayjs(time),
              })
            }
            return resolve({
              isT0orT1: false,
              aid: 0,
              nowTime,
            })
          })
          .catch((err: any) => {
            resolve({
              isT0orT1: false,
              aid: 0,
              nowTime,
            })
          })
      })
    })
  }

  setWrInfo(aid: number | string): Promise<number | (TActivityStatus & { isFail: boolean })> {
    const { activityStore, userStore } = focusStore.stores
    const _aid = Number(aid)
    if (!_aid) {
      console.error('没有活动id！不执行setwrinfo')
      return Promise.resolve(0)
    }
    return new Promise((resolve, reject) => {
      this.checkWrInfoStatus(aid).then((resAid) => {
        if (resAid) {
          return resolve(resAid)
        }
        focusCore
          .request(
            cgis.setWrInfo,
            {},
            {
              activity_id: Number(_aid),
            },
          )
          .then(
            (res: {
              retCode?: string // -1 无效活动；-2 未开户不可参与；-3 不在灰度中；-4 活动未开始；-5 活动已结束；1 正常
              activityState: {
                activityId: string
                checkAccountStatus: string //1-未开户可参与；2-未开户不可参与
                checkGrayStatus: string // 1-在灰度；2-不在灰度
                checkGrayUsedStatus: string // 1-已用灰度；2-未用灰度
                activityRangeStatus: string //1-有效期前；2-有效期中；3-有效期后
                activityStatus: boolean // 活动上下架状态
                needCheck: boolean
              }
            }) => {
              const { activityState } = res

              if (res.retCode === '1' || !res.retCode) {
                activityStore.updatedAids(_aid)
                return resolve(_aid)
              }
              let errCode = ''
              if (activityState.activityRangeStatus === '1') {
                errCode = '-4'
              }
              if (!activityState.activityStatus || activityState.activityRangeStatus === '3') {
                errCode = '-1'
              }
              if (activityState?.checkGrayStatus === '2' && activityState?.checkGrayUsedStatus === '1') {
                errCode = '-3'
              }
              console.log('🚀 ~ ActivityService ~ this.checkWrInfoStatus ~ errCode:', errCode)

              return reject({ isFail: true, errCode })
            },
          )
          .catch((err: any) => {
            console.log('🚀 ~ ActivityService ~ this.checkWrInfoStatus ~ err:', err)
            let errMsgs = {}
            return reject({ isFail: true })
          })
      })
    })
  }

  convertGoodByActCoins(
    activityId: string | number,
    goodsCode: string | number,
    userPwd: string,
  ): Promise<{ status: boolean; errorStatus?: number; errorMsg?: string }> {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.convertGoodByActCoins, { activityId, goodsCode, userPwd })
        .then((res: any) => {
          console.log('🚀 ~ file: activityService.ts ~ line 81 ~ ActivityService ~ .then ~ res', res)
          if (/0000$/.test(res.ret_code)) {
            return resolve({ status: true })
          }
          const errorCode = res.ret_code && res.ret_code.substr(res.ret_code.length - 4)

          // "5001", "暂时未能处理您的请求，请稍后再试"
          // "5004", "暂时未能处理您的请求，请稍候重试"

          // "0006", "密码错误"
          // "0007", "交易密码错误。当日连续输错3次将被锁定，次日才能解锁"
          // "0008", "交易密码错误。当日连续输错3次将被锁定，次日才能解锁。您已连续输错2次"
          // "0009", "您已连续输错3次，请明天再试，或点击忘记密码进行密码重置"
          // "0010", "交易密码错误。连续输错6次将被锁定，需要重置密码。您已连续输错4次"
          // "0011", "交易密码错误。连续输错6次将被锁定，需要重置密码。您已连续输错5次"

          // "0101", "商品已下架，或对用户不可见"
          // "0102", "商品库存不足"
          // "0103", "兑换次数已用完"
          // "0104", "系统繁忙，请稍后重试"
          const errorMsgMap = {
            '0006': '密码错误',
            '0009': '您已连续输错3次，请明天再试',
            '0101': '商品已下架',
            '0102': '商品库存不足',
            '0103': '兑换次数已用完',
          }
          const errorMsg = errorMsgMap[errorCode] || '系统繁忙，请重新尝试'

          resolve({ status: false, errorMsg })
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: activityService.ts ~ line 81 ~ ActivityService ~ focusCore.request ~ err', err)
          resolve({ status: false, errorMsg: '系统繁忙，请重新尝试' })
        })
    })
  }

  getConvertList(
    activityId: string | number,
  ): Promise<{ coinVOS: Array<NConvertItem.ItemPrice>; goodsVOS: Array<NConvertItem.itemGoods> }> {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.queryConvertList, { activityId })
        .then((res: { coinVOS: Array<NConvertItem.ItemPrice>; goodsVOS: Array<NConvertItem.itemGoods> }) => {
          console.log('🚀 ~ file: activityService.ts ~ line 74 ~ ActivityService ~ focusCore.request ~ res', res)

          // 用户当前活动代币数量列表
          // cardId: 100493
          // exchangeSuccUrl: "https://txy.test.webank.com/tc-k/querydata/html/hjAdmAdminPic/88-************.png"
          // exchangedTimes: 6
          // goodsCode: "jf"
          // haveInStock: 0
          // name: "积分"
          // price: Array(2)
          // 0: {coinType: "db_red", amount: 0, coinUrl: "https://txy.test.webank.com/tc-k/querydata/html/hjAdmAdminPic/ziqiu-************.png", coinName: "7星主题活动-星球-代币-红"}
          // 1: {coinType: "db_blue", amount: 3, coinUrl: "https://txy.test.webank.com/tc-k/querydata/html/hjAdmAdminPic/lanqiu-************.png", coinName: "7星主题活动-星球-代币-蓝"}
          // length: 2
          // __proto__: Array(0)
          // priority: 99
          // productId: "HJ"
          // showUrl: "https://txy.test.webank.com/tc-k/querydata/html/hjAdmAdminPic/88-************.png"
          // status: 1
          // userLimit: 20
          const { coinVOS = [], goodsVOS = [] } = res

          resolve({
            coinVOS,
            goodsVOS: goodsVOS.map((i) => {
              return {
                ...i,
                price: i.price && i.price.filter((_i) => _i.amount),
                userCanbuy: i.haveInStock ? i.userLimit - i.exchangedTimes : 0,
              }
            }),
          })
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: activityService.ts ~ line 79 ~ ActivityService ~ focusCore.request ~ err', err)

          resolve({ coinVOS: [], goodsVOS: [] })
        })
    })
  }

  getActCoinsHistory(coinTypes: string): Promise<
    {
      usageScenarios: string // 描述
    }[]
  > {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.queryActCoinsHistory, { virtualCoinTypeStr: coinTypes })
        .then((res: any) => {
          console.log('🚀 ~ file: activityService.ts ~ line 122 ~ ActivityService ~ focusCore.request ~ res', res)
          const { coinDetailList = [] } = res
          resolve(coinDetailList)
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: activityService.ts ~ line 124 ~ ActivityService ~ focusCore.request ~ err', err)
          resolve([])
        })
    })
  }

  /*
   * 营销中台的活动信息查询
   * ！！已废弃
   */
  queryActivityInfoFromOpfm(aid: number): Promise<{
    id?: number
    title?: string
    viewStartTime?: string //展示开始时间，date
    viewEndTime?: string //展示结束时间，date
    activityCode?: string
    status?: number
  }> {
    if (!aid) {
      return Promise.resolve({})
    }
    return new Promise((resolve) => {
      focusCore
        .request(cgis.activityInfoV2, {
          id: aid,
        })
        .then(
          (res: {
            activity: {
              status: number
              id: number
              title: string
              viewStartTime: string //展示开始时间，date
              viewEndTime: string //展示结束时间，date
              activityCode: string
            }
          }) => {
            const { activity } = res

            resolve(activity)
          },
        )
        .catch(() => {
          resolve({})
        })
    })
  }
}
export default new ActivityService()
