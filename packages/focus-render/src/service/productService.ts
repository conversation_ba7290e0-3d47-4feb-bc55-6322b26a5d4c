import { focusCore, focusStore } from '../packages/focus-render/index'

interface Product {
  product_code: string
  rate_value?: string | number
  earnings_rate?: string | number
  max_earnings_rate?: string | number
  min_earnings_rate?: string | number
  sale_status?: string
  earnings_rate_date?: string
  extra_info?: {
    min_amount_desc_recommend?: string
    ltp_rate_value?: string | number
  }
  productCanSale?: boolean
}
import dayjs from '@/utils/time-utils'

const cgis = {
  getProductListByCode: {
    needAccount: false,
    noAccountCatch: {},
    name: '通过产品代码查询货架信息（已登录且已开户）',
    url: '/wm-htrserver/finance/product_shelf/query_product_by_codes',
  },
  getProductListByCodeNoAuth: {
    needAccount: false,
    noAccountCatch: {},
    name: '通过产品代码查询货架信息（未登录或者未开户）',
    url: '/wm-htrserver/finance/product_shelf/query_product_by_codes_no_auth',
  },
  stockfundLadderData: {
    needAccount: false,
    noAccountCatch: {},
    name: '基金产品Code查询信息',
    url: '/wm-hjhtr/wm-pqs/history/query/stockfund/ladder_rate/by_codes',
  },
}

interface GetProductListByCodesRes {
  list: Product[]
  total: string
}
const percent = (rate: number, digit = 2, showUnit = true) => {
  return `${rate.toFixed(digit)}` + `${showUnit ? '%' : ''}`
}
/* app处理逻辑 */
const percentStr = (rateStr: string, digit = 2, canAutoAdjust = true, showUnit = true) => {
  if (rateStr === '' || rateStr === undefined || rateStr === null) {
    return '--'
  }
  if (isNaN(Number(rateStr))) {
    return rateStr
  } else {
    let _digit = digit
    const r = /\.(\d+)/.exec(rateStr)
    if (canAutoAdjust && Array.isArray(r)) {
      _digit = r[1].length >= 2 ? r[1].length : 2
    }
    return percent(Number(rateStr), _digit, showUnit)
  }
}
const getAnnuakRateText = (earningsRate?: string, maxEarningsRate?: string, minEarningsRate?: string) => {
  let getRateDigit = (rateStr: string) => {
    const r = /\.(\d+)/.exec(rateStr)
    if (Array.isArray(r)) {
      return r[1].length
    } else {
      return 3
    }
  }
  if (
    maxEarningsRate !== undefined &&
    maxEarningsRate !== '' &&
    minEarningsRate !== undefined &&
    minEarningsRate !== ''
  ) {
    if (maxEarningsRate === minEarningsRate) {
      const annualRate = Number(maxEarningsRate)
      const annualRateDigit = getRateDigit(maxEarningsRate)
      return percent(annualRate, annualRateDigit)
    } else {
      const maxAnnualRate = Number(maxEarningsRate)
      const maxAnnualRateDigit = getRateDigit(maxEarningsRate)
      const minAnnualRate = Number(minEarningsRate)
      const minAnnualRateDigit = getRateDigit(minEarningsRate)
      return `${percent(minAnnualRate, minAnnualRateDigit)}~${percent(maxAnnualRate, maxAnnualRateDigit)}`
    }
  } else {
    if (earningsRate !== undefined && earningsRate !== '') {
      if (isNaN(Number(earningsRate)) && typeof earningsRate === 'string') {
        return earningsRate
      } else {
        const annualRate = Number(earningsRate)
        const annualRateDigit = getRateDigit(earningsRate)
        return percent(annualRate, annualRateDigit)
      }
    } else {
      return '--'
    }
  }
}

class ProductService {
  /**
   *
   * @param data
   * @param support 这是个string数组；内容是  {ladderType}_{具体字段}
   * @returns
   */
  formatFundData(data: any, apiParams: any) {
    const support = ['yearbefore_maximumDrawdown']
    // api_09_1_productCanSale?product_code=850006
    let _data = data as { prodCode: string; ladderRateList: any[] }[]
    if (!_data.length) {
      _data =
        apiParams.codes &&
        apiParams.codes.map((code: string) => {
          return {
            prodCode: code,
            ladderRateList: [],
          }
        })
    }
    console.log('🚀 ~ ProductService ~ formatFundData ~ _data:', _data)
    const result: any = {}
    _data.forEach((i) => {
      const { prodCode, ladderRateList = [] } = i
      const prodKey = `prod_code=${prodCode}`
      support.forEach((i) => {
        const ladderType = i.split('_')[0]
        const targetKey = i.split('_')[1]
        const target = ladderRateList.find((i) => i.ladderType === ladderType)
        console.log('🚀 ~ ProductService ~ support.forEach ~ target:', target)
        const key = `${i}?${prodKey}`
        if (target && target[targetKey] !== undefined) {
          let val = target[targetKey]
          console.log('🚀 ~ ProductService ~ support.forEach ~ val:', val)
          if (!val || val === 0 || val === '0') {
            val = '0.00'
          } else {
            const mark = val.toString().indexOf('-') > -1 ? '-' : '+'
            let absNum = Math.abs(val)
            let num = parseInt((absNum * 1000).toString(), 10).toString()
            val =
              mark + (num.slice(0, num.length - 3) || '0') + '.' + (num.slice(num.length - 3, num.length - 1) || '00')
          }

          result[key] = val + '%'
        } else {
          result[key] = '--'
        }
      })
    })
    return result
  }
  getFundData(codeList: string[]): Promise<{ prodCode: string; ladderRateList: any[] }[]> {
    return new Promise((resolve) => {
      return focusCore
        .request(cgis.stockfundLadderData, {
          codes: codeList,
        })
        .then(
          (res: {
            list: {
              prodCode: string
              ladderRateList: any[]
            }[]
          }) => {
            console.log('🚀 ~ ProductService ~ returnnewPromise ~ res:', res)

            return resolve(res.list)
          },
        )
        .catch((err: any) => {
          console.log('🚀 ~ ProductService ~ returnnewPromise ~ err:', err)
          return resolve([])
        })
    })
  }

  format(list: Product[], codes: string[]): Product[] {
    const orderList: Product[] = []
    /* 按code排序 */
    codes.forEach((code) => {
      const itemByCode = list.find((item) => item.product_code === code)
      if (itemByCode) {
        orderList.push(itemByCode)
      }
    })
    orderList.forEach((item) => {
      console.log('🚀 ~ file: productService.ts:91 ~ ProductService ~ orderList.forEach ~ item:', item)
      const { rate_value = '--', earnings_rate, max_earnings_rate, min_earnings_rate, sale_status } = item
      const rate = percentStr(rate_value.toString())
      const rateRange = getAnnuakRateText(
        earnings_rate?.toString() || '',
        max_earnings_rate?.toString() || '',
        min_earnings_rate?.toString() || '',
      )
      const showRate = max_earnings_rate && min_earnings_rate ? rateRange : rate
      item.earnings_rate_date = item.earnings_rate_date ? dayjs(item.earnings_rate_date).format('YYYY-MM-DD') : '--'
      item.rate_value = showRate
      if (item?.extra_info?.min_amount_desc_recommend) {
        const text = item.extra_info.min_amount_desc_recommend
        item.extra_info.min_amount_desc_recommend = /起购$/.test(text) ? text : `${text}起购`
      }
      if (item.extra_info && item.extra_info.ltp_rate_value) {
        item.extra_info.ltp_rate_value =
          getAnnuakRateText(item.extra_info.ltp_rate_value?.toString() || '' || '') || '--'
      }
      if (sale_status) {
        item.productCanSale = item.sale_status === '02' || item.sale_status === '12'
      }
    })
    return orderList
  }
  /* TODO:内部判断login */
  getProductListByCodes(
    codes: string[] = [],
    accept_cache_asset_info: boolean, // 这个值其实不用传，但是避免改到太多地方，先留着
    hasAccount: boolean,
  ): Promise<Product[]> {
    const { modalStore } = focusStore.stores
    const cgi = hasAccount ? cgis.getProductListByCode : cgis.getProductListByCodeNoAuth
    // const cgi = cgis.getProductListByCodeNoAuth
    return new Promise((resolve) => {
      if (codes.length === 0) return resolve([])
      const queryCodes = codes.slice(0, 30)
      focusCore
        .request(cgi, {
          product_codes: queryCodes,
        })
        .then((res: GetProductListByCodesRes) => {
          resolve(this.format(res?.list || [], queryCodes))
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: taskListService2.ts ~ line 138 ~ TaskService ~ returnnewPromise ~ err', err)
          // modalStore.errorMaskContrl('getProductListByCodes')
          resolve([])
        })
    })
  }

  getProductListByCodesLogin(codes: string[] = [], accept_cache_asset_info = false): Promise<Product[]> {
    return this.getProductListByCodes(codes, accept_cache_asset_info, true)
  }

  getProductListByCodesWithoutLogin(codes: string[] = [], accept_cache_asset_info = false): Promise<Product[]> {
    return this.getProductListByCodes(codes, accept_cache_asset_info, false)
  }
}

export default new ProductService()
