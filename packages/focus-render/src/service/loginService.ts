import { UrlParser } from '../utils/index'
import { focusCore } from '../packages/focus-render/index'
const cgis = {
  querycookie: {
    name: '获取cookie',
    url: '/hjop/user/querycookie',
  },
}
const locationHrefPared = new UrlParser(window.location.href)

interface LoginStatus {
  dcnNo?: string
  hasAccount?: boolean
  uidType?: string
  userId?: string
  userType?: string
  webankToken?: string
  wechatAccessToken?: string
  wechatAppId?: string
  wechatOpenId?: string
  wechatUnionId?: string
  error?: string
  isLogined?: boolean
}

enum EnumErrorType {
  NOT_IN_PAGE_GRAY = 'not_in_page_gray',
  SAFE_LOCK = 'safe_lock',
}
class Login {
  getExchangeWebankTokenFlagCount: number
  constructor() {
    this.getExchangeWebankTokenFlagCount = 0
  }

  checkLoginedInApp(): Promise<{
    dcnNo?: string
    hasAccount?: Boolean
    uidType?: string
    userId?: string
    userType?: string
    webankToken?: string
    wechatAccessToken?: string
    wechatAppId?: string
    wechatOpenId?: string
    wechatUnionId?: string
  }> {
    let getExchangeWebankTokenFlagCount = 0
    const handleExchange = () => {
      console.log('handleExchange')
      return new Promise<{
        exchangeWebankTokenFlag: Boolean
        getExchangeWebankTokenFlagCount: number
      }>((resolve) => {
        try {
          window.hjCoreIab.getExchangeWebankTokenFlag(
            ({ exchangeWebankTokenFlag }: { exchangeWebankTokenFlag: any }) => {
              exchangeWebankTokenFlag = exchangeWebankTokenFlag
              getExchangeWebankTokenFlagCount++
              return resolve({
                exchangeWebankTokenFlag,
                getExchangeWebankTokenFlagCount,
              })
            },
            () => {
              return resolve({
                exchangeWebankTokenFlag: false,
                getExchangeWebankTokenFlagCount: 6,
              })
            },
          ) // 调用错误了(RN 内webview 未提供该方法)，qc
        } catch (e) {
          // 尚未实现该方法(在hj webview打开)，直接qc
          console.error(e)
          return resolve({
            exchangeWebankTokenFlag: false,
            getExchangeWebankTokenFlagCount: 6,
          })
        }
      })
    }

    const doExchangeToken = (): Promise<{
      dcnNo?: string
      hasAccount?: Boolean
      uidType?: string
      userId?: string
      userType?: string
      webankToken?: string
      wechatAccessToken?: string
      wechatAppId?: string
      wechatOpenId?: string
      wechatUnionId?: string
    }> => {
      console.log('doExchange')
      return new Promise((resolve) => {
        setTimeout(() => {
          handleExchange().then(({ getExchangeWebankTokenFlagCount, exchangeWebankTokenFlag }) => {
            if (getExchangeWebankTokenFlagCount < 5 && exchangeWebankTokenFlag === true) {
              doExchangeToken()
            } else {
              return resolve(this.queryCookie())
            }
          })
        }, 100) // 先来0.1s 等待
      })
    }
    return doExchangeToken()
  }

  checkWebankSafeIntercept() {
    const hash = window.location.hash
    const urlQuery = locationHrefPared.query
    const { wb_safe_code, wb_safe_msg, wb_safe_rule_action } = urlQuery
    console.log('获取到的web_safe_code', wb_safe_code)
    const info: {
      wb_safe_format: string
      wb_safe_code: string
      wb_safe_msg: string
      wb_safe_rule_action?: string
    } = {
      wb_safe_format: '',
      wb_safe_code: '',
      wb_safe_msg: '',
      wb_safe_rule_action: '',
    }
    if (hash.indexOf('/account/exception/') !== -1) {
      const exp = hash.match(/^.*\/account\/exception\/(\d)\/(.*$)/) || ''
      info.wb_safe_format = 'hashbang'
      info.wb_safe_code = exp[1]
      info.wb_safe_msg = decodeURIComponent(exp[2])
    } else if (wb_safe_code === '1') {
      info.wb_safe_format = 'param'
      info.wb_safe_code = wb_safe_code
      info.wb_safe_msg = wb_safe_msg
      info.wb_safe_rule_action = wb_safe_rule_action
    }

    return new Promise((resolve, reject) => {
      const isSafeReject =
        info.wb_safe_format === 'hashbang' || (info.wb_safe_format === 'param' && info.wb_safe_code === '1')
      if (isSafeReject) {
        return reject({
          ...info,
        })
      }
      return resolve(null)
    })
  }
  queryCookie(): Promise<{
    dcnNo?: string
    hasAccount?: Boolean
    uidType?: string
    userId?: string
    userType?: string
    webankToken?: string
    wechatAccessToken?: string
    wechatAppId?: string
    wechatOpenId?: string
    wechatUnionId?: string
    error?: string
    isLogined?: boolean
  }> {
    return new Promise((resolve, reject) => {
      this.checkWebankSafeIntercept()
        .then(() => {
          focusCore
            .request(cgis.querycookie)
            .then((res: any) => {
              console.log('🚀 ~ file: login.ts ~ line 7 ~ Login ~ focusCore.request ~ res', res)
              if (res && Object.keys(res).length) {
                const hasAccount = !/[AF]/.test(res.dcnNo[1]) // AF 未开户
                console.log('🚀 ~ file: login.ts ~ line 116 ~ Login ~ .then ~ hasAccount', hasAccount)

                return resolve({ ...res, hasAccount, uidType: this.getUidType(res), isLogined: true })
              }
              reject({ error: 'querycookie_fail' })
            })
            .catch((err: any) => {
              console.log('🚀 ~ file: login.ts ~ line 9 ~ Login ~ focusCore.request ~ err', err)
              const { status } = err
              if (status === 401) {
                return resolve({ error: '401' })
                // if (focusCore.env.isInApp) {
                //   window.hjCoreIab && window.hjCoreIab.login()
                // } else {

                // }
              }
              if (status === 502 && !locationHrefPared.query.first502) {
                console.log('可以尝试重新登录,估计是测试环境和生产环境的登录态冲突')
                // window.location.replace(locationHrefPared.appendQuery({ first502: '1' }).fullPath)
                setTimeout(() => {
                  this.loginWx(true)
                }, 5000)
                return resolve({ error: '502' })
              }
              console.log('确实挂了')
              reject({ error: 'querycookie_fail' })
            })
        })
        .catch((safeErrorInfo) => {
          console.log('🚀 ~ file: login.ts ~ line 171 ~ Login ~ returnnewPromise ~ safeErrorInfo', safeErrorInfo)
          reject({
            error: EnumErrorType.SAFE_LOCK,
            errorInfo: safeErrorInfo,
          })
        })
    })
  }

  checkLogined(): Promise<{
    dcnNo?: string
    hasAccount?: Boolean
    uidType?: string
    userId?: string
    userType?: string
    webankToken?: string
    wechatAccessToken?: string
    wechatAppId?: string
    wechatOpenId?: string
    wechatUnionId?: string
    qqOpenId?: string
    error?: any
  }> {
    console.log('checkLogined')
    if (focusCore.env.isInApp) {
      return this.checkLoginedInApp()
    }
    return this.queryCookie()
  }

  loginWx(isFirst502: boolean) {
    const pmbankUrl = this.getPmbankUrlForWx(isFirst502)
    const social_url_params = {
      appid: focusCore.baseConfig.appId,
      response_type: 'code',
      scope: 'snsapi_userinfo',
      state: 'STATE#wechat_redirect',
      redirect_uri: pmbankUrl,
      // forcePopup: true,
      // forceSnapShot: true,
    }

    const wxOauthUrl = 'https://open.weixin.qq.com/connect/oauth2/authorize'
    const urlParser = new UrlParser(wxOauthUrl)

    const url = urlParser.appendQuery(social_url_params).fullPath
    location.replace(url)
  }

  getPmbankUrlForWx(isFirst502: boolean) {
    let curUrl = locationHrefPared.fullPath
    const urlQuery: {
      first502?: string
      relogin?: string
    } = {
      relogin: '1',
    }
    if (isFirst502) {
      urlQuery.first502 = '1'
    }

    curUrl = locationHrefPared.appendQuery(urlQuery).fullPath

    console.log(
      '🚀 ~ file: loginService.ts:252 ~ Login ~ getPmbankUrlForWx ~ focusCore.baseConfig.isUseNewHost:',
      focusCore.baseConfig.isUseNewHost,
    )
    let host = `https://${location.host}`
    // let host = 'https://personal.webank.com'
    if (BUILD_TEST) {
      // host = focusCore.baseConfig.isUseNewHost
      //   ? `https://personalv6.test.webankwealth.com/${focusCore.env.testServerEnv}`
      //   : `https://personal.test.webank.com/${focusCore.env.testServerEnv}`
      host = `https://${location.host}/${focusCore.env.testServerEnv}`
    }

    const pmbankHost = host + '/pmbank-mbaccomm/usermanager/OAuth'
    console.log('🚀 ~ file: login.ts ~ line 41 ~ Login ~ getPmbankUrlForWx ~ pmbankHost', pmbankHost)
    const params = {
      type: '3',
      appId: focusCore.baseConfig.appId,
      // webankAppId: '********', // TEST: 测试一下app渠道
      webankAppId: '********', // 新ID，不和APP 共用，登录态不互斥
      extInfo: JSON.stringify({
        os_type: focusCore.env.device === 'ios' ? 1 : 2,
        time_flag: Math.round(new Date().getTime() / 1000),
      }),
      url: curUrl,
    }
    const urlParser = new UrlParser(pmbankHost)
    return urlParser.appendQuery(params).fullPath
  }

  getUidType(loginData: any) {
    const { userType, qqOpenId, wechatOpenId } = loginData
    const uidTypes = {
      '1': 'qq',
      '2': 'weixin',
      '4': 'phone',
    }
    if (qqOpenId || (wechatOpenId && userType === '1')) {
      return uidTypes['1']
    }
    if (wechatOpenId) {
      return uidTypes['2']
    }
    return uidTypes[loginData.userType] || ''
  }
}
export default new Login()
