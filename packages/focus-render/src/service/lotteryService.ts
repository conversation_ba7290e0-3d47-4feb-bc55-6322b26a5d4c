import dayjs from '../utils/time-utils'
import { focusCore, focusStore } from '../packages/focus-render/index'
import { EnumMethod, TcigItem } from './focus-core/plugins/Ajax'

export interface TRewardListItem {
  cardId: string
  activityId: string
  time: string
  coinData: Array<{ coinUrl: string; coinName: string; coinType: string; amount: number }>
}

export interface AwardRecordItem {
  awardName: string
  personalName: string
}

export const cgis: {
  [k: string]: TcigItem
} = {
  getLotteryTimes: {
    name: '通过聚合页ID(configId)查询抽奖资格',
    url: '/hjop/lottery/getNewYearGuestV2',
  },
  getLotteryTimesByAids: {
    name: '通过活动ID(aid)查询抽奖资格',
    url: '/hjop/lottery/getNewYearGuest',
  },
  getLotteryPriceInfo: {
    name: '查询抽奖活动配置',
    url: '/hjop/lottery/getNewYearAward',
  },
  doLottery: {
    name: '开始抽奖',
    method: EnumMethod.POST,
    url: '/hjop/lottery/getNewYearLottery',
  },
  getMyActivityPrize: {
    url: '/hjop/lottery/getMyActivityPrize',
    name: '查询我的奖品列表',
  },
  getLotteryLamp: {
    url: '/wm-htrserver/cop/hj/queryLotteryRecord',
    name: '获取跑马灯抽奖信息',
  },
}

class LotteryService {
  doLottery(aid: any) {
    console.log('🚀 ~ file: lotteryService.ts ~ line 27 ~ LotteryService ~ doLottery ~ aid', aid)
    if (!aid) {
      console.error('没有活动id!')
      return Promise.reject(false)
    }
    return new Promise((resolve, reject) => {
      focusCore
        .request(
          cgis.doLottery,
          {},
          {
            activity_id: aid,
          },
        )
        .then((res: { succ: any; item: any }) => {
          console.log('🚀 ~ file: lotteryService.js ~ line 35 ~ LotteryService ~ returnnewPromise ~ res', res)
          const { succ, item } = res
          if (succ && item) {
            return resolve({
              ...item,
              id: item.index,
            })
          }
          reject(null)
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: lotteryService.js ~ line 39 ~ LotteryService ~ returnnewPromise ~ err', err)
          reject(false)
        })
    })
  }

  //获取抽奖配置
  getLotteryConfig(aid: number) {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getLotteryPriceInfo, { activity_id: Number(aid) })
        .then((res: any) => {
          console.log('🚀 ~ file: lotteryService.js ~ line 36 ~ LotteryService ~ .then ~ res', res)
          const { items = [] } = res || {}

          resolve({ priceList: items })
        })
        .catch((err: any) => {
          console.log(err)
          const { modalStore } = focusStore.stores

          // resolve({ priceList: [] })
          modalStore.errorMaskContrl(`lotter_timeout`, err.ret_code, err.ret_msg)
        })
    })
  }

  /**
   * 抽奖次数和奖品信息
   * @param {*} aids
   * @returns
   */
  async getLotteryTimes(configId: string, aidsStr?: string) {
    if (!configId && !aidsStr) {
      return Promise.resolve({ priceList: [] })
    }
    let lotteryTimesResult: {
      activityIds: number[]
      activityLotteryChanceList: any[]
      chanceCount: number
      chanceEndTime: string
    } = {
      activityIds: [],
      activityLotteryChanceList: [],
      chanceCount: 0,
      chanceEndTime: '',
    }
    if (configId) {
      try {
        const res = await focusCore.request(cgis.getLotteryTimes, { configId: Number(configId) })
        lotteryTimesResult = res
      } catch (err) {
        console.log(err)
      }
    } else {
      if (aidsStr) {
        try {
          const activity_id = aidsStr.split(',').map((i) => Number(i))
          const res = (await focusCore.request(cgis.getLotteryTimesByAids, { activity_id: activity_id })) || {}
          lotteryTimesResult = {
            activityIds: activity_id,
            ...res,
          }
        } catch (err) {
          console.log('🚀 ~ file: lotteryService.ts ~ line 89 ~ LotteryService ~ getLotteryTimes ~ err', err)
        }
      }
    }
    console.log(
      '🚀 ~ file: lotteryService.ts ~ line 87 ~ LotteryService ~ getLotteryTimes ~ lotteryTimesResult',
      lotteryTimesResult,
    )

    return new Promise((resolve) => {
      const {
        activityIds = [],
        activityLotteryChanceList = [],
        chanceCount,
        chanceEndTime = '',
      } = lotteryTimesResult || {}

      let doLotteryAid: number = activityIds[0]
      let lotteryTimes = 0
      let lotteryTimesExpireTime = ''
      // 有抽奖机会，使用第一个活动ID获取抽奖配置和抽奖机会
      if (activityLotteryChanceList.length && chanceCount) {
        doLotteryAid = (activityLotteryChanceList[0] && activityLotteryChanceList[0].activityId) || 0
        lotteryTimesExpireTime = dayjs(chanceEndTime).format('MM月DD日 HH:mm')
        lotteryTimes = chanceCount
      }
      if (!doLotteryAid) {
        return resolve({
          doLotteryAid,
          lotteryTimes: 0,
          priceList: [],
          lotteryTimesExpireTime: '',
        })
      }
      // 获取抽奖配置
      this.getLotteryConfig(doLotteryAid).then((res: any = { priceList: [], lotteryTimes: 0 }) => {
        const { priceList = [] } = res

        return resolve({
          doLotteryAid,
          lotteryTimes,
          priceList,
          lotteryTimesExpireTime,
        })
      })
    })
  }

  /**
   * 获取我的奖品列表
   */

  getMyRewardList(aidsStr: string): Promise<Array<TRewardListItem>> {
    let aids: string | Array<number> = aidsStr && aidsStr.replace('，', ',')
    aids = aids
      .split(',')
      .filter((i) => i)
      .map((i) => Number(i))
    if (!aids.length) {
      console.error('getMyRewardList 接口没有传aidsStr！——————', aidsStr)
      return Promise.resolve([])
    }
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getMyActivityPrize, { activity_id: aids })
        .then(
          (res: {
            prizes: Array<{
              activityId: number
              awardChannel: string
              awardExtra: string
              createTime: string
              practicalCardId: number
              lotteryCardName: string
              // awardChannel: 1
              // awardExtra: "[{"coinType":"db_red","amount":0,"coinUrl":"https://txy.test.webank.com/tc-k/querydata/html/hjAdmAdminPic/ziqiu-************.png","coinName":"7星主题活动-星球-代币-红"},{"coinType":"db_blue","amount":3,"coinUrl":"https://txy.test.webank.com/tc-k/querydata/html/hjAdmAdminPic/lanqiu-************.png","coinName":"7星主题活动-星球-代币-蓝"}]"
              // createTime: "2022-11-09 16:33:16"
              // lotteryCardName: ""
              // practicalCardId: 10049
            }>
          }) => {
            console.log('🚀 ~ file: lotteryService.ts ~ line 188 ~ LotteryService ~ .then ~ res', res)
            const { prizes = [] } = res

            resolve(
              prizes.map((i) => {
                console.log('🚀 ~ file: lotteryService.ts ~ line 234 ~ LotteryService ~ prizes.map ~ i', i)

                const { awardExtra = '' } = i
                console.log(
                  '🚀 ~ file: lotteryService.ts ~ line 216 ~ LotteryService ~ prizes.map ~ awardExtra',
                  awardExtra,
                )
                let coinData: Array<{ coinUrl: string; coinName: string; coinType: string; amount: number }> = []
                try {
                  coinData = JSON.parse(awardExtra) || []
                } catch (err) {
                  console.error('没有awardExtra，判断不是用货币兑换的 ')
                }

                return {
                  cardId: i.practicalCardId.toString(),
                  activityId: i.activityId.toString(),
                  time: i.createTime,
                  coinData: coinData.filter((i) => i.amount),
                }
              }),
            )
          },
        )
        .catch((err: any) => {
          console.log('🚀 ~ file: lotteryService.js ~ line 10 ~ LotteryService ~ .then ~ err', err)
          resolve([])
        })
    })
  }

  getLotteryLamp(configId: string): Promise<Array<AwardRecordItem>> {
    if (!configId) {
      console.error('没有活动id!')
      return Promise.resolve([])
    }
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getLotteryLamp, { configId: Number(configId) })
        .then((res: { awardRecordList: Array<AwardRecordItem> }) => {
          console.log('🐬 ~ LotteryService ~ .then ~ res:', res)
          const { awardRecordList = [] } = res
          resolve(awardRecordList)
        })
        .catch((err: any) => {
          console.log('🐬 ~ LotteryService ~ returnnewPromise ~ err:', err)
          resolve([])
        })
    })
  }
}
export default new LotteryService()
