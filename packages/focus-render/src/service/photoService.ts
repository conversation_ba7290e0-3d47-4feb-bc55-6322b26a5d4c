// import { focusStore, focusServices, focusCore, focusUtils } from '@focus/render'
import { focusCore, focusStore } from '../packages/focus-render/index'
import { EnumMethod } from './focus-core/plugins/Ajax'

const cgis = {
  uploadFtp: {
    url: '/wm-hjhtr/file_upload/common',
    name: '上传文件到ftp服务器',
    method: EnumMethod.POST,
    baseUrlKey: 'file',

    headers: {
      'Content-Type': 'multipart/form-data',
    },
  },
  getFtpFile: {
    url: '/wm-hjhtr/common/download/fps_file',
    name: '获取ftp文件',
  },
}

class AppPhotoHandler {
  filePath: string = ''
  fileId: string = ''
  fileHash: string = ''
  getPhoto(): Promise<{
    file_id: string
    hash_id: string
    previewImg: string
  }> {
    if (focusCore.env.isInWx) {
      return new Promise((resolve) => {
        const res = {
          fileId: '99000003274181907202901208481643000000016889678720299272',
          fileHash: 'f2defa8dda2cf27d1151c45cccc9f0b5',
        }
        this.getPreviewPhoto(res.fileId, res.fileHash).then((previewImg) => {
          console.log(
            '🚀 ~ file: photoService.ts:30 ~ AppPhotoHandler ~ this.getPreviewPhoto ~ previewImg:',
            previewImg,
          )
          resolve({
            file_id: res.fileId,
            hash_id: res.fileHash,
            previewImg,
          })
        })
      })
    }

    return new Promise((resolve, reject) => {
      focusCore.invokeApp.selectPhoto({
        success: (res: { file_id: string; hash_id: string }[]) => {
          const photo = (res && res[0]) || {}
          const { file_id, hash_id } = photo
          console.log('🚀 ~ file: photoService.ts:71 ~ AppPhotoHandler ~ returnnewPromise ~ res:', res)

          this.getPreviewPhoto(file_id, hash_id).then((previewImg) => {
            console.log(
              '🚀 ~ file: photoService.ts:30 ~ AppPhotoHandler ~ this.getPreviewPhoto ~ previewImg:',
              previewImg,
            )
            resolve({
              file_id,
              hash_id,
              previewImg,
            })
          })
        },
        error: (res: { error: string }) => {
          return reject(res.error)
        },
      })
    })
  }

  getPreviewPhoto(file_id: string, hash_id: string): Promise<string> {
    return new Promise((resolve) => {
      if (!file_id || !hash_id) {
        return resolve('')
      }
      focusCore
        .request(cgis.getFtpFile, {
          file_id,
          file_hash: hash_id,
        })
        .then((res: { datas: string }) => {
          console.log('🚀 ~ file: photoService.ts:47 ~ AppPhotoHandler ~ returnnewPromise ~ res:', res)
          const { datas = '' } = res
          const imgData = datas ? 'data:image/jpg;base64,' + datas : ''
          console.log('🚀 ~ file: photoService.ts:88 ~ AppPhotoHandler ~ .then ~ imgData:', imgData)
          return resolve(imgData)
        })
        .catch(() => {
          resolve('')
        })
    })
  }

  upload(): Promise<{ file_id: string; hash_id: string }> {
    return new Promise((resolve, reject) => {
      const formData: any = new window.FormData()
      console.log('🚀 ~ file: photoService.ts:44 ~ AppPhotoHandler ~ returnnewPromise ~ this.filePath:', this.filePath)

      // disable-eslint-line
      // formData.append('file', {
      //   uri: this.filePath,
      //   type: 'image/jpg',
      //   name: `image.jpg`,
      // })

      formData.append('file', { uri: this.filePath, type: 'image/jpg', name: 'image.jpg' })

      console.log('🚀 ~ file: photoService.ts:38 ~ AppPhotoHandler ~ returnnewPromise ~ formData:')

      console.log(formData.get('file'))
      focusCore
        .request(cgis.uploadFtp, {}, formData)
        .then((res: { hash_id: string; file_id: string }) => {
          console.log('🚀 ~ file: photoService.ts:43 ~ AppPhotoHandler ~ .then ~ res:', res)
          const { file_id = '', hash_id = '' } = res
          resolve({ file_id, hash_id })
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: photoService.ts:52 ~ AppPhotoHandler ~ returnnewPromise ~ err:', err)
          reject(null)
        })
    })
  }
}

export default new AppPhotoHandler()
