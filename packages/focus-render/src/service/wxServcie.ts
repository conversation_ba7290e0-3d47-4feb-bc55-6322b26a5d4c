import { focusCore } from '../packages/focus-render/index'
const cgis = {
  wxSdk: {
    name: '注册微信sdk',
    url: '/hjop/wm-htrserver/yz/getapiticketsig',
  },
}
class WxServcie {
  init() {
    const appId = focusCore.baseConfig.appId
    const url = location.href.split('#')[0]
    if (focusCore.env.isInApp) {
      return Promise.reject(false)
    } else {
      return new Promise((resolve) => {
        focusCore.ajax
          .request(cgis.wxSdk, {
            url,
            appid: appId,
          })
          .then((data: any) => {
            console.log('🚀 ~ file: wxServcie.ts ~ line 22 ~ WxServcie ~ .then ~ data', data)
            if (!window.wx) {
              console.log('没有wxsdk')
              return false
            }
            console.log('有wxsdk！')
            const wx = window.wx
            wx.config({
              // debug: BUILD_MODE === 'prod' ? false : true,
              beta: true,
              appId: appId,
              timestamp: data.timestamp,
              nonceStr: data.nonce_str,
              signature: data.signature,
              jsApiList: [
                'launchApplication',
                'closeWindow',
                'showMenuItems',
                'hideMenuItems',
                'hideAllNonBaseMenuItem',
                'showAllNonBaseMenuItem',
                'updateAppMessageShareData',
                'updateTimelineShareData',
                'onMenuShareTimeline',
                'onMenuShareAppMessage',
                'addCard',
              ],
              openTagList: ['wx-open-launch-weapp'],
            })
            return resolve(true)
          })
      })
    }
  }
}
export default new WxServcie()
