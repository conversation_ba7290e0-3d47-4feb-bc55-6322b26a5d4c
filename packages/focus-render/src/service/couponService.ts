import { focusCore } from '../packages/focus-render/index'
import { EnumMethod } from './focus-core/plugins/Ajax'
import { dayjs, UrlParser } from '@/utils'

const cgis = {
  getCouponsByIds: {
    url: '/wm-htrserver/finance/coupons/query_by_couponidlist',
    name: '批量获取卡券信息-已开户',
  },
  getCouponsByIdsNoAuth: {
    url: '/wm-htrserver/finance/coupons/query_by_couponidlist_no_auth',
    name: '批量获取卡券信息-未登录/未开户',
  },

  claimCouponsByIds: {
    url: '/wm-htrserver/cop/hj/coupon/batch_get',
    name: '批量领取卡券',
    method: EnumMethod.POST,
  },
}

export declare type TCouponData = {
  coupon_config_id?: string
  cardTitle?: string
  isClaimed?: boolean
  isShowTop?: boolean
  targetLevel?: number
  targetDiscountMoney?: number
  overTime?: number
  overTimeText?: string
  userCouponStatus?: string
}

export interface GetCouponsResponse {
  couponList: TCouponData[]
  maxData: {
    isShowTop?: boolean
    targetDiscountMoney?: number
  }
}

export interface ClaimCouponsResponse {
  status: number
}

interface TCouponItem {
  coupon_config_id: string // 券配置id
  title: string // 主标题
  reward_rule: {
    buy_amount_min: number // 挡位最小值
    buy_amount_max: number // 档位最大值
    reduction_amount: number // 抵扣金额
  }[] // 奖励规则
  coupon_id: string // 用户领取的券id
  user_coupon_status: string // 用户领取的券状态0：已领取待使用；1：已冻结；2：已核销；3：已过期；4：已兑换 5：强制回收 6：已领取未生效
  max_reduction_amount: number // 最大抵扣金额
  min_buy_amount_min: number // 购买最低使用金额
  days_left: number // 剩余可用天数
}

class CouponService {
  getCoupons(ids: string[]): Promise<{
    couponList: TCouponData[]
    maxData: {
      isShowTop?: boolean
      targetDiscountMoney?: number
    }
  }> {
    return new Promise((resolve) => {
      let cgi = cgis.getCouponsByIdsNoAuth
      if (focusCore.hasAccount) {
        cgi = cgis.getCouponsByIds
      }

      focusCore
        .request(cgi, {
          coupon_id_list: ids,
        })
        .then(
          (res: {
            total_count: number // 券总数量
            claimed_count: number // 已领取券数量
            unclaimed_count: number // 未领取券数量
            max_reduction_amount: number // 最大抵扣金额
            reward_rule_count: number // 抵扣规则配置条数
            min_days_left: number // 已领取券的最小剩余天数
            title: string // 券简称
            coupon_list: TCouponItem[] // 券列表
          }) => {
            console.log('🚀 ~ CouponService ~ returnnewPromise ~ res:', res)
            const { coupon_list = [], max_reduction_amount } = res
            let isShowMaxTop = res.claimed_count > 1
            const couponList = coupon_list.map((i) => {
              const {
                user_coupon_status,
                coupon_config_id,
                max_reduction_amount,
                min_buy_amount_min,
                reward_rule = [],
                title,
              } = i
              return {
                coupon_config_id,
                targetDiscountMoney: max_reduction_amount,
                cardTitle: title,
                isClaimed: user_coupon_status === '0',
                targetLevel: min_buy_amount_min,
                isShowTop: reward_rule.length > 1,
                overTime: i.days_left,
                userCouponStatus: user_coupon_status,
              }
            })

            if (res.claimed_count === 1 && couponList.length) {
              isShowMaxTop = couponList[0] && couponList[0].isShowTop
            }
            return resolve({
              maxData: {
                isShowTop: isShowMaxTop,
                targetDiscountMoney: res.max_reduction_amount,
              },
              couponList,
            })
          },
        )
        .catch((err) => {
          console.log('🚀 ~ CouponService ~ returnnewPromise ~ err:', err)
          return resolve({
            couponList: [],
            maxData: {},
          })
        })
    })
  }

  claimCoupons(ids: string[]): Promise<{
    status: number
  }> {
    return new Promise((resolve, reject) => {
      focusCore
        .request(
          cgis.claimCouponsByIds,
          {},
          {
            couponConfigIds: ids,
          },
        )
        .then(
          (res: {
            status?: number // 领取结果：0（失败）、1（部分成功）、2（成功）
            sendCouponDetail?: {
              couponConfigId?: number // 非必须
              couponId?: number // 非必须
              sendResult?: number // 各券领取状态（20：领取成功；其他均为失败）
            }[] // 领取明细
          }) => {
            console.log('🚀 ~ CouponService ~ returnnewPromise ~ res:', res)
            if (res.status === 2) {
              return resolve({ status: res.status })
            }
            return reject(res.status)
          },
        )
        .catch((err) => {
          console.log('🚀 ~ CouponService ~ returnnewPromise ~ err:', err)
          reject(0)
        })
    })
  }
}

export default new CouponService()
