import { focusStore, focusCore } from '../packages/focus-render/index'
import focusServices, { grayService } from './index'
// const { userService, mgmService, productService } = focusServices
import userService from './userService'
import mgmService, { mgmV2 } from './mgmService'
import productService from './productService'
import type Product from './productService'
import wealthService from './wealthService'
import { formatAmount, dayjs, entitiestoUtf16 } from '../utils/index'
const defaultM1Avatar = require('../assets/img/common/m1_avator.png')
const cgis = {
  marketingData: {
    name: '营销数据接口',
    url: '/hjop/welfare/query_financial_festival_earnings',
  },
  activityDataPushRecord: {
    url: '/wm-htrserver/cop/hj/asset_reach/queryActivityDataPushRecord',
    name: '活动数据推送记录',
  },
  activityNotPushPushRecord: {
    url: '/wm-htrserver/cop/hj/asset_reach/queryActivityNonTagsRecord',
    name: '活动数据非标签推送记录',
  },
  getRankList: {
    url: '/wm-htrserver/cop/hj/query_mgm2_activity_ranking',
    name: 'mgm排行榜',
  },
  activityDataPushRecordV2: {
    url: '/wm-htrserver/cop/hj/asset_reach/queryActivityDataPushRecordV2',
    name: '活动数据推送记录V2-过滤参与&灰度',
  },
}
import { EnumConditionMap } from '../store/conditionStore'
import modalStore from '@/store/modalStore'

export interface ApiParams {
  dataSourceId?: number
  [key: string]: any
}

export interface ApiItem {
  apiKey: string
  apiParams: ApiParams
  apiDataType?: string
  apiIndex: number
}

export interface DepApiItem {
  apiIndex: number
  apiKey: string
  apiParams: ApiParams
  apiDataType?: string
}

export interface TDynimicDataKeys {}

export interface QueryApiResponse {
  code: number
  data: any
  message?: string
}

class DynimicDataService {
  apiList: any[] = []
  apiListWithoutLogin: any[] = []
  // apiMaps = {
  //   _api_01: () => mgmService.getShareUserinfo,
  //   _api_08: () => userService.getWxUserInfo,
  //   _api_09: () => productService.getProductListByCodesLogin,
  // }
  apiMapsWithoutLogin: {
    [key: string]: { url: string; method?: string; responseType?: string; transform?: (data: any) => any }
  } = {
    _api_09: { url: '/api/product/list', method: 'get', transform: (data: any) => data as Product[] },
    _api_13: { url: '/api/fund/data', method: 'get' },
  }
  batchQueryApi(needLogin = true) {
    const { dynimicDataStore, modalStore } = focusStore.stores
    const _apiList = needLogin ? this.apiList : this.apiListWithoutLogin
    // 检查是否有依赖其他数据源的接口
    const { resultApiList, depApiList } = this.filterDepList(_apiList)
    console.log('🚀 ~ DynimicDataService ~ batchQueryApi ~ depApiList:', depApiList)
    console.log('🚀 ~ DynimicDataService ~ batchQueryApi ~ resultApiList:', resultApiList)

    const doQueryApi = (apiKey: any, apiParams: any, apiDataType: any, index: number, cb?: any) => {
      modalStore.loadingStart('loadingSource')
      this.queryApi(apiKey, apiParams, needLogin)
        .then((res: any) => {
          console.log('🚀 ~ DynimicDataService ~ this.queryApi ~ res:', res)
          if (apiKey === '_api_10' || apiKey === '_api_11') {
            this.sendlogForPushData(apiKey, apiParams, res)
          }
          const formatData = this.resolveData(apiKey, res, apiDataType, apiParams)
          console.log(
            '🐬 ~ file: dynimicDataService.ts:59 ~ DynimicDataService ~ this.queryApi ~ formatData:',
            formatData,
          )
          dynimicDataStore.setDynimicDataByApiKey(apiKey, index, formatData)
          dynimicDataStore.setNumberDynimicData(
            apiKey,
            index,
            this.resolveData(apiKey, res, apiDataType, apiParams, true),
          )
          this.resolveDataForDrawCondition(apiKey, index, res, apiDataType, formatData)
          try {
            if (cb) {
              cb(res)
            }
          } catch (err) {
            console.log('🚀 ~ DynimicDataService ~ this.queryApi ~ err:', err)
          }
        })
        .finally(() => {
          modalStore.loadingEnd('loadingSource')
        })
    }

    const doDepListQuery = (topRes: any, params: any) => {
      console.log('🚀 ~ DynimicDataService ~ doDepListQuery ~ topRes:', topRes)
      console.log('🚀 ~ DynimicDataService ~ doDepListQuery ~ params:', params)
      const { dsDate } = topRes
      const { apiKey, apiParams, apiDataType, apiIndex } = params || {}
      doQueryApi(apiKey, { ...apiParams, dsDate }, apiDataType, apiIndex)
    }
    resultApiList.forEach((item, index) => {
      const { apiKey, apiParams, apiDataType, apiIndex } = item
      doQueryApi(apiKey, apiParams, apiDataType, apiIndex, (res: any) => {
        if (depApiList[`dep_${apiIndex}`]) {
          doDepListQuery(res, depApiList[`dep_${apiIndex}`])
        }
      })
    })
  }

  filterDepList(apiList: { apiDataType: any; apiKey: string; apiParams: any }[]) {
    console.log('🚀 ~ DynimicDataService ~ filterDepList ~ apiList:', apiList)
    const resultApiList: { apiDataType: any; apiKey: string; apiParams: any; apiIndex: number }[] = []
    const depApiList: any = {}
    apiList.forEach((i: any, apiIndex) => {
      if (i.apiParams.dataSourceId) {
        const index = i.apiParams.dataSourceId - 1
        depApiList[`dep_${index}`] = {
          apiIndex,
          ...i,
        }
      } else {
        resultApiList.push({
          apiIndex,
          ...i,
        })
      }
    })
    return {
      resultApiList,
      depApiList,
    }
  }

  init(
    apiList: any[],
    conditionList?: {
      conditionTypes?: string[]
      conditionApiKeys?: string[]
    },
  ) {
    this.apiList = apiList
    console.log('开始处理需要登录的动态数据.....', apiList)
    this.batchQueryApi(true)
    if (typeof conditionList === 'object' && Object.keys(conditionList).length) {
      this.resolveDrawConditionData(conditionList.conditionTypes || [], conditionList.conditionApiKeys || [])
    }
  }
  initWithoutLogin(apiList: any[]) {
    console.log('开始处理不需要登录的动态数据.....')
    console.log('🚀 ~ DynimicDataService ~ initWithoutLogin ~ apiList:', apiList)
    const apiKeysWithoutLogin: string[] = Object.keys(this.apiMapsWithoutLogin)
    this.apiListWithoutLogin = apiList.filter((item: { apiDataType: any; apiKey: string; apiParams: string }) =>
      apiKeysWithoutLogin.includes(item.apiKey),
    )
    this.batchQueryApi(false)
  }
  resolveData(apiKey: string, data: any, apiDataType?: any, apiParams?: any, notFormate?: boolean) {
    if (apiKey === '_api_01') {
      const { m1NickName, m1Avator } = data

      const result = { M1NickName: m1NickName || '微众银行用户', M1Avator: m1Avator || defaultM1Avatar }

      return result
    }

    if (apiKey === '_api_02') {
      let _data = data

      const restult = formateDataType(_data, apiDataType)
      return restult
    }

    if (apiKey === '_api_05') {
      const result = { fansNums: data.length }
      return result
    }

    if (apiKey === '_api_08') {
      const { nickName, avator } = data

      const result = {
        userNickName: nickName,
        userAvatar: avator,
      }
      return result
    }
    if (apiKey === '_api_07') {
      const { remaining_invitation_count = 0 } = data
      return { mgmRewardTimes: remaining_invitation_count }
    }
    if (apiKey === '_api_09') {
      return this.queryDataFromList(
        data,
        'product_code',
        [
          'rate_value',
          'extra_info.rate_desc',
          'earnings_rate_date',
          'extra_info.static_rate_tips',
          'extra_info.ltp_rate_desc',
          'extra_info.ltp_rate_value',
          'productCanSale',
        ],
        apiParams,
      )
    }

    // 使用默认的数据解析方式
    const baseList = ['_api_10', '_api_11', '_api_14', '_api_15', '_api_16']

    if (baseList.indexOf(apiKey) > -1) {
      return formateDataForActivityPushData(data, apiDataType, !!notFormate)
    }
    if (apiKey === '_api_12') {
      return formateRankListData(data, apiDataType)
    }
    if (apiKey === '_api_13') {
      return productService.formatFundData(data, apiParams)
    }
    return {}
  }

  queryApi(
    _apiKey: string,
    _apiParams: {
      activityId?: number
      dataType?: string
      mgmAid: number
      codes?: string[]
      pushKey?: string
      wealthBracket?: string
      baseDate?: string
      dsDate?: string
      checkGray?: string
      trailType?: string
    },
    needLogin?: boolean,
  ) {
    const apiKey = _apiKey || ''
    console.log('🚀 ~ file: dynimicDataService.ts:130 ~ DynimicDataService ~ apiKey:', apiKey)
    const apiParams = _apiParams || {}
    console.log('🐬 ~ file: dynimicDataService.ts:223 ~ DynimicDataService ~ apiParams:', apiParams)

    switch (apiKey) {
      case '_api_01':
        return mgmService.getShareUserinfo()
      case '_api_02':
        return this.queryMarketingData(apiParams.activityId || 0, apiParams.dataType || '')
      case '_api_05':
        return mgmService.queryM1HelpersList(apiParams.activityId || 0, 0, 100)
      case '_api_07':
        return mgmService.getRelationshipList(apiParams.mgmAid)
      case '_api_08':
        return userService.getWxUserInfo()
      case '_api_09':
        return productService.getProductListByCodes(apiParams.codes || [], false, focusCore.hasAccount)
      case '_api_10':
        return this.queryActivityDataPushRecord(
          apiParams.activityId || 0,
          apiParams.pushKey || '',
          apiParams.checkGray || '',
        )
      case '_api_11':
        return this.queryActivityNotTagPushRecord(apiParams.pushKey || '')
      case '_api_12':
        return this.getRankListData(apiParams.mgmAid + '' || '')
      case '_api_13':
        return productService.getFundData(apiParams.codes || [])
      case '_api_14':
        return wealthService.getCalcWealthForDynamic(apiParams)
      case '_api_15':
        return wealthService.getUserWealthInfo(apiParams.baseDate)
      case '_api_16':
        return mgmV2.getMgmMembers(apiParams.mgmAid)
      default:
        return Promise.resolve({})
    }
  }

  getDynimicDataByApiKey(apiKey: string) {
    if (!apiKey) {
      return undefined
    }
    const { dynimicDataStore } = focusStore.stores
    if (apiKey.indexOf('_shareConfig_') > -1) {
      console.log('🚀 ~ DynimicDataService ~ getDynimicDataByApiKey ~ apiKey:', apiKey)
      const [emptyStart, keyName, shareConfigId, useKey] = apiKey.split('_')
      const shareData = focusCore.focusRenderCtr?.getShareConfigByConfigId(shareConfigId) || {}
      if (shareData.mgmAidV2) {
        mgmV2.getInivteCode(shareData.mgmAidV2).then((res) => {
          console.log('🚀 ~ DynimicDataService ~ mgmV2.getInivteCode ~ res:', res)
          dynimicDataStore.useData[apiKey] = res.inviteCode || '--'
        })
        return dynimicDataStore.useData[apiKey]
      } else {
        return '--'
      }
    }
    if (apiKey.indexOf('_api_') < 0 && apiKey.indexOf('_custom_') < 0) {
      // console.log('不包含 _api_ 或者 _custom_ ,直接返回')
      return apiKey
    }
    const data = dynimicDataStore.useData[apiKey]
    if (/(M1Avator)|(userAvatar)/.test(apiKey) && !data) {
      return defaultM1Avatar
    }
    if (/(M1NickName)|(userNickName)/.test(apiKey) && !data) {
      return '微众银行用户'
    }
    return data
  }

  /**
   * TODO:这个要移除了
   * @param activityId
   * @param dataType
   * @returns
   */
  queryMarketingData(activityId: number | string, dataType: string) {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.marketingData, { activityId, dataType })
        .then((res: any) => {
          let { basicDate = '--', fdate = '--', extra = '{}', extendExtra = '{}' } = res || {}

          // datatype  88
          let apiData: {
            mgmListNew?: NMgmListCompany.dynamicData['mgmListNew'] | any[]
            mgmListOld?: NMgmListCompany.dynamicData['mgmListOld'] | any[]
          } = {}

          try {
            extra = JSON.parse(extra || '{}')
            extendExtra = JSON.parse(extendExtra || '{}') || {}
          } catch (err) {
            console.log(err)
            extra = {}
            extendExtra = {}
          }
          apiData = { ...apiData, ...extra, ...extendExtra }

          if (dataType === '88') {
            console.error('这是88，是mgm列表')
            const { mgmListNew = [], mgmListOld = [] } = apiData
            apiData.mgmListNew =
              (!(typeof mgmListNew === 'string') &&
                mgmListNew.map((i: any) => {
                  return {
                    ...i,
                    nickName: i.nickName ? entitiestoUtf16(i.nickName) : '微众银行用户',
                    headImgUrl: i.headImgUrl ? i.headImgUrl.replace('http://', 'https://') : defaultM1Avatar,
                  }
                })) ||
              []
            apiData.mgmListOld =
              (!(typeof mgmListOld === 'string') &&
                mgmListOld.map((i: any) => {
                  return {
                    ...i,
                    nickName: i.nickName ? entitiestoUtf16(i.nickName) : '微众银行用户',
                    headImgUrl: i.headImgUrl ? i.headImgUrl.replace('http://', 'https://') : defaultM1Avatar,
                  }
                })) ||
              []
          }
          resolve({ basicDate, fdate, ...apiData })
        })
        .catch(() => {
          resolve({})
        })
    })
  }

  queryActivityDataPushRecord(activityId: number | string, pushKey: string, checkGray?: string) {
    let cgi = cgis.activityDataPushRecord
    if (checkGray === '1') {
      cgi = cgis.activityDataPushRecordV2
    }
    console.log('🚀 ~ DynimicDataService ~ queryActivityDataPushRecord ~ cgi:', cgi)
    return new Promise((resolve) => {
      focusCore
        .request(cgi, { activityId, pushKey })
        .then((res: any) => {
          const { dsDate = null, activityDataPushRecord = { taskJson: '{}' } } = res

          let jsonData = (activityDataPushRecord && activityDataPushRecord.taskJson) || '{}'
          try {
            jsonData = JSON.parse(jsonData)
          } catch {
            jsonData = {}
          }
          console.log('推数的数据——', jsonData)
          resolve({ dsDate: dsDate ? `${dsDate} 00:00:00` : '--', ...jsonData })
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: dynimicDataService.ts ~ line 219 ~ DynimicDataService ~ focusCore.request ~ err', err)
          resolve({})
        })
    })
  }

  queryActivityNotTagPushRecord(pushKey: string) {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.activityNotPushPushRecord, { pushKey })
        .then((res: any) => {
          const { activityNonTagsRecord = { taskJson: '{}' }, dsDate = null } = res

          let jsonData = activityNonTagsRecord?.pushValues || '{}'
          try {
            jsonData = JSON.parse(jsonData)
          } catch {
            jsonData = {}
          }

          console.log('推数的数据....tagData', jsonData)
          resolve({
            dsDate: dsDate ? `${dsDate} 00:00:00` : '--',
            ...jsonData,
          })
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: dynimicDataService.ts ~ line 275 ~ DynimicDataService ~ returnnewPromise ~ err', err)
          resolve({})
        })
    })
  }

  /**
   * 通过queryStr生成列表字段文案映射
   * 格式: ${dataKey}?${queryStr}=${queryValue}
   * ex: rate_value?product_code=GDDK01 在列表中查找product_code=GDDK01的item，并取item.rate_value
   * @param list
   * @param queryStr
   * @param dataKeys
   * @returns
   */
  queryDataFromList(list: any[], queryStr: string, dataKeys: string[], apiParams: any): { [props: string]: any } {
    console.log('🚀 ~ file: dynimicDataService.ts:291 ~ DynimicDataService ~ queryDataFromList ~ apiParams:', apiParams)
    const map = {}
    const { codes = [] } = apiParams
    const data = codes.map((c: string) => {
      const itemByCode = list.find((item) => item.product_code === c)
      return (
        itemByCode || {
          product_code: c,
        }
      )
    })
    data.forEach((item: Product) => {
      dataKeys.forEach((key) => {
        if (/\./.test(key)) {
          /* queryStr支持多级对象取值，如item.extra_info.rate_desc */
          const keyArr: string[] = key.split('.')
          let val = item
          while (keyArr.length > 0) {
            let key = keyArr.shift() || ''
            val = (val && val[key]) || ''
          }
          map[`${key}?${queryStr}=${item[queryStr]}`] = val || '--'
        } else {
          map[`${key}?${queryStr}=${item[queryStr]}`] = item[key] || '--'
        }
      })
    })

    return map
  }

  /**
   * 处理渲染条件，更新到store中
   * @param conditionTypes
   */

  resolveDrawConditionData(conditionTypes: string[], conditionApiKeys?: string[]) {
    // console.log('🐬 ~ DynimicDataService ~ resolveDrawConditionData ~ conditionApiKeys:', conditionApiKeys)
    // console.log('🐬 ~ DynimicDataService ~ resolveDrawConditionData ~ conditionTypes:', conditionTypes)
    const { conditionStore } = focusStore.stores
    let dc03GrayIds: { id: string; subId: string }[] = []

    conditionTypes.forEach((type: string) => {
      // console.log('🐬 ~ DynimicDataService ~ conditionTypes.forEach ~ type:', type)
      let typeStr = type
      let extendStr = ''
      if (type.indexOf('__EXTENDSTR__')) {
        const temp = type.split('__EXTENDSTR__')
        typeStr = temp[0]
        extendStr = temp[1]
      }
      switch (typeStr) {
        // 关注状态
        case EnumConditionMap.isSbuscribe:
          userService.checkSubscribeStatus().then((status) => {
            conditionStore.setCondition(EnumConditionMap.isSbuscribe, status ? 2 : 1)
          })
          break
        case EnumConditionMap.hasAccount:
          conditionStore.setCondition(EnumConditionMap.hasAccount, focusCore.hasAccount ? 2 : 1)
          break
        case EnumConditionMap.hadInGrayId:
          const ids = extendStr.split('-')
          dc03GrayIds.push({
            id: ids[0],
            subId: ids[1] || '',
          })
          conditionStore.setCondition(type, 1)
          break
        case EnumConditionMap.dynamicDataCondition:
          console.error('由 resolveDrawConditionData 变更')
          conditionStore.setCondition(`dc05__EXTENDSTR__${extendStr}`, 1)
          break
      }
    })
    if (dc03GrayIds.length) {
      grayService.checkBussinessGray(dc03GrayIds).then((grayIds) => {
        grayIds.map(({ type, subType }) => {
          let str = type
          if (subType) {
            str = type + '-' + subType
          }
          conditionStore.setCondition(`dc03__EXTENDSTR__${str}`, 2)
        })
      })
    }
  }
  sendlogForPushData(apiKey: string, apiParams: any, resData: any) {
    const { dsDate, userPoints, userBuyPoints } = resData || {}
    focusCore.eventLog('info.pushdata', apiParams.pushKey, {
      query: {
        ...apiParams,
      },
      dsDate,
      userPoints,
      userBuyPoints,
    })
  }

  resolveDataForDrawCondition(apiKey: string, index: number, originData: any, targetType: any, formatData: any) {
    let defaultData: any = {}

    // console.log(data)
    console.log('defaultData....', defaultData)
    let type = 'dc05'
    const { conditionStore } = focusStore.stores

    if (apiKey === '_api_09') {
      type = 'dc09'
      defaultData = formateBooleanDataForProduct(formatData, targetType)
    } else {
      for (const key in targetType) {
        const type = targetType[key]
        if (type === 'boolean') {
          defaultData[key] = formateBooleanData(originData, key)
        }
      }
    }
    const drawConditionKey = `${type}__EXTENDSTR__${apiKey}_${index}`

    Object.keys(defaultData).forEach((dataKey) => {
      const key = `${drawConditionKey}_${dataKey}`
      console.error('由动态数据更新的 渲染条件')
      conditionStore.setCondition(key, !!defaultData[dataKey] ? 2 : 1)
    })
  }

  getRankListData(activityId: string) {
    console.log('🚀 ~ DynimicDataService ~ getRankListData ~ activityId:', activityId)
    return new Promise((resolve) => {
      let resRankList = {}
      let myRankData = {}
      const getRankList = (): Promise<{
        rankList: any[]
        isError?: boolean
        rankUpperLimit: number
        rankDsDate: string
        rankIndex: string
      }> => {
        return new Promise((_res, _rej) => {
          focusCore
            .request(cgis.getRankList, { activityId })
            .then(
              (res: {
                rankTopList: {
                  rank: number
                  nickName: string
                  rankValue: string
                }[]
                myRank: number
                myRankValue: number
                rankUpperLimit: number
                dsDate: string
              }) => {
                console.log('🚀 ~ file: service.ts ~ line 115 ~ FunExpTokenService ~ returnnewPromise ~ res', res)
                const { rankTopList = [], myRank = '--', myRankValue = 0, rankUpperLimit = 0, dsDate = '--' } = res
                const origin = rankTopList || []
                let list =
                  (origin.length &&
                    origin.map((i) => {
                      return {
                        ...i,
                        nickName: entitiestoUtf16(i.nickName),
                        points: formatAmount(i.rankValue, false),
                      }
                    })) ||
                  []

                // if(dsDate==='--'||!dsDate){
                //   return _res({

                //   })
                // }

                return _res({
                  rankIndex: myRank.toString(),
                  rankList: list,
                  rankUpperLimit,
                  rankDsDate: dsDate,
                })
              },
            )
            .catch((err: any) => {
              return _rej(false)
            })
        })
      }

      Promise.all([getRankList(), this.queryActivityDataPushRecord(activityId, activityId)])
        .then((res) => {
          console.log('🚀 ~ DynimicDataService ~ ]).then ~ res:', res)
          type TpersonalData = {
            dsDate: string
            userScore: string
            invited: string
          }
          const rankData = res[0]
          const personalData = res[1] as TpersonalData
          const { rankList, rankUpperLimit, rankDsDate, rankIndex } = rankData
          const { dsDate = '--', userScore = 0, invited = 0 } = personalData
          let _rankIndex = '--'
          let _userScore = userScore
          let _invited = invited
          if (dsDate === '--') {
            _userScore = '--'
            _invited = '--'
          } else {
            if (rankDsDate === '--' || userScore.toString() === '0') {
              _rankIndex = '未上榜'
            } else {
              _rankIndex = rankIndex !== '-1' ? rankIndex : rankUpperLimit + '+'
            }
            // if (rankIndex !== '--' && rankDsDate !== '--') {
            //   _rankIndex = rankIndex !== '-1' ? rankIndex : rankIndex + '+'
            // } else {
            //   _rankIndex = '未上榜'
            // }
          }
          const result = {
            personalDsDate:
              !dsDate || dsDate === '--' || dsDate === undefined
                ? '--'
                : dayjs(dsDate, 'YYYYMMDD').format('YYYY-MM-DD'),
            userScore: _userScore,
            invited: _invited,
            confRankIndex: rankUpperLimit || '--',
            rankIndex: _rankIndex,
            rankDsDate,
            rankList,
          }

          return resolve(result)
        })
        .catch((err: any) => {
          console.log('🚀 ~ DynimicDataService ~ Promise.all ~ err:', err)
          return resolve({})
        })
    })
  }
}

export default new DynimicDataService()

function formateRankListData(formatData: any, targetType: any) {
  console.log('🚀 ~ formateRankListData ~ formatData:', formatData)
  console.log('🚀 ~ formateRankListData ~ targetType:', targetType)
  let result = {}
  return formateDataForActivityPushData(formatData, targetType, false)
}

function formateBooleanDataForProduct(formatData: any, targetType: any) {
  console.log('🚀 ~ file: dynimicDataService.ts:450 ~ formateBooleanDataForProduct ~ formatData:', formatData)
  console.log('🚀 ~ file: dynimicDataService.ts:444 ~ formateBooleanDataForProduct ~ targetType:', targetType)
  let result = {}

  Object.keys(targetType).forEach((key) => {
    Object.keys(formatData).forEach((_k) => {
      if (_k.indexOf(key) > -1) {
        result[_k] = formatData[_k] === '--' ? false : formatData[_k] ? true : false
      }
    })
  })

  return result
}

function formateDataForActivityPushData(originData: any, targetType: any, notFormate: boolean): any {
  const data: any = JSON.parse(JSON.stringify(originData)) || {}
  const defaultData: any = {}
  // const hasDsDate = data.dsDate && data.dsDate !== null && data.dsDate !== '--' && data.dsDate !== ''
  for (const key in targetType) {
    // eslint-disable-next-line no-undefined
    defaultData[key] = '--'

    // if (key !== 'dsDate') {
    //   defaultData[key] = hasDsDate ? '0' : '--'
    // }
    const isValid = !(data[key] === undefined || data[key] === '--' || data[key] === '' || data[key] === null)
    if (isValid && !notFormate) {
      const type = targetType[key]

      switch (type) {
        case 'yyyy-mm-dd':
          data[key] = dayjs(data[key], 'YYYYMMDD').format('YYYY-MM-DD')
          break

        case 'thousands_fix2':
          data[key] = formatAmount(data[key], true)
          break

        case 'thousands':
          data[key] = formatAmount(data[key])
          break

        case 'string':
          break
        // data[key] = data[key]

        default:
          break
      }
    }
  }
  return { ...defaultData, ...data }
}

/**
 * 这里就是根据业务需求定制的条件数据，只返回 1 或者 0
 * @param originData
 * @param key
 */
function formateBooleanData(originData: any, key: string) {
  let result = 0
  switch (key) {
    case 'daysOver7':
      let targetData = Number(originData.countDaysOver5w === '--' ? 0 : originData.countDaysOver5w || 0)
      result = targetData >= 7 ? 1 : 0
      break
    case 'userMgmScoreOver0':
      targetData = Number(originData.userMgmScore === '--' ? 0 : originData.userMgmScore || 0)
      result = targetData >= 0 ? 1 : 0

      break
  }
  return result
}

function formateDataType(originData: any, targetType: any): any {
  console.log('🚀 ~ file: dynimicDataService.ts:458 ~ formateDataType ~ targetType:', targetType)
  console.log('🚀 ~ file: dynimicDataService.ts:458 ~ formateDataType ~ originData:', originData)
  const data: any = JSON.parse(JSON.stringify(originData)) || {}
  const defaultData: any = {}
  for (const key in targetType) {
    // eslint-disable-next-line no-undefined
    defaultData[key] = '--'
    const isValid = !(data[key] === undefined || data[key] === '--' || data[key] === '' || data[key] === null)
    if (isValid) {
      const type = targetType[key]

      switch (type) {
        case 'yyyy-mm-dd':
          data[key] = dayjs(data[key], 'YYYYMMDD').format('YYYY-MM-DD')
          break

        case 'thousands_fix2':
          data[key] = formatAmount(data[key], true)
          break

        case 'thousands':
          data[key] = formatAmount(data[key])
          break

        default:
          break
      }
    }
  }
  return { ...defaultData, ...data }
}
