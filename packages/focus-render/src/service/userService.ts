import { entitiestoUtf16, utf16toEntities } from '@/utils/formate-name'
import { focusCore } from '../packages/focus-render/index'
import grayService from './grayService'
const defaultAvator = require('../assets/img/common/m1_avator.png')
export const cgis = {
  checkSubscribe: {
    name: '查询关注公众号状态',
    url: '/hjop/wechat/check_subscribe',
  },
  wxUserInfo: {
    url: '/hjop/user/querywxuserinfo',
    name: '获取当前用户微信头像昵称',
    canSaveTimes: true,
  },
  checkBindWx: {
    url: '/hjop/wechat/check_bindingWx',
    name: '查询微信绑定状态',
  },
  qqUserInfo: {
    url: '/hjop/user/queryqquserinfo',
    name: '获取当前用户qq头像昵称',
  },
}

interface WxUserInfoResponse {
  nickname?: string
  figureURL?: string
}

interface CheckSubscribeResponse {
  ret_data?: {
    result?: boolean
  }
}

interface TWxUserInfo {
  nickName: string
  avator: string
  defaultAvator: string
}
export class UserService {
  wxUserInfo: TWxUserInfo
  constructor() {
    this.wxUserInfo = {
      nickName: '',
      avator: '',
      defaultAvator,
    }
  }
  checkSubscribeStatus(): Promise<boolean> {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.checkSubscribe)
        .then((res: any) => {
          const { result = false } = (res && res.ret_data) || {}
          console.log('关注接口结果：', result)
          if (!result) {
            this.checkSubscribeGray().then((isSbuscribe: boolean) => {
              console.log('灰度 573 结果：', isSbuscribe)
              resolve(isSbuscribe)
            })
          } else {
            return resolve(result)
          }
        })
        .catch((err: any) => {
          resolve(false)
        })
    })
  }

  checkSubscribeGray(): Promise<boolean> {
    const subscribeGrayId = '573'
    return new Promise((resolve) => {
      grayService.checkBussinessGray([{ id: subscribeGrayId, subId: '' }], '1').then(
        (
          whiteList: {
            type: string
            subType: string
          }[],
        ) => {
          const isSbuscribe = whiteList[0] && whiteList[0].type === subscribeGrayId
          console.log(
            '🚀 ~ file: userService.ts ~ line 53 ~ UserService ~ grayService.checkBussinessGray ~ isSbuscribe',
            isSbuscribe,
          )
          return resolve(isSbuscribe)
        },
      )
    })
  }
  checkBindWechat(): Promise<boolean> {
    const subscribeGrayId = 573
    return new Promise((resolve) => {
      focusCore.request(cgis.checkBindWx).then((res: any) => {
        console.log('🚀 ~ file: userService.ts ~ line 70 ~ UserService ~ focusCore.request ~ res', res)
        const { result = false } = (res && res.ret_data) || {}
        console.log('微信绑定状态：', result)
        return resolve(result)
      })
    })
  }

  getUserInfoForUpload(): Promise<{ nickNameEncode: string; avator: string }> {
    return new Promise((resolve) => {
      this.getWxUserInfo().then((res) => {
        console.log('🚀 ~ file: userService.ts:96 ~ UserService ~ this.getWxUserInfo ~ res:', res)
        const { nickName, avator } = res
        resolve({
          nickNameEncode: utf16toEntities(nickName),
          avator,
        })
      })
    })
  }

  getWxUserInfo(): Promise<{ nickName: string; avator: string }> {
    console.log('开始获取用户信息。。。')
    if (focusCore.env.isInApp) {
      console.log('在app中！')
      return new Promise((resolve) => {
        const hjCoreIab = window.hjCoreIab

        hjCoreIab.getUserInfo((res: { picurl: ''; username: '' }) => {
          this.wxUserInfo = {
            ...this.wxUserInfo,
            nickName: res.username,
            avator: res.picurl?.replace('http:', 'https:'),
          }

          resolve(this.wxUserInfo)
        })
      })
    }

    if (this.wxUserInfo.nickName && this.wxUserInfo.avator) {
      console.log('你调用时候我刚好有！', this.wxUserInfo)
      return Promise.resolve(this.wxUserInfo)
    }

    return new Promise((resolve) => {
      focusCore
        .request(cgis.wxUserInfo)
        .then((res: any) => {
          const { nickname = '', figureURL = '' } = res || {}
          const nickName = entitiestoUtf16(nickname)
          const avator = figureURL.replace('http://', 'https://')
          this.wxUserInfo = {
            ...this.wxUserInfo,
            nickName,
            avator,
          }
          resolve(this.wxUserInfo)
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: userService.ts ~ line 48 ~ UserService ~ focusCore.request ~ err', err)

          return resolve(this.wxUserInfo)
        })
    })
  }
}

export default new UserService()
