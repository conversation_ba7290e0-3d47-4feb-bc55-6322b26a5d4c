import jumpService from './jumpService'
import pageShowService from './pageShowService'
import activityService from './activityService'
import userService from './userService'
import wxServcie from './wxServcie'
import grayService from './grayService'
import mgmService from './mgmService'
import lotteryService from './lotteryService'
import taskService from './taskListService'
import kvService from './kvService'
import focusService from './focusService'
import productService from './productService'
import dynimicDataService from './dynimicDataService'
import loginService from './loginService'
import extendInstanceService from './extendInstanceService'
import photoService from './photoService'
import couponService from './couponService'
export {
  jumpService,
  pageShowService,
  activityService,
  taskService,
  userService,
  wxServcie,
  grayService,
  mgmService,
  lotteryService,
  kvService,
  focusService,
  dynimicDataService,
  loginService,
  productService,
  extendInstanceService,
  photoService,
  couponService,
}

interface Services {
  jumpService: typeof jumpService
  pageShowService: typeof pageShowService
  activityService: typeof activityService
  taskService: typeof taskService
  userService: typeof userService
  wxServcie: typeof wxServcie
  grayService: typeof grayService
  mgmService: typeof mgmService
  lotteryService: typeof lotteryService
  kvService: typeof kvService
  focusService: typeof focusService
  dynimicDataService: typeof dynimicDataService
  loginService: typeof loginService
  productService: typeof productService
  extendInstanceService: typeof extendInstanceService
  photoService: typeof photoService
  couponService: typeof couponService
}

export const services: Services = {
  jumpService,
  pageShowService,
  activityService,
  taskService,
  userService,
  wxServcie,
  grayService,
  mgmService,
  lotteryService,
  kvService,
  focusService,
  dynimicDataService,
  loginService,
  productService,
  extendInstanceService,
  photoService,
  couponService,
}

export default services
