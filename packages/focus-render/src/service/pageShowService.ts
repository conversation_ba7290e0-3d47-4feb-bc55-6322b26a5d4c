import dayjs from '@/utils/time-utils'
import { focusCore, focusStore } from '../packages/focus-render/index'
const cgis = {
  systemTime: {
    baseUrlKey: 'hjdata',
    url: '/hj/common/systimeinfo',
    name: '查询系统时间',
  },
}
class PageShowService {
  getNowTime() {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.systemTime)
        .then((res: any) => {
          console.log('🚀 ~ file: pageTimeService.ts ~ line 29 ~ PageTimeService ~ focusCore.request ~ res', res)
          return resolve(res.timestr ? dayjs(res.timestr) : dayjs())
        })
        .catch(() => {
          resolve(dayjs())
        })
    })
  }

  showPageInTimes(isValid: boolean, startTime: number | string, endTime: number | string): Promise<boolean> {
    if (!startTime || !endTime) {
      console.log('必须同时传入开始时间和结束时间！')
      return Promise.resolve(true)
    }
    return new Promise((resolve) => {
      focusCore.getNowTime().then((now: any) => {
        const { modalStore } = focusStore.stores

        if (!now) {
          modalStore.errorMaskContrl('focus_time_after', ['活动已结束，下次再来参与吧~'])
          return resolve(false)
        }

        const isBefore = isValid ? now.isBefore(startTime) : false // 下架就是 false
        const isEnd = isValid ? now.isAfter(endTime) : true // 下架就是结束
        const isDuring = isValid && now.isBetween(startTime, endTime, Number, '[]') // 在活动期间！
        if (isDuring) {
          console.log('在指定时间内，可以显示活动！')
          return resolve(true)
        }
        // hideLoading('checkActivityStatus')
        if (isBefore) {
          console.error(`活动还没开始`)
          modalStore.errorMaskContrl('focus_time_before', ['活动还没开始，请耐心等待哦'])
        }
        if (isEnd) {
          console.error(`活动已结束`)
          modalStore.errorMaskContrl('focus_time_after', ['活动已结束，下次再来参与吧~'])
        }
        return resolve(false)
      })
    })
  }
}
export default new PageShowService()
