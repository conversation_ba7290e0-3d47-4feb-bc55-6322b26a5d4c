import { focusCore, focusStore } from '../packages/focus-render/index'
import { EnumMethod } from './focus-core/plugins/Ajax'

const cgis = {
  pullPopup: {
    name: '获取弹窗配置',
    url: '/wm-htrserver/cop/hj/fm/msg/pull-popup',
  },
  setRead: {
    name: '设置已读',
    url: '/wm-htrserver/cop/hj/fm/msg/set-read',
  },
}

enum PopupEnumType {
  QYG = 'focus-qyg', // 企业购弹窗
}

//扩展接口服务
class ExtendInterfaceService {
  popup: any = null
  constructor() {
    this.popup = this.initPopupData()
  }
  initPopupData() {
    const instance = new PopupService()
    return {
      instance: instance,
      get: (popupEnumType: PopupEnumType) => {
        return instance.queryPopupMsgData(popupEnumType)
      },
      read: (msgId: number, testFlag: boolean) => {
        return instance.readPopupMsg(msgId, testFlag)
      },
    }
  }
}

class PopupService {
  queryPopupMsgData(popupEnumType: string) {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.pullPopup, {
          popupType: popupEnumType,
          clientType: 1,
          setStart: 0,
          offList: 10,
        })
        .then(
          (res: {
            popup_msg: {
              testMsg?: boolean // 是否测试消息
              msgId: number
              popupName: string // 弹窗名称
              popupType?: string
              popupPlace?: string
              popupSubtype?: string
              popupDesc?: string
              title?: string
              subTitle?: string
              actionInfo?: string //跳转信息配置
              advPicUrl?: string // 背景图片url
              moduleType?: string //模块类型
            }[]
          }) => {
            console.log('获取的弹窗数据', res)
            const { popup_msg = [] } = res
            const popupData = popup_msg[0]
              ? popup_msg[0]
              : { actionInfo: '[]', advPicUrl: '', msgId: 0, testMsg: false }

            const { actionInfo = '[]' } = popupData
            let _actionInfo = {
              action_param: '', // 跳转路径
              prod_code: '', // 产品code
              moduleType: '', // 1:内置浏览器；2:产品详情页；3:内部模块；4:小程序；5:加白跳转；   focus只支持1 2 3
              sceneParams: {},
            }
            try {
              const temp = JSON.parse(actionInfo)
              _actionInfo = (temp && temp[0]) || {}
            } catch {
              console.log('actionInfo 有问题！')
            }
            return resolve({
              popupData: {
                msgId: popupData.msgId || 0,
                bgImgUrl: popupData.advPicUrl || '',
                testMsg: popupData.testMsg || false,
                jumpConfig: {
                  path: _actionInfo.action_param || _actionInfo.prod_code,
                  query: _actionInfo.sceneParams,
                  method: (() => {
                    let m = 'wxUrl'
                    switch (_actionInfo.moduleType) {
                      case '1':
                        break
                      case '2':
                        m = 'productCode'
                        break
                      case '3':
                        m = 'appModule'
                        break
                    }
                    return m
                  })(),
                },
              },
              allDatas: popup_msg,
            })
          },
        )
        .catch(() => {
          return resolve({
            popupData: {},
            allDatas: [],
          })
        })
    })
  }

  readPopupMsg(msgId: number, testFlag: boolean) {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.setRead, { msgId, testFlag })
        .then((res: any) => {
          const { result = 0 } = res
          resolve(!!result)
        })
        .catch(() => {
          resolve(false)
        })
    })
  }
}

export default new ExtendInterfaceService()
