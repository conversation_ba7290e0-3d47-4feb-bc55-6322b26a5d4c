// import grayService from './grayService'

export interface TFocusData {
  fid: number
  apiList: any[]
  config: any[]
  shareConfig: any
  grayList: TGrayIdItem[]
  serviceConfig: any[]
  popupConfig: any[]
  pageVer: string
}

export interface ITagIdItem {
  id: string
  subId?: string
  isPage?: boolean
  hideModules: number[]
}

interface TGrayIdItem {
  id: string
  subId?: string
  isPage?: string
  tagIds?: Array<{ hideModules: number[] }>
  useGrayType?: string
}
import { focusCore } from '../packages/focus-render/index'
import { EnumBaseHostKey } from './focus-core/plugins/baseConfig'
import { dayjs, UrlParser } from '@/utils'
import grayService from './grayService'

const cgis = {
  focusConfig: {
    name: '获取focus配置',
    baseUrlKey: EnumBaseHostKey.Hjdata,
    url: '/hj/focus-api/get_config',
    setCredentialsFalse: true,
  },
  focusConfigByCDN: {
    name: '通过cdn获取focus页面',
    baseUrlKey: EnumBaseHostKey.CDN,
    url: '/hjupload/focus_config',
    timeout: 1000,
    setCredentialsFalse: true,
  },
}

class FocusService {
  getConfig(data: { fid: number; aid?: number; useLocalModuleData?: any }): Promise<TFcousData> {
    const { fid, aid = 0 } = data || {}
    const noNeedConfig = !fid && !aid
    console.log('🚀 ~ FocusService ~ getConfig ~ useLocalModuleData:', data.useLocalModuleData)

    if (noNeedConfig) {
      let _localData = {}
      try {
        _localData = data.useLocalModuleData && Object.assign({}, data.useLocalModuleData)
      } catch (err) {
        console.log('🚀 ~ FocusService ~ getConfig ~ err:', err)
      }
      return Promise.resolve({
        fid: fid || 0,
        pageVer: '',
        apiList: [],
        config: [],
        shareConfig: {},
        grayList: [],
        serviceConfig: [],
        popupConfig: [],
        ..._localData,
      })
    }

    return new Promise((resolve) => {
      this.queryConfigDataByCDN({ fid: fid, aid }).then((res) => {
        console.log('🚀 ~ file: focusService.ts ~ line 36 ~ FocusService ~ .then ~ res', res)
        if (!res) {
          return focusCore
            .request(cgis.focusConfig, { configId: fid, aid })
            .then((res: any) => {
              console.log('🚀 ~ file: focusBase.ts ~ line 8 ~ FcousBase ~ ajax.request ~ res', res)
              const _res = res

              const {
                id = fid,
                apiList = '[]',
                config = '[]',
                shareConfig = '{}',
                grayList = '[]',
                serviceConfig = '{}',
                popupConfig = '[]',
                pageVer = '',
              } = _res

              return resolve({
                fid: id,
                apiList: (apiList && JSON.parse(apiList)) || [],
                config: (config && JSON.parse(config)) || [],
                shareConfig: (serviceConfig && JSON.parse(shareConfig)) || {},
                grayList: (grayList && JSON.parse(grayList)) || [],
                serviceConfig: (serviceConfig && JSON.parse(serviceConfig)) || [],
                popupConfig: (popupConfig && JSON.parse(popupConfig)) || [],
                pageVer,
              })
            })
            .catch((err: any) => {
              console.log('🚀 ~ file: focusBase.ts ~ line 16 ~ FcousBase ~ returnnewPromise ~ err', err)
              return resolve({
                fid: fid || 0,
                apiList: [],
                config: [],
                shareConfig: {},
                grayList: [],
                serviceConfig: [],
                popupConfig: [],
              })
            })
        }
        resolve(res)
      })
    })
  }
  queryConfigDataByCDN({ fid, aid }: { fid: number; aid: number }): Promise<TFcousData | null> {
    const query = new UrlParser(location.href).query
    if (query.cdn === '0') {
      return Promise.resolve(null)
    }
    /**
     * 这里拼接cdn的地址，格式如下
     * 新活动：https://dbd.webankwealthcdn.net/wm-resm/hjupload/focus_config/fid-${fid}.json
     * 老活动：https://dbd.webankwealthcdn.net/wm-resm/hjupload/focus_config/aid-${aid}.json
     */

    let url = '/'
    let key = ''
    if (fid) {
      key = `fid-${fid}`
    } else if (aid) {
      key = `aid-${aid}`
    }
    try {
      if (focusCore.isPreview && query.focus_preview === '1') {
        key = `preview/${key}`
      } else if (focusCore.isPreview && query.focus_review === '1') {
        key = `review/${key}`
      }
      console.error('使用预览模式！')
      console.log('🚀 ~ FocusService ~ queryConfigDataByCDN ~ key:', key)
    } catch (err) {
      console.log('🚀 ~ FocusService ~ queryConfigDataByCDN ~ err:', err)
    }

    url += `${key}.json`
    const startTime = Date.now()

    const cgiConfig = {
      ...cgis.focusConfigByCDN,
      url: cgis.focusConfigByCDN.url + url,
    }
    return new Promise((resolve) => {
      focusCore
        .request(cgiConfig, { t: startTime })
        .then((res: any) => {
          const {
            id = Number(fid) || 0,
            apiList = '[]',
            config = '[]',
            shareConfig = '{}',
            grayList = '[]',
            serviceConfig = '{}',
            popupConfig = '[]',
            ver = 0,
            customerVer = 0,
          } = res
          return resolve({
            fid: id,
            apiList: (apiList && JSON.parse(apiList)) || [],
            config: (config && JSON.parse(config)) || [],
            shareConfig: (serviceConfig && JSON.parse(shareConfig)) || {},
            grayList: (grayList && JSON.parse(grayList)) || [],
            serviceConfig: (serviceConfig && JSON.parse(serviceConfig)) || [],
            popupConfig: (popupConfig && JSON.parse(popupConfig)) || [],
            pageVer: `${customerVer + 1}.${ver}`,
          })
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: focus.ts ~ line 110 ~ FcousBase ~ returnnewPromise ~ err', err)

          return resolve(null)
        })
    })
  }

  async getHideModules(grayList: TGrayIdItem[]): Promise<number[]> {
    if (!grayList.length) {
      return Promise.resolve([])
    }
    const isLogined = focusCore.isLogined
    // 未登录的隐藏组件，会放在 id为0 的hideModules下面
    if (!isLogined) {
      console.log('未登录哦！直接取 0 的隐藏组件')
      const target = grayList.find((i) => i.id === '0')
      const hideModuleIds = (target && target.tagIds && target.tagIds[0] && target.tagIds[0].hideModules) || []
      return Promise.resolve(hideModuleIds)
    }

    if (focusCore.mockData.isPreview && focusCore.mockData.mockGrayId && focusCore.mockData.mockGrayId.id === '0000') {
      console.log('模拟未登录哦！直接取 0 的隐藏组件')
      const target = grayList.find((i) => i.id === '0')
      const hideModuleIds = (target && target.tagIds && target.tagIds[0] && target.tagIds[0].hideModules) || []
      return Promise.resolve(hideModuleIds)
    }

    // 已登录未开户
    if (isLogined && !focusCore.hasAccount) {
      const target = grayList.find((i) => i.id === '1')
      const hideModuleIds = (target && target.tagIds && target.tagIds[0] && target.tagIds[0].hideModules) || []
      return Promise.resolve(hideModuleIds)
    }

    let userGrayIds: { type: string; subType: string }[] = [
      {
        type: '1',
        subType: '',
      },
    ]
    let userTagIds: number[] = [1]

    console.log('🚀 ~ 已配置的灰度名单列表', grayList)
    const { tagIds = [], useGrayType = '0' } = grayList.find((i) => i.id === '1') || {}
    // 待查询的业务灰度id
    const busGrayIds = grayList
      .filter((i) => i.id !== '1' && i.id !== '0')
      .map((i) => {
        return {
          id: i.id,
          subId: i.subId || '',
        }
      })
    //待查询的标签ID
    const tagIdParams = (tagIds as ITagIdItem[]).filter((i) => i.id !== '1').map((i) => Number(i.id))
    const ajaxRes = await Promise.all([
      grayService.checkBussinessGray(busGrayIds),
      grayService.checkMultiTagId(tagIdParams),
    ])

    userGrayIds = ajaxRes[0] || []
    console.log('🚀 ~ FocusService ~ getHideModules ~ userGrayIds:', userGrayIds)
    userTagIds = ajaxRes[1]

    let targetTagIdData: any = null // 找到目标标签id配置
    let hideModules = [] // 隐藏的组件
    const targetGrayData = await this.getTargetGrayDataByUserGrayId(grayList, userGrayIds)

    if (targetGrayData.id) {
      //找到对应标签id的配置
      const { tagIds = [] } = targetGrayData
      if (!userTagIds.length) {
        console.log('没有匹配的标签ID')
        targetTagIdData = (tagIds as ITagIdItem[]).find((i) => i.id === '1')
      } else {
        targetTagIdData = (tagIds as ITagIdItem[]).find((i) => i.id === (userTagIds[0] && userTagIds[0].toString()))
      }
    }

    hideModules = (targetTagIdData && targetTagIdData.hideModules) || []
    console.log('🚀 ~ 要隐藏的组件有', hideModules)
    return Promise.resolve(hideModules)
  }

  getTargetGrayDataByUserGrayId(
    grayList: TGrayIdItem[],
    userGrayIds: { type: string; subType: string }[],
  ): Promise<TGrayIdItem> {
    const pageGrayIds = grayList
      .filter((i) => i.isPage === '1')
      .map((i) => {
        return {
          id: i.id,
          subId: i.subId || '',
        }
      })
    const hasPageGrayIdConfig = !!pageGrayIds.length
    let targetGrayId = '1'
    let targetGraySubId = ''
    // 没有页面灰度ID，返回第一个满足的业务灰度ID的配置
    if (!hasPageGrayIdConfig) {
      targetGrayId = (userGrayIds[0] && userGrayIds[0].type) || '1'
      targetGraySubId = (userGrayIds[0] && userGrayIds[0].subType) || ''
    } else {
      // 有配置页面灰度ID，找到第一个满足的页面灰度ID
      let targetGray = pageGrayIds.find((i) => userGrayIds.some((u) => u.type === i.id && u.subType === i.subId))
      // 用户不在页面灰度中，返回失败
      if (!(targetGray && targetGray.id)) {
        return Promise.reject({ error: 'not_in_page_gray' })
      } else {
        targetGrayId = (targetGray && targetGray.id) || ''
        targetGraySubId = (targetGray && targetGray.subId) || ''
      }
    }
    //返回查到的灰度配置
    let targetGrayData: TGrayIdItem = {
      id: '',
      subId: '',
      isPage: '0',
      tagIds: [],
    }
    if (targetGraySubId) {
      targetGrayData = grayList.find((i) => i.id === targetGrayId.toString() && i.subId === targetGraySubId) || {
        id: '',
        subId: '',
        isPage: '0',
        tagIds: [],
      }
      console.log(
        '🚀 ~ file: grayService.js ~ line 117 ~ GrayService ~ checkPageGrayGonfig ~ targetGrayData',
        targetGrayData,
      )
    } else {
      targetGrayData = grayList.find((i) => i.id === targetGrayId.toString() && !i.subId) || {
        id: '',
        subId: '',
        isPage: '0',
        tagIds: [],
      }
      console.log(
        '🚀 ~ file: grayService.js ~ line 118 ~ GrayService ~ checkPageGrayGonfig ~ targetGrayData',
        targetGrayData,
      )
    }
    return Promise.resolve(targetGrayData)
  }
}

export const focusService = new FocusService()

export default focusService
