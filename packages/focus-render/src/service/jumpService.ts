import { base64encode } from '../utils/index'
import { UrlParser } from '../utils/url'
import { focusCore, focusStore } from '../packages/focus-render/index'
import baseConfig from './focus-core/plugins/baseConfig'
import { grayidsHanlder } from './grayService'

export enum EnumModuleType {
  AppWebview = '1',
  Prod = '2',
  AppModule = '3',
}

export interface VueRouterRedirect {
  routerInstance?: any
  rootPath?: string
  hashList?: string[]
}

export interface JumpConfig {
  path: string
  method?: string
  query?: Record<string, any>
  miniUsername?: string
  miniEnv?: string
  jumpAppPath?: string
  vueRouterRedirect?: VueRouterRedirect
}
class JumpService {
  delayClicked: number

  // 如果要使用路由跳转模式
  vueRouterRedirect: {
    routerInstance?: any
    rootPath?: string
    hashList?: string[]
  } | null = {
    routerInstance: null,
    rootPath: '',
    hashList: [],
  }

  constructor() {
    this.delayClicked = 0
  }
  checkPathType(path: string) {
    let method = 'wxUrl'
    if (/^\//.test(path)) {
      method = 'appModule'
    }
    if (!(/^http/.test(path) || /^\//.test(path))) {
      method = 'productCode'
    }
    return method
  }

  jumpNewWebview(h5Link: string) {
    const hjCoreIab = window.hjCoreIab
    if (!/^http/.test(h5Link)) {
      console.error('不是h5链接，不跳拉')
      return false
    }
    if (focusCore.env.isInApp) {
      hjCoreIab.navigate(h5Link)
    } else {
      location.href = h5Link
    }
  }

  jump(
    jumpConfig: {
      path: string
      method?: string // 跳转方式 url: 跳转到App的网页，wxUrl: 跳转微信网页，mini: 跳转到小程序，appModule: 跳转到app模块，productCode: 跳转到产品码
      query?: any // App模块的query参数
      miniUsername?: string
      miniEnv?: string
      jumpAppPath?: string //当选择了 跳转App网页时，需要调用后台生成签名，最终产物是一个 download-brdge的链接
      vueRouterRedirect?: {
        routerInstance?: any
        rootPath?: string
        hashList?: string[]
      }
    },
    replace?: boolean,
  ) {
    let { path = '', query = {}, vueRouterRedirect, jumpAppPath } = jumpConfig

    const { method, miniUsername, miniEnv } = jumpConfig
    const isReplace = replace || false
    console.log('🚀 ~ file: jumpService.ts ~ line 26 ~ JumpService ~ jump ~ jumpConfig', jumpConfig)

    if (!path) {
      console.log('没有跳转路径')
      return false
    }
    this.vueRouterRedirect = vueRouterRedirect || null
    let jumpMethod = method
    const pathParsed = new UrlParser(path)
    if (!jumpMethod) {
      path = pathParsed.path
      query = { ...query, ...pathParsed.query }
      jumpMethod = this.checkPathType(path) || ''
    }
    console.log('🚀 ~ 跳转路径', path)
    console.log('🚀 ~ 跳转方式：', jumpMethod)

    if (!path || !jumpMethod) {
      console.error('没有跳转方式')
      return false
    }
    focusCore.eventLog('redirect', pathParsed.fullPath, {
      path: pathParsed.fullPath,
      query,
      miniUsername,
      isReplace,
    })
    this.startJump(pathParsed, jumpMethod, query, miniUsername, miniEnv, isReplace, jumpAppPath)
  }

  locationHref(pathParsed: UrlParser, isReplace?: boolean) {
    const targetPath = baseConfig.isUseNewHost ? pathParsed.hrefUseCurHost : pathParsed.fullPath
    console.log('🚀 ~ file: jumpService.ts:101 ~ JumpService ~ locationHref ~ targetPath:', targetPath)
    if (isReplace) {
      location.replace(targetPath)
    } else {
      window.location.href = targetPath
    }
  }

  startJump(
    pathParsed: UrlParser,
    method: string,
    query: any,
    miniUsername?: string,
    miniEnv?: string,
    isReplace?: boolean,
    jumpAppPath?: string,
  ) {
    console.log('🚀 ~ JumpService ~ jumpAppPath:', jumpAppPath)
    const isInApp = focusCore.env.isInApp
    const hjCoreIab = window.hjCoreIab
    const newPathParsed = pathParsed && pathParsed.appendQuery(query || {})
    switch (method) {
      // 到app打开链接
      case 'url':
        if (isInApp) {
          // window.location.href = pathParsed.fullPath
          // 之前不知道基于什么理由，在App执行跳转h5，要repalce，这个版本去除
          this.locationHref(newPathParsed)
        } else {
          if (jumpAppPath) {
            const { modalStore } = focusStore.stores
            modalStore.loadingStart('jumpAppPath')

            grayidsHanlder.canUseBusid('1209').then((status) => {
              if (!status) {
                this.openOrDownloadApp(newPathParsed.fullPath, EnumModuleType.AppWebview, {
                  ...query,
                })
              } else {
                window.location.href = jumpAppPath
              }
              modalStore.loadingEnd('jumpAppPath')
            })
          } else {
            this.openOrDownloadApp(newPathParsed.fullPath, EnumModuleType.AppWebview, {
              ...query,
            })
          }
        }
        break
      // 在当前webview打开
      case 'wxUrl':
        if (isReplace) {
          window.location.replace(newPathParsed.fullPath)
        } else {
          this.locationHref(newPathParsed)
        }
        break
      // 打开app模块
      case 'appModule':
        if (isInApp && hjCoreIab) {
          const path = pathParsed.path
          const parsedPath = path.split('/').filter((i: any) => i)
          const module = parsedPath.length && parsedPath[0]
          const scene = parsedPath.length > 1 && parsedPath[1]
          const data = {
            ...query,
          }
          if (module) {
            if (!scene) {
              hjCoreIab.navigate({
                module,
                data,
              })
            } else {
              hjCoreIab.navigate({ module, scene, data })
            }
          }
        } else {
          const path = pathParsed.path
          this.openOrDownloadApp(path, EnumModuleType.AppModule, {
            scene_param: query,
            // moduleType: 2,
          })
        }
        break
      case 'productCode':
        console.log('跳产品！')
        if (isInApp) {
          if (query.useJumpToProduct) {
            console.log('可以使用PjumpToroduct!')
            try {
              delete query.useJumpToProduct
            } catch {}
            focusCore.invokeApp.jumpToProduct({
              productCode: pathParsed.path,
              extraParams: {
                scene_param: query,
              },
            })
            return
          }
          hjCoreIab && hjCoreIab.navigate(pathParsed.path)
        } else {
          // this.redirectToAppPage(path, true)
          this.openOrDownloadApp(pathParsed.path, EnumModuleType.Prod, {
            ...query,
            // moduleType: 3,
          })
        }
        break
      case 'mini':
        console.log('changshi ', pathParsed.fullPath)
        if (isInApp && hjCoreIab) {
          hjCoreIab.launchMiniProgram({
            userName: miniUsername,
            miniProgramType: miniEnv,
            path: pathParsed.href,
          })
          return
        }

        if (window.wx) {
          focusCore.env.checkIsInWxMini().then(() => {
            window.wx.miniProgram.navigateTo({
              url: pathParsed.h5InWxMiniJumpToWxMiniPath,
              data: {},
              success: function () {
                console.log('success')
              },

              fail: function () {
                console.log('fail')
              },

              complete: function () {
                console.log('complete')
              },
            })
          })
        }
        break
      default:
        break
    }
  }

  openOrDownloadApp(path: string, moduleType: string, query: any) {
    const jumpUrl = this.getSchemaUrlOutApp(path, moduleType, query)
    console.log('🚀 ~ file: jumpService.ts ~ line 123 ~ JumpService ~ openOrDownloadApp ~ jumpUrl', jumpUrl)
    this.openDownloadPage(jumpUrl)

    // if (focusCore.env.device === 'android') {
    //   const jumpUrl = this.getSchemaUrlOutApp(path, query)
    //   // var ifr = document.createElement('iframe')
    //   // ifr.src = jumpUrl
    //   // ifr.style.cssText = 'display:none;width:0;height:0;'
    //   // document.body.appendChild(ifr)
    //   // setTimeout(function () {
    //   //   document.body.removeChild(ifr)
    //   // }, 100)
    //   this.openDownloadPage(jumpUrl)
    // } else if (focusCore.env.device === 'ios') {
    //   const jumpUrl = this.getSchemaUrlOutApp(path, query)
    //   // window.location.href = jumpUrl
    //   // this.delayClicked = Date.now()
    //   this.openDownloadPage(jumpUrl)
    //   // setTimeout(this.openDownloadPage(jumpUrl), 500)
    // } else {
    //   window.location.href = 'https://gzhjdata.webank.com/hj/download/share'
    // }
  }
  openDownloadPage(webankSchemaPath: string) {
    let downloadPath = 'https://m.webank.com/s/hj/op/app/common/bridge-download/index.html'
    // if (location.href.indexOf('personal.test') > -1) {
    //   downloadPath = 'https://personal.test.webank.com/s/hj/op/app/common/bridge-download/index.html'
    // }
    const url = new UrlParser(downloadPath)
    const curParsed = new UrlParser(location.href)
    url.appendQuery({
      focuspageid: curParsed.query.pageid,
      aid: curParsed.query.aid,
      op: encodeURIComponent(webankSchemaPath),
    })
    const jumpUrl = url.fullPath
    console.log('🚀 ~ ', jumpUrl)
    if (this.delayClicked) {
      if (Date.now() - this.delayClicked < 2000) {
        window.location.href = jumpUrl
      }
    } else {
      window.location.href = jumpUrl
    }
  }

  // redirectToAppPage(path: string, query: { [key: string]: any }) {
  //   const urlParsed = new UrlParser(location.href)
  //   if (this.isInApp) {
  //     // 在app中使用schema跳转
  //     const baseHost = 'https://www.webank.com'
  //     const jumpParam = {
  //       webank_app_info: 'y',
  //     }
  //     const urlParsed = new UrlParser(baseHost)
  //     const appSchemaLink = {}
  //   }
  // }
  /**
   * 使用 schema 的方式在APP中打开目标页
   * @param path
   * @param query
   * @returns
   */
  // useSchemaOpenInApp(path: string, query: { [key: string]: any }) {
  //   const appSchemaParams = this.getAppSchemaParams(path, query)
  //   console.log('appSchemaLink:')
  //   console.log(appSchemaParams)
  //   console.log('>>>>>>>>>>')
  //   const baseHost = new UrlParser('https://www.webank.com')
  //   const jumpParam = {
  //     webank_app_info: 'y',
  //     t: Date.now().toString(),
  //     jump_to_page: base64encode(appSchemaParams),
  //   }
  //   return baseHost.appendQuery(jumpParam).fullPath
  // }

  getSchemaUrlOutApp(path: string, moduleType: string, query: { [key: string]: any }) {
    const appSchemaParams = this.getAppSchemaParams(path, moduleType, query)

    return `webank://?${appSchemaParams}`
  }

  getAppSchemaParams(path: string, moduleType: string, query: { [key: string]: any }): string {
    const curUrl = new UrlParser()
    const focusPageId = `${curUrl.query.fid || 0}_${curUrl.query.aid}`
    const jumpParam: any = {
      action_param: path,
      iosMinVer: 380,
      androidMinVer: 3040,
      fromFocusLocal: focusPageId,
    }
    // 对于 /a/b/x 的app内部模块跳转链接，需要移除开头的 ‘/’
    if (/^\//g.test(path) && (path.match(/\//g) || []).length >= 2) {
      jumpParam.action_param = path.slice(1)
    }

    if (query.wb_src) {
      jumpParam.wb_src = query.wb_src
    }
    if (Object.keys(query.scene_param || {}).length) {
      jumpParam.scene_param = query.scene_param
    }
    const jumpParamStr = encodeURIComponent(base64encode(JSON.stringify(jumpParam)))

    const str = `subType=2&moduleType=${moduleType}&jumpParam=${jumpParamStr}`
    return str
  }

  get commonUse() {
    return {
      appHome: (autoSwitchMiniOrApp?: boolean) => {
        if (autoSwitchMiniOrApp) {
          focusCore.env
            .checkIsInWxMini()
            .then(() => {
              this.jump({
                path: '/pages/index/index?specialJump=activityOpenAccount',
                method: 'mini',
              })
            })
            .catch(() => {
              this.jump({ path: '/home/<USER>' })
            })
          return
        }
        this.jump({ path: '/home/<USER>' })
      },
      appHomeOrMiniHome: () => {
        focusCore.env
          .checkIsInWxMini()
          .then(() => {
            this.jump({
              path: '/pages/index/index?specialJump=activityOpenAccount',
              method: 'mini',
            })
          })
          .catch(() => {
            console.log('🚀 ~ 准备跳转:', '/home/<USER>')
            this.jump({ path: '/home/<USER>' })
          })
      },
      openAccount: () => {
        focusCore.env
          .checkIsInWxMini()
          .then(() => {
            this.jump({
              path: '/pages/index/index?specialJump=activityOpenAccount',
              method: 'mini',
            })
          })
          .catch(() => {
            console.log('🚀 ~ 准备跳转:', '/home/<USER>')
            // 在App时候，去开户页
            if (focusCore.env.isInApp) {
              this.jump({
                path: '/register/RegisterPrepareScene',
              })
            } else {
              // 不在App去首页
              this.jump({
                path: '/home/<USER>',
                method: 'appModule',
              })
            }
          })
      },
      // 转账浮层
      transDialog: () => {
        if (focusCore.env.isInApp) {
          this.jump({ path: '/X_OTHER_BANK_TRANS_IN_DIALOG' })
          return
        }
        focusCore.env
          .checkIsInWxMini()
          .then(() => {
            this.jump({
              path: '/pages/transfer/wecard',
              method: 'mini',
            })
          })
          .catch(() => {
            this.jump({ path: '/X_OTHER_BANK_TRANS_IN_DIALOG' })
          })
      },
      userPoint: () => {
        if (focusCore.env.isInApp) {
          this.jump({ path: '/rewardPoint/RewardPointHomeScene', method: 'appModule' })

          return
        }
        focusCore.env
          .checkIsInWxMini()
          .then(() => {
            this.jump({
              path: '/packageRewardPoint/pages/home/<USER>',
              method: 'mini',
            })
          })
          .catch(() => {
            this.jump({ path: '/rewardPoint/RewardPointHomeScene', method: 'appModule' })
          })
      },
      couponList: () => {
        if (focusCore.env.isInApp) {
          this.jump({ path: '/welfare/MyWelfareScene', method: 'appModule' })

          return
        }
        focusCore.env
          .checkIsInWxMini()
          .then(() => {
            this.jump({
              path: '/packageEquity/pages/coupon/couponList',
              method: 'mini',
            })
          })
          .catch(() => {
            this.jump({ path: '/welfare/MyWelfareScene', method: 'appModule' })
          })
      },
    }
  }
}

export default new JumpService()
