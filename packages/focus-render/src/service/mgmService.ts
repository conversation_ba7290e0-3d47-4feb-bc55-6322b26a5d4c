import { focusCore, focusStore } from '../packages/focus-render/index'
// const { userService } = focusServices || {}
import userService from './userService'
import { UrlParser, dayjs } from '../utils'
import { utf16toEntities, entitiestoUtf16 } from '../utils/formate-name'
import { EnumMethod } from './focus-core/plugins/Ajax'
const defaultM1Avatar = require('../assets/img/common/m1_avator.png')

export interface TShareConfig {
  shareTitle?: string
  shareImg?: string
  shareDesc?: string
  shareUrl?: string
  w2kShareKey?: string
  userSid?: string
  appShareType?: '0' | '1' | '2' | '3'
  miniUserName?: 'H5' | 'wxcb823d713276a10d-release' | 'wxcb823d713276a10d-preview' | 'wxdb1f857b4f658a6f-preview'
  shareImageBg?: string
  shareImageHeight?: string
  shareMgmAid?: string
  mgmAidV2?: string
}

enum TAcceptShareFunctionType {
  Family = 'my_family',
}
export enum EnumFamilyKeys {
  IsInMyFamily = 'isInMyFamily',
}
export const cgis = {
  genSid: {
    url: '/wm-htrserver/op/mgm2/gen_sid',
    name: '查询用户的sid',
    canSaveTimes: true,
  },
  acceptShare: {
    url: '/wm-htrserver/op/mgm2/accept_share',
    name: '接受邀请关系',
  },
  checkM1IsM2: {
    url: '/wm-htrserver/op/mgm2/check_m1_is_m2',
    name: '检查当前用户是否是M2',
  },
  relationshipList: {
    url: '/wm-htrserver/op/mgm2/list_relation_enterprise',
    name: 'mgm邀请列表',
    cacheWithQueryKeys: ['activityId'],
    canSaveTimes: true,
  },
  getShareUserinfo: {
    url: '/wm-htrserver/op/mgm2/query_share',
    name: '获取M1用户头像昵称',
  },
  M2HelpM1: {
    url: '/hjop/share/mgm/saveActivitySid',
    name: 'M2给M1助力',
  },
  checkM2HelpedM1: {
    url: '/hjop/share/mgm/queryActivitySid',
    name: '查询当前M2是否给M1助力过',
  },
  M1HelpersList: {
    url: '/hjop/share/mgm/queryActivitySid',
    name: '查询M1助力列表',
  },
  w2kShareKey: {
    url: '/wm-htrserver/op/mgm2/create_share_key',
    name: '创建we2000的shareKey',
    method: EnumMethod.POST,
    canSaveTimes: true,
    cacheTimeOut: 5000,
  },
}

const cgisV2 = {
  generateInviteCode: {
    url: '/op-fmfront/mgm/hj/fm/generate-invite-code',
    method: EnumMethod.POST,
    canSaveTimes: true,
    cacheTimeOut: 10000,
    cacheWithQueryKeys: ['activityId'],
    name: '生成当前用户的邀请码',
  },
  queryInviter: {
    url: '/op-fmfront/mgm/hj/fm/query-inviter',
    name: '查看M1的头像和昵称',
  },
  acceptShare: {
    url: '/op-fmfront/mgm/hj/fm/accept-invite',
    name: '接受邀请关系V2',
    method: EnumMethod.POST,
  },
  queryInviteList: {
    url: '/op-fmfront/mgm/hj/fm/query-invite-list',
    name: '查询邀请列表',
    cacheWithQueryKeys: ['tabType', 'offset', 'm2Status', 'activityIds'],
    canSaveTimes: true,
    cacheTimeOut: 10000,
  },
  mgmMembers: {
    url: '/op-fmfront/mgm/hj/fm/query-invite-total',
    name: '查询MGM活动邀请人数',
    cacheWithQueryKeys: ['mgmAid'],
    canSaveTimes: true,
  },
  acceptShareByInputShareCode: {
    url: '/op-fmfront/mgm/hj/fm/register-invite-code',
    name: '填写邀请码',
    method: EnumMethod.POST,
  },
}

function sendShareMsgInMini(shareTitle: string, shareImg: string, shareDesc: string, shareUrl: string) {
  console.log('给小程序发消息！')
  console.log(`sharetitle:${shareTitle}`)
  console.log(`shareImg:${shareImg}`)
  console.log(`shareDesc:${shareDesc}`)
  console.log(`shareUrl:${shareUrl}`)
  const wx = window.wx
  wx.miniProgram.postMessage({
    data: {
      msgType: 'share',
      shareTitle,
      shareImg,
      shareDesc,
      shareUrl: `/pages/webview/webview?skipCheck=1&url=${encodeURIComponent(shareUrl)}`,
      // shareUrl: `/pages/index/index?type=page&page=/pages/webview/webview?url=${encodeURIComponent(shareUrl)}`,
    },
  })
}

class SharePlaceHandler {
  wxRTCorner(openFlag = false, shareConfig?: TShareConfig) {
    console.log('开始设置微信分享')
    const { shareTitle = '', shareImg = '', shareDesc = '', shareUrl = '' } = shareConfig || {}
    console.log('🚀 ~ file: mgmService.ts:61 ~ SharePlaceHandler ~ wxRTCorner ~ shareConfig:', shareConfig)
    const wx = window.wx

    if (!wx) {
      console.log('不是微信环境，不执行了')
      return false
    }

    if (!openFlag || !shareUrl) {
      console.log('关闭微信的分享~')
      wx.ready(() => {
        wx.hideMenuItems({
          menuList: [
            'menuItem:share:appMessage',
            'menuItem:share:timeline',
            'menuItem:favorite',
            'menuItem:share:qq',
            'menuItem:share:weiboApp',
            'menuItem:share:facebook',
            'menuItem:share:QZone',
            'menuItem:editTag',
            'menuItem:delete',
            'menuItem:copyUrl',
            'menuItem:originPage',
            'menuItem:readMode',
            'menuItem:openWithQQBrowser',
            'menuItem:openWithSafari',
            'menuItem:share:email',
            'menuItem:share:brand',
          ],
        })
      })
      return false
    }

    focusCore.env
      .checkIsInWxMini()
      .then((status) => {
        if (status) {
          console.log('小程序环境，给小程序发个消息')
          sendShareMsgInMini(shareTitle, shareImg, shareDesc, shareUrl)
        }
      })
      .catch(() => {
        console.error('这不是小程序环境哦！')
      })

    const appMessageShareData = {
      title: shareTitle,
      desc: shareDesc,
      link: shareUrl,
      imgUrl: shareImg,
    }

    const timelineShareData = {
      title: shareDesc,
      link: shareUrl,
      imgUrl: shareImg,
    }
    wx.ready(() => {
      wx.showMenuItems({
        menuList: ['menuItem:share:appMessage', 'menuItem:share:timeline', 'menuItem:favorite'],
      })

      wx.updateTimelineShareData({
        ...timelineShareData,
        success() {
          console.log('wx.updateTimelineShareData success', timelineShareData)
        },
      })
      wx.updateAppMessageShareData({
        ...appMessageShareData,
        success() {
          console.log('wx.updateAppMessageShareData success', appMessageShareData)
        },
      })
    })
  }

  formateAppShareConfig(shareConfig?: TShareConfig & { w2kShareKey?: string; userSid?: string }): {
    appShareConfig: TAppShareConfig
    isExecuteShare: boolean
  } {
    const {
      shareTitle = '',
      shareImg = '',
      shareDesc = '',
      shareUrl = '',
      appShareType = '1',
      miniUserName = 'H5',
      userSid = '',
      w2kShareKey = '',
      shareImageBg = '',
      shareImageHeight = '',
    } = shareConfig || {}

    console.log('🚀 ~ file: mgmService.ts ~ line 132 ~ SharePlaceHandler ~ appPannel ~ shareConfig', shareConfig)

    const hjCoreIab = window.hjCoreIab

    let shareType = hjCoreIab.CONS.ShareType.WX_SESSION | hjCoreIab.CONS.ShareType.WX_TIME_LINE
    let type = hjCoreIab.CONS.MIMEType.URL

    const isExecuteShare = appShareType === '3'
    if (appShareType === '1' || appShareType === '3') {
      shareType = hjCoreIab.CONS.ShareType.WX_SESSION
    }
    if (appShareType === '2') {
      shareType = hjCoreIab.CONS.ShareType.WX_TIME_LINE
    }

    // 存在图片分享时候，在App调用图片分享能力
    if (shareImageBg) {
      shareType = hjCoreIab.CONS.ShareType.WX_SESSION | hjCoreIab.CONS.ShareType.WX_TIME_LINE | 16
    }

    const shareH5Url = (() => {
      const parsedShareUrl = new UrlParser(shareUrl)
      const link = parsedShareUrl.appendQuery({ shareFrom: 'apph5' }).fullPath
      return link
    })()
    console.log('🚀 ~ SharePlaceHandler ~ shareH5Url ~ shareH5Url:', shareH5Url)

    const shareImgUrl = (() => {
      const parsedShareUrl = new UrlParser(shareUrl)
      const link = parsedShareUrl.appendQuery({ shareFrom: 'appimg' }).fullPath
      return link
    })()
    console.log('🚀 ~ SharePlaceHandler ~ shareImgUrl ~ shareImgUrl:', shareImgUrl)

    const appShareConfig: TAppShareConfig = {
      shareType,
      type,
      title: shareTitle,
      description: shareDesc,
      thumbImage: shareImg,
      webpageUrl: shareH5Url,
      shareImage: shareImageBg,
      shareImageWidth: 702,
      shareImageHeight,
      shareURL: shareImgUrl,
    }
    if (miniUserName && miniUserName !== 'H5') {
      const userName = miniUserName.split('-')[0] || ''
      const miniVer = miniUserName.split('-')[1] || ''
      const shareUrlParsed = new UrlParser(shareUrl)
      const { aid, fid2, fid3 } = shareUrlParsed.query
      const jumpParams = `aid=${aid}&fid2=${fid2}&fid3=${fid3}&sid=${userSid}&tag=${w2kShareKey}`
      shareUrlParsed.removeQuery(['aid', 'fid2', 'fid3'])
      shareUrlParsed.appendQuery({ jumpParams })
      console.log('w2k链接', shareUrlParsed.fullPath)
      if (userName && miniVer) {
        appShareConfig.path = shareUrlParsed.fullPath
        appShareConfig.hdImageData = shareImg
        appShareConfig.userName = userName
        appShareConfig.miniProgramType = miniVer
        appShareConfig.type = hjCoreIab.CONS.MIMEType.WEAPP
      }
    }

    console.log('🚀 ~ 最终的分享配置', appShareConfig)

    return { appShareConfig, isExecuteShare }
  }

  appRTCorner(openFlag = false, shareConfig?: TShareConfig & { w2kShareKey?: string; userSid?: string }) {
    const hjCoreIab = window.hjCoreIab
    const { appShareConfig } = this.formateAppShareConfig(shareConfig)
    console.log('🚀 ~ file: mgmService.ts:170 ~ SharePlaceHandler ~ appRTCorner ~ appShareConfig:', appShareConfig)
    if (!hjCoreIab || !focusCore.env.isInApp) {
      return false
    }
    if (!openFlag || !appShareConfig.webpageUrl) {
      hjCoreIab.setNavBar({
        rightButtonConfig: {
          type: 'custom',
          label: null,
        },
      })
      return false
    }
    hjCoreIab.setNavBar({
      rightButtonConfig: {
        type: 'share',
        shareConfig: appShareConfig,
      },
    })
  }

  appPannel(shareConfig: TShareConfig & { w2kShareKey?: string; userSid?: string }) {
    console.log('...拉起app分享面板')
    const { appShareConfig, isExecuteShare } = this.formateAppShareConfig(shareConfig)
    console.log('🚀 ~ 获取的App分享内容', appShareConfig)
    const hjCoreIab = window.hjCoreIab

    if (!hjCoreIab || !focusCore.env.isInApp) {
      return false
    }
    if (isExecuteShare) {
      hjCoreIab.executeShare(appShareConfig)
      return
    } else {
      hjCoreIab.share(appShareConfig)
    }
  }

  // 关闭所有位置的分享
  closeAllShare() {
    if (focusCore.env.isInApp) {
      this.appRTCorner(false)
    } else {
      this.wxRTCorner(false)
    }
  }
}

class MgmService {
  sharePlaceHandler = new SharePlaceHandler()
  userSid = ''
  mgmAidForSid = ''
  defaultRtConnerShare: TShareConfig = {
    shareTitle: '',
    shareDesc: '',
    shareImg: '',
    shareUrl: '',
    shareMgmAid: '',
  }

  defaultConfig: TShareConfig = {
    shareTitle: '',
    shareDesc: '',
    shareImg: '',
    shareUrl: '',
    shareMgmAid: '',
  }
  shareConfig: TShareConfig = {
    shareTitle: '',
    shareDesc: '',
    shareImg: '',
    shareUrl: '',
    shareMgmAid: '',
  }

  /**
   * 关闭分享
   */
  closeShare() {
    console.log('关闭所有分享')
    this.sharePlaceHandler.closeAllShare()
  }

  /**
   * 点击按钮来分享
   * @param reportKey
   * @param config
   */
  clickShare(reportKey: string, config?: TShareConfig & { sahreId?: string }) {
    console.log('🚀 ~ slickShare!', reportKey)
    // 点击分享，在APP里面拉起分享面板
    const { modalStore } = focusStore.stores

    modalStore.loadingStart('clickShare')
    this.setShareConfig(reportKey, config || {}).then((shareConfig: TShareConfig) => {
      modalStore.loadingEnd('clickShare')
      if (!shareConfig.shareUrl) {
        focusCore.eventLog(`share.error`, reportKey, { ...shareConfig, origin: config })
        return false
      }
      focusCore.eventLog(`share.click`, reportKey, { ...shareConfig, origin: config })
      if (focusCore.env.isInApp) {
        this.sharePlaceHandler.appPannel(shareConfig)
      } else {
        this.sharePlaceHandler.wxRTCorner(true, shareConfig)
        console.log('需要显示wx蒙层')
        modalStore.shareMaskContrl(true)
      }
    })
  }

  /**
   * 右上角分享
   */
  setRtConnerShare(reportKey: string, config: TShareConfig) {
    focusCore.eventLog(`share.rtconner`, reportKey, config)
    this.defaultRtConnerShare = config
    return new Promise((resolve) => {
      if (focusCore.env.isInApp) {
        this.setAppRTCornerShare(config).then((_shareConfig: any) => {
          focusCore.eventLog(`share.rtconner_app`, reportKey, { ..._shareConfig, origin: config })
          resolve(_shareConfig)
        })
      } else {
        this.setWxRTCornerShare(config).then((_shareConfig: any) => {
          focusCore.eventLog(`share.rtconner_wx`, reportKey, { ..._shareConfig, origin: config })
          resolve(_shareConfig)
        })
      }
    })
  }

  /**
   * 设置微信右上角分享
   */
  setWxRTCornerShare(config: TShareConfig) {
    return new Promise((resolve) => {
      this.setShareConfig('wx-rt-share', config || {}, true).then((shareConfig: TShareConfig) => {
        if (!shareConfig.shareUrl) {
          return resolve(false)
        }
        this.sharePlaceHandler.wxRTCorner(true, shareConfig)
        setTimeout(() => {
          return resolve(shareConfig)
        }, 100)
      })
    })
  }

  /**
   * 设置app右上角分享
   * @param reportKey
   */
  setAppRTCornerShare(config: TShareConfig) {
    return new Promise((resolve) => {
      this.setShareConfig('app-rt-share', config || {}, true).then((shareConfig: TShareConfig) => {
        if (!shareConfig.shareUrl) {
          return resolve(false)
        }
        this.sharePlaceHandler.appRTCorner(true, shareConfig)
        return resolve(shareConfig)
      })
    })
  }

  resetDefault(reportKey: string) {
    // this.handleShare(reportKey, this.defaultConfig)
    console.log('重置分享')
    this.closeShare()
    // 自动使用上一次的分享配置
    this.setRtConnerShare(reportKey, this.defaultRtConnerShare)
  }

  setShareConfig(
    reportKey: string,
    config: TShareConfig,
    resetDefault?: Boolean,
  ): Promise<TShareConfig & { w2kShareKey?: string; userSid?: string }> {
    console.log('🚀 ~ 设置分享！', config)
    focusCore.eventLog('share.change', reportKey, config)
    let {
      shareTitle,
      shareDesc,
      shareImg = 'https://dbd.webankwealthcdn.net/wm-resm/hj/focus/img/share_preview.jpg',
      shareUrl,
      shareMgmAid,
      appShareType,
      miniUserName = 'H5',
      mgmAidV2 = '',
      shareImageBg = '',
      shareImageHeight = '',
    } = config
    this.shareConfig = config
    if (resetDefault) {
      this.defaultConfig = config
    }
    if (!shareUrl) {
      console.error('没有分享连接诶！')
      return Promise.reject({})
    } else {
      // 限制域名是  personal.test.webank.com 或者 personal.webank.com
      const links = new RegExp(/(personal\.test\.webank\.com)|(personal\.webank\.com)/)
      // 限制链接是  /hj/focus2 的链接
      const paths = new RegExp(/(hj\/focus2)/)
      if (links.test(shareUrl) && paths.test(shareUrl)) {
        // 11.13版本后，强制分享链接都是m.test.webank.com 或者m.webank.com
        shareUrl = shareUrl
          .replace('personal.test.webank.com', 'm.test.webank.com')
          .replace('personal.webank.com', 'm.webank.com')
      }
    }
    // 未登录不走MGM逻辑，分享忽略mgmAid
    if (!focusCore.isLogined) {
      shareMgmAid = ''
    }

    if (!shareMgmAid) {
      // 在登录的情况下会从url取
      if (focusCore.isLogined) {
        console.log('没有传MGMAid，尝试从链接里面取')
        const parsedLink = new UrlParser(shareUrl)
        shareMgmAid = parsedLink.query.mgmAid || shareMgmAid || ''
        console.log('链接里面的mgmAid为', shareMgmAid)
      }

      if (!shareMgmAid) {
        console.log('依然没有mgmAId和')
        return Promise.resolve({
          shareTitle,
          shareDesc,
          shareImg,
          shareUrl,
          appShareType,
          shareImageBg,
          shareImageHeight,
        })
      }
    }

    return new Promise((resolve) => {
      if (shareMgmAid) {
        const { modalStore } = focusStore.stores
        modalStore.loadingStart('getUserSid')

        this.getUserSid(shareMgmAid).then(async (res: any) => {
          modalStore.loadingEnd('getUserSid')

          const { userSid = '' } = res
          if (!userSid) {
            modalStore.errorMaskContrl(`gensid_${shareMgmAid}`, ['生成分享链接失败，请重试'])
            return resolve({})
          }
          let w2kShareKey: string = ''
          if (miniUserName && miniUserName !== 'H5') {
            try {
              modalStore.loadingStart('getW2kShareKey')
              w2kShareKey = await this.getW2kShareKey(shareMgmAid || '', userSid)
            } catch {
              w2kShareKey = ''
            }
            modalStore.loadingEnd('getW2kShareKey')

            if (!w2kShareKey) {
              modalStore.errorMaskContrl(`w2kShareKey_${shareMgmAid}`, ['生成分享链接失败，请重试'])
              return resolve({})
            } else {
              return resolve({
                shareTitle,
                shareDesc,
                shareImg,
                shareUrl,
                appShareType,
                miniUserName,
                w2kShareKey,
                userSid,
                shareImageBg,
                shareImageHeight,
              })
            }
          }

          let shareLinkParsed = shareUrl && new UrlParser(shareUrl)
          let shareLink = shareUrl
          let appendKeys: any = {
            shareFrom: focusCore.env.isInApp ? 'apph5' : 'wxh5',
          }
          if (userSid && shareMgmAid) {
            appendKeys = {
              ...appendKeys,
              mgmAid: shareMgmAid,
              sid: userSid,
            }
          }

          // 同时分享新MGMID的share
          if (mgmAidV2) {
            try {
              const mgmV2 = new MgmServiceV2()
              const { inviteCode } = await mgmV2.getInivteCode(mgmAidV2)
              if (inviteCode) {
                appendKeys.inviteCode = inviteCode
              }
            } catch {}
          } else {
            appendKeys = {
              ...appendKeys,
            }
          }
          shareLink = shareLinkParsed && shareLinkParsed.appendQuery(appendKeys).fullPath

          const data = {
            shareTitle,
            shareDesc,
            shareImg,
            shareUrl: shareLink,
            appShareType,
            shareImageBg,
            shareImageHeight,
          }

          resolve(data)
        })
      }
    })
  }

  getUserSid(mgmAid: string): Promise<{ userSid: string; nickName: string; avator: string }> {
    return new Promise((resolve) => {
      if (this.userSid && this.mgmAidForSid === mgmAid) {
        return resolve({ userSid: this.userSid, ...userService.wxUserInfo })
      }
      this.mgmAidForSid = mgmAid
      userService.getWxUserInfo().then((res: any) => {
        const { nickName, avator } = res

        // 在App中可以尝试从  URL 拿，从大MGM页面的跳转都会带
        if (focusCore.env.isInApp) {
          const { bigMGMSid } = new UrlParser().query
          if (bigMGMSid) {
            this.userSid = bigMGMSid
            return resolve({
              userSid: bigMGMSid,
              nickName,
              avator,
            })
          }
        }

        // 重新请求吧~

        focusCore
          .request(cgis.genSid, {
            activity_id: Number(mgmAid),
            nick_name: utf16toEntities(nickName),
            head_img_url: avator,
          })
          .then((res: any) => {
            const { sid } = res
            this.userSid = sid || ''
            resolve({ userSid: this.userSid, nickName, avator })
          })
          .catch(() => {
            resolve({
              userSid: '',
              nickName,
              avator,
            })
          })
      })
    })
  }

  // 确认当前用户SID是否是url上的SID
  checkCurSidIsSameUrlSid(mgmAid: string, sid: string): Promise<boolean> {
    return new Promise((resolve) => {
      this.getUserSid(mgmAid).then((res: any) => {
        console.log('🚀 ~ file: mgmService.ts ~ line 279 ~ MgmService ~ this.getUserSid ~ res', res)

        const { userSid } = res
        resolve(userSid === sid)
      })
    })
  }

  M2AcceptMGM(params: TAcceptShareParam) {
    const { inviteCode } = new UrlParser().query
    console.log('🚀 ~ file: mgmService.ts:512 ~ MgmService ~ M2AcceptMGM ~ inviteCode:', inviteCode)
    const { m1Sid, mgmAid, function_type = '' } = params

    if (inviteCode) {
      try {
        const mgmV2 = new MgmServiceV2()
        setTimeout(() => {
          mgmV2.acceptInvite()
        }, 10)
      } catch {}
    }
    if (!m1Sid) {
      return Promise.resolve({
        err: 'no_m1Sid',
      })
    }
    return new Promise((resolve) => {
      this.getUserSid(mgmAid).then((res: any) => {
        const { userSid, nickName, avator } = res
        // 用户的sid和m1Sid一样，不能建立！
        if (userSid === m1Sid) {
          return resolve({ err: 'isM1' })
        }
        const queryParmas: any = {
          sid: m1Sid,
          activity_id: mgmAid,
          nick_name: utf16toEntities(nickName),
          head_img_url: avator,
        }
        if (function_type) {
          queryParmas.function_type = function_type
        }
        focusCore
          .request(cgis.acceptShare, queryParmas)
          .then((res: any) => {
            resolve(res)
          })
          .catch((err: any) => {
            resolve({ err })
            // console.log('🚀 ~ file: focusService.js ~ line 221 ~ FocusService ~ ajax ~ err', err)
            // const { modalStore } = focusStore.stores
            // modalStore.errorMaskContrl(`acceptShare_${mgmAid}`, ['建立邀请关系失败'], err.ret_msg)
          })
      })
    })
  }

  checkUserIsM2(activity_id: number, function_type?: TAcceptShareFunctionType): Promise<boolean> {
    return new Promise((resolve, reject) => {
      focusCore
        .request(cgis.checkM1IsM2, { activity_id, function_type: function_type || '' })
        .then((res: any) => {
          console.log('🚀 ~ file: mgmService.ts ~ line 272 ~ MgmService ~ focusCore.request ~ res', res)
          resolve(res.status || false)
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: mgmService.ts ~ line 274 ~ MgmService ~ focusCore.request ~ err', err)
          reject(false)
        })
    })
  }

  checkUserIsUrlM1() {
    return new Promise((resolve) => {})
  }

  /**
   * 建立家庭的邀请关系，会同时建立1939
   * @param params
   * @returns
   */

  acceptFamilyShare(params: TAcceptShareParam) {
    return new Promise((resolve, reject) => {
      const { m1Sid } = params
      // this.M2AcceptMGM({ m1Sid, mgmAid: '1939' })
      this.M2AcceptMGM({ ...params, function_type: TAcceptShareFunctionType.Family }).then((res: any) => {
        console.log('🚀 ~ file: mgmService.ts ~ line 308 ~ MgmService ~ .then ~ res', res)
        const { invite_status_code = '', ret_code } = res
        if (res.err || /2204$/.test(ret_code)) {
          return reject('不符合邀请规则，接受邀请失败')
        }
        if (invite_status_code && invite_status_code !== '0000') {
          console.log('接受家庭邀请失败')
          let errorMsg = '不符合邀请规则，接受邀请失败'
          switch (invite_status_code) {
            case '1001': // 被邀请人不合符年龄要求
              errorMsg = '被邀请人不符合年龄要求'
              break
            case '1002': // 被邀请人已经组建家庭圈
              errorMsg = '您已经加入了一个家庭，无法再加入其他家庭'
              break
            case '1003': // 邀请人组建的家庭圈已满
              errorMsg = '很抱歉，邀请人已与其他人组建成功'
              break
            case '1000': // m2已经是m1,不能再作为m2
            case '1004': // 邀请人家庭圈中已经有同性别的成员
            case '1005': // 登录状态下的m1的ecif与建立关系中ecif不是同一个
            case '1006': // 我的家庭组建失败
            case '1100': // 家庭链预审方法出现异常
            default:
              break
          }
          return reject(errorMsg)
        }
        resolve(true)
      })
    })
  }

  /**
   * 检查用户在MGM中是M1还是M2,同时返回当前用户名和名称
   */
  checkUserMgmStatus(params: {
    mgmAid: string
    m1Sid: string
  }): Promise<{ isM1?: boolean; isM2?: boolean; userSid: string; nickName: string; avator: string }> {
    const { mgmAid, m1Sid } = params
    return new Promise((resolve) => {
      this.getUserSid(mgmAid.toString()).then((res) => {
        const { userSid, nickName, avator } = res

        // 用户的sid和m1Sid一样，不能建立！
        if (userSid === m1Sid) {
          return resolve({ isM1: true, userSid, nickName, avator })
        }
        return resolve({
          isM2: true,
          userSid,
          nickName,
          avator,
        })
      })
    })
  }

  getRelationshipList(
    activityId: number,
    function_type?: TAcceptShareFunctionType,
  ): Promise<{
    remaining_invitation_count: number // m1邀请m2剩余次数
    not_register: TRelationShipItem[] // 未开户列表
    register: TRelationShipItem[] // 已开户列表
    enterprise: TRelationShipItem[] // 已完成列表
    notRegisterCount: number //未开户人数
    registerCount: number // 已开户人数
    enterpriseCount: number //已完成人数
  }> {
    const queryParams: {
      activityId: number
      function_type?: TAcceptShareFunctionType
    } = {
      activityId,
    }
    if (function_type) {
      queryParams.function_type = function_type
    }
    return new Promise((resolve) => {
      focusCore
        .request(cgis.relationshipList, queryParams)
        .then((res: any) => {
          console.log('🚀 ~ mgm邀请列表', res)
          const {
            not_register = [],
            register = [],
            enterprise = [],
            remaining_invitation_count = 0,
            notRegisterCount = 0,
            registerCount = 0,
            enterpriseCount = 0,
          } = res
          const setListData = (i: {
            account_dto: { sid: string; nick_name: string; head_img_url: string; gender: string; user_name: string }
            time: string
            id: number
          }): TRelationShipItem => ({
            id: i.id.toString(),
            time: i.time,
            ...i.account_dto,
            nick_name: entitiestoUtf16(i.account_dto.nick_name),
            head_img_url: i.account_dto.head_img_url.replace('http:', 'https:'),
          })

          return resolve({
            notRegisterCount,
            registerCount,
            enterpriseCount,
            remaining_invitation_count,
            not_register: not_register.map((i: any) => setListData(i)), // 未开户
            register: register.map((i: any) => setListData(i)), // 已开户
            enterprise: enterprise.map((i: any) => setListData(i)), // 已完成
          })
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: mgmService.ts ~ line 307 ~ MgmService ~ focusCore.request ~ err', err)
          resolve({
            notRegisterCount: 0,
            registerCount: 0,
            enterpriseCount: 0,
            remaining_invitation_count: 0,
            not_register: [],
            register: [],
            enterprise: [],
          })
        })
    })
  }

  getShareUserinfo() {
    const parsedLink = new UrlParser(window.location.href)

    const { sid, mgmAid, inviteCode } = parsedLink.query

    const defaultData = {
      m1NickName: '微众银行用户',
      m1Avator: '',
    }

    let v2Data:
      | {
          m1NickName: string
          m1Avator: string
        }
      | any = {}
    if (inviteCode) {
      console.log('🚀 ~ file: mgmService.ts:715 ~ MgmService ~ getShareUserinfo ~ invieteCode:', inviteCode)
      try {
        const mgmV2 = new MgmServiceV2()
        v2Data = mgmV2.getM1Info()
      } catch {}
    }
    if (!sid || !mgmAid) {
      return Promise.resolve(defaultData)
    }
    if (v2Data.m1NickName && v2Data.m1Avator) {
      return Promise.resolve(v2Data)
    }
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getShareUserinfo, { sid, activity_id: mgmAid })
        .then((res: any) => {
          console.log('🚀 ~ file: focusService.js ~ line 55 ~ FocusService ~ ajax ~ res', res)
          const { nick_name = '', head_img_url = '' } = res || {}
          resolve({ m1NickName: entitiestoUtf16(nick_name), m1Avator: head_img_url.replace('http:', 'https:') })
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: mgmService.ts ~ line 398 ~ MgmService ~ focusCore.request ~ err', err)
          resolve(defaultData)
        })
    })
  }
  /**
   * 检查M2是不是帮助过m1！
   * @param params
   * @returns
   */
  checkM2HelpedM1(params: { mgmAid: number | string; m1Sid: string }): Promise<-1 | 0 | 1> {
    const { mgmAid = '', m1Sid = '' } = params
    return new Promise((resolve) => {
      this.checkUserMgmStatus({ mgmAid: mgmAid.toString(), m1Sid }).then((res) => {
        const { isM2, isM1, userSid, nickName, avator } = res
        if (isM1) {
          return resolve(-1)
        }
        if (isM2) {
          focusCore
            .request(cgis.checkM2HelpedM1, { activityId: mgmAid, m1Sid, m2Sid: userSid })
            .then((res: any) => {
              console.log('🚀 ~ file: mgmService.ts ~ line 687 ~ MgmService ~ .then ~ res', res)

              let data = { sids: [] }
              try {
                data = JSON.parse(res) || {}
              } catch (err) {}
              console.log(data)
              const { sids = [] } = data
              return resolve(sids.length ? 1 : 0)
            })
            .catch((err: any) => {
              console.log('🚀 ~ file: mgmService.ts ~ line 606 ~ MgmService ~ focusCore.request ~ err', err)
              resolve(0)
            })
        }
      })
    })
  }

  setM2helperM1(params: { mgmAid: number | string; m1Sid: string }): Promise<boolean> {
    const { mgmAid = '', m1Sid = '' } = params
    return new Promise((resolve) => {
      this.getUserSid(mgmAid.toString()).then((res: any) => {
        const { userSid, nickName, avator } = res

        focusCore
          .request(cgis.M2HelpM1, {
            activityId: Number(mgmAid),
            m1Sid,
            m2Sid: userSid,
            m2NickName: utf16toEntities(nickName),
            m2HeadImgUrl: avator,
          })
          .then((res: any) => {
            console.log('🚀 ~ file: mgmService.ts ~ line 607 ~ MgmService ~ this.getUserSid ~ res', res)
            resolve(true)
          })
          .catch((err: any) => {
            console.log('🚀 ~ file: mgmService.ts ~ line 610 ~ MgmService ~ this.getUserSid ~ err', err)
            resolve(false)
          })
      })
    })
  }

  queryM1HelpersList(mgmAid: number | string, setStart: number, offList: number) {
    return new Promise((resolve) => {
      this.getUserSid(mgmAid.toString()).then((res: any) => {
        const { userSid, nickName, avator } = res
        focusCore
          .request(cgis.M1HelpersList, {
            activityId: Number(mgmAid),
            m1Sid: userSid,
            setStart: setStart || 0,
            offList: offList || 100,
          })
          .then((res: string = '{}') => {
            console.log('🚀 ~ file: mgmService.ts ~ line 687 ~ MgmService ~ .then ~ res', res)

            let data = { sids: [] }
            try {
              data = JSON.parse(res) || {}
            } catch (err) {}
            console.log(data)
            const { sids = [] } = data
            resolve(
              sids.map((i: any) => {
                return {
                  ...i,
                  time: dayjs(Number(i.updateTime.toString() + '000')).format('YYYY-MM-DD hh:mm'),
                  m2NickName: entitiestoUtf16(i.m2NickName),
                  m2HeadImgUrl: i.m2HeadImgUrl.replace('http://', 'https://'),
                }
              }),
            )
          })
          .catch(() => {
            resolve([])
          })
      })
    })
  }

  /**
   * 获取we2000的shareKey
   * @param mgmAid
   * @param sid
   * @returns
   */
  getW2kShareKey(mgmAid: string | number, sid: string): Promise<string> {
    return new Promise((resolve) => {
      focusCore
        .request(
          cgis.w2kShareKey,
          {},
          {
            activity_id: mgmAid,
            sid,
          },
        )
        .then((res: { status: boolean; share_key: string }) => {
          return resolve(res.share_key || '')
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: mgmService.ts:767 ~ MgmService ~ returnnewPromise ~ err:', err)
          return resolve('')
        })
    })
  }
}

class MgmServiceV2 {
  canUseV2() {
    return new Promise((resolve) => {
      return
    })
  }

  getM1Info(m1InviteCode?: string): Promise<{
    m1NickName: string
    m1Avator: string
  }> {
    const { query } = new UrlParser()
    const { inviteCode, inviteAid } = query
    const _inviteCode = m1InviteCode || inviteCode || ''

    return new Promise((resolve) => {
      focusCore
        .request(cgisV2.queryInviter, {
          inviteCode: _inviteCode,
        })
        .then(
          (res: {
            activityStatus: boolean
            inviterNickName: string
            inviterHeadImgUrl: string
            ret_code: string
            ret_msg: string
          }) => {
            console.log('🚀 ~ file: mgmService.ts:876 ~ MgmServiceV2 ~ .then ~ res:', res)
            if (/0000$/.test(res.ret_code)) {
              const { inviterNickName, inviterHeadImgUrl } = res || {}
              return resolve({
                m1NickName: entitiestoUtf16(inviterNickName),
                m1Avator: inviterHeadImgUrl && inviterHeadImgUrl.replace('http://', 'https://'),
              })
            }
            return resolve({
              m1NickName: '',
              m1Avator: '',
            })
          },
        )
        .catch((err: any) => {
          return resolve({
            m1NickName: '',
            m1Avator: '',
          })
        })
    })
  }

  getInviteList(
    aids: string[],
    m2Status: string,
    tabType: string,
    offset: number,
    limit: number,
  ): Promise<{
    offset: number
    list: {
      avator: string
      nickName: string
      time: string
      statusText: string
      isCustomEvent: boolean
    }[]
    total: number
  }> {
    console.log('🚀 ~ file: mgmService.ts:925 ~ MgmServiceV2 ~ getInviteList ~ aids:', aids)
    return new Promise((resolve) => {
      focusCore
        .request(cgisV2.queryInviteList, {
          activityIds: aids.join(','),
          m2Status,
          tabType,
          offset,
          limit,
        })
        .then(
          (res: {
            ret_code: string
            offset: number
            total: number
            inviteList: {
              inviteeAccount: {
                gender: string // '1' '2'
                headImgUrl: string
                nickName: string
                userName: string
              }
              showTime: number
              statusTip: string
              userAttrs: string[]
            }[]
          }) => {
            console.log('🚀 ~ file: mgmService.ts:933 ~ MgmServiceV2 ~ returnnewPromise ~ res:', res)

            const { inviteList = [], ret_code = '', offset = 0, total } = res
            let _list = inviteList

            // if (_list.length) {
            //   for (let i = 0; i < 100; i++) {
            //     _list.push(inviteList[0])
            //   }
            // }
            if (/0000$/.test(ret_code)) {
              return resolve({
                list: _list.map((i) => {
                  console.log('🚀 ~ file: mgmService.ts:1058 ~ MgmServiceV2 ~ list:_list.map ~ i:', i)

                  let isCustomEvent = false
                  const { inviteeAccount, showTime, statusTip, userAttrs = [] } = i
                  const { gender, headImgUrl, nickName, userName } = inviteeAccount
                  let statusText = ''

                  // 列-未开户
                  if (tabType === '0000') {
                    statusText = '已邀请'
                  }
                  // 列-已开户
                  if (tabType === '0001') {
                    statusText = '已邀请开户'

                    if (statusTip === '1') {
                      statusText = '他人邀请开户'
                    }
                    if (statusTip === '-1') {
                      statusText = '历史已开户'
                    }

                    // 不区分新老户
                    if (m2Status === '0') {
                    }
                    // 新户
                    if (m2Status === '1') {
                    }
                    // 老户
                    if (m2Status === '2') {
                    }
                  }
                  // 列-已认证
                  if (tabType === '0002') {
                    statusText = '已邀请'
                    isCustomEvent = true
                    // 不区分新老户
                    if (m2Status === '0') {
                    }
                    // 新户
                    if (m2Status === '1') {
                      if (statusTip === '1') {
                        statusText = '他人邀请'
                      }
                    }
                    // 老户
                    if (m2Status === '2') {
                      if (statusTip === '1') {
                        statusText = '他人邀请'
                      }
                      if (statusTip === '-1') {
                        statusText = '历史已'
                      }
                    }
                  }
                  if (tabType === '9999') {
                    statusText = '已完成'
                  }

                  return {
                    avator: headImgUrl ? headImgUrl.replace('http:', 'https:') : defaultM1Avatar,
                    nickName: nickName ? entitiestoUtf16(nickName) : '微众用户',
                    userAttrs,
                    time: showTime ? dayjs(showTime).format('YYYY-MM-DD') : '',
                    statusText,
                    isCustomEvent,
                  }
                }),
                offset,
                total,
              })
            }
            return resolve({ list: [], offset: 0, total: 0 })
          },
        )
        .catch((err: any) => {
          console.log('🚀 ~ file: mgmService.ts:935 ~ MgmServiceV2 ~ returnnewPromise ~ err:', err)
          return resolve({ list: [], offset: 0, total: 0 })
        })
    })
  }

  getInivteCode(activityId: string | number): Promise<{ inviteCode: string; activityStatus: boolean }> {
    return new Promise((resolve) => {
      userService.getUserInfoForUpload().then(({ nickNameEncode, avator }) => {
        focusCore
          .request(cgisV2.generateInviteCode, {}, { activityId, nickName: nickNameEncode, headImgUrl: avator })
          .then((res: { inviteCode: string; activityStatus: boolean; ret_code: string; ret_msg: string }) => {
            const { inviteCode = '', activityStatus } = res
            resolve({
              inviteCode,
              activityStatus,
            })
          })
          .catch((err: any) => {
            console.log('🚀 ~ file: mgmService.ts:854 ~ MgmServiceV2 ~ focusCore.request ~ err:', err)
            return resolve({
              inviteCode: '',
              activityStatus: false,
            })
          })
      })
    })
  }

  acceptInvite(m1InviteCode?: string): Promise<{
    status: boolean
  }> {
    const { query } = new UrlParser()
    const { inviteCode, inviteAid } = query
    const _m1InviteCode = m1InviteCode || inviteCode || ''
    if (!_m1InviteCode) {
      return Promise.resolve({
        status: false,
      })
    }

    return new Promise((resolve) => {
      userService.getUserInfoForUpload().then(({ nickNameEncode, avator }) => {
        focusCore
          .request(
            cgisV2.acceptShare,
            {},
            {
              inviteCode: _m1InviteCode,
              nickName: nickNameEncode,
              headImgUrl: avator,
            },
          )
          .then(
            (res: {
              ret_data: {
                activityStatu: boolean
                inviteStatus: boolean
                inviteeSid: string
                inviterSid: string
                inviterNickName: string
                inviterHeadImgUrl: string
              }
              ret_code: string
            }) => {
              console.log('🚀 ~ file: mgmService.ts:916 ~ MgmServiceV2 ~ returnnewPromise ~ res:', res)

              if (/0000$/.test(res.ret_code)) {
                return resolve({
                  status: res.ret_data && res.ret_data.inviteStatus,
                })
              }
              return resolve({
                status: false,
              })
            },
          )
          .catch((err: any) => {
            console.log('🚀 ~ file: mgmService.ts:883 ~ MgmServiceV2 ~ ).then ~ err:', err)
            return resolve({
              status: false,
            })
          })
      })
    })
  }

  getMgmMembers(mgmAid: string | number): Promise<{
    inviteTotal: number | string
  }> {
    return new Promise((resolve) => {
      focusCore
        .request(cgisV2.mgmMembers, { activityId: mgmAid })
        .then((res: { inviteTotal: number }) => {
          console.log('🚀 ~ MgmServiceV2 ~ .then ~ res:', res)
          resolve({ inviteTotal: res.inviteTotal || 0 })
        })
        .catch((err: any) => {
          console.log('🚀 ~ MgmServiceV2 ~ returnnewPromise ~ err:', err)
          resolve({ inviteTotal: '--' })
        })
    })
  }

  submitShareCode(shareCode: string): Promise<{
    status: boolean
    statusMsg?: string
  }> {
    return new Promise((resolve) => {
      userService.getUserInfoForUpload().then(({ nickNameEncode, avator }) => {
        focusCore
          .request(
            cgisV2.acceptShareByInputShareCode,
            {},
            {
              inviteCode: shareCode,

              nickName: nickNameEncode,
              headImgUrl: avator,
            },
          )
          .then((res) => {
            console.log('🚀 ~ MgmServiceV2 ~ .then ~ res:', res)
            const { ret_code } = res
            console.log('🚀 ~ MgmServiceV2 ~ .then ~ ret_code:', ret_code)
            const errMaps = {
              69372500: '提交成功',
              69372501: '超过单日次数上限，提交失败', //'单日单个M2提交超过10次',
              69372502: '邀请码不正确，请核对后重新提交', //'无法识别M2填写文字邀请码（M2填写文字邀请码不存在）',
              69372503: '邀请码无效，提交失败', //'邀请码无效，提交失败', //'M2填写的文字邀请码对应的M1是M2本人',
              69372504: '您已接受其他邀请，请勿重复提交', //'M2已存在开户事件已归属的关系',
              69372505: '邀请码无效，提交失败', //'MGMID已失效',
              69372506: '邀请码无效，提交失败', //'文字邀请码对应的MGMID未勾选开户事件',
              69372507: '邀请码无效，提交失败', //'M2在填写的文字邀请码对应的活动下作为M1已建立过邀请关系',
              69372508: '邀请码已失效，提交失败', //'M1已不在活动白名单',
              69372509: '您的开户时间不符合要求，提交失败', //'M2已超过限制开户天数',
              693725010: '提交失败，请重试',
            }
            const errorMsg = errMaps[ret_code] || '提交失败，请重试'
            return resolve({
              status: ret_code === '69372500',
              statusMsg: errorMsg,
            })
          })
          .catch((err) => {
            console.log('🚀 ~ MgmServiceV2 ~ ).then ~ err:', err)
            let errorMsg = '提交失败，请重试'

            return resolve({
              status: false,
              statusMsg: errorMsg,
            })
          })
      })
    })
  }
}

const mgmV2 = new MgmServiceV2()
export { mgmV2 }

export default new MgmService()
