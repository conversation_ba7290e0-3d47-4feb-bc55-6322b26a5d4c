import { focusCore, focusStore } from '../packages/focus-render/index'

const cgis = {
  getTaskProgressAndConfig: {
    needAccount: true,
    noAccountCatch: {},
    name: '获取抽奖聚合页配置和任务进度',
    url: '/wm-htrserver/cop/hj/query_task_complete_progress',
  },
  getRewardByMultiTaskDone: {
    url: '/wm-htrserver/cop/hj/get_activity_week_draw_more_task_award',
    name: '领取多任务完成的奖励',
  },
  setViewPageRecord: {
    url: '/wm-htrserver/cop/event/browsing/task_record',
    name: '浏览页面的任务',
  },
}
import dayjs from '../utils/time-utils'

export enum EnumTaskMoneyType {
  XFZFLJ = '4010', // 消费支付累计 n 元
  SHRJ_XH = '4012', // 顺汇入金循环累计
  TOP7_MoreThen_Base = '4013', // 活动期间，最高7天日均资金 比 基准时间段的日均 新增 n 元
  // '4007', '4008', '4013', '4012'
}

class TaskService {
  getTaskProgressByConfigId(configId: any) {
    const { modalStore } = focusStore.stores
    const errorText = {
      passTime: ['活动已结束，下次再来参与吧~'],
    }

    return new Promise((resolve) => {
      focusCore
        .request(cgis.getTaskProgressAndConfig, { configId })
        .then(
          async (res: {
            taskProgressList?: never[] | undefined
            activityWeekDraw?: any
            noAccount?: Boolean
            configSummaryCount?: number
            starsFlag?: number // 是否合成星球
            taskCoins: number // 任务获得的代币总数
            taskFinishDetail: any
            taskProgressSwitch: number // 切换新任务排序逻辑
          }) => {
            console.log('🚀 ~ file: taskListService.ts ~ line 24 ~ TaskService ~ .then ~ res', res)
            const {
              taskProgressList = [],
              activityWeekDraw = { taskConfig: '{}', awardLink: '' },
              noAccount = false,
              configSummaryCount = 0,
              starsFlag = 0,
              taskCoins = 0, //
              taskFinishDetail = {},
              taskProgressSwitch = 0,
            } = res
            if (noAccount) {
              return resolve(null)
            }
            if (!res.activityWeekDraw) {
              return modalStore.errorMaskContrl('no_activityWeekDraw', errorText.passTime)
            }
            const {
              taskConfig,
              awardLink,
              // ltAumActivityId,
              // gtAumActivityId,
              mgmActivityId,
              inviteShareAbstract,
              inviteShareLink,
              inviteShareTitle,
              inviteShareUrl,
              startTime,
              endTime,
              drawStatus,
            } = activityWeekDraw || {}
            // 检查活动时间
            let canShowPage = false
            try {
              canShowPage = await focusCore.showPageInTimes(drawStatus, startTime, endTime)
            } catch (err) {
              console.log('不能显示！')
              return modalStore.errorMaskContrl('tasklist-timeout', errorText.passTime)
            }
            console.log('🚀 ~ 查看是否在活动时间：', canShowPage)
            if (!canShowPage) {
              console.log('不能显示！')
              return modalStore.errorMaskContrl('tasklist-timeout', errorText.passTime)
            }
            const { endTime: taskListEndTime, taskConfigTitle } = JSON.parse(taskConfig || {})

            let resultTaskList = taskProgressList.map((item: any) => {
              // console.log('🚀 ~ file: taskListService.ts:93 ~ TaskService ~ resultTaskList ~ item:', item)
              const {
                taskId,
                confTransAmt,
                confTransNum,
                currTransAmt, // 当前累计转账值，用于计算【每转入N元完成一次任务】
                currTransNum, // 当前累计更新的数值，用于计算【每n次可完成一次任务】eg: 登录天数(每n天登录完成一次)、邀请人数（每邀请n人完成一次）、
                buttonText, // 按钮文案
                finishFlag,
                lotteryFinishStatus, // 完成状态 1 未完成；2 已结束； 3已完成
                finishType,
                taskAction,
                taskName, // 主标题
                subTaskName, // 抽奖机会文案
                taskLotteryImgUrl, // 提示弹窗图片
                taskLotteryDesc, // 副标题
                lotteryChanceDesc, // 奖励发放文案
                taskPerfectibilityTime,
                taskSummaryCount, // 已完成这个任务的用户数
                taskImgUrl,
                shareExtra,
                latestFinishTime,
                weight, // 任务排序权重
                taskCoinNum, //当次任务获得代币数量
                taskCoinAmount, //当次任务每X元获得代币
                maxCoinSum, //最大可获得代币数量
                currTransCoin, //当前累计代币数量
                coinUrl, //代币url
                condExtra = '{}',

                lotteryTaskShowStatus = '', //任务状态-新
                lotteryTaskValidShowEndTime = 0, //任务有效期-开始时间
                lotteryTaskValidShowStartTime, //任务有效期-结束时间
              } = item

              // finishType：登录 4001、邀请好友 4003、购买指定产品4007、消费支付4010、活期+ 4017、活期+PLUS 4018 显示进度条
              const showProgress = ['4001', '4003', '4007', '4010', '4017', '4018'].indexOf(finishType) > -1
              // 金钱类任务
              const isMoneyTask =
                [
                  '4007',
                  '4008',
                  '4013',
                  '4012',
                  '4018',
                  '4017',
                  EnumTaskMoneyType.SHRJ_XH,
                  EnumTaskMoneyType.TOP7_MoreThen_Base,
                  EnumTaskMoneyType.XFZFLJ,
                ].indexOf(finishType) > -1

              let maxProgress: number | string = 0
              let curProgress: number = 0
              let progressPercent: string = ''
              const isShare: Boolean = finishType === '4003'
              let mgmAid: number = isShare ? Number(item.mgmAid || 0) : 0
              if (showProgress) {
                maxProgress = confTransAmt ? confTransAmt : confTransNum
                curProgress = finishFlag ? maxProgress : confTransAmt ? currTransAmt : currTransNum
                if (curProgress !== undefined) {
                  progressPercent = finishFlag
                    ? '100%'
                    : parseInt(((curProgress / Number(maxProgress)) * 10000).toString()) / 100 + '%'
                }
              }
              if (finishType === '4003') {
                mgmAid = Number(item.mgmAid)
              }
              let btnText = lotteryFinishStatus === '2' ? '已结束' : lotteryFinishStatus === '3' ? '已完成' : buttonText
              // 不可点！
              let btnDisabled = false

              if (finishType === '4001' && latestFinishTime) {
                btnDisabled = dayjs(latestFinishTime).isToday()
                btnText = lotteryFinishStatus === '3' ? '已完成' : btnDisabled ? '已登录' : btnText
              }

              let isDone = lotteryFinishStatus !== '1'
              let showTimeText = ''
              let prefixTime = (taskPerfectibilityTime && taskPerfectibilityTime + '000') || 0

              // 启用新的任务展示逻辑
              if (taskProgressSwitch === 1 && lotteryTaskShowStatus) {
                prefixTime = ''
                const statusText = {
                  '1': buttonText || '去完成',
                  '2': '未开始',
                  '3': '已完成',
                  '4': '已过期',
                }
                const statusValue = ['1', '2', '3', '4']
                if (lotteryTaskShowStatus === '1') {
                  btnDisabled = false
                } else {
                  btnDisabled = true
                }
                if (lotteryFinishStatus === '3') {
                  isDone = true
                } else {
                  isDone = false
                }

                btnText = statusValue.indexOf(lotteryTaskShowStatus) > -1 ? statusText[lotteryTaskShowStatus] : ''

                if (lotteryTaskValidShowStartTime && lotteryTaskValidShowEndTime) {
                  const _startTime = dayjs(Number(lotteryTaskValidShowStartTime + '000'))

                  const _endTime = dayjs(Number(lotteryTaskValidShowEndTime + '000'))

                  const formatValid = _startTime.isSame(_endTime, 'year') ? 'MM月DD日HH:mm' : 'YYYY年MM月DD日HH:mm'

                  showTimeText = `${_startTime.format(formatValid)}-${_endTime.format(formatValid)}`
                }
              }

              let shareData = {}
              // 分享任务
              if (isShare) {
                try {
                  shareData = JSON.parse(shareExtra)
                } catch (err) {
                  shareData = {}
                }
              }

              let jumpParams: any = {}

              // 浏览任务
              if (finishType === '4015') {
                let extraData: {
                  browseTime?: number
                  browsingId?: string
                } = {}
                try {
                  extraData = JSON.parse(condExtra) || {}
                } catch {}
                const { browseTime, browsingId } = extraData
                jumpParams = {
                  taskId,
                  browsingId,
                  browsingTime: browseTime,
                  viewPageTask: 1,
                  useJumpToProduct: true,
                }
              }

              // 产品购买类型增加单位：元
              if (finishType === '4007') {
                maxProgress = maxProgress + '元'
              }

              return {
                ...item,
                taskId,
                lotteryTimesChange: subTaskName,
                title: taskName,
                taskLotteryDesc, // 副标题
                lotteryChanceDesc, // 奖励发放文案
                maxProgress,
                curProgress: curProgress === undefined ? '--' : curProgress.toString(),
                progressPercent,
                btnText,
                jumpPath: taskAction,
                jumpParams,
                isShare,
                mgmAid,
                isDone,
                reportid: `btn-task_${taskId}_${finishType}_${finishFlag ? 1 : 0}`,
                taskPerfectibilityTime: prefixTime,
                taskSummaryCount,
                taskImgUrl,
                shareData,
                btnDisabled,
                isMoneyTask,
                showTimeText,
              }
            })
            resolve({
              taskList: resultTaskList,
              sortTaskList: resultTaskList,
              expireTime: dayjs(taskListEndTime).format('MM月DD日 HH:mm'),
              taskListVer: taskConfigTitle,
              mgmAid: mgmActivityId,
              myRewardLink: awardLink,
              configSummaryCount,
              starsFlag,
              taskCoins,
              taskFinishDetail: {
                ...taskFinishDetail,
                btnText:
                  taskFinishDetail && taskFinishDetail.moreTaskFinishStatus === 0
                    ? '待完成'
                    : taskFinishDetail.moreTaskFinishStatus === 1
                    ? '领奖励'
                    : '已领取',
              },
            })
          },
        )
        .catch((err: any) => {
          console.log('🚀 ~ file: taskListService2.ts ~ line 138 ~ TaskService ~ returnnewPromise ~ err', err)
          modalStore.errorMaskContrl(`getTaskProgressByConfigId_${configId}`, err.ret_code)
          resolve(null)
        })
    })
  }

  taskViewPage(
    status: string,
    browsingId: string,
    browsingTime: number,
    taskId: number,
    browsingKey?: string,
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      focusCore
        .request(cgis.setViewPageRecord, {
          operate_type: status,
          browsingTime,
          taskId,
          browsingId,
          browsingKey: browsingKey || '',
        })
        .then((res: any) => {
          console.log('🚀 ~ file: taskListService.ts:243 ~ TaskService ~ returnnewPromise ~ res:', res)
          if (res.result_code !== '0000') {
            return reject(false)
          }
          if (res.browsingKey) {
            let browsingKey = res.browsingKey
            return resolve(browsingKey)
          }
        })
        .catch(() => {
          resolve('')
        })
    })
  }

  getRewardByMultiTaskDone(
    configId: string,
    periodNo: number,
  ): Promise<{
    getAwardStatus: 0 | 1 | 2 | null // 0 领取失败；1 领取成功；2 已领取过；null 其他异常
    awardPopupUrl: string // 奖励弹窗图片
  }> {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getRewardByMultiTaskDone, { configId, periodNo })
        .then(
          (res: {
            getAwardStatus: 0 | 1 | 2 | null // 0 领取失败；1 领取成功；2 已领取过；null 其他异常
            awardPopupUrl: string // 奖励弹窗图片
          }) => {
            console.log('🚀 ~ file: taskListService.ts ~ line 238 ~ TaskService ~ focusCore.request ~ res', res)
            const { getAwardStatus = 0, awardPopupUrl = '' } = res
            resolve({
              getAwardStatus,
              awardPopupUrl,
            })
          },
        )
        .catch((err: any) => {
          resolve({
            getAwardStatus: 0,
            awardPopupUrl: '',
          })
        })
    })
  }
}

export default new TaskService()
