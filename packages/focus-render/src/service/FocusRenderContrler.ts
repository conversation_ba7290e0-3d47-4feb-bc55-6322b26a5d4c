import { focusStore, focusCore, focusServices } from '../packages/focus-render/index'
import { dayjs, UrlParser } from '@/utils'
import { ref, Ref } from 'vue'
import { TFocusData, focusService } from './focusService'

export interface TFocusDataResolve {
  _pageVer: string
  shareConfig: ShareConfig
  allModulesData: ModuleData[]
  apiList: any[]
  popupConfig: any[]
  hideModuleIds: number[]
  renderModules: (data: any[]) => void
  renderPopupModules: (data: any[]) => void
  systemTime: dayjs.Dayjs
  _conditionList: ConditionList
  moduleTree: any[]
  changedModuleDataRef: Ref<any>
  moduleChangedList: string[]
  serviceConfig: any
  grayList: GrayListItem[]
  pageTitleDatas: PageTitleDatas
  fid: number
  pageVer: string
  popupModuleTree: any[]
  conditionList: ConditionList
  pageConfig: PageConfig
  isUseCommonGray: boolean
  resolveModuleTree: () => any[]
  initExtendService: (param: { popupCallback?: Function }) => void
}

export interface ShareConfig {
  base?: any
  configList: ShareConfigItem[]
}

export interface ShareConfigItem {
  desc: string
  imgUrl: string
  mgmAid: string
  path: string
  title: string
  appShareType: '0' | '1' | '2' | '3'
  miniUserName?: 'H5' | 'wxcb823d713276a10d-release' | 'wxcb823d713276a10d-preview' | 'wxdb1f857b4f658a6f-preview'
  mgmAidV2?: string
  shareImageBg?: string
  shareImageHeight?: string
}

export interface ModuleData {
  moduleId: number
  moduleType: string
  moduleName: string
  pid: number
  moduleSubType?: string
  styles?: ModuleStyles
  data: {} | any
}

export interface ModuleStyles {
  bg?: string
  margin?: string
  time?: string
}

export interface ConditionList {
  conditionTypes?: string[]
  conditionApiKeys?: string[]
}

export interface PageTitleDatas {
  title: string
  customerService: string
}

export interface PageConfig {
  data?: PageConfigData
  styles?: PageConfigStyles
  customerService?: string
}

export interface PageConfigData {
  startTime: string
  endTime: string
  needLogin: string
  showWxGuest: string
  pageTitle: string
  forceLoginInApp: '1'
}

export interface PageConfigStyles {
  bg: string
}

export interface GrayListItem {
  id: string
  useGrayType?: string
}
const { activityService } = focusServices
export default class FocusRenderContrler implements TFocusDataResolve {
  _pageVer: string = ''
  shareConfig: {
    base?: any
    configList: Array<{
      desc: string
      imgUrl: string
      mgmAid: string
      path: string
      title: string
      appShareType: '0' | '1' | '2' | '3'
      miniUserName?: 'H5' | 'wxcb823d713276a10d-release' | 'wxcb823d713276a10d-preview' | 'wxdb1f857b4f658a6f-preview'
      mgmAidV2?: string
      shareImageBg?: string
      shareImageHeight?: string
    }>
  } = {
    base: {},
    configList: [],
  }
  allModulesData: {
    moduleId: number
    moduleType: string
    moduleName: string
    pid: number
    moduleSubType?: string
    styles?: {
      bg?: string
      margin?: string
      time?: string
    }
    data: {} | any
  }[] = []
  apiList: any[] = []
  popupConfig: any[] = []
  hideModuleIds: number[] = []
  renderModules: (data: any[]) => void
  renderPopupModules: (data: any[]) => void
  systemTime: dayjs.Dayjs = dayjs()
  _conditionList: {
    conditionTypes?: string[]
    conditionApiKeys?: string[]
  } = {}
  moduleTree: any[] = []
  changedModuleDataRef: Ref<any> | any = {}
  moduleChangedList: string[] = []
  serviceConfig: any = {}
  grayList: any[] = []
  pageTitleDatas: {
    title: string
    customerService: string
  } = {
    title: '微众银行',
    customerService: '',
  }
  fid: number = 0

  constructor({ focusData, hideModuleIds = [] }: { focusData: TFcousData; hideModuleIds?: number[] }) {
    const {
      apiList = [],
      config = [],
      shareConfig = {},
      grayList = [],
      serviceConfig = {},
      popupConfig = [],
      pageVer = '',
      fid,
    } = focusData
    this.fid = Number(fid) as number
    this._pageVer = pageVer
    this.shareConfig = shareConfig
    this.allModulesData = config
    this.popupConfig = popupConfig
    this.hideModuleIds = []
    this.renderModules = () => {}
    this.renderPopupModules = () => {}
    this.apiList = apiList
    this.serviceConfig = serviceConfig
    this.grayList = grayList
    // this.moduleTree = this.resolveModuleTree()
  }
  // TODO: 废弃250610
  initExtendService(param: { popupCallback?: Function }) {
    if (!param || !Object.keys(param).length) {
      return null
    }
    const { extendInstanceService } = focusServices

    const list = Object.keys(this.serviceConfig) || []
    list.forEach((k) => {
      const val = this.serviceConfig[k]
      switch (k) {
        // 处理营销弹窗
        case 'popupType':
          extendInstanceService.popup.get(val).then((data: { popupData: any }) => {
            param.popupCallback && param.popupCallback(data.popupData)
          })
          break
      }
    })
  }
  get pageVer() {
    return this._pageVer
  }

  get popupModuleTree(): any[] {
    return this.popupConfig.map((item: any) => {
      const data = item.popup || []
      const treeData = this.rebuildTree(data, true)
      return {
        ...item,
        tree: treeData[0] || null,
      }
    })
  }

  get conditionList() {
    console.log('getter...', this._conditionList)
    return this._conditionList
  }

  get pageConfig():
    | {
        data?: {
          startTime: string
          endTime: string
          needLogin: string
          showWxGuest: string
          pageTitle: string
          forceLoginInApp: '1'
        }
        styles?: {
          bg: string
        }
        customerService?: ''
      }
    | any {
    const target = this.allModulesData.find((item) => item.moduleId === 1)
    return (
      target || {
        data: {
          startTime: '',
          endTime: '',
          needLogin: '1',
          showWxGuest: '0',
          pageTitle: '',
          forceLoginInApp: '1',
        },

        styles: {
          bg: '',
        },
        customerService: '',
      }
    )
  }

  /**
   * 是否使用通用灰度
   */
  get isUseCommonGray() {
    const { useGrayType = '0' } = this.grayList.find((i) => i.id === '1') || {}
    console.log('🚀 ~ 使用通用灰度的配置 useGrayType:', useGrayType)
    return useGrayType === '1'
  }

  resolveModuleTree() {
    const moduleQuantityCtl: any = {
      SwiperBox: 6,
    }
    const modules = this.allModulesData.filter((i) => {
      // console.log('🚀 ~ file: focusService.ts ~ line 317 ~ FocusDataResolve ~ data ~ i', i)
      const { moduleId, styles, moduleType, moduleSubType, data } = i

      // 数量限制强控制
      if (typeof moduleQuantityCtl[moduleType] === 'number' && moduleQuantityCtl[moduleType] === 0) {
        return false
      } else if (typeof moduleQuantityCtl[moduleType] === 'number' && moduleQuantityCtl[moduleType] > 0) {
        moduleQuantityCtl[moduleType] = moduleQuantityCtl[moduleType] - 1
      }

      // 在灰度中要隐藏的组件，要隐藏
      if (this.hideModuleIds.find((id) => Number(moduleId) === Number(id))) {
        return false
      }
      // 时间不对，要隐藏
      if (styles && styles.time) {
        const [status, startTime, endTime] = styles.time?.split(',')
        if (status === 'on') {
          const _startTime = Number(startTime)
          const _endTime = Number(endTime)
          console.log('组件', moduleId, this.systemTime.isBetween(dayjs(_startTime), dayjs(_endTime)))
          if (!this.systemTime.isBetween(dayjs(_startTime), dayjs(_endTime))) {
            return false
          }
        }
      }
      if (moduleSubType === 'conditionBox') {
        console.log('conditionBox.....')
        const { conditionType, conditionApiKey, extendStr = '' } = data || {}
        this.updateConditionList(conditionType, conditionApiKey, extendStr)
      }
      return true
    })
    return this.rebuildTree(modules)
  }

  updateConditionList(conditionType: string, conditionApiKey: string, extendStr?: string) {
    if (conditionType) {
      let data = this._conditionList.conditionTypes || []
      let str = conditionType
      if (extendStr) {
        str = `${conditionType}__EXTENDSTR__${extendStr}`
      }
      data = Array.from(new Set(data.concat(str)))
      this._conditionList.conditionTypes = data
    }
    if (conditionApiKey) {
      let data = this._conditionList.conditionApiKeys || []
      data = Array.from(new Set(data.concat(conditionApiKey)))
      this._conditionList.conditionApiKeys = data
    }
    console.log('.............', this._conditionList)
  }

  get showWxGuest() {
    if (!Object.keys(this.pageConfig?.data || {}).length) {
      return false
    }
    const urlPared = new UrlParser(location.href)
    // 检查是否需要显示微信授权弹窗
    const isGzh = urlPared.query.gzh === '1'
    const showWxGuest = this.pageConfig.data?.showWxGuest !== '0' && !isGzh
    return showWxGuest
  }

  get rtConnerShareData(): TShareConfig {
    const { base = {} } = this.shareConfig
    const id = base?.rtConner || ''
    console.log('🚀 ~ FocusRenderContrler ~ getrtConnerShareData ~ id:', id)

    if (!id) {
      return {}
    }
    return this.getShareConfigByConfigId(id)
  }

  get needLogin(): boolean {
    console.log('.....是否需要登录！', this.pageConfig.data?.needLogin === '1')
    return !!(this.fid && this.pageConfig.data?.needLogin === '1')
  }

  get needLoginInApp(): boolean {
    return !!(this.fid && !(this.pageConfig.data?.forceLoginInApp === '0'))
  }

  rebuildTree(data: any[], isPopup?: boolean) {
    console.log('🚀 ~ 这是可以渲染的组件', data)
    const cloneData = JSON.parse(JSON.stringify(data)) // 对源数据深度克隆

    const tree = cloneData.filter((father: { moduleId: any; children: any; pid: number }) => {
      const branchArr = cloneData.filter((child: { pid: any }) => father.moduleId == child.pid) //返回每一项的子级数组
      branchArr.length > 0 ? (father.children = branchArr) : '' //如果存在子级，则给父级添加一个children属性，并赋值
      if (isPopup) {
        return father.pid == 0
      }
      return father.pid == 1 //返回第一层
    })
    console.log('🚀 ~ file: FocusRenderContrler.ts:245 ~ FocusRenderContrler ~ tree ~ tree:', tree)
    return tree
  }
  /**
   *
   * @param id
   * @returns
   */
  getShareConfigByConfigId(id: number | string): TShareConfig {
    console.log('🚀🚀🚀🚀 ~传入了shareConfigId:', id)

    //0723版本，这个id 由 单个字符串改成了多个值带逗号的格式
    // 例如  0723前 是 '1';0723后可能是  '1,2'
    let _id = id.toString()
    if (_id.indexOf(',') > -1) {
      const arr = id.toString().split(',')
      console.log('🚀 ~ FocusRenderContrler ~ getShareConfigByConfigId ~ arr:', arr)
      // 随机取出一个值
      const randomIndex = Math.floor(Math.random() * arr.length)
      console.log('🚀 ~ FocusRenderContrler ~ getShareConfigByConfigId ~ randomIndex:', randomIndex)
      _id = arr[randomIndex]
    }
    console.log('随机到了shareConfigId是', _id)
    const config = this.shareConfig && this.shareConfig.configList[Number(_id) - 1]
    console.log(`🚀 ~ 通过“分享配置”获取了当前分享配置，id是${_id}`, config)
    if (!config) {
      return {}
    }
    const {
      desc = '',
      imgUrl = '',
      mgmAid = '',
      path = '',
      title = '',
      appShareType = '0',
      miniUserName = 'H5',
      mgmAidV2 = '',
      shareImageBg = '',
      shareImageHeight = '',
    } = config
    const shareData = {
      shareTitle: title,
      shareDesc: desc,
      shareImg: imgUrl,
      shareUrl: path,
      shareMgmAid: mgmAid,
      appShareType,
      miniUserName,
      mgmAidV2,
      shareImageBg,
      shareImageHeight,
      shareId: _id,
    }
    console.log('🚀 ~ 分享链接是：', shareData)
    return shareData
  }

  /**
   * 传入控制方法
   * @param arg0
   */

  setContrlMethod(arg0: { renderModules: (data: any[]) => void; renderPopupModules: (data: any[]) => {} }) {
    if (arg0.renderModules) {
      this.renderModules = arg0.renderModules
    }
    if (arg0.renderPopupModules) {
      this.renderPopupModules = arg0.renderPopupModules
    }
  }

  resetPageTitle() {
    if (this.pageTitleDatas) {
      focusCore.setPageTitle(this.pageTitleDatas.title, this.pageTitleDatas.customerService || '')
    }
  }

  renderPageConfig(param?: { title?: string; bg?: string }) {
    const title = param?.title || this.pageConfig?.data?.pageTitle
    const bg = param?.bg || this.pageConfig?.styles?.bg
    const customerService = this.pageConfig?.customerService
    this.pageTitleDatas = {
      title,
      customerService,
    }
    focusCore.setPageTitle(title, customerService)
    if (bg) {
      document.body.style.background = bg

      if (/http/.test(bg)) {
        document.body.style.backgroundImage = `url(${bg})`
      } else {
        const colorArr = bg.split('-')

        const style = document.createElement('style')
        style.type = 'text/css'

        if (colorArr.length > 1) {
          document.body.style.backgroundColor = ''
          document.body.style.backgroundImage = `linear-gradient(to bottom,${colorArr[0]},${colorArr[1]}`
          style.innerHTML = `.fixed-b-bar::after{ background: ${colorArr[1]} }`
        } else {
          document.body.style.backgroundImage = ''
          document.body.style.backgroundColor = bg.replace('_', '#')
          // sheet.addRule('.red::before', 'color: green')
          style.innerHTML = `.fixed-b-bar::after { background: ${bg.replace('_', '#')} }`
        }
        document.head.appendChild(style)
      }
    }
  }

  /**
   * 页面可访问时间
   * @returns
   */
  checkPageShowIntime(): Promise<boolean> {
    const { startTime, endTime } = this.pageConfig.data || {}
    const { modalStore } = focusStore.stores
    return focusCore.showPageInTimes(true, startTime || '', endTime || '')
  }

  changeModuleConfig(
    moduleId: number | string,
    options?: {
      hide?: boolean
      remove?: boolean
      data?: any
      style?: any
    },
  ) {
    console.error('调整moduleConfig!')
    console.log('🚀 ~ 目标组件ID', moduleId)
    console.log('🚀 ~ file: 更新的配置是啥？', options)
    const key = moduleId.toString()
    const data = {
      ...this.changedModuleDataRef.value,
    }
    data[key] = {
      ...options,
    }

    const changeStyle = function (_style: any) {
      return {
        ..._style,
      }
    }

    if (options?.style) {
      data[key].style = changeStyle(options.style)
    }

    return {
      changeModuleConfig: this.changeModuleConfig,
      render: () => {
        this.changedModuleDataRef.value = data
      },
    }
  }
  /**
   * 检查是否已经修改过
   * @param moduleId
   */
  checkModuleHadChanged(moduleId: string | number) {
    const key = moduleId.toString()
    if (this.moduleChangedList.indexOf(key) > -1) {
      return true
    }
    this.moduleChangedList.push(key)

    return false
  }

  clearModuleChanged(moduleId: number | string) {
    const key = moduleId.toString()
    const index = this.moduleChangedList.findIndex((id: string) => id === key)
    if (index > -1) {
      this.moduleChangedList.splice(index, 1)
    }
  }

  findModulesByType(moduleType: string) {
    return this.moduleTree.filter((i) => i.moduleType === moduleType)
  }

  checkBeforeRender(): Promise<boolean> {
    const { modalStore } = focusStore.stores
    return new Promise((resolve) => {
      this.checkPageShowIntime().then(async (status) => {
        if (!status) {
          console.log('无法展示页面！')
          return resolve(false)
        }

        try {
          this.hideModuleIds = await focusService.getHideModules(this.grayList)
        } catch (err: any) {
          if (err.error === 'not_in_page_gray') {
            modalStore.errorMaskContrl(`NOT_IN_PAGE_GRAY`, ['非常抱歉，本活动仅限受邀客户参与'])
            modalStore.loadingEnd('focusrendercontrl_render')
            return resolve(false)
          }
        }
        return resolve(true)
      })
    })
  }

  render(): Promise<boolean> {
    const { modalStore } = focusStore.stores
    modalStore.loadingStart('focusrendercontrl_render')
    console.log('渲染开始。。。。')
    this.changedModuleDataRef = ref({})

    return new Promise(async (resolve) => {
      this.systemTime = await focusCore.getNowTime()
      this.moduleTree = this.resolveModuleTree()
      this.renderPageConfig()
      this.renderModules(this.moduleTree)
      this.renderPopupModules(this.popupModuleTree)
      modalStore.loadingEnd('focusrendercontrl_render')
      resolve(true)
    })
  }
}
