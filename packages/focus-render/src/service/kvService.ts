import { focusCore } from '../packages/focus-render/index'
import { EnumMethod } from './focus-core/plugins/Ajax'
export const cgis = {
  getUserKv: {
    url: '/wm-htrserver/cop/common_info/get_user_kv',
    name: '获取用户KV配置',
  },
  setUserKv: {
    name: '写入用户KV配置',
    method: EnumMethod.POST,
    url: '/wm-htrserver/cop/common_info/set_user_kv',
  },
}
export enum EnumKvDataType {
  FAMILY = 'family',
}

class KvService {
  setUserKv(
    key: EnumKvDataType,
    keyValues: {
      [k: string]: any
    },
  ): Promise<boolean> {
    return new Promise((resolve) => {
      focusCore
        .request(
          cgis.setUserKv,
          {},
          {
            uid_type: focusCore.cgiParams.uid_type,
            dataType: key,
            dataValue: JSON.stringify(keyValues),
          },
        )
        .then((res: any) => {
          console.log(res)
          resolve(true)
        })
        .catch((err: any) => {
          resolve(false)
        })
    })
  }
  getUerKv(kvKeys: EnumKvDataType[] = []) {
    const queryKey = kvKeys.join(',')
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getUserKv, { dataType: queryKey })
        .then((res: any) => {
          console.log('🚀 ~ file: kvService.ts ~ line 50 ~ KvService ~ .then ~ res', res)
          const { ret_data, ret_code } = res
          if (/0000$/.test(ret_code)) {
            return resolve(ret_data.ret_data || {})
          }
          return resolve(null)
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: lotteryService.js ~ line 39 ~ LotteryService ~ returnnewPromise ~ err', err)
          resolve(null)
        })
    })
  }
}
export default new KvService()
