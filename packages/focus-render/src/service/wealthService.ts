import { focusCore } from '../packages/focus-render/index'
import { EnumBaseHostKey } from './focus-core/plugins/baseConfig'
import { formatAmount, dayjs, entitiestoUtf16 } from '../utils/index'

const cgis = {
  calcWealth: {
    url: '/wm-htrserver/cop/hj/good_start/queryGoodStartActivityTrialCalculation',
    name: '新增标准财富值转入试算',
  },
  userWealthInfo: {
    url: '/wm-htrserver/cop/hj/good_start/queryNamedDateStandardWealthInfo',
    name: '用户财富值信息',
  },
}

export interface GetCalcWealthParams {
  standardDsDate: string
  gearAmountList: string[]
  dsDate: string
  trailType: string
}

export interface WealthCalcItem {
  gearAmount: number
  gearTransferAmount: number
}

export interface GetCalcWealthResponse {
  list: WealthCalcItem[]
}

export interface GetUserWealthInfoResponse {
  wealthScore: number | string
}

class WealthService {
  /**
   *
   * @returns
   * dsDate	是	20241113  指定标准财富值日期
   * standardDsDate	是	20241030  基准财富值日期
   * gearAmountList	是	[10000,50000,100000] 挡位达标金额
   * gearAmount 是 挡位金额
   * gearTransferAmount 是 挡位转入金额
   */
  getCalcWealth(
    standardDsDate: string,
    gearAmountList: string[],
    dsDate: string,
    trailType: string,
  ): Promise<{
    list: {
      gearAmount: number
      gearTransferAmount: number
    }[]
  }> {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.calcWealth, {
          standardDsDate,
          gearAmountList,
          dsDate,
          trailType,
        })
        .then(
          (res: {
            trialCalculationDetailList: {
              gearAmount: number
              gearTransferAmount: number
            }[]
          }) => {
            const { trialCalculationDetailList = [] } = res

            return resolve({
              list: trialCalculationDetailList || [],
            })
          },
        )
        .catch((res) => {
          return resolve({ list: [] })
        })
    })
  }

  getCalcWealthForDynamic(
    params?: { baseDate: string; wealthBracket: string[]; dsDate: string; trailType: string } | any,
  ) {
    console.log('🚀 ~ WealthService ~ getCalcWealthForDynamic ~ params:', params)
    const { baseDate, wealthBracket, dsDate = '', trailType } = params
    let _dsDate =
      dsDate === '--' || !dsDate ? dayjs().add(-1, 'day').format('YYYYMMDD') : dayjs(dsDate).format('YYYYMMDD')
    console.log('🐬 ~ file: wealthService.ts:71 ~ WealthService ~ _dsDate:', _dsDate)
    return new Promise((resolve) => {
      this.getCalcWealth(baseDate, wealthBracket, _dsDate, trailType).then((res) => {
        return resolve(formatWealthCalc(wealthBracket, res.list, trailType))
      })
    })
  }

  getUserWealthInfo(dsDate?: string): Promise<{
    wealthScore: number | string
  }> {
    console.log('🚀 ~ WealthService ~ getUserWealthInfo ~ dsDate:', dsDate)
    const _dsDate = dsDate || dayjs().format('YYYYMMDD')
    console.log('🚀 ~ WealthService ~ getUserWealthInfo ~ _dsDate:', _dsDate)
    return new Promise((resolve) => {
      focusCore
        .request(cgis.userWealthInfo, {
          dsDate: _dsDate,
        })
        .then((res: { standardWealth: number }) => {
          return resolve({
            wealthScore: res.standardWealth || 0,
          })
        })
        .catch((err: any) => {
          console.log('🚀 ~ WealthService ~ returnnewPromise ~ err:', err)
          return resolve({
            wealthScore: '--',
          })
        })
    })
  }
}

function formatWealthCalc(
  targetList: string[],
  list: {
    gearAmount: number
    gearTransferAmount: number
  }[],
  trailType: string,
) {
  const obj: any = {}
  targetList.forEach((i) => {
    const key = `newWealth?wealthBracket=${i}`
    obj[key] = '--'
  })

  list.forEach((i) => {
    const { gearAmount, gearTransferAmount } = i
    const key = `newWealth?wealthBracket=${gearAmount}`
    let str = ''
    switch (trailType) {
      case 'gearTrial':
        str = '保持至月末可达标'
        break
      case 'wealthUpgrade':
        str = '昨日已达标'
        break
      default:
        str = '保持至月末可达标'
        break
    }
    let aum = gearTransferAmount > 0 ? formatAmount(gearTransferAmount.toString()) : str
    obj[key] = aum
  })

  return obj
}

export default new WealthService()
