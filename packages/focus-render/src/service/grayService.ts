import { focusCore } from '../packages/focus-render/index'
import { EnumMethod } from './focus-core/plugins/Ajax'
import { QueryApiResponse } from './dynimicDataService'

export interface GrayListItem {
  id: string
  subId: string
}

export interface CheckBussinessGrayResponse {
  type: string
  subType: string
}

export interface BusinessGrayItem {
  type: string
  itemId?: string
  idType?: string
  isOpen?: boolean
}
import { dayjs, UrlParser } from '@/utils'

const cgis = {
  whitelistV4: {
    url: '/hj/check_whitelist/v4',
    name: '查询业务灰度',
  },
  matchTag: {
    url: '/wm-htrserver/cop/match_tag',
    name: '获取用户是否匹配标签ID的灰度',
  },
  comonGray: {
    url: '/wm-hjhtr/gray/general/query',
    name: '查询通用灰度',
    method: EnumMethod.POST,
  },
}

class GrayService {
  // checkFocusGray(useGrayType: '0' | '1', busGrayIds: Array<{ id: string; subId: string }>) {
  //   if (useGrayType === '1') {
  //     return this.checkCommonGray(busGrayIds.map((i) => i.id))
  //   }
  //   return this.checkBussinessGray(busGrayIds)
  // }

  /**
   * 检查用户是否满足业务灰度或者通用灰度
   * @param tagId
   * @returns
   */
  checkBussinessGray(
    grayList: Array<{ id: string; subId: string }>,
    grayType?: string, // 0,1,2
  ): Promise<
    {
      type: string
      subType: string
    }[]
  > {
    const query = new UrlParser(location.href).query
    console.log(
      '🚀 ~ GrayService ~ focusCore.mockData.isPreview && focusCore.mockData.mockGrayId:',
      focusCore.mockData.isPreview && focusCore.mockData.mockGrayId,
    )
    if (focusCore.mockData.isPreview && focusCore.mockData.mockGrayId) {
      let id = ''
      try {
        id = (focusCore.mockData.mockGrayId && focusCore.mockData.mockGrayId.id) || ''
        id = id.split('_') && id.split('_')[0]
        console.log('🚀 ~ GrayService ~ id:', id)
      } catch (err) {
        console.log('🚀 ~ GrayService ~ err:', err)
      }
      if (id) {
        return Promise.resolve([
          {
            type: id,
            subType: '',
          },
        ])
      }
    }

    const _isUseCommonGray = grayType
      ? grayType === '2'
        ? true
        : false
      : !!(focusCore.focusRenderCtr && focusCore.focusRenderCtr.isUseCommonGray)

    if (_isUseCommonGray) {
      console.log('要使用通用灰度！')
      // 使用通用灰度
      const grayIds = (grayList && grayList.map((i) => Number(i.id))).filter((i) => i) || []
      if (!grayIds.length) {
        return Promise.resolve([])
      }
      return focusCore
        .request(cgis.comonGray, {}, { grayIdList: grayIds })
        .then((res: any) => {
          const data = res || {}
          const result = grayList
            .map((i) => {
              const k = i.id.toString()
              if (data[i.id]) {
                return {
                  type: k,
                  subType: i.subId || '',
                }
              }
              return null
            })
            .filter((i) => i)

          return Promise.resolve(result)
        })
        .catch((err: any) => {
          return Promise.resolve([])
        })
    }

    const cycle =
      (grayList &&
        grayList.map((i) => {
          return {
            type: i.id,
            itemId: i.subId,
            idType: 'ecif',
          }
        })) ||
      []
    if (!grayList.length) {
      return Promise.resolve([])
    }

    return focusCore.request(cgis.whitelistV4, { cycle }).then(
      (res: any) => {
        console.log('🚀 ~ file: grayService.ts ~ line 24 ~ GrayService ~ checkBussinessGray ~ res', res)

        const whiteList: { inFlag: boolean; type: number; itemId: string }[] = res.whiteList || []

        const grayIds = whiteList
          .filter((i) => i.inFlag)
          .map((i) => {
            return {
              type: i.type.toString(),
              subType: i.itemId || '',
            }
          })
        return Promise.resolve(grayIds)
      },
      (err: any) => {
        console.log('🚀 ~ file: grayService.ts ~ line 46 ~ GrayService ~ checkBussinessGray ~ err', err)

        return Promise.resolve([])
      },
    )
  }

  checkMultiTagId(tagIds: number[]): Promise<number[]> {
    const isUseCommonGray = !!(focusCore.focusRenderCtr && focusCore.focusRenderCtr.isUseCommonGray)

    return new Promise((resolve) => {
      if (isUseCommonGray) {
        return resolve([])
      }
      Promise.all(
        tagIds.map((tagId) => {
          return this.checkUserMatchTagId(tagId)
        }),
      ).then((res) => {
        resolve(res.filter((i) => i))
      })
    })
  }

  /**
   * 检查用户是否满足标签ID
   * @param tagId
   * @returns
   */
  checkUserMatchTagId(tagId: number): Promise<number> {
    if (!tagId) {
      return Promise.resolve(0)
    }
    return new Promise((resolve) => {
      focusCore
        .request(cgis.matchTag, {
          ruleConfigId: Number(tagId),
        })
        .then((res: any) => {
          if (/0000$/.test(res.ret_code)) {
            const { ret_data } = res
            if (ret_data.match) {
              return resolve(Number(tagId))
            }
            return resolve(0)
          }
          resolve(0)
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: grayService.js ~ line 104 ~ GrayService ~ returnnewPromise ~ err', err)
          resolve(0)
        })
    })
  }
}

class GrayidsHandler {
  bussinessIds: {
    [k: string]: {
      type: string
      itemId?: string
      idType?: string
      isOpen?: boolean
    }
  } = {
    '1209': {
      type: '1209',
      itemId: '',
      idType: 'openid',
      // label: '外部拉起app时候决定跳转路径是否使用签名路径',
      // ver: '2024-11-13',
    },
  }

  constructor() {}

  canUseBusid(targetId: string): Promise<boolean> {
    const target = this.bussinessIds[targetId]
    console.log('🚀 ~ GrayidsHandler ~ canUseBusid ~ target:', target)

    if (!target) {
      // 没有这个灰度，那就当做没不满足灰度
      return Promise.resolve(false)
    }
    // 全量了，直接放开
    if (target.isOpen) {
      return Promise.resolve(true)
    }
    const cycle = [
      {
        type: target.type,
        itemId: target.itemId,
        idType: target.idType,
      },
    ]
    return new Promise((resolve) => {
      focusCore
        .request(cgis.whitelistV4, {
          cycle,
        })
        .then(
          (res: {
            whiteList: Array<{
              idType: string
              inFlag: boolean
              itemId: string
              type: number
            }>
          }) => {
            console.log('🚀 ~ GrayidsHandler ~ focusCore.request ~ res:', res)
            const isOpen = res.whiteList || []

            return resolve(isOpen.some((i) => i.type.toString() === targetId && i.inFlag))
          },
        )
        .catch((err: any) => {
          return resolve(false)
        })
    })
  }
}

const grayidsHanlder = new GrayidsHandler()
export { grayidsHanlder }
export default new GrayService()
