import waInstance from '@webank/wa-sdk'
import { UrlParser } from '../../utils/index'

const formatHash = (hash: string) => {
  let res = ''
  if (!/#\//.test(hash)) return res
  if (/\?/.test(hash)) res = hash.split('?')[0]
  return res.replace('#/', '')
}

enum EnumDefindName {
  INFO_SOURCE = 'info.source',
  NETWORK = 'network',
  ERROR_SOURCE = 'error.source',
  INFO_PUSHDATA = 'info.pushdata',
}

enum EnumErrorType {
  LOG_OVER_LONG = 'log_over_long',
}

// 控制是否上报资源
const logResourceFlag = false

class Formatter {
  formateCurUrlStr(url: string) {
    const urlPared = new UrlParser(url)
    const { query } = urlPared
    let queryStrings = `path-${location.pathname.replace(/(\/)|(\.)/, '_')}`
    Object.keys(query).forEach((k) => {
      const str = `,${k}-${query[k]}`
      queryStrings += str
    })
    return queryStrings
  }
}

class WaLog {
  wa: any
  formatter: any
  useCustomPageId: string
  constructor() {
    this.wa = waInstance
    this.formatter = new Formatter()
    this.useCustomPageId = ''
    const defaultData = {
      curUrl: window.location.href,
      urlHash: window.location.hash,
    }
    if (window.FOCUS_WA) {
      //全局共享同一个 实例
      this.wa = window.FOCUS_WA
      this.setGlobalParam(defaultData)
      this.waClickStat('info.waready-old', encodeURIComponent(location.href))
    } else {
      window.FOCUS_WA = this.wa
      console.log('wa start')
      this.wa.init({
        appId: 'hj',
        account: 'hj-focus2',
        pageId: this.waPageId,
        autoReport: false,
        // storageLimit: 6,
        // autoStorageReport: true,
      })
      this.setGlobalParam(defaultData)
      this.waClickStat('info.waready-new', encodeURIComponent(location.href))
      this.waClickStat('showed', 'page_init')
    }
  }
  get waPageId() {
    if (this.useCustomPageId) {
      return this.useCustomPageId
    }
    const { pageid = 0, aid = 0, fid = 0 } = new UrlParser(window.location.href).query
    if (!aid && !pageid && !fid) {
      const pagePath = location.pathname.replace(/(\/s\/hj\/)|(\.html)/g, '').replace(/\//g, '_')
      return pagePath
    }
    const focusFid = fid || pageid
    return `focus2_${focusFid}_${aid}`
  }
  logMulti(definedName: string, definedValue: string, definedInfo?: any) {
    // console.log('开始批量上报！', definedName, definedValue)
    this.wa.storage('event', definedName, definedValue, definedInfo)
  }
  isValidStr(waKeyName: string, str: string) {
    const regs = {
      definedName: /^[a-zA-Z0-9_\\u4e00-\u9fa5\-=.|]{1,64}$/,
      defiendValue: /^[a-zA-Z0-9_\\u03391-\uffe5\-:|,.\?]{1,64}$/,
      fieldY: /^[a-zA-Z_0-9\-,]{1,256}$/,
    }
    const reg = regs[waKeyName]
    if (!reg) {
      return true
    }
    const result = reg.test(str)
    if (!result) {
      console.error(`上报校验有问题！，请检查`)
      console.error(`${waKeyName}, ${str}`)
    }
    return
  }
  setCustomPageId(pageId: string) {
    this.useCustomPageId = pageId
    this.wa.setParam('pageId', pageId)
  }
  setGlobalParam(config: {
    openId?: string
    MD5Ecif?: string
    unionid?: string
    uidType?: string
    curUrl?: string
    urlHash?: string
    platform?: string
  }) {
    const { openId } = config
    // 按照数组顺序，分别对应 field_y_0 到 field_y_n
    // field_y_0 到 field_y_n 的文本规则限制：256位,大小写字母+数宇＋下划线＋中划线＋英文逗号;命中过滤规则值会被替换为FILTER_BY_WA
    const logKeys = ['MD5Ecif', 'unionid', 'uidType', 'curUrl', 'urlHash', 'platform']
    Object.keys(config).forEach((key: string) => {
      const index = logKeys.indexOf(key)
      if (index < 0) {
        // console.error('存在一个字段不能写入！', key)
        return false
      }
      const feildKey = `field_y_${index}`
      let data = config[key] || ''
      switch (key) {
        case 'curUrl':
          data = this.formatter.formateCurUrlStr(data)
          break
      }
      const formatData = data.replace(/(\/)|(\.)|(\#)|(\,)/g, '_')
      this.isValidStr('fieldY', formatData)
      if (data) {
        console.log(`设置wa预留字段`, key, feildKey, formatData)
        this.wa.setParam(feildKey, formatData)
      }
    })
    openId && this.wa.setParam('openId', openId)
  }

  renderLog(stepKey: string, definedInfo?: any) {
    // focusrender 开始执行时候才计算pv
    const definedName = 'info.focus_redner'
    this.wa.clickStat(definedName, stepKey, definedInfo || {})
  }

  logAjaxRequest(config: {
    method: string
    url: string
    urlQuery: any
    postBody?: any
    rid: number
    baseUrl?: string
  }) {
    const { method, url, urlQuery, rid, postBody, baseUrl } = config
    let path = url.replace(/(\/)|(\.)/g, '_')

    const definedName = EnumDefindName.NETWORK
    let definedValue = `req_${method}_${path}`
    if (definedValue.length >= 62) {
      try {
        path = url.split('/').slice(-2).join('_')
        definedValue = `req_${method}_${path}`
      } catch (err) {
        console.log('🚀 ~ WaLog ~ err:', err)
      }
    }
    const params: {
      rid: number
      urlQuery: any
      postBody?: any
      baseUrl?: string
      url: string
    } = {
      urlQuery,
      rid,
      baseUrl,
      url,
    }
    if (postBody) {
      params.postBody = postBody
    }
    this.waClickStat(definedName, definedValue, params)
  }

  logAjaxResponse(config: {
    method: string
    url: string
    resStatus: 'succ' | 'fail'
    rid: number
    ret_code?: string
    hj_biz_no?: string
    spaceTime: number
    postBody: any
    urlQuery: any
    httpStatus: Number
    errString?: string
    originErrorRes?: any
    baseUrl?: string
  }) {
    const {
      method,
      url,
      resStatus,
      rid,
      ret_code,
      hj_biz_no,
      spaceTime,
      httpStatus = 0,
      postBody = {},
      urlQuery = {},
      errString,
      originErrorRes = {},
      baseUrl,
    } = config
    let path = url.replace(/(\/)|(\.)/g, '_')
    const definedName = EnumDefindName.NETWORK

    let definedValue = `res_${resStatus}_${method}_${path}`
    if (definedValue.length >= 62) {
      try {
        path = url.split('/').slice(-2).join('_')
        definedValue = `req_${method}_${path}`
      } catch (err) {
        console.log('🚀 ~ WaLog ~ err:', err)
      }
    }
    let definedInfo: any = {
      rid,
      httpStatus,
      spaceTime,
      ret_code,
      hj_biz_no,
      postBody,
      urlQuery,
      baseUrl,
      url,
    }
    if (resStatus === 'fail') {
      definedInfo = {
        ...definedInfo,
        errString,
        originErrorRes,
      }
    }
    this.waClickStat(definedName, definedValue, definedInfo)
  }

  /**
   * 自定义错误上报
   * @param errorType
   * @param params
   */
  errorLog(errorType: string, params?: { msg?: string; line?: string; col?: string; url?: string; stack?: string }) {
    const { msg = '', line = '', col = '', url = '', stack = '' } = params || {}
    this.wa.errorReport({
      errorType,
      _msg: msg,
      _line: line,
      _col: col,
      _url: url,
      _stack: stack,
    })
  }

  eventLog(definedName: string, definedValue: string, definedInfo?: any) {
    this.waClickStat(definedName, definedValue, definedInfo || {})
  }
  /**
   * 代理一下，方便log
   * @param definedName  //64位,大小写宇母＋数宇＋下划线＋中划线＋等于号＋中文＋点＋竖线;命中过漶规则值会被替换为FILTER BY WA
   * @param definedValue //64位,大小写字母＋数宇＋汉宇＋中划线＋下划线＋空格+问号+句号+中文符号＋英文逗号＋英文冒号＋竖线;命中过漶规则值会被替换为FILTER_BY_WA
   * @param definedInfo
   */
  waClickStat(definedName: string, _definedValue: string, _definedInfo?: any) {
    // console.log('-----log start---')
    // console.log('definedName:', definedName)
    // console.log('definedValue:', definedValu
    // console.log('definedInfo', definedInfo)
    // console.log('-----log end----')
    const defaultDefinedInfo = {
      curUrl: location.href,
    }
    const definedValue = (_definedValue && _definedValue.replace(/(\/)|(\.)/g, '_')) || ''
    let definedInfo = _definedInfo || {}
    try {
      definedInfo = Object.assign({}, defaultDefinedInfo, _definedInfo || {})
    } catch {}

    this.isValidStr('definedName', definedName)
    this.isValidStr('definedValue', definedValue)
    this.splitLongLog(definedName, definedValue, definedInfo).forEach((i: any) => {
      const { definedName, definedValue, definedInfo } = i
      let reportDefinedInfo = this.logLongError(definedName, definedValue, definedInfo)
      this.wa.clickStat(definedName, definedValue, reportDefinedInfo || {})

      const imsErrorList = ['error.errorMask', 'error.ajax']
      if (imsErrorList.indexOf(definedName) > -1) {
        this.wa.warnEvent(definedName, definedValue, reportDefinedInfo || {})
      }
    })
  }

  splitLongLog(definedName: string, definedValue: string, definedInfo: any = {}) {
    const strSize = 400

    const checkDefinedInfoLen = (defined_info: any) => {
      const str = JSON.stringify(definedInfo || {})
      return str.length > strSize
    }
    if (checkDefinedInfoLen(definedInfo)) {
      console.error('definedinfo超长啦！')
      console.log('>>>>> 开始尝试分拆上报哦！')
      const randomeId = Date.now()
      const definedInfoArrs: any = []
      // let i = definedInfoArrs.length+1
      Object.keys(definedInfo)
        .sort((a, b) => {
          let r = -1
          try {
            let s1 = JSON.stringify(definedInfo[a]) || ''
            let s2 = JSON.stringify(definedInfo[b]) || ''

            r = s1.length > s2.length ? -1 : 1
          } catch (err) {
            console.log('🚀 ~ WaLog ~ .sort ~ err:', err)
          }
          return r
        })
        .forEach((k) => {
          const len = definedInfoArrs.length
          for (let _i = 0; _i < len + 1; _i++) {
            const d = JSON.stringify(definedInfoArrs[_i]) || ''
            const s = JSON.stringify(definedInfo[k]) || ''
            if (!definedInfoArrs[_i]) {
              definedInfoArrs[_i] = {}
            }
            if (s.length + d.length < strSize) {
              definedInfoArrs[_i][k] = definedInfo[k] || ''
              break
            }
          }
        })
      return definedInfoArrs.map((i: any, index: number) => {
        return {
          definedName,
          definedValue,
          definedInfo: {
            ...i,
            splitLogId: randomeId,
          },
        }
      })
    } else {
      // console.log('log没超长，不用分拆上报')
      return [
        {
          definedName,
          definedValue,
          definedInfo,
        },
      ]
    }
  }

  clickLog(btnName: string, definedInfo?: any) {
    this.waClickStat('click', btnName, definedInfo)
  }

  /**
   * 超长错误上报
   */
  logLongError(definedName: string, definedValue: string, definedInfo: any = {}): string {
    // console.log('🚀 ~ WaLog ~ logLongError ~ definedInfo:', definedInfo)
    const checkDefinedInfoLen = (defined_info: any) => {
      const str = JSON.stringify(definedInfo || {})
      return str.length > 450
    }
    let reportDefinedInfo = {
      ...definedInfo,
    }
    if (checkDefinedInfoLen(definedInfo)) {
      console.error('defined_info 超过了400字符，换 errorLog')
      // 原始的上报只保留基础的字段
      reportDefinedInfo = {
        log_over_long: '1',
        logUrl: `${definedName}__${definedValue}`,
        httpStatus: definedInfo?.httpStatus || '',
        spaceTime: definedInfo?.spaceTime || '',
        urlQuery: definedInfo?.urlQuery || '',
        errString: definedInfo?.errString || '',
        ret_code: definedInfo?.ret_code || '',
        hj_biz_no: definedInfo?.hj_biz_no || '',
        postBody: definedInfo?.postBody || '',
      }

      // 直接使用以下4个字段来存储，每个字段存450字符;剩余抛弃
      const [msg, line, col, stack] = ['', '', '', '', ''].map((i, index) => {
        const str = JSON.stringify(definedInfo || {})
        const stratIndex = index * 450
        const endIndex = (index + 1) * 450
        return str.substring(stratIndex, endIndex)
      })

      this.errorLog(EnumErrorType.LOG_OVER_LONG, {
        url: `${definedName}__${definedValue}`,
        msg,
        line,
        col,
        stack,
      })
    }
    return reportDefinedInfo
  }

  logResource() {
    if (!logResourceFlag) {
      console.log('关闭资源上报！')
      return false
    }
    console.log('开始监控资源上报！')
    window.addEventListener(
      'error',
      (e) => {
        // 过滤js error
        const target: any = e.target || e.srcElement
        const isElementTarget =
          target instanceof HTMLScriptElement || target instanceof HTMLLinkElement || target instanceof HTMLImageElement
        if (!isElementTarget) return false
        // 上报资源地址,一般在这里的都是404，总之就是没加载出来，都归类为404
        this.waClickStat(EnumDefindName.ERROR_SOURCE, '404', {
          url: (target as any)?.src,
        })
      },
      true,
    )
    if (window.PerformanceObserver) {
      // return false
      new PerformanceObserver((entryList) => {
        // 任意资源加载完成基本都会回调（极少数情况不会，可忽略）
        entryList.getEntries().forEach((entry: any) => {
          // 通过 entry.name 后缀或 entry.initiatorType 来判断资源类型。

          const { duration, name, initiatorType, responseEnd, fetchStart } = entry || {}
          const resourceTypes = ['script', 'link', 'img']
          const entryName = name && name.split('/')
          let sourceName = entryName[entryName.length - 1]
          sourceName = sourceName.replace(/(\.)|(\/)/g, '_') //defined_value不支持 .
          if (resourceTypes.indexOf(initiatorType) < 0) {
            // console.log('这不是要监控的资源！', sourceName)
            return false
          }

          let sourceType = initiatorType
          if (sourceName.indexOf('_js') > -1) {
            sourceType = 'script'
          }
          if (sourceName.indexOf('_css') > -1) {
            sourceType = 'css'
          }
          const definedName = EnumDefindName.INFO_SOURCE

          // console.log(`${sourceName} 加载耗时：`, duration.toFixed(2))
          const resourceDuration = duration.toFixed(2)
          const resourceResponseEnd = responseEnd.toFixed(2)
          const resourceFetchStart = fetchStart.toFixed(2)
          const definedValue = `${sourceType}__${sourceName}`
          this.eventLog(definedName, definedValue, {
            duration: resourceDuration, // 加载时长
            responseEnd: resourceResponseEnd, // 资源加载结束时候页面经过的时长
            fetchStart: resourceFetchStart, // 资源加载开始时候页面经过的时长
          })
          // this.logMulti(definedName, definedValue, {
          //   duration: resourceDuration, // 加载时长
          //   responseEnd: resourceResponseEnd, // 资源加载结束时候页面经过的时长
          //   fetchStart: resourceFetchStart, // 资源加载开始时候页面经过的时长
          // })
          // wa.storage('event', definedName, definedValue, {
          //   duration: resourceDuration, // 加载时长
          //   responseEnd: resourceResponseEnd, // 资源加载结束时候页面经过的时长
          //   fetchStart: resourceFetchStart, // 资源加载开始时候页面经过的时长
          // })
        })
      }).observe({ entryTypes: ['resource'] })
    }
  }
}

export default new WaLog()
