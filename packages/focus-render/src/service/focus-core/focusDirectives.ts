import focusCore from '.'
import { isRef, isProxy } from 'vue'
interface IReportData {
  definedValue: string
  definedInfo: any
}
class DirectiveWalog {
  reportDatas: any = {}
  reportDataFns: any = {}
  constructor() {}

  createReportData() {}

  addClickLog(el: HTMLElement, reportData: IReportData) {
    // console.log('🚀 ~ DirectiveWalog ~ addClickLog ~ reportData:', reportData)
    // console.log('🚀 ~ DirectiveWalog ~ addClickLog ~ el:', el)
    // console.log('🚀 ~ DirectiveWalog ~ addClickLog ~ el:', el.innerText)

    let definedValue = reportData.definedValue
    if (!definedValue) {
      console.log(`reportData中没携带 definedValue! 默认从 el 的 data-reportid 获取`)
      definedValue = (el && el.getAttribute('data-reportid')) || ''
      if (!definedValue) {
        console.error('无法获取上报的definedValue')
        console.error('上报异常的组件是：', el)
        return
      }
    }
    const _reportData = {
      ...reportData,
      definedValue,
    }
    // const fn = this.recoredReportData(el, _reportData)
    el.addEventListener('click', () => {
      const _definedInfo = Object.assign(
        {
          innerText: el.innerText,
        },
        _reportData.definedInfo || {},
      )

      _reportData.definedInfo || {}
      console.log('。。。自动上报')
      focusCore.clickLog(_reportData.definedValue, _definedInfo)
    })
  }

  recoredReportData(el: HTMLElement, reportData: IReportData) {
    const key = reportData.definedValue
    if (this.reportDatas[key]) {
      console.error(`已存在 definedValue=${key} 的上报`, this.reportDatas[key])
      console.log('现在要更新为', reportData.definedInfo || {})
      console.log('移除旧上报')
      this.removeClickLog(el, key)
    }
    this.reportDatas[key] = reportData.definedInfo || {}
    this.reportDataFns[key] = this.clickLog(reportData)
    return this.reportDataFns[key]
  }

  removeClickLog(el: HTMLElement, key: string) {
    const fn = this.reportDataFns[key]
    el.removeEventListener('click', fn)
    this.reportDatas[key] = null
    this.reportDataFns[key] = null
  }

  logShowed(el: HTMLElement, reportData: IReportData) {
    focusCore.eventLog('showed', reportData.definedValue, reportData.definedInfo || {})
  }

  clickLog(reportData: IReportData): any {
    return (e: any) => {
      console.log('...自动点击上报~')
      focusCore.clickLog(reportData.definedValue, reportData.definedInfo || {})
    }
  }
}

class FocusDirectives {
  walog: any
  constructor() {
    this.walog = this.initWalog()
  }
  definedReportData(definedValue: string, definedInfo?: any) {
    return {
      definedValue,
      definedInfo: definedInfo || {},
    }
  }
  initWalog() {
    const directiveWalog = new DirectiveWalog()
    return {
      // 在绑定元素的 attribute 前
      // 或事件监听器应用前调用
      created(el: any, binding: any, vnode: any, prevVnode: any) {
        // console.log('🚀 ~created', el, binding, vnode, prevVnode)
        // 下面会介绍各个参数的细节
      },
      // 在元素被插入到 DOM 前调用
      beforeMount(el: any, binding: any, vnode: any, prevVnode: any) {
        // console.log('🚀 ~beforeMount', el, binding, vnode, prevVnode)
      },
      // 在绑定元素的父组件
      // 及他自己的所有子节点都挂载完成后调用
      mounted(
        el: HTMLElement,
        binding: {
          value: IReportData
          modifiers: {
            click: boolean
            show: boolean
          }
        },
        vnode: any,
        prevVnode: any,
      ) {
        // console.log('🚀 ~mounted', el, binding, vnode, prevVnode)
        const reportData = binding.value || {}
        const { modifiers } = binding

        if (modifiers.click) {
          directiveWalog.addClickLog(el, reportData)
        }
        if (modifiers.show) {
          directiveWalog.logShowed(el, reportData)
        }
        // console.log('🚀 ~ file: focusDirectives.ts ~ line 29 ~ DirectiveWalog ~ mounted ~ reportData', reportData)
      },
      // 绑定元素的父组件更新前调用
      beforeUpdate(el: any, binding: any, vnode: any, prevVnode: any) {},
      // 在绑定元素的父组件
      // 及他自己的所有子节点都更新后调用
      updated(el: any, binding: any, vnode: any, prevVnode: any) {
        console.log('🚀 ~updated', el, binding, vnode, prevVnode)
        const reportData = binding.value || {}
        const { modifiers } = binding

        if (modifiers.click) {
          directiveWalog.addClickLog(el, reportData)
        }
      },
      // 绑定元素的父组件卸载前调用
      beforeUnmount(el: HTMLElement, binding: any, vnode: any, prevVnode: any) {
        console.log('🚀 ~beforeUnmount', el, binding, vnode, prevVnode)

        directiveWalog.removeClickLog(el, binding.value)
      },
      // 绑定元素的父组件卸载后调用
      unmounted(el: any, binding: any, vnode: any, prevVnode: any) {
        console.log('🚀 ~unmounted', el, binding, vnode, prevVnode)
      },
    }
  }

  init(app: any) {
    app.directive('focuslog', this.walog)
  }
}

export default new FocusDirectives()
