import baseConfig from './plugins/baseConfig'
console.log('🚀 ~ file: index.ts ~ line 2 ~ baseConfig', baseConfig)
import env, { TypeEnv, EnumPlatform, EnumDevice } from './plugins/env'
console.log('🚀 ~ file: index.ts ~ line 4 ~ env', env)
import ajax, { EnumMethod, TcigItem } from './plugins/Ajax/index'
import waLog from './waLog'
import { loginService, wxServcie, mgmService } from '../index'
import md5 from 'js-md5'
import { Base64 } from 'js-base64'
import dayjs from '@/utils/time-utils'
import { focusService } from '../focusService'
import FocusRenderContrler from '../FocusRenderContrler'
import { focusStore } from '@/store'
import hjCoreIabPlus, { AppInvokeName } from './plugins/hjCoreIabPlus/hjCoreIabPlus'
import { StorageKey } from '@/consts/storage'
import { UrlParser } from '@/utils'
const cgis = {
  systemTime: {
    baseUrlKey: 'hjdata',
    url: '/hj/common/systimeinfo',
    name: '查询系统时间',
    canSaveTimes: true,
    setCredentialsFalse: true,
  },
}

enum KTestImages {
  Lossy = 'UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA', // 有损
  Lossless = 'UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==', // 无损
  Alpha = 'UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA==', // 透明
  Animation = 'UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA', // 动图
}

class FocusCore implements TFocusCore {
  env: TypeEnv
  baseConfig: any
  ajax: any
  waLog: typeof waLog
  loginData: any = {}
  cgiParams: any = {
    uid_type: '',
    h5_ver: 9999,
    app_ver: '*******',
    app_ver_code: 900909,
    terminal_type: 'Android',
    // Android iOS
    focus_ver: FOCUS_CORE_VER,
    focus_pageid: '',
  }
  focusRenderCtr: TFocusDataResolve | null
  _systemTimestamp: number = 0
  btnClickWatcher: any = null
  beforeBtnClickFn: any = null
  _traceAdTag: string = '' // 用来在请求头中各存的adtag
  // 如果要使用路由跳转模式
  vueRouterRedirect: {
    routerInstance: any
    rootPath: string
    hashList: string[]
  } = {
    routerInstance: null,
    rootPath: '',
    hashList: [],
  }

  // 页面被隐藏的监听
  pageVisibilityHandler: any

  // kaiqi预览模式
  isPreview: boolean = false

  get hasAccount(): boolean {
    return this.loginData.hasAccount
  }
  get isLogined(): boolean {
    return this.loginData.isLogined
  }
  get uidType(): string {
    return this.loginData.uidType
  }

  get mockData() {
    const urlQuery = new UrlParser().query
    const { mockTime, mockData, focus_preview, focus_review } = urlQuery
    if (!this.isPreview || (focus_preview !== '1' && focus_review !== '1')) {
      return {}
    }
    let mockDatas: any = {}
    try {
      const d = (mockData && Base64.decode(mockData)) || '{}'
      mockDatas = JSON.parse(d) || {}
      console.log('🚀 ~ FocusCore ~ getmockData ~ mockDatas:', mockDatas)
    } catch (err) {
      mockDatas = {}
    }

    const { mockGrayId, mockCondition } = mockDatas

    return {
      isPreview: this.isPreview,
      mockTime: !mockTime || mockTime === '0' ? null : mockTime,
      mockCondition,
      mockGrayId,
    }
  }

  constructor(props: any = {}) {
    console.log('FocusCore>>>>>>>>!!!')
    const parsed = new UrlParser()
    const { fid, aid, hj_h5_ver } = parsed.query
    if (hj_h5_ver) {
      this.cgiParams.h5_ver = hj_h5_ver
    }
    const { ajaxConfig = {}, commonConfig = {} } = props
    /* 挂载插件 */
    this.env = env
    this.baseConfig = baseConfig
    this.ajax = ajax
    this.waLog = waLog
    /* 初始化插件配置 */
    this.initPlugins({ ajaxConfig, commonConfig })
    this.focusRenderCtr = null
    this.btnClickWatcher = new BtnClickWatcher()
    this.cgiParams.focus_pageid = `${fid || 0}_${aid || 0}`

    this.pageVisibilityHandler = new PageVisibilityHandler()
    waLog.setGlobalParam({
      platform: `${this.env.platform},isInWxMini_${this.env.isInWxMini}`,
    })
  }

  useRefsData() {
    return
  }

  /**
   * TODO: 即将废弃
   */
  useTestMode(params?: { baseConfig: any }) {
    // console.log('focusCore use test mode')
    // const baseConfigParams = params?.baseConfig || {}
    // baseConfig.useTestMode(baseConfigParams)
  }

  usePreviewMode() {
    this.isPreview = true
  }

  initPlugins({ ajaxConfig = {}, commonConfig = {} }) {
    this.baseConfig.init(commonConfig)
    this.env.init()
    this.baseConfig.updateApiHost()

    this.cgiParams.terminal_type = this.env.device === EnumDevice.ios ? 'iOS' : 'Android'
    const parsed = new UrlParser()
    const { query } = parsed
    this.cgiParams.fid_aid = `${query.fid || 0}_${query.aid || 0}`
    this._traceAdTag = query.x_traceId
    console.error('🚀 ~ FocusCore ~ initPlugins ~ this._traceAdTag:', this._traceAdTag)

    this.ajax.init({
      interceptors: {
        requestInterceptors: (config: any) => {
          const originParams = JSON.stringify(config.params)
          config.params = {
            ...this.cgiParams,
          }
          config.hasAccount = this.hasAccount
          if (config.params && Object.keys(config.params).length) {
            config.params.param = originParams
          }

          // console.log('config.params', config)
          return config
        },
      },
      ...ajaxConfig,
    })
  }

  loginWx() {
    if (this.env.checkPlatformIs(EnumPlatform.wx)) {
      this.checkBaseConfig()
      console.error('loginwx')
      loginService.loginWx(false)
    }
  }

  login(noNeedLogin?: boolean): Promise<{
    dcnNo?: string
    hasAccount?: Boolean
    uidType?: string
    userId?: string
    userType?: string
    webankToken?: string
    wechatAccessToken?: string
    wechatAppId?: string
    wechatOpenId?: string
    wechatUnionId?: string
    error?: string
  }> {
    return new Promise((resolve, reject) => {
      loginService
        .checkLogined()
        .then((res) => {
          // console.log('🚀 ~ file: index.ts ~ line 34 ~ FocusCore ~ loginService.checkLogined ~ res', res)
          const { userId, wechatOpenId, wechatUnionId, uidType, qqOpenId, hasAccount, error } = res
          if (error === '401') {
            return resolve(res)
          }
          const { userStore } = focusStore.stores
          if (wechatOpenId) {
            userStore.wechatOpenId = wechatOpenId
          }
          this.loginData = res
          this.cgiParams.uid_type = uidType
          let MD5Ecif = ''
          if (userId && hasAccount) {
            MD5Ecif = md5(md5(userId).toUpperCase()).toUpperCase()
          }
          waLog.setGlobalParam({
            openId: uidType === 'qq' ? qqOpenId || wechatOpenId : wechatOpenId,
            unionid: wechatUnionId,
            uidType,
            MD5Ecif,
            platform: `${this.env.platform}-isInWxMini_${this.env.isInWxMini}`,
          })
          waLog.eventLog('querycookie_succ', hasAccount ? '1' : '0')
          resolve(res)
        })
        .catch((err) => {
          console.log('🚀 ~ file: index.ts ~ line 37 ~ FocusCore ~ loginService.checkLogined ~ err', err)
          waLog.eventLog('querycookie_fail', '0')
          reject(err)
        })
    })
  }

  getNowTime(needRealTime?: boolean): Promise<dayjs.Dayjs> {
    const parsed = new UrlParser()

    // 预览功能启用的时候，接mock时间！
    if (this.mockData.isPreview && this.mockData.mockTime) {
      console.error('要用模拟时间了！')
      const time = Number(this.mockData.mockTime) !== 0 ? Number(this.mockData.mockTime) : ''
      if (time) {
        console.log('🚀 ~ FocusCore ~ getNowTime ~ time:', time)
        return Promise.resolve(dayjs(time))
      }
    }

    if (!needRealTime && this._systemTimestamp) {
      const localTime = Date.now()

      if (localTime - dayjs(this._systemTimestamp).valueOf() < 3000) {
        console.error('本地时间没超过缓存时间3秒，直接返回缓存时间')
        // 5秒内不会再获取系统时间couponService.getCoupo
        return Promise.resolve(dayjs(this._systemTimestamp))
      }
    }

    return new Promise((resolve) => {
      this.request(cgis.systemTime)
        .then((res: any) => {
          const time = res.timestr || res.timestamp
          console.log('🚀 ~ 接口时间', time, dayjs(time).format('YYYY-MM-DD hh:mm:ss'))
          this._systemTimestamp = res.timestamp || Date.now()
          console.log('🚀 ~ ', this._systemTimestamp)

          return resolve(res.timestr ? dayjs(res.timestr) : dayjs())
        })
        .catch(() => {
          console.error('catch systemTime')
          resolve(dayjs())
        })
    })
  }

  showPageInTimes(isValid: boolean, startTime: number | string, endTime: number | string): Promise<boolean> {
    console.log('检查页面是否在可渲染时间....')
    let _startTime = startTime
    let _endTime = endTime

    if (!_startTime || !_endTime) {
      console.log('必须同时传入开始时间和结束时间！否则默认为 true')
      return Promise.resolve(true)
    }
    return new Promise((resolve, reject) => {
      this.getNowTime().then((now: any) => {
        const { modalStore } = focusStore.stores
        console.log('现在时间', now.format())
        console.log('开始时间', dayjs(_startTime).format())
        console.log('结束时间', dayjs(_endTime).format())
        const isBefore = isValid ? now.isBefore(_startTime) : false // 下架就是 false
        const isEnd = isValid ? now.isAfter(_endTime) : true // 下架就是结束
        const isDuring = isValid && now.isBetween(dayjs(_startTime), dayjs(_endTime)) // 在活动期间！
        if (isDuring) {
          console.log('在指定时间内，可以显示活动！')
          return resolve(true)
        }
        if (isBefore) {
          console.error(`活动还没开始`)
          modalStore.errorMaskContrl('pagetime_before', ['活动还没开始，请耐心等待哦'])
          return resolve(false)
        }
        if (isEnd) {
          console.error(`活动已结束`)
          modalStore.errorMaskContrl('pagetime_after', ['活动已结束，下次再来参与吧~'])
          return resolve(false)
        }
        return resolve(false)
      })
    })
  }

  request(cgi: TcigItem, urlQuery?: { [key: string]: any }, postBody?: { [key: string]: any }) {
    const { needAccount = false, noAccountRes = {} } = cgi
    // 如果该请求必须开户，且此时用户未开户，则返回默认的假数据
    if (needAccount && !this.hasAccount) {
      console.log('用户未开户，且该接口必须已开户')
      return Promise.resolve({ noAccount: true, ...noAccountRes })
    }
    const _cgi = cgi
    _cgi.headers = cgi.headers || {}
    if (this._traceAdTag) {
      _cgi.headers['x-wb-biz-traceId'] = `ADTAG_${this._traceAdTag}`
    }

    return this.ajax.request(_cgi, urlQuery || {}, postBody || {})
  }

  /*
   *检查baseconfig的内容
   *
   */
  checkBaseConfig() {
    const isUseNewHost = this.baseConfig.isUseNewHost
    console.log('🚀 ~ file: index.ts:281 ~ FocusCore ~ checkBaseConfig ~ isUseNewHost:', isUseNewHost)
    const baseHosts = this.baseConfig.baseHosts
    console.log('🚀 ~ file: index.ts:283 ~ FocusCore ~ checkBaseConfig ~ baseHosts:', baseHosts)
    const { query } = new UrlParser()
    const { fid } = query
    const local = localStorage.getItem(`${fid}_newHost`)
    console.log('🚀 ~ file: index.ts:288 ~ FocusCore ~ checkBaseConfig ~ local:', local)

    this.eventLog('useNewHost', isUseNewHost ? '1' : '0', {
      ...baseHosts,
      local,
    })
  }

  async init(config?: TFocusCoreInitConfig): Promise<TFocusInitResult> {
    const {
      focusFid = 0,
      ajaxConfig = {},
      commonConfig = {},
      focusAid = 0,
      noNeedLogin = false,
      useLocalModuleData = {},
    } = config || {}
    this.checkBaseConfig()

    console.log('🚀 ~ file: index.ts ~ line 95 ~ FocusCore ~ config', config)
    console.log('🚀 ~ FocusCore ~ init ~ useLocalModuleData:', useLocalModuleData)

    const { conditionStore } = focusStore.stores

    conditionStore.setCondition('dc06', this.env.device === 'ios' ? 2 : 1)
    conditionStore.setCondition('dc07', this.env.isInApp ? 2 : 1)

    return new Promise((resolve, reject) => {
      Promise.all([
        this.login(noNeedLogin),
        focusService.getConfig({ fid: focusFid, aid: focusAid, useLocalModuleData }),
        this.getNowTime(),
        this.checkWebpFeature(),
      ])
        .then(async (res) => {
          console.log('🚀 ~ file: index.ts ~ line 39 ~ FocusCore ~ Promise.all ~ res', res)
          const loginData = res[0]
          const _focusData = res[1]
          const nowTime = res[2]
          const surportWebp = res[3]
          console.log('focus的数据---->', _focusData)
          console.log('登录的数据---->', loginData)
          console.log('是否支持webp---->', surportWebp)
          this.checkBaseConfig()

          waLog.eventLog('is_surport_webp', surportWebp ? '1' : '0')
          const result: TFocusInitResult = {
            focusRenderCtr: null,
            loginData,
            nowTime,
          }

          const focusRenderCtr = new FocusRenderContrler({ focusData: _focusData })

          result.focusRenderCtr = focusRenderCtr
          this.focusRenderCtr = focusRenderCtr

          return resolve(result)
        })
        .catch((err) => {
          console.log('🚀 ~ file: index.ts ~ line 178 ~ FocusCore ~ ]).then ~ err', err)
          reject(err)
        })
    })
  }

  invokeApp = hjCoreIabPlus.invokeApp
  checkAppFn = hjCoreIabPlus.checkAppFn
  getEncryptData = hjCoreIabPlus.getEncryptData

  setLocalData(key: string, value: { [key: string]: any }): Promise<{ key; value }> {
    return new Promise((resolve) => {
      if (this.env.isInApp) {
        hjCoreIabPlus
          .setAppCache(key, JSON.stringify(value))
          .then((res) => {
            resolve(res)
          })
          .catch(({ error }) => {
            if (error === 'no_api') {
              window.localStorage.setItem(key, JSON.stringify(value))
              return resolve({ key, value })
            }
          })
      } else {
        window.localStorage.setItem(key, JSON.stringify(value))
        return resolve({ key, value })
      }
    })
  }
  getLocalData(key): Promise<{ key: string; value: { [key: string]: any } }> {
    return new Promise((resolve) => {
      if (this.env.isInApp) {
        hjCoreIabPlus
          .getAppCache(key)
          .then((res) => {
            console.log('🚀 ~ FocusCore ~ hjCoreIabPlus.getAppCache ~ res:', res)
            const { key, value } = res
            let _val = {}
            try {
              _val = JSON.parse(value) || {}
            } catch (err) {
              _val = {}
            }
            resolve({
              key,
              value: _val,
            })
          })
          .catch(({ error }) => {
            if (error === 'no_api') {
              const val = window.localStorage.getItem(key) || '{}'
              let result = {}
              try {
                result = JSON.parse(val)
              } catch (err) {
                result = {}
              }

              return resolve({ key, value: result })
            }
          })
      } else {
        const val = window.localStorage.getItem(key) || '{}'
        let result = {}
        try {
          result = JSON.parse(val)
        } catch (err) {
          result = {}
        }
        return resolve({ key, value: result })
      }
    })
  }

  clickLog(btnName: string, definedInfo: any) {
    waLog.clickLog(btnName, definedInfo)
  }

  eventLog(definedName: string, definedValue: string, definedInfo?: any) {
    waLog.eventLog(definedName, definedValue, definedInfo)
  }

  setPageTitle(title: string, customerService?: string) {
    if (title) {
      document.title = title

      if (focusCore.env.isInApp && window.hjCoreIab) {
        const hjCoreIab = window.hjCoreIab
        hjCoreIab.setNavBar({
          title: title,
          useAutoFixNavBar: true,
          customerServiceQuestion: customerService,
        })
      } else {
        if (/ip(hone|od|ad)/i.test(navigator.userAgent)) {
          var i = document.createElement('iframe')
          // i.src = '/favicon.ico'
          i.style.display = 'none'
          i.onload = function () {
            setTimeout(function () {
              i.remove()
            }, 9)
          }
          document.body.appendChild(i)
        }
      }
    }
  }

  checkWebpFeature() {
    const storageKey = StorageKey.surportWebp
    const cache: string | null = localStorage.getItem(storageKey)
    if (cache !== null) {
      return Promise.resolve(cache === 'true')
    }
    const arr: Promise<boolean>[] = []
    for (let key in KTestImages) {
      arr.push(
        new Promise((resolve) => {
          const img = new Image()
          img.onload = function () {
            const result = img.width > 0 && img.height > 0
            resolve(result)
          }
          img.onerror = function () {
            resolve(false)
          }
          img.src = 'data:image/webp;base64,' + KTestImages[key]
        }),
      )
    }
    return Promise.all(arr).then((res) => {
      const surportWebp = res.every((item: boolean) => item)
      localStorage.setItem(storageKey, String(surportWebp))
      return surportWebp
    })
  }

  updateDynamicData(key: string, value: any, isCustomKey: boolean) {
    console.log('🚀 ~ file: index.ts ~ line 345 ~ FocusCore ~ updateDynamicData ~ value', value)
    console.log('🚀 ~ file: index.ts ~ line 345 ~ FocusCore ~ updateDynamicData ~ key', key)
    const { dynimicDataStore } = focusStore.stores

    if (isCustomKey) {
      console.error('更新customkey~')
      dynimicDataStore.setDynimicDataByCustomKey(key, value)
    }
  }

  useVueRouterRedirect(routerInstance: any, rootPath: string, hashList: string[]) {
    console.error('启用vue router ！！！')
    this.vueRouterRedirect = {
      routerInstance: routerInstance || null,
      rootPath: rootPath || '',
      hashList: (hashList && hashList.length && hashList) || [],
    }
  }

  routerBeforeEach(
    to: {
      fullPath: string
      hash: string
      href: string
      matched: Array<any>
      meta: any
      name: string
      params: any
      path: string
      query: any
      redirectedFrom: string
    },
    from: {
      fullPath: string
      hash: string
      href: string
      matched: Array<any>
      meta: any
      name: string
      params: any
      path: string
      query: any
      redirectedFrom: string
    },
    next: Function,
  ) {
    const parsed = new UrlParser()
    const { path } = to
    if (path.indexOf('&wb_safe_code=1') > -1) {
      const { fid } = parsed.query
      const safeArr = path.split('&')
      safeArr.shift()
      const safeStr = safeArr.join('&')
      location.replace(`${location.origin}/s/hj/focus2/client/index.html?fid=${fid}&${safeStr}`)
    } else {
      const { title } = to.meta
      focusCore.setPageTitle(title)
      next()
    }
  }

  /**
   * 监听focus指定的id的按钮点击调用
   *
   * @param id
   * @param fn
   * @returns
   */
  onBtnClick(moduleId: string, fn: Function): number {
    return this.btnClickWatcher.addWatcher(moduleId, fn)
  }

  onRedirectToSpecialPath(path: string, fn: Function) {
    return this.btnClickWatcher.addWatcherForSpecialPath(path, fn)
  }

  beforeBtnClick(fn: any) {
    this.beforeBtnClickFn = fn
  }

  emitRedirectToSpecialPath(path: string) {
    this.btnClickWatcher.triggerSpecialPath(path)
  }

  emitBtnClick(params: { clickEvent: NClickEvent.item; moduleType: string; moduleId: string; next: Function }) {
    if (this.beforeBtnClickFn) {
      console.error('有监听拦截哦！')
      this.beforeBtnClickFn(
        { clickEvent: params.clickEvent, moduleType: params.moduleType, moduleId: params.moduleId },
        params.next,
      )
      return
    }
    this.btnClickWatcher.trigger({
      ...params,
      next: () => {
        console.log('........ervice.ts ~ line 67 ~ JumpService ~ vueRouterRedire', this.vueRouterRedirect)
        params.next && params.next({ vueRouterRedirect: this.vueRouterRedirect })
      },
    })
  }

  onPageVisibilityChange(key: string, cb: Function, status: 'show' | 'hide') {
    this.pageVisibilityHandler.appendCb(key, cb, status)
  }

  getTargetFidData(fid: number) {
    return new Promise((resolve) => {
      focusService.getConfig({ fid }).then((res) => {
        const focusRenderCtr = new FocusRenderContrler({ focusData: res })
        return resolve({
          shareConfigById: (id: number) => {
            return focusRenderCtr.getShareConfigByConfigId(id)
          },
        })
      })
    })
  }
}

class PageVisibilityHandler {
  showCb: any = {}
  hideCb: any = {}
  flag: boolean = false
  constructor() {}

  init() {
    console.log('初始化页面监听')
    if (this.flag) {
      console.error('已经监听啦，不需要重复监听')
      return
    }
    this.flag = true

    if (focusCore.env.isInApp) {
      console.error(' App 内可以用这个触发 RN页面的返回')
      // 订阅webview onResume事件
      window.hjCoreIab.onWebviewResume(() => {
        // 结果页 RN->H5 刷新接口
        try {
          console.log('这里是用 hjcore来执行的！')
          this.startShowCb()
        } catch (err) {
          console.log('hjcore resume出问题啦！', err)
        }
      })
    }

    // 同时订阅web的事件
    window.addEventListener('pageshow', (event) => {
      // 检测事件是否由缓存触发
      if (event.persisted) {
        console.log('页面是从缓存中加载的')
        // 这里可以执行需要在页面显示时进行的操作
        this.startShowCb()
      } else {
        console.log('页面是初次加载的')
        // 这里可以执行需要在页面首次加载时进行的操作
      }
    })
  }

  appendCb(key: string, cb: Function, status: 'show' | 'hide') {
    if (status === 'show') {
      console.log('注册一个show')
      this.showCb[key] = cb
    } else if (status === 'hide') {
      console.log('注册一个hide')
      this.hideCb[key] = cb
    }
    this.init()
  }

  startHideCb() {
    Object.keys(this.hideCb).forEach((i) => {
      const fn = this.hideCb[i]
      if (fn) {
        fn()
      }
    })
  }

  startShowCb() {
    Object.keys(this.showCb).forEach((i) => {
      console.log('现在执行', i)
      const fn = this.showCb[i]
      if (fn) {
        fn()
      }
    })
  }
}

class BtnClickWatcher {
  fnObj: any = {}
  fnObjForSpecialPath: any = {}
  constructor() {}
  addWatcher(moduleId: string, fn: Function): string {
    this.fnObj[moduleId] = fn
    return moduleId
  }
  addWatcherForSpecialPath(path: string, fn: Function) {
    this.fnObjForSpecialPath[path] = fn
    return path
  }
  removeWatcher(id: string) {
    delete this.fnObj[id]
  }
  triggerSpecialPath(path: string) {
    console.log('🚀 ~ file: index.ts ~ line 408 ~ BtnClickWatcher ~ triggerSpecialPath ~ path', path)
    console.log(
      '🚀 ~ file: index.ts ~ line 410 ~ BtnClickWatcher ~ triggerSpecialPath ~ this.fnObjForSpecialPath',
      this.fnObjForSpecialPath,
    )
    console.log(this.fnObjForSpecialPath[path] && typeof this.fnObjForSpecialPath[path] === 'function')
    if (this.fnObjForSpecialPath[path] && typeof this.fnObjForSpecialPath[path] === 'function') {
      console.log('有方法！触发他！')
      try {
        this.fnObjForSpecialPath[path]()
      } catch (err) {
        console.log('🚀 ~ file: index.ts ~ line 408 ~ BtnClickWatcher ~ triggerSpecialPath ~ err', err)
      }
    }
  }
  trigger(params: { clickEvent: any; moduleType: string; moduleId: string; next: Function }) {
    const key = params.moduleId.toString()
    const targetFn = this.fnObj[key]
    if (typeof targetFn === 'function') {
      try {
        targetFn(params)
      } catch (err) {
        console.log('🚀 ~ file: index.ts ~ line 338 ~ BtnClickWatcher ~ this.list.forEach ~ err', err)
      }
    } else {
      params.next && params.next()
    }
  }
}

export const focusCore = new FocusCore()

export default focusCore
