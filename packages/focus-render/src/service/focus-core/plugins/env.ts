import { UrlParser } from '@/utils'

export enum EnumPlatform {
  wx = 'wx',
  app = 'app',
  qq = 'qq',
  other = 'other',
}
export enum EnumWxMini {
  none = 'none',
  hj = 'hj',
  we2000 = 'we2000',
  other = 'other',
}

export enum EnumDevice {
  ios = 'ios',
  android = 'android',
  other = 'other',
}

export interface TypeEnv {
  ua: NavigatorID['userAgent']
  device: EnumDevice
  platform: EnumPlatform
  wxMini: EnumWxMini
  isInApp: boolean
  isInWx: boolean
  isInWxMini: boolean
  testServerEnv: string
  isIpv6: boolean
  checkPlatformIs: (arg0: EnumPlatform) => boolean
  init: () => void
  checkIsInWxMini: () => Promise<boolean>
}
const locationHrefPared = new UrlParser()
class Env implements TypeEnv {
  ua: string = ''
  device: EnumDevice
  platform: EnumPlatform
  wxMini: EnumWxMini
  testServerEnv: string = ''
  isInApp: boolean = false
  isIpv6: boolean = false
  isInWx: boolean = false
  isInWxMini: boolean = false
  constructor() {
    this.device = EnumDevice.other
    this.platform = EnumPlatform.other
    this.wxMini = EnumWxMini.none
    this.isInWxMini = false
  }
  init() {
    console.log('env init!!!')
    this.ua = window && window.navigator && window.navigator.userAgent.toLowerCase()
    this.initUrl()
    this.initDevice()
    this.initPlatform()
  }
  initUrl() {
    const urlQuery = locationHrefPared.query
    const { env } = urlQuery
    this.testServerEnv = env || 'k'
    this.isIpv6 = /personalv6/.test(location.origin)
  }

  initDevice() {
    let device: EnumDevice = EnumDevice.other
    if (/android/.test(this.ua)) {
      device = EnumDevice.android
    } else if (/(ios|ipad|iphone)/.test(this.ua)) {
      device = EnumDevice.ios
    }
    this.device = device
  }
  initPlatform() {
    const urlQuery = locationHrefPared.query
    let platform: EnumPlatform = EnumPlatform.other
    if (/micromessenger/.test(this.ua)) {
      platform = EnumPlatform.wx
    } else if (/v1_(and|iph)_sq_([\d\\.]+)/.test(this.ua) || /qq\/([\d\\.]+)/i.test(this.ua)) {
      platform = EnumPlatform.qq
    } else if (/webankapp/.test(this.ua) || (window && window.isProWebView) || urlQuery.isProWebView === 'true') {
      // 其实pro 也是这个UA，但是现在很多地方使用了 platform === 'hj' 来进行app 判定
      platform = EnumPlatform.app
    }
    this.platform = platform
    this.isInApp = this.checkPlatformIs(EnumPlatform.app)
    this.isInWx = this.checkPlatformIs(EnumPlatform.wx)
    if (window.wx && window.wx.miniProgram && this.isInWx) {
      window.wx.miniProgram.getEnv((res: any) => {
        console.log('🚀 ~ file: env.ts ~ line 89 ~ Env ~ window.wx.miniProgram.getEnv ~ res', res)
        this.isInWxMini = res.miniprogram || false
        if (this.isInWxMini) {
          console.error('现在在微信小程序哦！')
        }
      })
    }
  }
  checkIsInWxMini() {
    return new Promise<boolean>((resolve, reject) => {
      console.log('检查是否是小程序~。。。')
      if (window.wx && window.wx.miniProgram && this.isInWx) {
        window.wx.miniProgram.getEnv((res: any) => {
          console.log('🚀 ~查询是否是小程序', res)
          if (res.miniprogram) {
            console.error('现在在微信小程序哦！')
            return resolve(true)
          }
          return reject(false)
        })
      } else {
        console.error('现在不是微信小程序哦！')
        return reject(false)
      }
    })
  }
  checkPlatformIs(platformKey: EnumPlatform) {
    return this.platform === platformKey
  }
  chekAppH5Ver(params: { ios: number; android: number }): Promise<boolean> {
    console.log('🚀 ~ 检查app版本号~', params)
    const { ios, android } = params || {}
    if (!this.isInApp || this.device === EnumDevice.other) {
      return Promise.resolve(false)
    }
    return new Promise((resolve) => {
      const hjCoreIab = window.hjCoreIab
      hjCoreIab.getAppInfo((appInfo: { h5_ver: string; app_ver: string }) => {
        const { h5_ver } = appInfo
        if (this.device == EnumDevice.ios) {
          return resolve(Number(h5_ver) >= ios)
        }
        if (this.device === EnumDevice.android) {
          return resolve(Number(h5_ver) >= android)
        }
      })
    })
  }
}
export default new Env()
