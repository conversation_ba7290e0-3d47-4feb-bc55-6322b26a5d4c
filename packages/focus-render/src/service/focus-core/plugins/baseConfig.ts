import { UrlParser } from '@/utils'
import env from './env'

const hjAppIdProd = 'wx90bfe8ac7aa1338a'
export enum EnumNewBaseHost {
  Personal = 'https://${personalHostName}.${domain}/${server_env}',
  PersonalTest = 'https://${personalHostName}.test.${domain}/${server_env}',
  Hjdata = 'https://hjdatav6.webankwealth.com/${server_env}',
  HjdataTest = 'https://txyv6.test.webankwealth.com/${server_env}',
  CDN = 'https://dbd.webankwealthcdn.net/wm-resm',
  CDNTest = 'https://dbd.test.webankwealthcdn.net/wm-resm',
}

export enum EnumBaseHost {
  Personal = 'https://personal${ipType}.webank.com/${server_env}',
  PersonalTest = 'https://personal${ipType}.test.webank.com/${server_env}',
  Hjdata = 'https://hjdata${ipType}.webank.com/${server_env}',
  HjdataTest = 'https://txy.test.webank.com/${server_env}',
  CDN = 'https://dbd.webankwealthcdn.net/wm-resm',
  CDNTest = 'https://dbd.test.webankwealthcdn.net/wm-resm',
}

export enum EnumBaseHostKey {
  Personal = 'personal',
  Hjdata = 'hjdata',
  CDN = 'cdn',
  // File = 'file',
}
export interface TBaseConfig {}
class BaseConfig {
  baseHosts: {
    personal: string
    hjdata: string
    cdn: string
    [key: string]: string
  } = {
    personal: EnumBaseHost.Personal, //新域名
    hjdata: EnumBaseHost.Hjdata,
    cdn: EnumBaseHost.CDN,
  }
  isTestMode: boolean = BUILD_TEST

  hostName: string = 'personal'
  domain: string = 'webank.com'
  personalHostName: string = 'personal'

  _isUseNewHost: boolean = false // 默认不用新接口

  get isUseNewHost() {
    return this._isUseNewHost
  }

  init(config?: { baseHosts?: {} }) {
    console.log('BaseConfig init')
    const { baseHosts = {} } = config || {}
    // this.initbaseHosts(baseHosts)
  }

  useTestMode(params?: {
    testBaseHosts?: {
      [key: string]: string
    }
  }) {
    // console.log('env use test mode')
    // const baseHosts = params?.testBaseHosts || {}
    // if (!this._isUseNewHost) {
    //   this.baseHosts = Object.assign(this.baseHosts, {
    //     hjdata: EnumBaseHost.HjdataTest,
    //     personal: EnumBaseHost.PersonalTest,
    //     cdn: EnumBaseHost.CDNTest,
    //     ...baseHosts,
    //   })
    // } else {
    //   this.baseHosts = Object.assign(this.baseHosts, {
    //     hjdata: EnumNewBaseHost.HjdataTest,
    //     personal: EnumNewBaseHost.PersonalTest,
    //     cdn: EnumNewBaseHost.CDNTest,
    //     ...baseHosts,
    //   })
    // }
    // this.initbaseHosts()
  }

  initbaseHosts(baseHosts: any = {}) {
    this.baseHosts = Object.assign(this.baseHosts, baseHosts || {})
    const serverEnv = BUILD_TEST ? `/${env.testServerEnv || 'k'}` : ''

    let ipType = location.host.indexOf('v6') > -1 ? 'v6' : ''

    Object.keys(this.baseHosts).forEach((key) => {
      let baseUrl = this.baseHosts[key]
      if (!baseUrl) {
        return null
      }
      baseUrl = baseUrl.replace('${ipType}', ipType)
      baseUrl = baseUrl.replace('${hostName}', this.hostName)
      baseUrl = baseUrl.replace('${personalHostName}', this.personalHostName)
      baseUrl = baseUrl.replace('${domain}', this.domain)

      baseUrl =
        baseUrl.indexOf('txy') > -1
          ? baseUrl.replace('/${server_env}', '/tc-k')
          : baseUrl.replace('/${server_env}', serverEnv)
      this.baseHosts[key] = baseUrl
      console.log('🚀 ~ file: baseConfig.ts:95 ~ BaseConfig ~ Object.keys ~ this.baseHosts:', this.baseHosts)
    })

    // 换hjdata域名
    if (/(^m\.test.webank\.com$)|(^m\.webank\.com$)/.test(window.location.host)) {
      try {
        console.log('m.webnka.com 要换 hjdata域名')
        this.baseHosts.hjdata = this.baseHosts.hjdata.replace('webank.com', 'webankwealth.com')
      } catch (err) {}
    }
  }

  getBaseUrl(baseUrlKey?: EnumBaseHostKey | string): string | EnumBaseHost.Personal {
    if (baseUrlKey) {
      return this.baseHosts[baseUrlKey]
    }
    return this.baseHosts.personal
  }

  updateApiHost() {
    const urlHost = location.host
    this.personalHostName = urlHost.split('.') && urlHost.split('.')[0]

    if (/webankwealth/.test(urlHost)) {
      this.domain = 'webankwealth.com'
    } else if (/webankapp/.test(urlHost)) {
      this.domain = 'webankapp.com'
    } else {
      this.domain = 'webank.com'
    }

    // 这两个只有App内才会有
    if (/(webankwealth)|(webankapp)|(^m\.webank\.com$)/.test(urlHost)) {
      this._isUseNewHost = true
    }

    console.error(`在不在App里面？  ${env.isInApp}`)
    console.error(`访问的域名host是？ ${location.host}`)
    console.error(`现在能否使用新域名？${this._isUseNewHost}`)
    console.error(`现在要用的域名是`, this.hostName)

    if (this._isUseNewHost) {
      this.baseHosts = {
        hjdata: EnumNewBaseHost.Hjdata,
        personal: EnumNewBaseHost.Personal,
        cdn: EnumNewBaseHost.CDN,
      }
    }

    if (BUILD_TEST) {
      // 测试环境
      if (/^m\.test\.webank\.com$/.test(urlHost)) {
        this.personalHostName = 'm'
        this._isUseNewHost = true
      }
      if (!this._isUseNewHost) {
        this.baseHosts = Object.assign(this.baseHosts, {
          hjdata: EnumBaseHost.HjdataTest,
          personal: EnumBaseHost.PersonalTest,
          cdn: EnumBaseHost.CDNTest,
        })
      } else {
        this.baseHosts = Object.assign(this.baseHosts, {
          hjdata: EnumNewBaseHost.HjdataTest,
          personal: EnumNewBaseHost.PersonalTest,
          cdn: EnumNewBaseHost.CDNTest,
        })
      }
    }

    this.initbaseHosts()
  }

  get appId() {
    if (this.isTestMode) {
      const appIds = {
        k: 'wx633b1f56cc1ff95f',
        // k: 'wxdc3dce3bdd8339a5',
        i: 'wx66ecc9fad0a325fc',
        l: 'wx106b275d7f0ed823',
        h: 'wx92b30036ab8ea730',
        ck: 'wxdc3dce3bdd8339a5',
        cm: 'wx712b82f5bd3a5c5e',
        o: 'wx633b1f56cc1ff95f',
        m: 'wx6893f66ec534da32',
        m1: 'wx633b1f56cc1ff95f',
        e: 'wxdc3dce3bdd8339a5',
        g1: 'wxdc3dce3bdd8339a5',
      }
      if (env.testServerEnv) {
        const key: string = env.testServerEnv || 'k'
        if (Object.keys(appIds).indexOf(key)) {
          return appIds[key]
        }
      }
      return appIds.k
    }
    return hjAppIdProd
  }
}

export default new BaseConfig()
