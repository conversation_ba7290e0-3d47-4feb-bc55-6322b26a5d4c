import { Request } from './Request'
import type { RequestConfig } from './Request'
import baseConfig from '../baseConfig'
import { successLog, failLog, requestLog } from './logger'
import { EnumBaseHostKey } from '../baseConfig'
import hjCoreIabPlus from '../hjCoreIabPlus/hjCoreIabPlus'
export interface AjaxInitConfig extends RequestConfig {}
export interface TAjax {
  init: (config: AjaxInitConfig) => void
}
/**
 * cgi相关
 */
export enum EnumMethod {
  GET = 'get',
  POST = 'post',
}

export interface TcigItem {
  name: string
  url: string
  method?: EnumMethod
  baseUrlKey?: EnumBaseHostKey | string
  baseUrl?: string
  needAccount?: boolean
  noAccountRes?: any
  timeout?: number
  canSaveTimes?: boolean
  cacheTimeOut?: number // 缓存超时时间。 设置了缓存超时时间后，可以在指定时间内不发出请求，只使用缓存数据
  cacheWithQueryKeys?: string[]
  headers?: any
  setCredentialsFalse?: boolean
}
export class Ajax implements TAjax {
  countNums: number = 0
  instance: any = null
  commonParams: { [key: string]: string } = {}
  customRequestInterceptors: any = null
  customResponseInterceptors: any = null
  hasAccount = false
  queryList: any[] = []
  constructor() {}
  init(config?: AjaxInitConfig) {
    this.instance = new Request({
      timeout: 10000,
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      interceptors: {
        // 请求拦截器
        requestInterceptors: (config) => {
          // console.log('🚀 ~ file: index.ts:53 ~ Ajax ~ init ~ config:', config)
          // console.log('实例请求拦截器>>>>>>')
          const { method, baseURL, headers, url = '' } = config
          if (config.headers) {
            config.headers = {
              ...config.headers,
            }
          }
          if (method === 'get' && baseURL && baseURL.indexOf('personal.') < 0 && url?.indexOf('.json') > -1) {
            if (config.headers) {
              config.headers['Content-Type'] = 'application/x-www-form-urlencoded'
            }
          }
          if (method === 'get' && url?.indexOf('.svga') > -1) {
            // console.log('🚀 ~ file: index.ts ~ line 57 ~ Ajax ~ init ~ config', config)
            if (config.headers) {
              config.responseType = 'arraybuffer'
              config.headers['Content-Type'] = 'application/x-www-form-urlencoded'
            }
          }
          if (this.customRequestInterceptors) {
            const newConfig = this.customRequestInterceptors(config)
            if (!this.hasAccount) {
              this.hasAccount = newConfig.hasAccount
            }

            return newConfig
          }
          return config
        },
        // 响应拦截器
        responseInterceptors: (result: any) => {
          // console.log('实例响应拦截器', result)
          const { isEnrypt, data } = result
          if (isEnrypt) {
            return new Promise((resolve) => {
              hjCoreIabPlus.decryptData(data).then(
                (result) => {
                  resolve(result)
                },
                () => {
                  console.log('解密响应体失败')
                  resolve({})
                },
              )
            })
          }
          if (this.customResponseInterceptors) {
            return this.customResponseInterceptors(data)
          }
          return data
        },
        responseInterceptorsCatch: (err) => {
          console.log('实例响应拦截器____ERROR')
          console.log(err)

          return Promise.reject(err)
        },
      },
    })
    const { responseInterceptors, requestInterceptors } = config?.interceptors || {}
    if (requestInterceptors) {
      this.customRequestInterceptors = requestInterceptors
    }
    if (responseInterceptors) {
      this.customResponseInterceptors = responseInterceptors
    }
  }

  clearAllQueryList(url: string, type: 'resolve' | 'reject', data: any) {
    console.error(`现在来清理接口的数据了 ${url}`)
    // console.log(JSON.stringify(this.queryList[url]))
    const target = this.queryList[url]
    if (target && target.list && target.list.length) {
      console.log('开始返回之前需要接口的结果。。。。', url)
      this.queryList[url].list.forEach((item: any, index: number) => {
        console.log(`处理 第${index + 1}个的 ${type}，还有${this.queryList[url].list.length - index - 1}个`)
        item[type](data)
      })
    }
    // 如果需要保存chache
    if (target.queryDataExpireTime && (!target.reject || !target.resolve)) {
      this.queryList[url][type] = data
      console.log(`${url} 保存了缓存，缓存是 ${data}`)
    } else {
      this.queryList[url] = null
    }
  }

  /**
   * 使用缓存！
   * @param url
   * @param redoFn
   * @returns
   */
  ajaxUseCache(url: string, redoFn: Function) {
    const target = this.queryList[url]
    if (target.resolve) {
      console.log('有 resolve 缓存')
      return Promise.resolve(target.resolve)
    } else if (target.reject) {
      console.log('有 reject 缓存')
      return Promise.reject(target.reject)
    }
    console.log('没有查到有返回的缓存，清空重来！')
    this.queryList[url] = null
    if (redoFn) {
      return redoFn()
    }
  }

  /**
   * 检查缓存是否可以被使用
   *
   */
  checkQueryCache(url: string): boolean {
    const target = this.queryList[url]
    console.log(`🚀 ~ 查询是否有缓存可用${url}`, target)
    if (target && target.queryDataExpireTime) {
      console.log('配置里可以用缓存')
      if (target.resolve || target.reject) {
        console.log('存在缓存的数据')
        if (Date.now() < target.queryDataExpireTime) {
          console.log(`${url} 缓存没超时哦！继续使用`)
          return true
        } else {
          console.log(`${url} 缓存已经超时，清空配置`)
          this.queryList[url] = null
        }
      } else {
        console.log('没缓存可用！')
      }
    }

    return false
  }

  request(cgi: TcigItem, urlQuery = {}, postBody = {}) {
    // console.log('🚀 ~ 发送请求的参数！', cgi)
    this.countNums++
    const {
      method = 'get',
      url = '',
      baseUrlKey = '',
      baseUrl = '',
      timeout = 10000,
      canSaveTimes = false,
      cacheTimeOut = 0,
      cacheWithQueryKeys = [],
      headers = {},
      setCredentialsFalse = false,
    } = cgi

    // 接口请求过程中，重复请求，只使用第一次请求返回的数据，属于永久性的数据缓存
    let cacheKey = url
    if (canSaveTimes) {
      const queryDataExpireTime = cacheTimeOut ? Date.now() + cacheTimeOut : 0
      console.log('🚀 设置超时时间！', queryDataExpireTime)
      if (cacheWithQueryKeys.length) {
        const queryKeys = cacheWithQueryKeys
          .map((i) => {
            const val = method === 'post' ? postBody[i] : urlQuery[i]

            return val
          })
          .join('_')
        cacheKey = cacheKey + `_${queryKeys}`
        console.log('🚀 ~ file: index.ts:217 ~ Ajax ~ request ~ cacheKey:', cacheKey)
      }
      // 先检查有没有缓存
      if (this.checkQueryCache(cacheKey)) {
        console.error('我要用缓存哦！')
        return this.ajaxUseCache(cacheKey, () => {
          // 缓存不可用的话，清空并重新请求
          return this.request(cgi, urlQuery, postBody)
        })
      }

      if (!this.queryList[cacheKey]) {
        console.log(cacheKey + ' 需要缓存接口，先新建一个')
        this.queryList[cacheKey] = {
          dataRid: this.countNums,
          isConnetting: false,
          list: [],
          queryDataExpireTime, // 这个接口缓存的有效期，如果是0 就是单次，用完清除
        }
      }

      // 正在请求中
      if (this.queryList[cacheKey].isConnetting) {
        console.log(`${cacheKey} 接口正在请求中哦！`)
        if (cacheWithQueryKeys.length) {
          console.log(
            `接口 ${cacheKey} 正在请求，请求参数包含了${Object.keys(urlQuery)
              .map((key) => {
                const val = urlQuery[key]
                return `${key}:${val}`
              })
              .join(';')}该数据由 请求-${this.queryList[cacheKey].dataRid} 接口中返回！一共有 ${
              this.queryList[cacheKey].list.length + 2
            } 个需要这个接口数据`,
          )
        } else {
          console.log(
            `接口 ${cacheKey} 正在请求，该数据由 请求-${this.queryList[cacheKey].dataRid} 接口中返回！一共有 ${
              this.queryList[cacheKey].list.length + 2
            } 个需要这个接口数据`,
          )
        }

        return new Promise((resolve, reject) => {
          this.queryList[cacheKey].list.push({ resolve, reject, rid: this.countNums })
        })
      }
      console.log('接口要先改为正在请求状态')
      this.queryList[cacheKey].isConnetting = true
    }
    let customHeaders = headers ? headers : {}

    return new Promise(async (resolve, reject) => {
      const rid = this.countNums
      const _baseUrl = baseUrl ? baseUrl : baseConfig.getBaseUrl(baseUrlKey)
      const startTime = Date.now()
      requestLog({ cgi, method, urlQuery, postBody, rid, baseUrl: _baseUrl })

      let _postBody = postBody
      if (customHeaders.encrypt === 'Y' && method === 'post') {
        const { signature, encryptedData } = await hjCoreIabPlus.getEncryptData(postBody)
        console.log('getEncryptData', signature, encryptedData)
        _postBody = encryptedData
        customHeaders = {
          ...customHeaders,
          signature,
        }

        console.log('signature', signature)
        console.log('_postBody', _postBody)
        console.log('_headers', customHeaders)

        //加密失败
        if (signature === undefined || encryptedData === undefined) {
          console.log('加密失败')
          return reject({ isEncryptErr: true })
        }
      }

      this.instance
        .request({
          baseURL: _baseUrl,
          url,
          method,
          params: { ...urlQuery },
          data: _postBody,
          timeout,
          headers: customHeaders,
          withCredentials: !setCredentialsFalse,
        })
        .then((res: any) => {
          const { ret_code, ret_data = {}, errString } = res
          const spaceTime = Date.now() - startTime
          if (errString) {
            console.log(JSON.parse(errString))
          }
          if (url.indexOf('.json') > -1) {
            successLog({
              cgi,
              method,
              baseUrl: _baseUrl,
              urlQuery,
              postBody,
              responseData: res,
              rid,
              spaceTime,
            })
            return resolve(res)
          }
          if (/0000$/.test(ret_code)) {
            successLog({
              cgi,
              method,
              baseUrl: _baseUrl,
              urlQuery,
              postBody,
              responseData: res || {},
              rid,
              spaceTime,
            })
            if (canSaveTimes) {
              console.info('请求成功！，准备清空queryList', this.queryList)
              this.clearAllQueryList(cacheKey, 'resolve', ret_data || {})
            }
            return resolve(ret_data || {})
          }
          failLog({
            cgi,
            method,
            urlQuery,
            postBody,
            responseData: res || {},
            rid,
            baseUrl: _baseUrl,
            wholeUrl: `${_baseUrl}${url}`,
            spaceTime,
            httpStatus: 200,
          })
          if (canSaveTimes) {
            this.clearAllQueryList(cacheKey, 'reject', res)
          }

          return reject(res)
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: index.ts:261 ~ Ajax ~ returnnewPromise ~ err:', err)
          const { resData = {}, fullUrl, status = 0, errString } = err || {}
          const spaceTime = Date.now() - startTime

          failLog({
            cgi,
            method,
            urlQuery,
            postBody,
            baseUrl: _baseUrl,
            responseData: resData,
            rid,
            wholeUrl: fullUrl,
            httpStatus: status,
            spaceTime,
            errString,
          })
          if (canSaveTimes) {
            this.clearAllQueryList(cacheKey, 'reject', err)
          }
          reject(err)
        })
    })
  }
}

export default new Ajax()
