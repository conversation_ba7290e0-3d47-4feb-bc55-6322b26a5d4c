const color = {
  blue: '#0074D9',
  red: '#FF4136',
  pink: '#B10DC9',
  orange: '#FF851BB0',
  orange2: '#ffa500',
  green: '#3D9970',
  green2: '#7fb80e',
  gray: '#74787c',
  yellow: '#F9CE00',
}
import waLog from '../../waLog'

class Logger {
  logRequest({ cgi, method, urlQuery, postBody, rid, baseUrl }: any) {
    console.log(`发送请求-${rid}-${method}-${cgi.name}-${cgi.url}`)
    console.log(
      `%cRequest-${rid}%c %c${method} ${cgi.name} \n baseUrl: ${baseUrl} \n path: ${cgi.url} \n urlQuery: %O \n postBody: `,
      'color: white; background-color: #0074D9B0; padding: 2px 5px; border-radius: 2px',
      '',
      `color: ${color.blue}`,
      urlQuery,
      postBody,
    )
    console.log('\n --------------')
  }

  logResponse({ cgi, method, urlQuery, postBody, responseData, rid, baseUrl }: any) {
    console.log(`-------返回请求-${rid}-${method}-${cgi.name}-${cgi.url}`)
    console.log(
      `%cResponse-${rid}%c %c${method} ${cgi.name} \n baseUrl: ${baseUrl} \n path: ${cgi.url} \n urlQuery: %O \n postBody: %O \n responseData: `,
      'color: white; background-color: #3D9970B0; padding: 2px 5px; border-radius: 2px',
      '',
      `color: ${color.green}`,
      urlQuery,
      postBody,
      JSON.parse(JSON.stringify(responseData)),
    )
    console.log('--------------')
  }

  logErrorResponse({ wholeUrl, cgi, method, urlQuery, postBody, responseData, rid, httpStatus, baseUrl }: any) {
    console.error(`%c⚠️❎⚠️返回失败-${rid}-${method}-${cgi.name}-${cgi.url}`, 'color: #ff4136')
    console.log(
      `%cResponse-${rid}%c %c${method || 'get'} ${cgi.name} \n baseUrl: ${baseUrl} \n path: ${
        cgi.url
      }  \n httpStatus: ${httpStatus} \n urlQuery: %O \n postBody: %O \n responseData:  `,
      'color: white; background-color: #FF4136B0; padding: 2px 5px; border-radius: 2px',
      '',
      `color: ${color.red}`,
      urlQuery,
      postBody,
      JSON.parse(JSON.stringify(responseData)),
    )
    console.log('--------------')
  }

  logHitCache({ cgi, method, params, data, rid }: any) {
    console.log(
      `%cCache-${rid}%c %c${method} ${cgi.name} \n url: ${cgi.url} \n params: %O \n data: %O`,
      'color: white; background-color: #B10DC9B0; padding: 2px 5px; border-radius: 2px',
      '',
      `color: ${color.pink}`,
      params,
      data,
    )
  }

  logMockResponse({ cgi, params, data, rid }: any) {
    console.log(
      `%cMock-${rid}%c %c${cgi.method} ${cgi.name} \n url: ${cgi.url} \n params: %O \n response: %O`,
      'color: white; background-color: #FF851BB0; padding: 2px 5px; border-radius: 2px',
      '',
      `color: ${color.orange}`,
      params,
      data,
    )
  }

  logActionChange(key: any, oldValue: any, newValue: any) {
    console.log(
      `%cAction-${key}%c \n prev store: %O %c\n next store: %O`,
      `color: white; background-color: ${color.yellow}; padding: 2px 5px; border-radius: 2px`,
      `color: ${color.gray}`,
      oldValue,
      `color: ${color.green2}`,
      newValue,
    )
  }

  logWaStorage(arg: any[]) {
    console.log(
      `%c WA-storage %c \n defined_name: ${arg[1]} \n defined_info: %O`,
      'color: white; background-color: #ff8c00; padding: 2px 5px; border-radius: 2px',
      '',
      arg[3],
    )
  }

  logWaStat(arg: any[]) {
    console.log(
      `%c WA-clickStat %c \n defined_name: ${arg[0]} \n defined_info: %O`,
      'color: white; background-color: #ff8c00; padding: 2px 5px; border-radius: 2px',
      '',
      arg[2],
    )
  }

  logWarn(arg: any[]) {
    console.log(
      `%c WA-warn %c \n defined_name: ${arg[0]} \n defined_info: %O`,
      'color: white; background-color: #ff8c00; padding: 2px 5px; border-radius: 2px',
      '',
      arg[2],
    )
  }

  logWaStorageReport() {
    console.log(
      '%c WA-storage-report %c POST wa批量上报发送(h5_batch_data)',
      'color: white; background-color: #ff8c00; padding: 2px 5px; border-radius: 2px',
      `color: ${color.orange2}`,
    )
  }
}

const logger = new Logger()
export function successLog(config: {
  cgi: any
  method: string
  urlQuery: any
  postBody: any
  responseData: any
  rid: number
  spaceTime: number
  baseUrl: string
}) {
  const { cgi, urlQuery, postBody, responseData, rid, method, spaceTime, baseUrl } = config

  const { ret_code = '', hj_biz_no = '' } = responseData || {}
  waLog.logAjaxResponse({
    method,
    url: cgi.url,
    ret_code,
    hj_biz_no,
    resStatus: 'succ',
    rid,
    spaceTime,
    urlQuery,
    postBody,
    baseUrl,
    httpStatus: 200,
  })

  logger.logResponse({ cgi, method, urlQuery, postBody, responseData, rid, baseUrl })
}
export function failLog(config: {
  cgi: any
  urlQuery: any
  postBody: any
  responseData: any
  rid: number
  wholeUrl: string
  httpStatus: number
  method: string
  spaceTime: number
  errString?: string
  baseUrl?: string
}) {
  const { cgi, urlQuery, postBody, responseData, rid, wholeUrl, httpStatus, method, spaceTime, errString, baseUrl } =
    config
  // console.log('🚀 ~ file: logger.ts:168 ~ httpStatus:', httpStatus)
  // console.log('🚀 ~ file: logger.ts:168 ~ errString:', errString)
  // console.log('原始的失败数据...')
  // console.log(responseData)

  if (httpStatus.toString().indexOf('Network') > -1) {
    console.error('网络错误！')
    waLog.eventLog('error.ajax', `${httpStatus}`, {
      method,
      url: cgi.url,
      rid,
      spaceTime,
      urlQuery,
      postBody,
      baseUrl,
    })
  }

  if (errString) {
    console.error('彻底挂啦！')
    waLog.logAjaxResponse({
      method,
      url: cgi.url,
      resStatus: 'fail',
      httpStatus,
      rid,
      spaceTime,
      urlQuery,
      postBody,
      errString,
      originErrorRes: responseData,
      baseUrl,
    })
    return false
  }
  const { ret_code = '', hj_biz_no = '' } = responseData || {}

  waLog.logAjaxResponse({
    method,
    url: cgi.url,
    ret_code,
    hj_biz_no,
    resStatus: 'fail',
    rid,
    spaceTime,
    urlQuery,
    postBody,
    httpStatus,
    originErrorRes: responseData,
    baseUrl,
  })

  logger.logErrorResponse({
    cgi,
    method,
    urlQuery,
    postBody,
    responseData,
    rid,
    wholeUrl,
    httpStatus,
    baseUrl,
  })
}
export function requestLog({
  cgi,
  method,
  urlQuery,
  postBody,
  rid,
  baseUrl,
}: {
  cgi: any
  urlQuery: any
  postBody: any
  rid: number
  method: string
  baseUrl: string
}) {
  waLog.logAjaxRequest({ method, url: cgi.url, urlQuery, postBody, rid, baseUrl })
  logger.logRequest({ cgi, method, urlQuery, postBody, rid, baseUrl })
}
export default logger
