import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

export interface RequestInterceptors {
  // 请求拦截
  requestInterceptors?: (config: AxiosRequestConfig & { customHeaders?: any }) => AxiosRequestConfig
  requestInterceptorsCatch?: (err: any) => any
  // 响应拦截
  responseInterceptors?: <T = AxiosResponse>(config: T) => T
  responseInterceptorsCatch?: (err: any) => any
}

// 自定义传入的参数
export interface RequestConfig extends AxiosRequestConfig {
  interceptors?: RequestInterceptors
}

export class Request {
  // axios 实例
  instance: AxiosInstance
  // 拦截器对象
  interceptorsObj?: RequestInterceptors

  constructor(config: RequestConfig) {
    this.instance = axios.create(config)
    this.interceptorsObj = config.interceptors

    this.instance.interceptors.request.use(
      (res: AxiosRequestConfig) => {
        // console.log('全局请求拦截器')
        return res
      },
      (err: any) => err,
    )

    // 使用实例拦截器
    this.instance.interceptors.request.use(
      this.interceptorsObj?.requestInterceptors,
      this.interceptorsObj?.requestInterceptorsCatch,
    )

    // 全局响应拦截器
    this.instance.interceptors.response.use(
      // 因为我们接口的数据都在res.data下，所以我们直接返回res.data
      (res: AxiosResponse) => {
        // console.log('全局响应拦截器', res)
        if (res.headers['encrypt'] === 'Y') {
          return {
            data: res.data,
            isEnrypt: true,
          }
        }
        return {
          data: res.data,
        }
      },
      (err: any) => {
        // console.log('全局响应拦截器>>>>>err')

        // console.log('axios errorData.......')
        // console.log(err)
        if (err.response) {
          new Error(err.response.data)
          return Promise.reject({
            resData: err.response.data,
            fullUrl: err.response?.config?.url,
            status: err.response.status,
          })
        } else {
          const { config, message = 'ajax_err' } = err
          console.log('🚀 ~ file: Request.ts ~ line 62 ~ Request ~ constructor ~ config', config)
          return Promise.reject({
            resData: err,
            fullUrl: config && config.url,
            status: message,
          })
        }
      },
    )
    // 实例响应拦截器
    this.instance.interceptors.response.use(
      this.interceptorsObj?.responseInterceptors,
      this.interceptorsObj?.responseInterceptorsCatch,
    )
  }
  request<T>(config: RequestConfig): Promise<T> {
    return new Promise((resolve, reject) => {
      this.instance
        .request<any, T>(config)
        .then((data) => {
          resolve(data)
        })
        .catch((err: any) => {
          reject(err)
        })
    })
  }
}
