/* istanbul ignore next */
export default class WebViewProtocol {
  promiseChain: any
  promises: any
  callbacks: any

  constructor() {
    // this.hackPostMessage()
    // awaitPostMessage hack
    try {
      this.awaitPostMessageHack()
    } catch (e) {
      console.warn('WebViewProtocol.constructor awaitPostMessageHack', e)
    }
    // init protocol
    this.initProtocol()
  }

  hackPostMessage() {
    const messageQueue: any[] = []
    let messagePending: boolean = false

    function processQueue() {
      if (!messageQueue.length || messagePending) return
      messagePending = true
      window.location.href = 'react-js-navigation://postMessage?' + encodeURIComponent(messageQueue.shift())
    }

    const postMessage = function (data: any) {
      if (navigator.userAgent && navigator.userAgent.toLowerCase().indexOf('android') !== -1) {
        __REACT_WEB_VIEW_BRIDGE.postMessage(String(data), '')
      } else if (navigator.userAgent && navigator.userAgent.toLowerCase().indexOf('iphone') !== -1) {
        messageQueue.push(String(data))
        processQueue()
      }
    }

    document.addEventListener('message:received', function (e) {
      messagePending = false
      processQueue()
    })
  }

  // @see https://github.com/facebook/react-native/issues/11594
  awaitPostMessageHack() {
    let isReactNativePostMessageReady = !!window.originalPostMessage
    const queue: any[] = []
    let currentPostMessageFn = function store(message: any) {
      if (queue.length > 100) queue.shift()
      queue.push(message)
    }
    if (!isReactNativePostMessageReady) {
      const originalPostMessage = window.postMessage
      Object.defineProperty(window, 'postMessage', {
        configurable: true,
        enumerable: true,
        get: function () {
          return currentPostMessageFn
        },
        set: function (fn) {
          currentPostMessageFn = fn
          isReactNativePostMessageReady = true
          setTimeout(sendQueue, 0)
        },
      })
      window.postMessage.toString = function () {
        return String(originalPostMessage)
      }
    }

    function sendQueue() {
      while (queue.length > 0) window.postMessage(queue.shift(), '')
    }
  }

  // @see https://gist.github.com/blankg/d5537a458b55b9d15cb4fd78258ad840
  initProtocol() {
    // chains
    this.promiseChain = Promise.resolve()
    this.promises = {}
    this.callbacks = {}

    // receive (listener)
    window.document.addEventListener('message', (e: any) => {
      console.log('message received from react native', e.data)

      let message
      try {
        message = JSON.parse(e.data)
      } catch (err) {
        console.error('failed to parse message from react-native ' + err)
        return
      }

      // resolve promise - send next message if available
      // if (this.promises[message.msgId]) {
      //   this.promises[message.msgId].resolve()
      //   delete this.promises[message.msgId]
      // }

      // trigger callback
      if (this.callbacks[message.msgId]) {
        if (message.emitEvent) {
          this.callbacks[message.msgId].emitEvent(message.args)
        } else {
          if (message.isSuccess) {
            this.callbacks[message.msgId].onsuccess(message.args)
          } else {
            this.callbacks[message.msgId].onerror(message.args)
          }
        }

        if (!message.keepCallback) {
          delete this.callbacks[message.msgId]
        }
      }
    })
  }

  // send (postMessage)
  send(targetFunc: any, data: any, success: any, error: any, emitEvent: any) {
    success =
      success ||
      function () {
        console.log('success function')
      }
    error =
      error ||
      function () {
        console.log('error function')
      }
    emitEvent =
      emitEvent ||
      function () {
        console.log('emitEvent function')
      }

    const msgObj: any = {
      targetFunc,
      data: data || {},
      msgId: this.guid(),
    }

    const msg = JSON.stringify(msgObj)

    console.log('sending message ' + msgObj.targetFunc)

    this.callbacks[msgObj.msgId] = {
      onsuccess: success,
      onerror: error,
      emitEvent: emitEvent,
    }

    try {
      window.postMessage(msg, '')
    } catch (e) {
      // 目前发现 iOS 9 需要重试
      const ua = window.navigator.userAgent.toLowerCase()
      if (/ipad|iphone|ipod/.test(ua) && !window.MSStream) {
        const execUa = /iphone\D+([\d_.]+)/.exec(ua) || ''
        const ver = execUa[1].replace('_', '.')
        if (/^9/.test(ver)) {
          setTimeout(() => {
            window.postMessage(msg, '')
          }, 1000)
        }
      }
    }
    // iOS 9 时序问题
    // setTimeout(() => {
    //   window.postMessage(msg)
    // })
  }

  s4() {
    return Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1)
  }

  guid() {
    return this.s4() + '-' + this.s4() + '-' + this.s4() + '-' + this.s4()
  }
}
