// 用于直接扩展hjcore的能力，不发版hjcore
// import WebViewProtocol from './webViewProtocol'
import env from '../env'
export enum AppInvokeName {
  AppPassword = 'passwordValidatorEC',
  AppSelectPhoto = 'showAlbumPickerEC',
  AppSystemSetting = 'launchAppSystemSetting',
  AppJumpToProduct = 'jumpToProduct',
  AppHealth = 'getStepCountEC',
  AppPermission = 'getPermissionEC',
  AppDeviceInfo = 'getDeviceInfoEC',
  AppCacheSet = 'saveWebCacheToNativeEC',
  AppCacheLoad = 'loadWebCacheFromNativeEC',
  AppBindWeChatEC = 'startBindWeChatEC',
}

type DeviceObject = {
  DeviceID: string
  Brand?: string
  imei?: string
  Model?: string
  AndroidID?: string
  clientMac?: string

  idfa?: string
  idfv?: string
  iosDeviceName?: string
  iosMachine?: string
  iosOriginDeviceString?: string

  iserror?: boolean
}

class HjCoreIabPlus {
  webViewProtocol: any

  constructor() {
    if (window && window.document && window.hjCoreIab && window.hjCoreIab.webViewProtocol) {
      this.webViewProtocol = window.hjCoreIab.webViewProtocol
    }
  }

  checkAppFn(fnName: string): Promise<boolean> {
    return new Promise((resolve) => {
      if (window.hjCoreIab && env.isInApp) {
        console.log('start caniuse')
        window.hjCoreIab.canIUse(fnName, (status: { flag: boolean }) => {
          console.log('....canIUse', status)
          return resolve(!!status.flag)
        })
      } else {
        return resolve(false)
      }
    })
  }

  invoke(
    functionName: AppInvokeName,
    {
      options,
      success,
      error,
      emitEvent,
    }: { options?: any; success?: Function; error?: Function; emitEvent?: Function } = {},
  ) {
    console.log('iab.invoke', functionName, options)

    const hjcoreIab = window.hjCoreIab
    if (typeof functionName === 'string') {
      if (hjcoreIab && hjcoreIab[functionName]) {
        hjcoreIab[functionName](options, success, error, emitEvent)
      } else {
        this.webViewProtocol.send(functionName, options, success, error, emitEvent)
      }
    } else {
      console.error('传入方法名才行！')
    }
  }

  invokeApp = {
    showPassword: (params: {
      options?: {
        subTitle: string
        type?: string // withAmount: 显示金额, withoutAmount: 不显示余额
        amount?: string // 金额
      }
      success?: (parmas: {
        sessionId: string
        passwordMD5: string
        passwordRSA: string
        factor: string
      }) => void | undefined
      error?: Function | undefined
      emitEvent?: Function | undefined
    }) => {
      this.invoke(AppInvokeName.AppPassword, {
        options: params.options || {},
        success: (res: {
          factor: string
          passwordMD5: string
          passwordRSA: string
          response: {
            process_type: string
            ret_code: string
            ret_data: {
              session_expire_time: number
              session_id: string
            }
          }
        }) => {
          console.log('🚀 ~ file: index.ts ~ line 254 ~ FocusCore ~ hjCoreIabPlus.invoke ~ res', res)
          const { factor, passwordMD5, passwordRSA } = res
          if (typeof params.success === 'function') {
            params.success({ sessionId: res?.response?.ret_data?.session_id, passwordMD5, passwordRSA, factor })
          }
        },
        error: (data: any) => {
          console.log('🚀 ~ error', data)
          params.error
        },
        emitEvent: (data: any) => {
          console.log('🚀 ~ emitEvent', data)
          params.emitEvent
        },
      })
    },

    selectPhoto: (params?: {
      options?: {
        imgNums?: number
        noNeedUpload?: boolean
      }
      success?: (params: { file_id: string; hash_id: string }[]) => void | undefined
      error?: Function | undefined
      emitEvent?: Function | undefined
    }) => {
      const { options } = params || {}
      this.invoke(AppInvokeName.AppSelectPhoto, {
        options: {
          maxNum: (options && options.imgNums) || 1,
          needUpload: !(options && options.noNeedUpload),
        },
        success: (res: { images: { file_id: string; hash_id: string }[] }) => {
          console.log('🚀 ~ file: hjCoreIabPlus.ts:118 ~ HjCoreIabPlus ~ res:', res)

          params && params.success && params.success(res.images || [])
        },
        error: (data: {
          error: string // denied: 拒绝授权;cancel: 用户取消；unknown
        }) => {
          console.log('🚀 ~ error', data)
          params && params.error && params.error()
        },
        emitEvent: (data: any) => {
          console.log('🚀 ~ emitEvent', data)
          params && params.emitEvent && params.emitEvent()
        },
      })
    },

    getStepCount: (params?: {
      success?: (count: number) => void | undefined
      error?: Function | undefined
      emitEvent?: Function | undefined
    }) => {
      this.invoke(AppInvokeName.AppHealth, {
        success: (res: { count: number }) => {
          console.log('🚀 ~ file: hjCoreIabPlus.ts:118 ~ HjCoreIabPlus ~ res:', res)

          params && params.success && params.success(res.count || 0)
        },
        error: (data: { code: number; message: string }) => {
          console.log('🚀 ~ error', data)
          params && params.error && params.error(data.code || 0)
        },
        emitEvent: (data: any) => {
          console.log('🚀 ~ emitEvent', data)
          params && params.emitEvent && params.emitEvent()
        },
      })
    },

    getPermission: (params: {
      options: {
        type: string
      }
      success?: (status: string) => void | undefined
      error?: Function | undefined
      emitEvent?: Function | undefined
    }) => {
      const { options } = params || {}
      this.invoke(AppInvokeName.AppPermission, {
        options,
        success: (res: { status: string }) => {
          console.log('🚀 ~ file: hjCoreIabPlus.ts:118 ~ HjCoreIabPlus ~ res:', res)

          params && params.success && params.success(res.status)
        },
        error: (data: { code: number; message: string }) => {
          console.log('🚀 ~ error', data)
          params && params.error && params.error(data.code || 0)
        },
      })
    },

    jumpToProduct: (params?: {
      productCode: string
      extraParams: any
      success?: () => void | undefined
      error?: Function | undefined
      emitEvent?: Function | undefined
    }) => {
      const { productCode = '', extraParams = {} } = params || {}
      this.invoke(AppInvokeName.AppJumpToProduct, {
        options: {
          productCode,
          extraParams,
        },
        success: (res: any) => {
          console.log('🚀 ~ file: hjCoreIabPlus.ts:118 ~ HjCoreIabPlus ~ res:', res)

          params && params.success && params.success()
        },
        error: (data: {
          error: string // denied: 拒绝授权;cancel: 用户取消；unknown
        }) => {
          console.log('🚀 ~ error', data)
          params && params.error && params.error()
        },
        emitEvent: (data: any) => {
          console.log('🚀 ~ emitEvent', data)
          params && params.emitEvent && params.emitEvent()
        },
      })
    },

    lunchSystemSetting: () => {
      this.invoke(AppInvokeName.AppSystemSetting)
    },
  }

  getEncryptData(postBody: any): Promise<{ signature: any; encryptedData: any }> {
    postBody = typeof postBody !== 'string' ? JSON.stringify(postBody) : postBody || ''
    return new Promise((resolve, reject) => {
      Promise.all([
        new Promise((resolve, reject) => {
          window.hjCoreIab.SM2Sign(
            postBody,
            (result: any) => resolve(result),
            () => resolve(undefined),
          )
        }),
        new Promise((resolve, reject) => {
          window.hjCoreIab.SM4Encrypt2B64(
            postBody,
            (result: any) => resolve(result),
            () => resolve(undefined),
          )
        }),
      ]).then((res) => {
        console.log('promiseAll', res)
        if (res[0] === undefined) console.log('SM2Sign失败')
        if (res[1] === undefined) console.log('SM4Encrypt2B64失败')
        resolve({ signature: res[0], encryptedData: res[1] })
      })
    })
  }

  decryptData(data: any) {
    data = typeof data !== 'string' ? JSON.stringify(data) : data || ''
    return new Promise((resolve, reject) => {
      window.hjCoreIab.SM4Decrypt(
        data,
        (result: any) => resolve(typeof result !== 'object' ? JSON.parse(result) : result),
        () => resolve({}),
      )
    })
  }
  /**
   * 返回设备信息
   * @returns {Promise<DeviceObject>}
   * @example { DeviceID:string, AndroidID:string, Brand:string, imei:string, Model:string, clientMac:string }
   */

  getDeviceInfo(): Promise<DeviceObject> {
    return new Promise((resolve, reject) => {
      window.hjCoreIab.getDeviceInfo(
        (info: DeviceObject) => {
          console.log('设备信息 %o', info)
          resolve(info)
        },
        (err: DeviceObject) => {
          console.log('设备信息获取失败 %o', err)
          reject(err)
        },
      )
    })
  }

  getAppCache(key: string): Promise<{ key: string; value: string }> {
    return new Promise((resolve, reject) => {
      this.checkAppFn(AppInvokeName.AppCacheLoad).then((status) => {
        if (!status) {
          return reject({ error: 'no_api' })
        }
        this.invoke(AppInvokeName.AppCacheLoad, {
          options: {
            key,
          },
          success: (res: string) => {
            console.log('🚀 ~ file: hjCoreIabPlus.ts:118 ~ HjCoreIabPlus ~ res:', res)
            resolve({ key, value: res })
          },
          error: (data: { error: string }) => {
            console.log('🚀 ~ HjCoreIabPlus ~ this.checkAppFn ~ data:', data)

            reject(data)
          },
        })
      })
    })
  }

  setAppCache(key: string, value: string): Promise<{ key: string; value: string }> {
    return new Promise((resolve, reject) => {
      this.checkAppFn(AppInvokeName.AppCacheSet).then((status) => {
        console.log('🚀 ~ HjCoreIabPlus ~ this.checkAppFn ~ status:', status)
        if (!status) {
          return reject({ error: 'no_api' })
        }
        this.invoke(AppInvokeName.AppCacheSet, {
          options: {
            key,
            value,
          },
          success: (res: any) => {
            console.log('🚀 ~ file: hjCoreIabPlus.ts:118 ~ HjCoreIabPlus ~ res:', res)

            resolve({ key, value })
          },
          error: (data: {
            error: string // denied: 拒绝授权;cancel: 用户取消；unknown
          }) => {
            reject(data)
          },
        })
      })
    })
  }

  bindWechat(): Promise<{
    status?: boolean
    error?: string
  }> {
    return new Promise((resolve, reject) => {
      this.checkAppFn(AppInvokeName.AppBindWeChatEC).then((status) => {
        console.log('🚀 ~ HjCoreIabPlus ~ this.checkAppFn ~ AppInvokeName.AppBindWeChatEC status:', status)
        // 如果hjcore有这个方法，那就能调通，只需要处理又返回的情况
        if (!status) {
          return reject({ error: 'no_api' })
        }
        this.invoke(AppInvokeName.AppBindWeChatEC, {
          success: (res: any) => {
            console.log('🚀 ~ HjCoreIabPlus ~ AppInvokeName.AppBindWeChatEC ~ res:', res)

            resolve({ status: true })
          },
          error: (data: {
            error: string // denied: 拒绝授权;cancel: 用户取消；unknown
          }) => {
            reject(data)
          },
        })
      })
    })
  }
}

export default new HjCoreIabPlus()
