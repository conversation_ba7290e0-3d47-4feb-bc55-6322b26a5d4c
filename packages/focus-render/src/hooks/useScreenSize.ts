// src/hooks/useScreenSize.ts
import { ref, onMounted, onBeforeUnmount } from 'vue'

// 全局状态
const screenSize = {
  width: ref(window.innerWidth),
  height: ref(window.innerHeight),
  isLandscape: ref(window.innerWidth > window.innerHeight),

  // 监听器相关
  listeners: 0, // 全局监听计数
  components: 0, // 使用组件的计数
  updateCallbacks: [] as (() => void)[],
  updateListeners: () => {
    window.requestAnimationFrame(() => {
      const width = window.innerWidth
      const height = window.innerHeight

      screenSize.width.value = width
      screenSize.height.value = height

      screenSize.isLandscape.value = width > height

      // 执行所有组件的回调
      screenSize.updateCallbacks.forEach((cb) => cb())
    })
  },
}

// 单次事件监听设置
function setupGlobalListener() {
  if (screenSize.listeners === 0) {
    window.addEventListener('resize', screenSize.updateListeners)
    window.addEventListener('orientationchange', screenSize.updateListeners)
  }
  screenSize.listeners++
}

function teardownGlobalListener() {
  screenSize.listeners--
  if (screenSize.listeners === 0) {
    window.removeEventListener('resize', screenSize.updateListeners)
    window.removeEventListener('orientationchange', screenSize.updateListeners)
  }
}

// 提供状态更新通知
function registerCallback(cb: () => void) {
  screenSize.updateCallbacks.push(cb)
  screenSize.components++

  return () => {
    const index = screenSize.updateCallbacks.indexOf(cb)
    if (index > -1) {
      screenSize.updateCallbacks.splice(index, 1)
      screenSize.components--
    }
  }
}

// Hook 函数
export default function useScreenSize() {
  // 设置全局监听
  onMounted(setupGlobalListener)
  onBeforeUnmount(teardownGlobalListener)

  // 组件内部状态（可选）
  const localWidth = ref(screenSize.width.value)
  const localHeight = ref(screenSize.height.value)

  // 注册状态更新回调
  onMounted(() => {
    const unregister = registerCallback(() => {
      localWidth.value = screenSize.width.value
      localHeight.value = screenSize.height.value
    })
    onBeforeUnmount(unregister)
  })

  return {
    // 全局响应式状态
    screenWidth: screenSize.width,
    screenHeight: screenSize.height,
    isLandscape: screenSize.isLandscape,

    // 组件内部状态（按需使用）
    localWidth,
    localHeight,

    // 监听器状态
    listenersCount: () => screenSize.listeners,
    componentsCount: () => screenSize.components,
  }
}
