<template>
  <!-- 盒子组件 -->
  <m-box
    v-if="isMBox && moduleCanshow"
    :moduleData="curModuleData"
    :style="{ zIndex: moduleZindex }"
    :id="`anchor_${props.focusFid}_${curModuleData.moduleId}`"
    :class="positionFiexed"
  >
    <template v-if="!isConditionBox">
      <!-- 盒子组件的子组件 -->
      <module-center
        :moduleData="childData"
        v-for="(childData, index) in curModuleData.children"
        :key="`${childData.moduleType}_${childData.moduleId}_${index}`"
        :focusFid="props.focusFid || 0"
        :focusRenderCtr="props.focusRenderCtr"
        :canShowTypes="props.canShowTypes"
      ></module-center>
    </template>

    <!-- 条件渲染盒子组件 -->

    <template v-else>
      <!-- 盒子组件的子组件 -->
      <module-center
        :moduleData="childData"
        v-for="(childData, index) in conditionBoxChildren"
        :key="`${childData.moduleType}_${childData.moduleId}_${index}`"
        :focusFid="props.focusFid || 0"
        :focusRenderCtr="props.focusRenderCtr"
        :canShowTypes="props.canShowTypes"
      ></module-center>
    </template>
  </m-box>
  <!-- 实际渲染的组件 -->
  <component
    :is="moduleData.moduleType"
    v-if="!isMBox && moduleCanshow"
    :moduleData="moduleData"
    :style="{ zIndex: moduleZindex, ...curModuleChangeStyules }"
    :id="`anchor_${props.focusFid}_${moduleData.moduleId}`"
    :data-reportid="`${moduleData.moduleType}_${moduleData.moduleId}`"
    :class="positionFiexed"
    :focusFid="props.focusFid || 0"
    :childrenModules="moduleData.children"
    :focusRenderCtr="props.focusRenderCtr"
  >
  </component>
</template>

<script lang="ts">
import allModules from './allModules'
console.log('🚀 ~ file: modue-center.vue:50 ~ allModules:', allModules)
import { defineAsyncComponent } from 'vue'
import { focusCore } from '../index'

export default {
  name: 'module-center',
  components: {
    ...allModules,
    // FocusLottery,
    // FocusTaskList,
  },
}
</script>

<script setup lang="ts">
import {
  computed,
  toRefs,
  toRaw,
  unref,
  useSlots,
  useAttrs,
  Slots,
  inject,
  watch,
  watchEffect,
  ref,
  nextTick,
  provide,
} from 'vue'
import { storeToRefs } from 'pinia'
import { focusStore } from '../../../store/index'
import { useModuleDataResolve } from '../../hooks/index'

const { conditionStore } = focusStore.stores
// console.log('🚀 ~ conditionStore:', conditionStore)

const props = defineProps<{
  moduleData: TModuleData
  focusFid: number
  focusRenderCtr: any
  canShowTypes?: Array<string>
}>()

const { moduleData } = toRefs(props)
const curModuleData = moduleData
const moduleCanshow = ref(true)
const curModuleChangeStyules = ref({})
const { styleData } = useModuleDataResolve(curModuleData)

const isMBox = computed(() => {
  return curModuleData.value.moduleType === 'MBox'
})
const isConditionBox = computed(() => {
  return (
    curModuleData.value.moduleType === 'MBox' &&
    curModuleData.value.moduleSubType === 'conditionBox' &&
    curModuleData.value.data &&
    curModuleData.value.data.conditionType
  )
})

const positionFiexed = computed(() => {
  const { position } = unref(styleData)
  if (position && (position.isPT || position.isPB)) {
    return position.isPT ? 'p_fixed_t' : position.isPB ? 'p_fixed_b' : ''
  }
  return ''
})

const moduleZindex = computed(() => {
  const { position } = unref(styleData)
  if (position.position === 'absolute') {
    return 300 + Number(curModuleData.value.moduleId)
  }
  return curModuleData.value.moduleId
})

watchEffect(() => {
  const changedModuleDataRef = (props.focusRenderCtr && props.focusRenderCtr.changedModuleDataRef.value) || {}
  // console.log('🚀 ~ watch!!! changedModuleDataRef', changedModuleDataRef)
  if (props.canShowTypes && props.canShowTypes.length) {
    console.log('🚀 ~ file: modue-center.vue:164 ~ watchEffect ~ props.canShowTypes:', props.canShowTypes)
    moduleCanshow.value = props.canShowTypes.indexOf(curModuleData.value.moduleType) > -1
  }
  const moduleId = curModuleData.value.moduleId
  if (changedModuleDataRef[moduleId] && props.focusRenderCtr && !props.focusRenderCtr.checkModuleHadChanged(moduleId)) {
    const changeConfigData = changedModuleDataRef[moduleId]
    console.log('🚀 ~ 要修改的配置', changeConfigData)
    console.log('当前组件的 configdata', curModuleData.value)
    console.log('changeConfigData.style', changeConfigData.style)
    // 展示控制
    if (changeConfigData.hide) {
      moduleCanshow.value = false
    }
    if (changeConfigData.show) {
      moduleCanshow.value = true
    }

    // 样式控制
    if (changeConfigData.style) {
      curModuleData.value.changeStyles = changeConfigData.style
      console.log('🚀 ~ file: modue-center.vue:144 ~ watchEffect ~ changeConfigData.style:', changeConfigData.style)
      curModuleChangeStyules.value = changeConfigData.style
    }

    // data控制
    if (changeConfigData.data) {
      curModuleData.value.changeData = changeConfigData.data
    }

    props.focusRenderCtr.clearModuleChanged(moduleId)
  }
})

const conditionBoxChildren = computed<TModuleData[]>((): TModuleData[] => {
  const moduleData = toRaw(props.moduleData)
  const {
    conditionType,
    conditionList,
    extendStr = '',
  }: { conditionType: string; conditionList: FocusConditionBox.conditionList; extendStr?: string } = curModuleData.value
    .data
  if (!isConditionBox) {
    return []
  }
  const { children = [] } = moduleData

  const { conditions } = storeToRefs(conditionStore)
  let typeStr = conditionType
  if (extendStr) {
    typeStr = `${conditionType}__EXTENDSTR__${extendStr}`
  }
  let conditionStatus = conditions.value[typeStr]
  if (focusCore.mockData.isPreview && focusCore.mockData.mockCondition) {
    try {
      const target = focusCore.mockData.mockCondition[typeStr] || {}
      console.log('🚀 ~ conditionBoxChildren ~ focusCore.mockData.mockCondition:', focusCore.mockData.mockCondition)
      console.log('🚀 ~ conditionBoxChildren ~ target:', target)
      console.log('模拟的渲染条件：', typeStr, target.val)
      conditionStatus = target.val || conditions.value[typeStr]
      console.log('现在的渲染条件', typeStr, conditionStatus)
    } catch (err) {
      console.log('🚀 ~ conditionBoxChildren ~ err:', err)
    }
  }

  let targetModules: any[] = []
  conditionList
    .filter((i) => i.conditionId === conditionStatus)
    .forEach((i) => {
      targetModules = targetModules.concat(i.moduleIds)
    })

  const childrenModules = children.filter((i) => {
    return targetModules.indexOf(i.moduleId) > -1
  })
  return childrenModules
})
</script>

<!-- <style>
@font-face {
  font-family: DINAlternate-bold-number;
  src: url('../../../assets/font/DINAlternate-bold-number.ttf') format('truetype');
}
</style> -->

<style lang="scss" scoped>
.p_fixed_b {
  position: fixed;
  bottom: 0;
  left: 50%;
  right: 0;
  transform: translateX(-50%);
  z-index: 4001 !important;
}
.p_fixed_t {
  position: fixed;
  top: 0;
  left: 50%;
  right: 0;
  transform: translateX(-50%);
  z-index: 4001 !important;
}
</style>
