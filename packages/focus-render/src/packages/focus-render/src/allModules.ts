import { defineAsyncComponent } from 'vue'
import MBox from '../../focus-modules/focus-box.vue'
import MText from '../../focus-modules/focus-text.vue'
import MImage from '../../focus-modules/focus-image.vue'
import FocusLottery from '../../focus-lottery/src/focus-lottery.vue'
import FocusTaskList from '../../focus-modules/focus-task-list.vue'
import BtnClick from '../../focus-modules/focus-btn-click.vue'
import BtnAcceptShare from '../../focus-btn-accept-share/focus-btn-accept-share.vue'
import BtnMGM from '../../focus-modules/focus-btn-mgm.vue'
import MgmList from '../../focus-modules/focus-mgm-list.vue'
import PlaceHolder from '../../focus-modules/focus-place-holder.vue'
import LogoBottom from '../../focus-modules/focus-logo-bottom.vue'
import ActCoinConvertList from '../../focus-modules/focus-actcoin-convert-list.vue'
import FamilyProductList from '../../focus-modules/family-product-list/family-product-list.vue'
import ActCoinHistory from '../../focus-modules/focus-actcoin-history.vue'
import BtnDownload from '@/packages/focus-modules/btn-download.vue'
import MyRewardList from '../../focus-modules/focus-my-reward-list.vue'
import BtnAdding from '../../focus-modules/focus-btn-adding.vue'
import AnchorNav from '../../focus-modules/focus-anchor-nav.vue'
import MGMListCompany from '../../focus-modules/mgm-list-company/mgm-list-company.vue'
import BtnAssistanceV2 from '../../focus-modules/focus-btn-assistance.vue'
import MVideo from '../../focus-modules/focus-video.vue'
import MGMListClassic from '../../focus-modules/focus-mgm-list-classic.vue'
import SwiperBox from '../../focus-modules/focus-swiper.vue'
import RankList from '../../focus-modules/focus-rank-list.vue'
import CountDownTimer from '@/packages/focus-modules/focus-countdown-timer.vue'
import InputShareCode from '@/packages/focus-modules/focus-input-sharecode.vue'
import FundCoupon from '@/packages/focus-modules/focus-fund-coupon.vue'
export default {
  MBox,
  MText,
  MImage,
  MVideo,
  FocusLottery,
  FocusTaskList,
  AnchorNav,
  BtnAdding,
  BtnClick,
  BtnAcceptShare,
  BtnMGM,
  MgmList,
  PlaceHolder,
  LogoBottom,
  ActCoinConvertList,
  FamilyProductList,
  ActCoinHistory,
  BtnDownload,
  MyRewardList,
  MGMListCompany,
  BtnAssistanceV2,
  MGMListClassic,
  SwiperBox,
  RankList,
  CountDownTimer,
  InputShareCode,
  FundCoupon,
}
