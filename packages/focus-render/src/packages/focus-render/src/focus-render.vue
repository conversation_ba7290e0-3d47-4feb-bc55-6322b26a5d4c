<template>
  <template v-for="(_module, index) in moduleTree" :key="`${_module.moduleType}_${_module.moduleId}`">
    <module-center
      v-if="!hideFocusModules"
      :moduleData="_module"
      :focusFid="props.focusFid || 0"
      :focusRenderCtr="_focusRenderCtr"
    >
      <slot></slot>
    </module-center>
  </template>
  <focusCounttime></focusCounttime>
  <Loading></Loading>
  <confirm></confirm>
  <Toast></Toast>
  <error-mask></error-mask>
  <share-mask></share-mask>
  <GusetModPannel
    v-if="showWxGuest"
    :loginWx="
      () => {
        focusCore.loginWx()
      }
    "
  ></GusetModPannel>
  <PopupView v-if="popupConfigData.length" :popupData="popupConfigData"></PopupView>
  <!-- <MarkettingDialogVue :popupData="markettingPopupData"></MarkettingDialogVue> -->
</template>

<script lang="ts">
import ModuleCenter from './modue-center.vue'
import Loading from '../../../components/Loading.vue'
import Confirm from '../../../components/Confirm.vue'
import ErrorMask from '../../../components/Error.vue'
import ShareMask from '../../../components/ShareMask.vue'
import GusetModPannel from '../../../components/GusetModPannel.vue'
import PopupView from '../../focus-modules/focus-popup-view.vue'
import focusCounttime from '../../focus-modules/focus-counttime/focus-counttime.vue'
import Toast from '../../../components/Toast.vue'
import MarkettingDialogVue from '../../../components/MarkettingDialog.vue'
// import { photoService } from '@/service'
export default {
  name: 'focus-render',
}
</script>

<script setup lang="ts">
import { ref, useSlots, provide, onMounted, inject, watch, watchEffect, toRef } from 'vue'
import type { Ref } from 'vue'
import { UrlParser } from '../../../utils/url'
import waLog from '../../../service/focus-core/waLog'
import { focusStore, focusCore, focusServices } from '../index'
import type TFocusRenderContrler from '../../../service/FocusRenderContrler'
enum EnumErrorType {
  NOT_IN_PAGE_GRAY = 'not_in_page_gray',
  SAFE_LOCK = 'safe_lock',
}

const { mgmService, activityService, wxServcie, dynimicDataService, extendInstanceService } = focusServices

const slots = useSlots()
provide('focus_slots', slots)
const emit = defineEmits(['onLoginSucc', 'onLoginFail', 'onFocusConfigSucc'])
const props = defineProps({
  focusFid: Number, // 页面配置ID 不需要获取配置则传 0
  focusAid: {
    type: Number,
    require: false,
  },
  needLogin: {
    require: false,
    type: Boolean,
    default: true,
  }, // 禁止登录 默认true, 传入false则不调用登录
  showWxGuest: {
    type: Boolean, // 展示微信授权弹窗
    require: false,
  },
  hideWxGuest: {
    type: Boolean, // 隐藏微信授权弹窗
    require: false,
  },
  slotMountedCb: {
    require: false,
    type: Function,
  },
  hideFocusModules: {
    require: false,
    type: Boolean,
  },
  renderWhenParentIsReadyStatus: {
    require: false,
    type: Number, // 0: 直接渲染；1: 不渲染，等待指令为2再渲染；2: 渲染
  },
  useLocalModuleData: {
    require: false,
    type: Object,
  },
})
provide('slotMountedCb', props.slotMountedCb)
const { modalStore, userStore } = focusStore.stores
const localhostPared = new UrlParser(window.location.href)
const renderWhenParentIsReadyStatus = toRef(props, 'renderWhenParentIsReadyStatus')

const moduleTree = ref<any[]>([])
const showWxGuest = ref(false)
const popupConfigData = ref<any[]>([])
const activityIsReady = ref(false)

let nextRenderMain: any = null
let _focusRenderCtr: any = null

console.log('init focusinstance')
initFocus()

watchEffect(() => {
  console.log('renderWhenParentIsReadyStatus.value...', renderWhenParentIsReadyStatus.value)
  if (renderWhenParentIsReadyStatus.value === 2) {
    nextRenderMain && nextRenderMain()
  }
})

function initFocus() {
  focusCore.eventLog('showed', 'focus_render_init', { fid: props.focusFid, aid: props.focusAid })
  // 默认关闭分享
  mgmService.closeShare()

  modalStore.loadingStart('init-focus')
  focusCore
    .init({
      focusFid: props.focusFid,
      focusAid: props.focusAid,
      useLocalModuleData: props.useLocalModuleData,
    })
    .then((res) => {
      console.log('🚀 ~ file: focus-render.vue ~ line 94 ~ .then ~ res', res)
      modalStore.loadingEnd('init-focus')
      const { loginData, focusRenderCtr, nowTime } = res
      focusRenderCtr.setContrlMethod({
        renderModules: (data: any[]) => {
          moduleTree.value = data
        },
        renderPopupModules: (data: any[]) => {
          popupConfigData.value = data
        },
      })
      _focusRenderCtr = focusRenderCtr
      // 现在未登录
      if (loginData.error === '401') {
        const configNeedLogin = _focusRenderCtr.needLogin // 配置了 ”静态页“ 不需要登录
        console.log('🚀 ~ 配置需要登录吗？', configNeedLogin)
        const localNeedLogin = props.needLogin ? true : false
        // 默认情况下，在App需要强制登录
        let needLoginInApp = true
        console.log('🚀 ~ .then ~ localNeedLogin:', localNeedLogin)

        // 静态页
        if (!configNeedLogin || !localNeedLogin) {
          needLoginInApp = false
        }

        // 本地代码需要登录
        if (localNeedLogin) {
          needLoginInApp = true
        } else {
          // 是静态页，不需要登录
          if (!configNeedLogin) {
            needLoginInApp = false
          } else {
            // 是活动页
            needLoginInApp = _focusRenderCtr.needLoginInApp
          }
        }
        console.log('🚀 ~ 需要在App登录哦！', needLoginInApp)

        console.log('🚀 ~ 本地需要登录吗？', configNeedLogin)
        console.log(focusCore.env.isInWx)

        // 静态页和活动页在微信都强制登录
        if (focusCore.env.isInWx) {
          showWxGuest.value = props.hideWxGuest ? false : props.showWxGuest || _focusRenderCtr.showWxGuest
          console.log('🚀 ~ file: focus-render.vue ~ line 139 ~ .then ~ showWxGuest.value', showWxGuest.value)
          // 不需要显示微信授权弹窗，直接登录
          if (!showWxGuest.value) {
            return focusCore.loginWx()
          } else {
            waLog.eventLog('wx_guest_show', '1')
          }
        }

        // 在App
        if (focusCore.env.isInApp) {
          showWxGuest.value = false

          if (needLoginInApp) {
            console.log('需要登录哦！')
            // 在App只有活动页需要强制登录
            return window.hjCoreIab && window.hjCoreIab.login()
          }
        }

        // 既不在wx 也不在app，看是否需要登录
        if (!focusCore.env.isInApp && !focusCore.env.isInWx) {
          // 需要登录就报错
          if (localNeedLogin || configNeedLogin) {
            return modalStore.errorMaskContrl('not_in_app_or_wx', ['请在微信中打开'])
          } else {
            waLog.eventLog('no_need_login', '1')
          }
        }
      } else {
        // 现在已登录
        if (!focusCore.env.isInApp) {
          checkForceRelogin(_focusRenderCtr.pageConfig?.data)
        }
      }

      renderMain(loginData, _focusRenderCtr)
    })
    .catch((err) => {
      modalStore.loadingEnd('init-focus')
      console.log('🚀 ~初始化出错咯', err)
      const { error, errorInfo } = err
      switch (error) {
        case EnumErrorType.SAFE_LOCK:
          console.log('被安全拦截啦!')
          // emit('onLoginSucc', { error: EnumErrorType.SAFE_LOCK })

          const { wb_safe_code, wb_safe_format, wb_safe_msg, wb_safe_rule_action } = errorInfo
          const key = EnumErrorType.SAFE_LOCK
          let msgs = wb_safe_msg.split('，')
          const logDefinedValue = `${wb_safe_code}_${wb_safe_format}_${wb_safe_rule_action}`

          if (wb_safe_rule_action === 'SL001' || wb_safe_rule_action === 'SL002') {
            msgs = ['为了您的账户安全', '请使用微众银行App登录验证']
          }
          modalStore.errorMaskContrl(key, msgs, logDefinedValue)
          break
        default:
          modalStore.errorMaskContrl('focus-init')
      }
    })
}

function emitFocusEvent(_loginData: any, _focusRenderCtr: TFocusRenderContrler) {
  emit('onLoginSucc', _loginData)
  emit('onFocusConfigSucc', _focusRenderCtr)
}

function renderMain(loginData: any, _focusRenderCtr: TFocusRenderContrler) {
  let { autoJoinAid = 0, setM2 = {} } = _focusRenderCtr.pageConfig?.data
  console.log('🚀 ~ renderMain ~ autoJoinAid:', autoJoinAid)
  const hasAccount = loginData.hasAccount

  // 未登录、已登录都要做的事情
  const doSameAfterRender = () => {
    reportActivityBrowse(autoJoinAid, hasAccount)
    handlePageonshow()
  }

  // 登录后的渲染流程
  const renderFocusModulesAfterLogin = () => {
    modalStore.loadingStart('renderFocusModulesAfterLogin')
    // 更新开户状态
    userStore.setAccountStatus(hasAccount)
    // 初始化微信
    wxServcie.init().then((canDoWx) => {
      if (canDoWx) {
        console.log('可以执行微信相关的逻辑啦')
      }
    })
    // 开始渲染focus相关内容
    _focusRenderCtr.render().then(() => {
      // 自动参与活动
      if (activityIsReady.value) {
        autoJoinActivity(autoJoinAid)
      }
      // 自动接受邀请关系
      autoAcceptShare(setM2)
      // 通用操作
      doSameAfterRender()
      dynimicDataService.init(_focusRenderCtr.apiList, _focusRenderCtr.conditionList)
      //右上角分享
      handleRtConnerShare()
      modalStore.loadingEnd('renderFocusModulesAfterLogin')
    })
  }

  const next = () => {
    modalStore.loadingStart('renderMain_next')
    _focusRenderCtr.checkBeforeRender().then(async (status) => {
      console.log('🚀 ~ FocusRenderContrler ~ this.checkPageShowIntime ~ status:', status)
      if (!status) {
        console.log('无法展示页面！')
        modalStore.loadingEnd('renderMain_next')
        return
      }
      if (loginData.isLogined) {
        if (!autoJoinAid) {
          modalStore.loadingEnd('renderMain_next')
          renderFocusModulesAfterLogin()
          return
        }
        activityService
          .checkAidsIsReady(autoJoinAid)
          .then((res) => {
            console.log('🚀 ~ .then ~ res:', res)
            const d = res

            if (d.noNeedCheck) {
              activityIsReady.value = true
              renderFocusModulesAfterLogin()
              return
            }

            if (!d.activityId) {
              return
            }

            if (!d.isSupportNoAuth && !hasAccount) {
              modalStore.errorMaskContrl(`not_support_noauth_${autoJoinAid}`, ['非常抱歉，本活动仅限受邀客户参与'])
              return
            }
            if (d.isUsedAndNotInGray) {
              modalStore.errorMaskContrl(`not_ingray_aid_${autoJoinAid}`, ['非常抱歉，本活动仅限受邀客户参与'])
              return
            }

            if (d.isAfterEndTime || d.isBeforeStartTime) {
              activityIsReady.value = false
            } else {
              activityIsReady.value = true
            }
            renderFocusModulesAfterLogin()
          })
          .catch((err) => {
            console.log('🚀 ~ next ~ err:', err)
            modalStore.errorMaskContrl('checkAidError')
          })
          .finally(() => {
            modalStore.loadingEnd('renderMain_next')
          })
      } else {
        // 不需要登录就走 无需登录的逻辑
        _focusRenderCtr
          .render()
          .then((status) => {
            if (!status) {
              return
            }
            doSameAfterRender()
            dynimicDataService.initWithoutLogin(_focusRenderCtr.apiList)
          })
          .finally(() => {
            modalStore.loadingEnd('renderMain_next')
          })
      }
    })
  }

  emitFocusEvent(loginData, _focusRenderCtr)
  if (renderWhenParentIsReadyStatus.value === 1) {
    console.error(
      `传入了renderWhenParentIsReadyStatus.value=${renderWhenParentIsReadyStatus.value},要控制一下！不能直接渲染哦！`,
    )
    nextRenderMain = () => {
      console.error('这里是由状态位控制渲染的！')
      next()
    }
  } else {
    next()
  }
}

/**
 * 右上角分享
 */
function handleRtConnerShare() {
  const shareData = _focusRenderCtr.rtConnerShareData
  console.log('🚀 ~ 右上角分享-----', shareData)
  if (Object.keys(shareData).length) {
    mgmService.setRtConnerShare('rtconner_share_init', shareData).then((shareConfig) => {
      console.log('🚀 ~ file: focus-render.vue:314 ~ mgmService.setRtConnerShare ~ shareConfig:', shareConfig)
      if (!shareConfig) {
        return
      }
      // 大mgm过来的页面需要自动拉起分享面板
      const query = new UrlParser(location.href).query

      if (focusCore.env.isInApp) {
        if (query.autoShare) {
          modalStore.loadingStart('autoShare')
          mgmService.clickShare('share.bigMgm_autoShare', shareData)
          modalStore.loadingEnd('autoShare')
        }
      }
    })
  }
}

function checkForceRelogin(pageData: any) {
  const { relogin } = pageData || {}
  const query = new UrlParser(location.href).query
  if (relogin === '1' && query.relogin !== '1') {
    console.error('需要强制重新登录微信！')
    focusCore.loginWx()
  }
}
/**
 * 自动建立邀请关系
 */

function autoAcceptShare(setM2Data: any) {
  if (focusCore.env.isInApp) {
    return false
  }
  const { status = '0', mgmAid = '', fromAid = '', targetAid = '' } = setM2Data

  const query = new UrlParser(location.href).query
  const urlMgmAid = query.mgmAid && query.mgmAid.toString()
  let acceptMgmAids = (mgmAid && mgmAid.toString() && mgmAid.toString().split(',')) || []
  acceptMgmAids = acceptMgmAids.slice(0, 5)
  const m1Sid = query.sid
  if (status === '1' && acceptMgmAids.indexOf(urlMgmAid) > -1) {
    console.log('开始建立邀请关系......')
    const needNoAccountMgmIds = ['1939']

    mgmService.M2AcceptMGM({ mgmAid: urlMgmAid, m1Sid })
  }
}

/**
 * 上报活动浏览
 * app环境下，要有活动id
 * 已登录未开户
 * 未登录未开户
 * @param aid
 * @param hasAccount
 */
function reportActivityBrowse(aid: number | string, hasAccount: boolean) {
  console.log('🐬 ~ file: focus-render.vue:378 ~ reportActivityBrowse ~ hasAccount:', hasAccount)
  if (!aid) {
    console.log('不存在aid')
    return false
  }
  if (focusCore.env.isInApp) {
    // 未登录直接传false未开户
    if (!hasAccount) {
      activityService.activityBrowseMarkingReport(aid)
    }
  }
}

/**
 * 自动参与活动
 */
function autoJoinActivity(aid: number | string) {
  console.log('。。。。自动参与活动', aid)
  if (!aid) {
    console.log('不存在aid')
    return false
  }

  activityService
    .setWrInfo(aid)
    .then((res) => {})
    .catch(({ errCode }) => {
      console.log('🚀 ~ autoJoinActivity ~ errCode:', errCode)
      // 只有-2 -3 展示错误蒙层
      if (errCode !== '-2' && errCode !== '-3') {
        return
      }
      const errMsgs = {
        '-1': ['活动已结束，下次再来参与吧~'],
        '-2': ['非常抱歉，本活动仅限受邀客户参与'],
        '-3': ['非常抱歉，本活动仅限受邀客户参与'],
        '-4': ['活动还没开始，请耐心等待哦'],
        '-5': ['活动已结束，下次再来参与吧~'],
      }
      const msg = errMsgs[errCode]
      modalStore.errorMaskContrl('autoJoinActivity_fail', msg)
    })
}

function handlePageonshow() {
  const { isReload } = new UrlParser(location.href).query
  if (focusCore.env.isInApp) {
    window.onpageshow = (event) => {
      if (event.persisted) {
        // App内返回需要重新设置标题
        _focusRenderCtr.resetPageTitle()
        // App内返回需要重新设置分享
        mgmService.resetDefault('page_back')

        //! 需要重新reload的页面需要带上isreload参数，保证重新请求数据
        if (isReload) window.location.reload()
      }
    }
  } else {
    window.onpageshow = (event) => {
      if (event.persisted && isReload) window.location.reload()
    }
  }
}

onMounted(() => {
  console.log('测试cdn结果！')
  if (focusCore.env.isInApp) {
    // 处理app里面 ios拖动图片问题
    if (document.documentElement && document.documentElement.style) {
      const style: any = document.documentElement.style
      style.webkitTouchCallout = 'none'
      style.webkitUserSelect = 'none'
    }
  }
})
</script>
