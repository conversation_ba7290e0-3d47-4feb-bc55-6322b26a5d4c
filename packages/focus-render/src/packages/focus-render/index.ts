// 导入组件
import focusRender from './src/focus-render.vue'
import { focusCore } from '@/service/focus-core'
import focusDirectives from '@/service/focus-core/focusDirectives'
import * as focusServices from '../../service/index'
console.log('🚀 ~ file: index.ts ~ line 5 ~ focusServices', focusServices)
import { focusStore } from '../../store'
import * as focusUtils from '../../utils/index'
import Mask from '@/components/Mask.vue'
import DialogMask from '@/components/DialogMask.vue'
const focusRenderPlugin: {
  install: (app: any, options: any) => void
} = {
  install: (app: any, options: any) => {
    console.log('🚀 ~ file: index.ts ~ line 6 ~ options', options)
    const { $pinia } = options
    if ($pinia) {
      // 注册所有store
      focusStore.setPinia($pinia)
    } else {
      console.error('注册 focusRenderPlugin 时候没有提供 pinia实例，请手动调用  focusStore.setPinia($pinia)')
    }
    app.component(focusRender.name, focusRender)
    focusDirectives.init(app)
  },
  ...focusRender
}
const definedReportData = focusDirectives.definedReportData

export { focusRenderPlugin, focusCore, focusStore, focusServices, focusUtils, Mask, DialogMask, definedReportData }
// 默认导出组件
export default focusRenderPlugin
