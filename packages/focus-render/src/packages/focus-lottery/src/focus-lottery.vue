<template>
  <div class="focus-lottery" :style="curStyles">
    <lottery-controller :skinIndex="1" :configData="configData"></lottery-controller>
  </div>
</template>
<script lang="ts">
export default {
  name: 'focus-lottery',
}
</script>
<script setup lang="ts">
import { unref, computed, provide } from 'vue'
import LotteryController from './lottery-controler.vue'
import { useModuleDataResolve } from '../../hooks'
const skin = {
  mechinBg: require('../../../assets/img/focus-lottery/mechine.png'),
  priceSlectedBg: '#ffff6cd2',
  btnUp: require('../../../assets/img/focus-lottery/btn-up.png'),
  btnDisabled: require('../../../assets/img/focus-lottery/btn-down.png'),
  btnColor: '#fff',
  light: require('../../../assets/img/focus-lottery/light.png'),
  lightOff: require('../../../assets/img/focus-lottery/light-off.png'),
}

const props = defineProps<{ moduleData: NFocusLottery.moduleData }>()

const { styleData, configData } = useModuleDataResolve<NFocusLottery.configData>(props.moduleData)
const configSkin: any = configData.value
provide('lotterySkin', {
  mechinBg: configSkin.mechinBg || skin.mechinBg,
  priceSlectedBg: configSkin.priceSlectedBg || skin.priceSlectedBg,
  btnUp: configSkin.btnUp || skin.btnUp,
  btnDisabled: configSkin.btnDisabled || skin.btnDisabled,
  btnColor: configSkin.btnColor || skin.btnColor,
  light: configSkin.light || skin.light,
  lightOff: configSkin.lightOff || skin.lightOff,
})
const curStyles = computed(() => {
  const { boxSize, margins } = unref(styleData)

  return {
    ...boxSize,
    ...margins,
    zIndex: 5555,
  }
})
</script>

<style lang="scss" scoped>
.focus-lottery {
  flex: none;
  box-sizing: border-box;
  width: 750px;
}
</style>
