<template>
  <div class="lottery-box">
    <lottery-default-skin v-if="skinIndex === 1"></lottery-default-skin>
    <!-- 跑马灯组件 -->
    <lottery-lamp v-if="isShowLamp" class="lottery-lamp" :configId="configId"></lottery-lamp>
    <dialog-mask :show="priceData.show">
      <template #dialog_contain>
        <div
          class="price-dialog contain"
          aria-modal="true"
          role="dialog"
          aria-live="assertive"
          aria-labelledby="dilaog-title"
        >
          <p class="title" id="dilaog-title">恭喜抽中</p>
          <p class="title-price">{{ priceData.title }}</p>
          <img class="cover" :src="priceData.priceImgUrl" :alt="priceData.desc" aria-hidden="true" />
          <p class="desc">{{ priceData.desc }}</p>
          <div class="btns">
            <div class="btn-detail" @click="jumpToMyReward" role="button">查看详情</div>
            <div class="btn-share" @click="clickSharePrice" role="button">立即分享</div>
          </div>
          <div class="btn-close" @click="showPrice({ show: false })" role="button" aria-label="关闭"></div>
        </div>
      </template>
    </dialog-mask>
  </div>
</template>

<script setup lang="ts">
import LotteryDefaultSkin from './skins/default.vue'
import LotteryLamp from './lottery-lamp.vue'
import { readonly, provide, ref, reactive, onMounted, computed, watch, watchEffect, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { focusStore } from '../../../store/index'
import { mgmService, lotteryService, taskService, userService, jumpService } from '../../../service'
import waLog from '../../../service/focus-core/waLog'
import focusCore from '@/service/focus-core'
import DialogMask from '@/components/DialogMask.vue'
// import { AppInvokeName } from '@/service/focus-core/focus-core'
import hjCoreIabPlus, { AppInvokeName } from '@/service/focus-core/plugins/hjCoreIabPlus/hjCoreIabPlus'
const props = defineProps<{
  skinIndex: number
  // configId: string
  // aids: string
  configData: NFocusLottery.configData
}>()

const { configId, aids, myRewardLink, isShowLamp } = props.configData
console.log(
  '🚀 ~ file: lottery-controler.vue ~ line 34 ~ configId, aids, myRewardLink',
  configId,
  aids,
  myRewardLink,
  isShowLamp
)
const { taskStore, userStore, modalStore } = focusStore.stores
const { loadingStart, loadingEnd } = modalStore
const selectedPlaceId = ref(0) // 奖品的位置id
const priceList = ref([]) // 奖品列表
const lotteryInfo = reactive({
  lotteryAids: [],
  lotteryTimes: 0,
  doLotteryAid: 0,
  myRewardLink,
  lotteryTimesExpireTime: '',
})
const priceData = ref<{
  show: boolean
  placeId?: number
  title?: string
  priceImgUrl?: string
  desc?: string
  shareUrl?: string
  shareImg?: string
  shareTitle?: string
  shareDesc?: string
}>({
  show: false,
  placeId: 0,
  title: '',
  priceImgUrl: '',
  desc: '',
  shareUrl: '',
  shareImg: '',
  shareTitle: '',
  shareDesc: '',
})

if (focusCore.hasAccount) {
  getLotteryData(configId, aids)
}

// 设置抽奖动画
const lotterAnimation = animationHandler()
let mechineIsRunnig = false

provide('lotteryInfo', readonly(lotteryInfo))
provide('selectedPlaceId', readonly(selectedPlaceId))
provide('clickLotteryStart', lotteryHandler)
provide('priceList', readonly(priceList))
provide('clickMyReward', () => {
  if (mechineIsRunnig || !lotteryInfo.myRewardLink) {
    return false
  }
  jumpService.jump({
    path: lotteryInfo.myRewardLink,
  })
})

function test() {
  // const copyData = JSON.parse(JSON.stringify(priceData.value))
  // console.log('🚀 ~ watch ~ copyData:', copyData)

  // priceData.value = {
  //   show: false,
  // }
  // nextTick(() => {
  //   setTimeout(() => {
  //     showPrice(copyData)
  //   }, 350)
  // })
  const dom = document.querySelector('.target-price-wrap')
  console.log('🚀 ~ watchEffect ~ dom:', dom)
  if (dom) {
    setScale.value = 5
    const calssName = dom.classList
    console.log('🚀 ~ watchEffect ~ calssName:', calssName)
    dom.classList.remove(`setsmall_${setScale.value}`)
    dom.classList.add(`setsmall_${setScale.value}`)
    // dom.className = `${calssName} setsmall_${setScale.value}`
  }
}

function jumpToMyReward() {
  modalStore.loadingStart('jumpToMyReward')
  jumpService.jump({
    path: lotteryInfo.myRewardLink,
  })
  modalStore.loadingEnd('jumpToMyReward')
}

function clickSharePrice() {
  const { shareUrl = '', shareImg = '', shareTitle = '', shareDesc = '', placeId } = priceData.value

  mgmService.clickShare(`share.btnprice-${placeId}`, {
    shareUrl,
    shareImg,
    shareTitle,
    shareDesc,
  })
}

function animationHandler() {
  let stepTimer: any = null
  let totalStep = 999 //默认跑999步
  let endPlaceId = 0
  let stepSpaceTime = 60
  let animateEndCb: any = null
  let isStop = false
  // 初始化参数
  const reset = () => {
    selectedPlaceId.value = 0
    endPlaceId = 0
    totalStep = 399
    stepSpaceTime = 60
    isStop = true
  }
  const oneStep = () => {
    if (isStop) {
      console.log('已强制停止，无法启动')
      return false
    }
    //计算还差几步
    if (endPlaceId) {
      const lastStep = endPlaceId + 8 - selectedPlaceId.value
      totalStep = lastStep
      // 清空endPlaceId
      endPlaceId = 0
    }

    let placeId = selectedPlaceId.value
    placeId++
    if (placeId > 8) {
      placeId = 1
    }
    selectedPlaceId.value = placeId
    totalStep--
    //快到目标了，匀减速运动
    if (totalStep < 9) {
      stepSpaceTime = stepSpaceTime + (10 - totalStep) * 5
    }
    stepTimer = setTimeout(() => {
      if (totalStep) {
        oneStep()
      } else {
        endPlaceId = 0
        clearTimeout(stepTimer)
        if (animateEndCb) {
          animateEndCb()
        }
      }
    }, stepSpaceTime)
  }

  return {
    reset: reset,
    start: () => {
      reset()
      isStop = false
      oneStep()
    },
    endInPlaceId(targetIndexId, endCb) {
      if (targetIndexId === 0) {
        return reset()
      }
      endPlaceId = targetIndexId
      animateEndCb = endCb
    },
  }
}

const lotteryLog = {
  getConfigUnit: (configId: string, lotteryAids: Array<any> = [], taskIds: Array<any> = []) => {
    const _lotteryAids = lotteryAids || []
    const _taskId = taskIds || []
    const value = `${configId}_${_lotteryAids.join('-')}_${_taskId.join('-')}`
    waLog.eventLog('lottery.getConfigUnit', value)
  },
  getLotteryConfig: (configId: string, priceListLength: number) => {
    waLog.eventLog('lottery.getConfigUnit', `${configId}_${priceListLength}`)
  },
  checkCanlottery: (lotteryTimes: number, isSubscrib: Boolean) => {
    waLog.eventLog('lottery.checkCanlottery', `${lotteryTimes}_${isSubscrib}`)
  },
  doLottery: (doLotteryAid: string | number, lotteryTimes: number) => {
    waLog.eventLog('lottery.doLottery', `${doLotteryAid}_${lotteryTimes}`)
  },
}

onMounted(() => {
  focusCore.onPageVisibilityChange(
    'lottery',
    () => {
      setTimeout(() => {
        // 延迟300毫秒执行，这样等抽奖机会更新好
        getLotteryData(configId, aids)
      }, 300)
    },
    'show'
  )
})

function lotteryHandler() {
  if (mechineIsRunnig) {
    console.log('机器正在跑，别动！')
    focusCore.eventLog('lottery.mechineIsRunnig', `${mechineIsRunnig}`)
    return false
  }
  mechineIsRunnig = true
  modalStore.loadingStart('checkBeforeLottery')
  checkBeforeLottery()
    .then(() => {
      modalStore.loadingEnd('checkBeforeLottery')
      focusCore.eventLog('lottery.mechineIsRunnig', `${mechineIsRunnig}`)

      lotterAnimation.start()
      doLottery().then((rewardData) => {
        console.log('🚀 ~ file: lottery-controler.vue ~ line 173 ~ doLottery ~ rewardData', rewardData)

        if (!rewardData) {
          console.error('出错啦！')
          lotterAnimation.endInPlaceId(0, () => {
            mechineIsRunnig = false
            focusCore.eventLog('lottery.mechineIsRunnig', `${mechineIsRunnig}`)
          })
          return false
        }
        const { placeId } = rewardData
        console.log('中奖了，奖品是', placeId)
        lotterAnimation.endInPlaceId(placeId, () => {
          setTimeout(() => {
            showPrice(rewardData)
            mechineIsRunnig = false
            focusCore.eventLog('lottery.mechineIsRunnig', `${mechineIsRunnig}`)
          }, 100)
        })
      })
    })
    .catch((failCode) => {
      console.log('不能抽奖哦！')
      modalStore.loadingEnd('checkBeforeLottery')
      focusCore.eventLog('checkBeforeLottery_fail', `${failCode}`)
      setTimeout(() => {
        mechineIsRunnig = false
      }, 100)
      focusCore.eventLog('lottery.mechineIsRunnig', `${mechineIsRunnig}`)
      switch (failCode) {
        case 1:
          console.log('没关注公众号，弹窗')
          modalStore.confirmContrl({
            show: true,
            title: '请关注微众银行App公众号',
            contents: ['关注后才能抽奖哦，如果已关注仍提示未关注，请先取消关注后再重新关注'],
            btnConfirmText: '去关注',
            btnConfirmJumpConfig: focusCore.env.isInApp
              ? {
                  path: '/pages/webview/webview?url=https%3A%2F%2Fmp.weixin.qq.com%2Fs%2FfEo-nWpcKo-KJtIs_2i7IA',
                  method: 'mini',
                }
              : {
                  path: 'https://personal.webank.com/s/hj/op2/wxschema/index.html?configid=54',
                  method: 'wxUrl',
                },
          })
          break
        case 2:
          console.log('没绑定微信，弹窗')

          // 2025-06-10 新弹窗
          focusCore.checkAppFn(AppInvokeName.AppBindWeChatEC).then((status) => {
            if (status) {
              modalStore.confirmContrl({
                show: true,
                contents: ['参与抽奖，请先绑定微信'],
                btnConfirmText: '去绑定',
                btnCancelText: '取消',
                confirmCb: () => {
                  // window.hjCoreIab.login()
                  hjCoreIabPlus.bindWechat().then(() => {
                    // modalStore.toastShow('绑定微信成功')
                    focusCore.eventLog('lottery_bindWx', 'success')
                  })
                },
              })
            } else {
              modalStore.confirmContrl({
                show: true,
                title: '请绑定微信',
                contentAlign: 'left',
                contents: [
                  '1. 在登录界面选择微信登录',
                  '2. 在三分钟急速开户界面点“已有账户”',
                  '3. 输入姓名、身份证号、手机号获取验证码，再输入验证码通过验证即可绑定微信',
                ],
                btnConfirmText: '去绑定',
                btnCancelText: '知道了',
                confirmCb: () => {
                  if (window.hjCoreIab) {
                    window.hjCoreIab.login()
                  }
                },
              })
            }
          })

          break
        default:
          break
      }
    })
}
function checkBeforeLottery() {
  return new Promise((resolve, reject) => {
    console.log('开始检查是否有资格')
    lotteryLog.checkCanlottery(lotteryInfo.lotteryTimes, userStore.isSubscribe)
    // return resolve(true)
    if (!lotteryInfo.lotteryTimes) {
      focusCore.eventLog('stop_lottery', `${lotteryInfo.lotteryTimes}_${userStore.isSubscribe}`)
      return reject(0)
    }
    // 获取公众号关注状态
    Promise.all([userService.checkSubscribeStatus(), userService.checkBindWechat()]).then((res: Array<Boolean>) => {
      const isSubscribe = res[0]
      const isBindWechat = res[1]
      console.log('🚀 ~ 关注结果：', isSubscribe)
      console.log('🚀 ~ 绑定了微信：', isBindWechat)
      if (!isBindWechat) {
        return reject(2)
      }
      if (!isSubscribe) {
        return reject(1)
      }

      return resolve(0)
    })
    // return resolve(0)
  })
}
function getLotteryData(configId: string = '', aids: string) {
  console.log('获取抽奖信息')

  console.log('🚀  ~ configId', configId)
  console.log('🚀 ~ aids', aids)
  loadingStart('getLotteryData')

  lotteryService.getLotteryTimes(configId, aids).then((res: any) => {
    console.log('🚀 ~ 抽奖信息：！', res)
    loadingEnd('getLotteryData')
    if (!res.priceList || !res.priceList.length) {
      console.log('抽奖信息出错了！')
      // modalStore.errorMaskContrl('lotteryService.getLotteryTimes')
      return false
    }
    const { doLotteryAid, lotteryTimes, lotteryTimesExpireTime } = res || {}
    lotteryInfo.lotteryTimes = lotteryTimes
    lotteryInfo.doLotteryAid = doLotteryAid
    lotteryInfo.lotteryTimesExpireTime = lotteryTimesExpireTime
    priceList.value = [
      { placeId: 1 },
      { placeId: 2 },
      { placeId: 3 },
      { placeId: 8 },
      { placeId: 9 },
      { placeId: 4 },
      { placeId: 7 },
      { placeId: 6 },
      { placeId: 5 },
    ].map((item, index) => {
      const placeId = item.placeId
      if (index === 4) {
        return item
      }
      const data = res.priceList[index >= 4 ? index - 1 : index] || {}
      return {
        ...data,
        placeId,
      }
    })
  })
}

function doLottery() {
  console.log('开始抽奖咯！')
  focusCore.eventLog('lotteryStart', `${lotteryInfo.lotteryTimes}`)
  return new Promise((resolve) => {
    lotteryService
      .doLottery(lotteryInfo.doLotteryAid)
      .then((rewardData) => {
        lotteryLog.doLottery(lotteryInfo.doLotteryAid, lotteryInfo.lotteryTimes)
        const priceData = formatePriceData(rewardData)
        // 抽奖结束，更新一下抽奖信息
        setTimeout(() => {
          getLotteryData(configId, aids)
        }, 100)
        resolve(priceData)
      })
      .catch((err) => {
        console.log('🚀 ~ file: lottery-controler.vue ~ line 205 ~ lotteryService.doLottery ~ err', err)
        modalStore.errorMaskContrl('lotteryService.doLottery')

        resolve(null)
      })
  })
}
function formatePriceData(priceData: any) {
  const {
    lotteryCardDesc = '0',
    lotteryCardName = '0',
    resultPicUrl = '',
    shareAwardResultDesc = '',
    shareAwardResultPic = '',
    shareAwardTitle = '',
    shareAwardUrl = '',
    id,
  } = priceData
  console.log('整理中奖信息')
  const { placeId } = priceList.value.find((i) => i.id === id) || { placeId: 0 }
  return {
    placeId,
    show: true,
    title: lotteryCardName,
    priceImgUrl: resultPicUrl,
    desc: lotteryCardDesc,
    shareUrl: shareAwardUrl,
    shareImg: shareAwardResultPic,
    shareTitle: shareAwardTitle,
    shareDesc: shareAwardResultDesc,
  }
}
function showPrice(_priceData: any) {
  console.log('🚀 ~ file: lottery-controler.vue ~ line 156 ~ showPrice ~ priceData', _priceData)
  console.log('展示奖品内容！')
  if (!_priceData.show) {
    lotterAnimation.reset()
    priceData.value = {
      show: false,
    }
    mgmService.resetDefault('close-price')
  }
  priceData.value = {
    ...priceData.value,
    ..._priceData,
  }
}
</script>

<style lang="scss" scoped>
.lottery-box {
  position: relative;
}
.lottery-lamp {
  position: absolute;
  bottom: 20px;
  left: 30px;
  z-index: 1;
}
.price-dialog {
  &.contain {
    width: 560px;
    background: #fff;
    border-radius: 20px;
    position: relative;
    // box-sizing: border-box;
    padding-top: 60px;
    padding-bottom: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    color: #405080;
    text-align: center;
    &.setsmall_7 {
      transform: scale(0.7) !important;
    }
    &.setsmall_6 {
      transform: scale(0.6) !important;
    }
    &.setsmall_5 {
      transform: scale(0.5) !important;
    }
    &.setsmall_1 {
      transform: scale(1) !important;
    }
    .title {
      font-size: 43px;
      font-weight: bold;
      line-height: 1.5;
    }
    .title-price {
      font-size: 43px;
      font-weight: bold;
      color: #f05649;
      line-height: 1.5;
    }
    .cover {
      width: 290px;
      height: 260px;
      margin-top: 40px;
    }
    .desc {
      font-size: 24px;
      margin-top: 30px;
      padding: 0 30px;
      line-height: 1.5;
    }

    .btns {
      width: 100%;
      margin-top: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30px;
      z-index: 222;
      box-sizing: border-box;
      .btn-share,
      .btn-detail {
        width: 240px;
        height: 72px;
        line-height: 70px;
        background: #f3ab56 100%;
        font-size: 28px;
        color: #fff;
        border-radius: 72px;
        cursor: pointer;
        position: relative;
        z-index: 222;
      }
      .btn-share {
        background-color: #456ce6;
      }
    }

    .btn-close {
      position: absolute;
      width: 60px;
      height: 60px;
      top: -90px;
      right: 0px;
      background: url('../../../assets/img/common/btn-close.png') no-repeat;
      background-size: contain;
      z-index: 222;
    }
  }
}
</style>
