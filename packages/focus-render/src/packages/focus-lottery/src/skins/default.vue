<template>
  <div class="lottery-default-skin">
    <img :src="skinData.mechinBg" alt="机器" class="mechine" aria-hidden="true" />
    <div class="main">
      <div class="light-bar" aria-hidden="true">
        <div class="bar">
          <div class="light" v-for="(item, index) in 7" :style="{ backgroundImage: `url(${skinData.lightOff})` }">
            <img :src="skinData.light" alt="" />
          </div>
        </div>
        <div class="bar right">
          <div class="light" v-for="(item, index) in 7" :style="{ backgroundImage: `url(${skinData.lightOff})` }">
            <img :src="skinData.light" alt="" />
          </div>
        </div>
        <div class="bar left">
          <div class="light" v-for="(item, index) in 7" :style="{ backgroundImage: `url(${skinData.lightOff})` }">
            <img :src="skinData.light" alt="" />
          </div>
        </div>
      </div>
      <div class="price-list">
        <div
          class="price"
          v-for="(price, index) in priceList"
          :key="price.placeId"
          :style="{ background: selectedPlaceId === price.placeId ? skinData.priceSlectedBg : '' }"
          :aria-label="`第${index < 5 ? index + 1 : index}个奖品 ${price.name}`"
          :aria-hidden="price.placeId === 9 ? true : false"
          role="img"
        >
          <img class="cover" :src="price.picUrl" aria-hidden="true" />
        </div>
      </div>
    </div>
    <div class="my-reward">
      <div class="btn" @click="clickMyReward" data-reportid="my-reward" role="button">我的奖品</div>
    </div>
    <p class="lottery-times" v-if="lotteryInfo.lotteryTimes">
      有{{ lotteryInfo.lotteryTimes }}次抽奖机会(将于{{ lotteryInfo.lotteryTimesExpireTime }}失效)
    </p>
    <p class="lottery-times" v-else>暂无抽奖机会</p>
    <div
      class="btn-lottery"
      @click="clickLotteryStart"
      :class="lotteryInfo.lotteryTimes ? '' : 'disabled'"
      v-bind:data-reportid="lotteryBtnReportId"
      :style="{ ...btnStyle }"
      role="button"
      :aria-disabled="lotteryInfo.lotteryTimes ? false : true"
    >
      {{ btnTitle }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, watchEffect, toRefs, ref, computed, onMounted } from 'vue'
const priceList = inject<any[]>('priceList')
const lotteryInfo = inject<{
  lotteryAids: []
  lotteryTimes: 0
  doLotteryAid: 0
  myRewardLink: ''
  lotteryTimesExpireTime: ''
}>('lotteryInfo', { lotteryAids: [], lotteryTimes: 0, doLotteryAid: 0, myRewardLink: '', lotteryTimesExpireTime: '' })

const skinData =
  inject<{
    mechinBg?: string
    priceSlectedBg?: string
    btnUp?: string
    btnDisabled?: string
    btnColor?: string
    light?: string
    lightOff?: string
  }>('lotterySkin') || {}
const clickLotteryStart = inject('clickLotteryStart')
const selectedPlaceId = inject('selectedPlaceId')
let btnTitle = '立即抽奖'
const clickMyReward = inject('clickMyReward')
const lotteryBtnReportId = computed(() => {
  if (lotteryInfo) {
    return `btn-lottery_${lotteryInfo.lotteryTimes}`
  }
  return `btn-lottery`
})

const btnStyle = computed(() => {
  console.log('....', skinData)
  const res: any = {
    color: skinData.btnColor,
  }
  if (skinData.btnUp && lotteryInfo.lotteryTimes) {
    res.backgroundImage = `url(${skinData.btnUp})`
  }
  if (skinData.btnDisabled && !lotteryInfo.lotteryTimes) {
    res.backgroundImage = `url(${skinData.btnDisabled})`
  }

  return res
})
onMounted(() => {
  changeLittleLightImg()
})
function changeLittleLightImg() {
  const lightOn = skinData.light
  const lightOff = skinData.lightOff
  //   console.log('🚀 ~ file: default.vue ~ line 101 ~ changeLittleLightImg ~ lightOn', lightOn)

  //   const keyframes = `@keyframes blink {
  //   0% {
  //     background-image:${lightOn};
  //   }
  //   50% {
  //     background-image: ${lightOff};
  //   }
  //   100% {
  //     background-image:${lightOn};
  //   }
  // }
  // `
  //   const keyframes2 = `@keyframes blink2 {
  //   0% {
  //     background-image: ${lightOff};
  //   }
  //   50% {
  //     background-image:${lightOn};
  //   }
  //   100% {
  //     background-image: ${lightOff};
  //   }
  // }`
  //   const style = document.createElement('style')
  //   style.setAttribute('type', 'text/css')
  //   document.head.appendChild(style)
  //   let sheet = style.sheet
  //   sheet?.insertRule(keyframes, 0)
}
</script>

<style lang="scss" scoped>
$light-on: url('../../../../assets/img/focus-lottery/light.png');
$light-off: url('../../../../assets/img/focus-lottery/light-off.png');
.lottery-default-skin {
  width: 100%;
  height: 912px;
  overflow: hidden;
  position: relative;
  .mechine {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 1;
    width: 100%;
    height: 912px;
  }
  .main {
    width: 600px;
    height: 560px;
    margin: 0 auto;
    position: relative;
    box-sizing: border-box;
    z-index: 2;
    .light-bar {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: 1;
      .bar {
        height: 40px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-around;
        position: absolute;
        top: 0;
        left: 0;
        box-sizing: border-box;
        padding: 0 30px;
        .light {
          width: 30px;
          height: 30px;
          background-size: 100% 100%;
          img {
            animation: 1s step-start 0s infinite blink;
          }
          &:nth-child(odd) {
            img {
              animation: 1s step-start 0s infinite blink2;
              opacity: 0;
            }
          }
        }
        &.right,
        &.left {
          right: 0;
          left: auto;
          height: 100%;
          width: 40px;
          flex-direction: column;
          padding: 30px 0;
        }
        &.left {
          left: 0;
        }
        &.bottom {
          top: auto;
          bottom: 0;
        }
      }
    }
    .price-list {
      position: absolute;
      left: 40px;
      top: 40px;
      right: 40px;
      bottom: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      z-index: 2;
      .price {
        width: 33.33%;
        height: 33.33%;
        box-sizing: border-box;
        border: 5px solid transparent;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 40px;
        position: relative;
        .cover {
          position: relative;
          z-index: 2;
        }
      }
    }
  }
  .my-reward {
    position: relative;
    z-index: 2;
    width: 600px;
    height: 70px;
    margin: 0 auto;
    .btn {
      width: 200px;
      height: 70px;
      position: absolute;
      right: 20px;
      top: -30px;
      opacity: 0;
      cursor: pointer;
    }
  }
  .lottery-times {
    position: relative;
    z-index: 2;
    margin: 0 auto;
    font-size: 28px;
    color: #fff;
    height: 30px;
    line-height: 30px;
  }
  .btn-lottery {
    width: 410px;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    background: url('../../../../assets/img/focus-lottery/btn-up.png') no-repeat;
    background-size: 100% 100%;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    color: #fff;
    cursor: pointer;
    &.disabled {
      background-image: url('../../../../assets/img/focus-lottery/btn-down.png');
    }
  }
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes blink2 {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>
