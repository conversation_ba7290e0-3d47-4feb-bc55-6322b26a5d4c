<template>
  <div v-if="messages.length" class="marquee-container">
    <div class="marquee-wrapper">
      <div
        v-for="(item, index) in [currentMessage, nextMessage]"
        :key="index"
        class="marquee-item"
        :class="{ 'scroll-top': index === 0 && isAnimating, 'scroll-bottom': index === 1 && isAnimating }"
        :style="{ opacity: index === 1 ? 0 : 1 }"
      >
        恭喜{{ item.personalName }}抽中{{ truncateText(item.awardName, 15) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { lotteryService } from '../../../service'

const props = defineProps<{
  configId: string
}>()

interface LotteryMessage {
  awardName: string
  personalName: string
}

const messages = ref<LotteryMessage[]>([
  // { personalName: '张三', awardName: '一等奖' },
  // { personalName: '李四', awardName: '二等奖' },
  // { personalName: '王五', awardName: '三等奖' },
  // { personalName: '赵六', awardName: '幸运奖' },
])

const currentIndex = ref(0)
const isAnimating = ref(false)
// const timer = ref<ReturnType<typeof setInterval> | null>(null)
let startTime = performance.now()
let animationFrameId: number | null = null

// 当前显示的消息
const currentMessage = computed(() => messages.value[currentIndex.value])
// 下一条消息
const nextMessage = computed(() => {
  const nextIndex = (currentIndex.value + 1) % messages.value.length
  return messages.value[nextIndex]
})

function truncateText(text: string, maxLength: number): string {
  if (text.length > maxLength) {
    return text.slice(0, maxLength) + '...'
  }
  return text
}

function animate() {
  if (messages.value.length === 1) return // 如果只有一条消息，直接返回
  const currentTime = performance.now()
  if (currentTime - startTime >= 3000) {
    startTime = currentTime
    isAnimating.value = true
    setTimeout(() => {
      currentIndex.value = (currentIndex.value + 1) % messages.value.length
      isAnimating.value = false
    }, 500) // 动画持续时间 0.5s
  }
  animationFrameId = requestAnimationFrame(animate)
}

// 获取跑马灯抽奖信息
function getLotteryLamp() {
  return new Promise((resolve) => {
    lotteryService.getLotteryLamp(props.configId).then((result) => {
      messages.value = result
      // 最多20条消息
      if (messages.value.length > 20) {
        messages.value = messages.value.slice(0, 20)
      }
      if (messages.value.length > 0) {
        startTime = performance.now() // 重置时间，避免动画跳跃
        animationFrameId = requestAnimationFrame(animate)
      }
      resolve(result)
    })
  })
}
getLotteryLamp()

function handleVisibilityChange() {
  if (document.visibilityState === 'visible') {
    getLotteryLamp()
  } else if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId) // 页面隐藏时暂停动画
  }
}

// 启动定时器
onMounted(() => {
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

onUnmounted(() => {
  if (animationFrameId) cancelAnimationFrame(animationFrameId)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})
</script>

<style scoped>
.marquee-container {
  width: 690px;
  height: 60px;
  margin: auto;
  overflow: hidden;
  background-color: rgba(255, 64, 38, 0.25); /* 背景色 #FF4026，25% 不透明度 */
  border-radius: 35px; /* 圆角 35px */
  position: relative;
}

.marquee-wrapper {
  position: relative;
  height: 100%;
}

.marquee-item {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26px;
  color: #ffffff;
  opacity: 1;
}

.scroll-top {
  animation: scrollTop 0.4s ease-in-out forwards;
}

.scroll-bottom {
  animation: scrollBottom 0.4s ease-in-out forwards;
}

@keyframes scrollTop {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-100%);
    opacity: 0;
  }
}

@keyframes scrollBottom {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
