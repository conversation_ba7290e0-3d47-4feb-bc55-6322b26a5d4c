<template>
  <BtnBase
    :moduleData="moduleData"
    :text="successText"
    :img="successImg"
    :customStyle="successStyles"
    :class="{ noclick: !canClick }"
    @click="clickAddingBtn"
  ></BtnBase>
</template>

<script setup lang="ts">
import BtnBase from '@/components/BtnBase.vue'
import { toRefs, toRaw, watch, unref, computed, ref, onMounted } from 'vue'
import { jumpService, mgmService, activityService } from '../../service'
import { focusStore } from '../../store/index'
import { focusCore, definedReportData } from '../focus-render'
import { StyleHandler } from '../hooks/StyleHandler'
import { useModuleDataResolve, handleClickByClickEvent } from '../hooks'
import { storeToRefs } from 'pinia'

const { modalStore, activityStore } = focusStore.stores

const props = defineProps<{ moduleData: NFocusBtnClick.moduleData }>()
const { moduleData } = toRefs(props)
const { addingAids, aidCheckStatus } = storeToRefs(activityStore)
console.log('🚀 ~ file: focus-btn-adding.vue ~ line 17 ~ moduleData', moduleData.value.data)
const { aid } = moduleData.value.data
const { success } = moduleData.value.data
const isFirst = ref(true)
const reportData = ref({
  aid,
  isFirst,
})

onMounted(() => {
  if (focusCore.isLogined) {
    console.error('登录啦！可以请求！')
    modalStore.loadingStart('checkAidsIsReady')
    activityService.checkAidsIsReady(aid).finally(() => {
      modalStore.loadingEnd('checkAidsIsReady')
    })
    activityService.checkWrInfoStatus(aid).then((_aid) => {
      if (_aid) {
        // 已经参与过了，不是首次
        console.log('已经参与过了，不是首次')
        isFirst.value = false
      }
    })
  }
})

const isSuccess = computed(() => {
  console.log('...isSuccess', addingAids)
  const status = aidCheckStatus.value[aid] || {}
  const hasAccountCheck = focusCore.hasAccount && (status.isUsedAndInGray || status.isNotUsedGray)
  const notHasAccountCheck = !focusCore.hasAccount && status.isSupportNoAuth
  if (hasAccountCheck || notHasAccountCheck || status.noNeedCheck) {
    return addingAids.value.indexOf(Number(aid)) > -1
  }
  return false
})
const successText = computed(() => {
  return isSuccess.value ? success.text : ''
})
const successImg = computed(() => {
  return isSuccess.value ? success.imgUrl : ''
})
const canClick = ref(false)

const { once = '1', isJumpNow = '1', clickEvent, joinType = 'joinNow' } = moduleData.value.data

if (joinType === 'joinNow') {
  canClick.value = true
}

if (isSuccess.value && once === '1' && !isFirst.value) {
  canClick.value = false
}

function clickAddingBtn() {
  modalStore.loadingStart('clickAddingBtn')

  activityService
    .checkAidsIsReady(aid)
    .then(
      ({
        isSupportNoAuth,
        isDuring,
        isUsedAndNotInGray,
        isBeforeStartTime,
        isAfterEndTime,
        isReadyStatus,
        noNeedCheck,
      }) => {
        console.log(
          '🚀 ~ .then ~ isSupportNoAuth, isDuring, isUsedAndNotInGray, isBeforeStartTime, isAfterEndTime, isReadyStatus:',
          isSupportNoAuth,
          isDuring,
          isUsedAndNotInGray,
          isBeforeStartTime,
          isAfterEndTime,
          isReadyStatus
        )
        console.log('🚀 ~ .then ~ focusCore.hasAccount:', focusCore.hasAccount)
        console.log('🚀 ~ clickAddingBtn ~ isSupportNoAuth:', isSupportNoAuth)
        console.log('🚀 ~ clickAddingBtn ~ isDuring:', isDuring)

        if (noNeedCheck) {
          setwrinfo(noNeedCheck)
          return
        }

        if (!focusCore.hasAccount) {
          if (!isSupportNoAuth || !isDuring) {
            console.log('用户未开户，要去开户')
            jumpService.commonUse.openAccount()
            return
          }
        }

        if (isUsedAndNotInGray) {
          modalStore.toastShow('非常抱歉，本活动仅限受邀客户参与')
          return
        }

        if (isAfterEndTime || !isReadyStatus) {
          modalStore.toastShow('活动已结束，下次再来参与吧~')
          return
        }
        if (isBeforeStartTime) {
          modalStore.toastShow('活动还没开始，请耐心等待哦')
          return
        }

        setwrinfo()
      }
    )
    .catch(() => {
      modalStore.errorMaskContrl('clickAddingBtn_checkAid_fail')
    })
    .finally(() => {
      modalStore.loadingEnd('clickAddingBtn')
    })
}

function setwrinfo(noNeedCheck?: boolean) {
  const { once = '1', isJumpNow = '1', clickEvent } = moduleData.value.data
  if (!canClick.value) {
    console.log('配置--->禁止点击，只能展示')
    return
  }
  modalStore.loadingStart('btn-adding')
  activityService
    .setWrInfo(aid)
    .then((_aid) => {
      console.log('🚀 ~ .then  setwrinfo~ _aid:', _aid)
      modalStore.loadingEnd('btn-adding')

      if (_aid) {
        console.log(moduleData.value.data)
        let canJump = true
        if (once === '1') {
          // 只有首次才触发跳转
          console.log('配置----> 只有首次才触发跳转')
          if (isFirst.value) {
            console.log('当前是首次参与，可以跳转')
            isFirst.value = false
          } else {
            console.log('当前不是首次参与，不可以跳转')
            canJump = false
          }
        } else if (once === '0') {
          // 每次都可以触发效果
          console.log('配置----> 每次都可以触发效果')
        }
        if (isJumpNow === '0') {
          // 触发跳转时，首次不跳转
          console.log('配置----> 触发跳转时，首次不跳转')
          if (isFirst.value) {
            console.log('当前是首次参与，不可以跳转')
            isFirst.value = false // 调整后，下次点击就可以跳转了
            canJump = false
          }
        } else if (isJumpNow === '1') {
          // 触发跳转时，每次都跳转
          console.log('配置----> 触发跳转时，每次都跳转')
        }

        if (!canJump) {
          console.log('不能跳转')
          return
        }
        console.log('开始跳转')
        setTimeout(() => {
          handleClickByClickEvent(clickEvent, 'BtnAdding', moduleData.value.moduleId.toString())
        }, 300)
      }
    })
    .catch(({ isFail, errCode }) => {
      if (!focusCore.hasAccount) {
        jumpService.commonUse.openAccount()
        return
      }
      console.log('🚀 ~ setwrinfo ~ errCode:', errCode)
      if (isFail) {
        let toastMsg = '网络开了会小差，再试一次哦~'
        const errMsgs = {
          '-1': '活动已结束，下次再来参与吧~',
          '-2': '非常抱歉，本活动仅限受邀客户参与',
          '-3': '非常抱歉，本活动仅限受邀客户参与',
          '-4': '活动未开始，下次再来参与吧~',
          '-5': '活动已结束，下次再来参与吧~',
        }
        toastMsg = errMsgs[errCode] || '网络开了会小差，再试一次哦~'
        modalStore.toastShow(toastMsg)
      }
    })
    .finally(() => {
      modalStore.loadingEnd('btn-adding')
    })
}

const successStyles = computed(() => {
  if (!isSuccess.value) {
    return {}
  }
  const styles = new StyleHandler({ ...moduleData.value, styles: success })
  const { bg, fontSize, fontStyles } = styles
  console.log('🚀 ~ file: focus-btn-adding.vue ~ line 128 ~ successStyles ~ bg', bg)
  return {
    ...bg,
    ...fontSize,
    ...fontStyles,
  }
})
</script>

<style lang="scss" scoped>
.noclick {
  &:active {
    opacity: 1;
  }
}
</style>
