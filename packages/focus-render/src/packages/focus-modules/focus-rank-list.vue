<template>
  <div class="rank-list">
    <div class="item header">
      <div class="index">排名</div>
      <div class="name">昵称</div>
      <div class="score">{{ title3 }}</div>
    </div>
    <div class="empty" v-if="!showList.length">还没有人上榜，快去分享吧~</div>
    <div class="item" v-for="(item, index) in showList" :key="index">
      <div class="index" :class="`index_${index}`" :style="{ color: nameColor }">
        {{ index + 1 }}
      </div>
      <div class="name" :style="{ color: nameColor }">{{ item.nickName }}</div>
      <div class="score" :style="{ color: scoreColor }">{{ item.points }}</div>
    </div>

    <div class="bottom-info">
      <p class="time">数据统计时间: {{ dynamicData.rankDsDate }}</p>
      <div class="btn-more" @click="clickShowMore" v-if="canShowMoreLink">更多排名</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, onMounted, unref, ref, toRaw, toRefs, watchEffect } from 'vue'
import { useModuleDataResolve } from '../hooks/index'
import 'swiper/swiper.min.css'
import { jumpService } from '@/service'

const props = defineProps<{
  moduleData: TModuleData
}>()

const { configData, styleData, dynamicData } = useModuleDataResolve<
  NFocusRankList.configData,
  NFocusRankList.dynamicData
>(props.moduleData, ['confRankIndex', 'rankDsDate', 'rankList'])
watchEffect(() => {
  console.log('.....dynamicData.....', dynamicData.value)
  console.log('.....configData.....', configData.value)
})

const canShowMoreLink = ref(false)
const showList = computed(() => {
  const showNums = Number(configData.value.showListLen || '0')
  const confShowMax = Number(configData.value.showListMax || '0')
  const originRankList = dynamicData.value.rankList
  const originLen = originRankList.length || 0

  if (originLen) {
    const max = originLen >= confShowMax ? confShowMax : originLen
    if (max > showNums) {
      canShowMoreLink.value = true
    } else {
      canShowMoreLink.value = false
    }
    return originRankList.slice(0, showNums)
  }
  return []
})
// color1: '#7A7A7A', // 序号、昵称颜色
// color2: '#828282' // 分数颜色
const nameColor = computed(() => {
  return configData.value.color1 || '#7A7A7A'
})

const scoreColor = computed(() => {
  return configData.value.color2 || '#828282'
})

const title3 = computed(() => {
  return configData.value.title3
})

function clickShowMore() {
  jumpService.jump({
    path: configData.value.moreListLink,
  })
}
</script>

<style lang="scss" scoped>
.rank-list {
  width: 650px;
  margin: 0 auto;
  .empty {
    height: 290px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 24px;
  }
  .item {
    width: 650px;
    height: 110px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: center;
    border-bottom: 1px solid #d9d2c942;
    color: #7a7a7a;
    font-weight: 500;
    font-size: 28px;
    position: relative;
    margin: 0 auto;
    &.header {
      height: 97px;
      font-size: 30px;
      color: #5b5a5a;
    }
    .index {
      margin-left: 60px;
      width: 80px;
      height: 80px;
      flex: none;
      background-size: 100% 100%;
      background-position: center center;
      line-height: 80px;
      &.index_0 {
        color: transparent !important;
        background-image: url('../../assets/img/focus-rank-list/index_0.png');
      }
      &.index_1 {
        color: transparent !important;
        background-image: url('../../assets/img/focus-rank-list/index_1.png');
      }
      &.index_2 {
        color: transparent !important;
        background-image: url('../../assets/img/focus-rank-list/index_2.png');
      }
    }
    .name {
      line-height: 1;
      flex: 1;
      text-align: center;
      margin-left: 70px;
    }
    .score {
      flex: none;
      width: 150px;
      margin-right: 55px;
      text-align: right;
      line-height: 1;
    }
  }
  .bottom-info {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 84px;
    .time {
      font-size: 24px;
      color: #999;
    }
  }
  .btn-more {
    font-size: 24px;
    color: #456ce6;
    line-height: 42px;
    font-weight: 400;
    height: 84px;
    width: fit-content;
    text-align: center;
    line-height: 82px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    &::after {
      content: '';
      width: 13px;
      height: 22px;
      background: url('../../assets/img/focus-rank-list/icon_right.png') no-repeat;
      background-size: cover;
      background-position: center;
      margin-left: 20px;
    }
  }
}
</style>
