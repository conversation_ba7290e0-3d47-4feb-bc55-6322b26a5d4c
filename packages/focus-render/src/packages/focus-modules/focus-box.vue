<template>
  <div class="focus-box" :style="curStyles">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { unref, computed } from 'vue'
import { useModuleDataResolve } from '../hooks/index'

const props = defineProps<{
  moduleData: TModuleData
}>()
const { configData, styleData } = useModuleDataResolve<NFocusBox.configData>(props.moduleData)

const curStyles = computed(() => {
  const { boxSize, bg, margins, position, borderRadius } = unref(styleData)

  const flexStyles = (() => {
    let { flex = '', boxh } = configData.value
    const _flex = flex.split(',')
    const result: any = {}
    if (boxSize.width === '100%') {
      result.flex = 'auto'
    }
    if (_flex[0] !== 'column') {
      result.flexDirection = _flex[0]
    }
    if (_flex[1] !== 'center') {
      result.justifyContent = _flex[1]
    }
    if (_flex[2] !== 'center') {
      result.alignItems = _flex[2]
    }
    if (boxh === '1') {
      return {
        ...result,
        height: 'auto',
        width: 'auto',
        flex: 'none',
      }
    }
    if (boxh === '2') {
      return {
        ...result,
        height: 'auto',
        flex: 'none',
      }
    }

    return result
  })()
  return {
    ...boxSize,
    ...bg,
    ...margins,
    ...position,
    ...borderRadius,
    ...flexStyles,
  }
})
</script>

<style lang="scss">
.focus-box {
  background-color: #eeeeee;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  width: 100%;
  height: 420px;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  overflow: hidden;
  flex: none;
  position: relative;
  box-sizing: border-box;
  border: none;
  line-height: 0;
}
</style>
