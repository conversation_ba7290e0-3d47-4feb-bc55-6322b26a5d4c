<template>
  <p class="count-down-timer" :style="curStyles">
    {{ timeText }}
  </p>
</template>

<script setup lang="ts">
import { computed, defineProps, onUnmounted, unref, watch, watchEffect } from 'vue'
import { useModuleDataResolve } from '../hooks/index'
import { focusStore } from '../focus-render'
const { timerStore } = focusStore.stores
const props = defineProps<{
  moduleData: TModuleData
}>()

const { configData, styleData, dynamicData } = useModuleDataResolve<NFocusCountDownTimer.configData>(props.moduleData, [
  'text',
])

const afterCountEnd = () => {
  window.location.reload()
}

timerStore.initTimer(props.moduleData.moduleId, configData.value.endTime, configData.value.uiType, afterCountEnd)

const timeText = computed(() => {
  return timerStore.timerText[`${props.moduleData.moduleId}`]
})

const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    timerStore.visibleTimer(props.moduleData.moduleId, afterCountEnd)
  } else {
    timerStore.clearTimer()
  }
}

document.addEventListener('visibilitychange', handleVisibilityChange)

onUnmounted(() => {
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})

const curStyles = computed(() => {
  const { boxSize, bg, margins, position, borderRadius, fontStyles, fontSize } = unref(styleData)
  const { changeStyles = {} } = props.moduleData
  let color = fontStyles.color
  return {
    ...boxSize,
    ...bg,
    ...margins,
    ...fontStyles,
    ...fontSize,
    ...position,
    ...changeStyles,
    color,
  }
})
</script>

<style lang="scss" scoped>
.count-down-timer {
  line-height: 1.5;
}
</style>
