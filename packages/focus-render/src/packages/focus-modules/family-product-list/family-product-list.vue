<template>
  <div v-if="productList.length > 0" class="module" :style="{ ...curStyles }">
    <div class="container">
      <div
        :class="['p-item', { 'is-last': index === productList.length - 1 }]"
        v-for="(item, index) in productList"
        :key="item.product_code"
      >
        <div class="p-title">
          <img class="p-titie-img" :src="item.extra_info.bank_logo_url" alt="" />
          <span v-for="str in item.product_name" class="p-title-text">{{ str }}</span>
        </div>
        <div class="content">
          <div class="l-content">
            <div class="p-rate">{{ item.rate_value }}</div>
            <div class="p-desc">{{ item.extra_info.rate_desc }}</div>
          </div>
          <div class="r-content">
            <div class="p-period">{{ item.product_period }}</div>
            <div class="p-min-amount">{{ item.extra_info.min_amount_desc_recommend }}</div>
          </div>
          <div
            @click="
              () => {
                handleShare(item.product_code)
              }
            "
            class="p-btn"
          >
            去分享
          </div>
        </div>
        <div
          class="text-more"
          @click="showMore(item.product_code)"
          :class="{ active: showMoreTip[item.product_code] }"
          v-if="item.extra_info.static_rate_tips"
        >
          <p>
            {{ item.extra_info.static_rate_tips }}
          </p>
          <div class="icon"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, provide, toRefs, watch, ref, Ref, unref, isRef, isProxy, toRaw, markRaw, computed } from 'vue'
import { useModuleDataResolve } from '@/packages/hooks'
import { focusStore } from '@/store/index'
import { jumpService, mgmService, productService } from '@/service'
import { UrlParser } from '@/utils/url'
import { focusCore } from '@/packages/focus-render'
const props = defineProps<{
  moduleData: TModuleData
}>()

const { configData, styleData } = useModuleDataResolve<FamilyProductList.configData>(props.moduleData)
const curStyles = computed(() => {
  const { margins } = unref(styleData)
  return {
    ...margins,
  }
})
const productList: Ref<Product[]> = ref([])
if (focusCore.isLogined) {
  productService.getProductListByCodesLogin(configData.value.codes).then((res) => {
    productList.value = res
  })
}

const showMoreTip = ref({})

const handleShare = function (code: string) {
  const shareData = focusCore.focusRenderCtr?.getShareConfigByConfigId(configData.value.configId) || {}
  shareData.shareUrl = new UrlParser(shareData.shareUrl).appendQuery({ code }).fullPath
  mgmService.clickShare('family-share', shareData)
}

function showMore(code: string) {
  console.log('🚀 ~ file: family-product-list.vue ~ line 85 ~ showMore ~ code', code)
  if (showMoreTip.value[code]) {
    delete showMoreTip.value[code]
  } else {
    showMoreTip.value[code] = true
  }
}
</script>

<style lang="scss" scoped>
@mixin text-eslipe {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.module {
  width: 690px;
  // min-height: 980px;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0px 2px 10px 0px rgba(143, 182, 191, 0.6);
  border-radius: 10px;
}

.container {
  position: relative;
  width: 670px;
  padding: 0 30px;
  // min-height: 960px;
  background-color: #ffffff;
  box-shadow: 0px 2px 10px 0px rgba(143, 182, 191, 0.6);
  border-radius: 10px;
  line-height: 1;
  overflow: hidden;
}

.p-item {
  position: relative;
  margin: 0 auto;
  width: 100%;
  border-bottom: 1px solid #c3d8de;
  padding-bottom: 40px;
  overflow: hidden;
  &.is-last {
    border-bottom: none;
  }
}

.p-title {
  display: flex;
  flex-wrap: wrap;
  margin-top: 32px;
}
$iconWidth: 48px;
.p-titie-img {
  display: inline;
  width: $iconWidth;
  height: $iconWidth;
  margin-right: 9px;
}

.p-title-text {
  line-height: $iconWidth;
  font-size: 28px;
  font-weight: bold;
}

.content {
  text-align: left;
  display: flex;
  align-items: flex-end;
  height: 80px;
  margin: 30px 0 0 0;
  position: relative;
}

.l-content {
  overflow: hidden;
  width: 270px;
}

.r-content {
  width: 148px;
}

.p-rate {
  font-size: 36px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #ff2433;
  font-weight: bold;
}

$descMarginTop: 20px;

.p-desc {
  margin-top: $descMarginTop;
  font-size: 24px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #666666;
  opacity: 0.7;
  @include text-eslipe();
}
.text-more {
  background: #f3f7f8;
  width: 100%;
  color: #9b9b9b;
  text-align: left;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  margin-top: 30px;
  border-radius: 10px;
  .icon {
    width: 40px;
    height: 40px;
    background: url('./imgs/btn_down.png') no-repeat;
    background-size: 100% 100%;
    flex: none;
    margin-right: 20px;
    margin-top: 10px;
  }
  p {
    flex: auto;
    font-size: 24px;
    line-height: 1.5;
    padding: 10px 20px;
    overflow: hidden;
    margin-right: 20px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &.active {
    .icon {
      transform: rotate(180deg);
    }
    p {
      word-wrap: break-word;
      white-space: normal;
    }
  }
}
.p-period {
  font-size: 32px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #242d3a;
  font-weight: bold;
}

.p-min-amount {
  margin-top: $descMarginTop;
  font-size: 24px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #666666;
  opacity: 0.7;
  @include text-eslipe();
}

.p-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 180px;
  height: 72px;
  background: url('./imgs/share-btn.png') no-repeat;
  background-size: contain;
  font-size: 36px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #ffffff;
}
</style>
