<template>
  <div class="focus-logo-bottom" :style="curStyles">
    <img class="img" :src="configData.imgUrl" alt="m_image" />
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, unref } from 'vue'
import { useModuleDataResolve } from '../hooks/index'

const props = defineProps<{
  moduleData: TModuleData
}>()
// const dynamicKey = NFocusImage?.dynamicKey
// alert(dynamicKey)
const { configData, styleData } = useModuleDataResolve<NFocusImage.configData>(props.moduleData)
const curStyles = computed(() => {
  const { boxSize, bg, margins, position, borderRadius } = unref(styleData)
  return {
    ...boxSize,
    ...bg,
    ...margins,
    ...position,
    ...borderRadius,
  }
})
</script>

<style lang="scss" scoped>
.focus-logo-bottom {
  flex: none;
  box-sizing: border-box;
  border: none;
  display: block;
  line-height: 0;
  width: 100%;
  font-size: 0;
  overflow: hidden;
  .img {
    width: 100%;
    height: 100%;
    display: block;
  }
}
</style>
