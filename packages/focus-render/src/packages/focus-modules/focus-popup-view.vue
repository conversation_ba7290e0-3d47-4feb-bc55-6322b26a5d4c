<template>
  <Mask class="popup-view" v-if="!!showPopData" :canScroll="'.popup-view .contain'">
    <div class="btn-close" @click="clickCloseBtn">
      <img src="../../assets/img/common/close-btn.png" />
    </div>

    <div class="contain">
      <module-center :moduleData="showPopData"></module-center>
    </div>
  </Mask>
</template>

<script setup lang="ts">
import ModuleCenter from '../focus-render/src/modue-center.vue'
import Mask from '../../components/Mask.vue'
import { focusStore } from '../../store/index'
import { storeToRefs } from 'pinia'
import { computed, inject, toRefs, toRaw, watch } from 'vue'
const { modalStore } = focusStore.stores
const { popupViewId } = storeToRefs(modalStore)
const props = defineProps<{
  popupData: TModuleData[]
}>()
const { popupData } = toRefs(props)
const showPopData = computed(() => {
  console.log('popupData.value', popupData.value)
  const target = popupData.value.find((i: any) => {
    return i.id === popupViewId.value
  })
  return target && target.tree
})

const clickCloseBtn = function () {
  modalStore.setPopupViewId(0)
}
</script>

<style lang="scss" scoped>
.popup-view {
  font-size: 20px;
  line-height: 1;
  width: 100%;
  .contain {
    position: relative;
    width: 690px;
    max-height: 80%;
    z-index: 2;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
    margin-top: 10px;
  }
  .btn-close {
    position: relative;
    width: 56px;
    height: 56px;
    align-self: flex-end;
    margin-right: 30px;
    margin-bottom: 5px;
    z-index: 2;
    cursor: pointer;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
