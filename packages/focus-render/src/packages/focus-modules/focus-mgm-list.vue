<template>
  <div class="mgm-list" :style="{ ...curStyles, height: 'auto' }">
    <div class="tabs" :style="{ backgroundColor: configData.titleBgColor }">
      <div
        class="item"
        :class="selectedIndex === index ? 'actived' : ''"
        v-for="(tab, index) in tabsData"
        :key="index"
        :style="selectedIndex === index ? activedStyle : {}"
        @click="clickTab(index)"
      >
        {{ tab.name }}
      </div>
    </div>
    <div class="content-list">
      <div class="empty" v-if="!showList.length">
        {{ noneList[selectedIndex] }}
      </div>
      <div class="item" v-for="(item, index) in showList">
        <img class="avatar" :src="item.head_img_url" />
        <p class="name">{{ item.nick_name }}</p>
        <div class="info">
          <p class="status" :style="{ color: tabsData[selectedIndex].statusColor }">
            {{ tabsData[selectedIndex].name }}
          </p>
          <p class="time">{{ item.time }}</p>
        </div>
      </div>

      <div
        class="btn-more"
        @click="clickShowMore"
        v-if="showBtnShowMore"
        :style="{ color: !isShowMore ? '#5f9bfa' : 'rgba(104, 83, 80, 0.7)' }"
      >
        {{ !isShowMore ? '显示更多 >>' : '全部朋友已展示' }}
      </div>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'mgm-list',
}
</script>
<script setup lang="ts">
import {
  inject,
  provide,
  toRefs,
  watch,
  ref,
  watchEffect,
  unref,
  isRef,
  isProxy,
  toRaw,
  markRaw,
  computed,
  defineProps,
} from 'vue'
import { focusStore } from '../../store/index'
import { jumpService, mgmService, focusService } from '../../service'
import { dayjs } from '../../utils/index'
import { useModuleDataResolve } from '../hooks/index'
const props = defineProps<{
  moduleData: NFocusMgmList.moduleData
}>()

const { userStore } = focusStore.stores

// const { moduleData } = toRefs(props)

// const stylesData = baseHooks.useModuleStylesHook(moduleData)
// const { configData } = baseHooks.useModuleConfigHook(moduleData)
const { configData, styleData } = useModuleDataResolve<NFocusMgmList.configData>(props.moduleData)
const tabsData = ref<{ name: string; id: number; statusColor: string }[]>([])
const selectedIndex = ref(0)
console.log('🚀 ~ file: focus-mgm-list.vue ~ line 26 ~ configData', configData)
const activedStyle = computed(() => {
  return {
    color: configData.value.activedColor,
    borderColor: configData.value.activedColor,
  }
})
const curStyles = computed(() => {
  const { boxSize, margins, position } = unref(styleData)
  return {
    ...boxSize,
    ...margins,
    ...position,
  }
})

const noneList = ['暂无待开户邀请记录', '暂无已开户邀请记录', '暂无已完成邀请记录']
const showDataLimit = ref(3)
const allDataList = ref<[TRelationShipItem[], TRelationShipItem[], TRelationShipItem[]]>([[], [], []])
const isShowMore = ref(false)
if (configData.value.mgmAid && userStore.hasAccount) {
  mgmService.getRelationshipList(Number(configData.value.mgmAid)).then((res) => {
    console.log('🚀 ~ file: focus-mgm-list.vue ~ line 61 ~ mgmService.getRelationshipList ~ res', res)
    const { enterprise = [], not_register = [], register = [] } = res
    allDataList.value = [
      not_register.map((i, index) => {
        return { ...i, time: dayjs(i.time).format('YYYY-MM-DD'), id: `0_${index}` }
      }),
      register.map((i, index) => {
        return { ...i, time: dayjs(i.time).format('YYYY-MM-DD'), id: `0_${index}` }
      }),
      enterprise.map((i, index) => {
        return { ...i, time: dayjs(i.time).format('YYYY-MM-DD'), id: `0_${index}` }
      }),
    ]
    console.log('allDataList', allDataList.value)
  })
}

function clickTab(index: number) {
  selectedIndex.value = index
  isShowMore.value = false
  showDataLimit.value = 3
}

function clickShowMore() {
  if (isShowMore.value) {
    return false
  }
  isShowMore.value = true
  showDataLimit.value = 13
}

const fontList = (configData.value.titleFont && configData.value.titleFont.split(',')) || []
if (fontList.length) {
  const colorList = ['#787E83', '#FF9667', '#f29661']

  tabsData.value = fontList.map((i: string, index: number) => {
    return {
      name: i,
      id: index,
      statusColor: colorList[index],
    }
  })
}
const showList = computed(() => {
  const target = allDataList.value[selectedIndex.value]
  return (target && target.filter((i, index) => index < showDataLimit.value)) || []
})
const showBtnShowMore = computed(() => {
  const target = allDataList.value[selectedIndex.value]
  return target && target.length > 3
})
</script>

<style lang="scss" scoped>
.mgm-list {
  box-shadow: 0 5px 10px rgba($color: #000000, $alpha: 0.1);
  font-size: 28px;
  border-radius: 10px;
  overflow: hidden;
  width: 690px;
  .tabs {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    .item {
      cursor: pointer;
      width: 100%;
      flex: 1;
      padding: 28px 0;
      line-height: 1;
      text-align: center;
      color: #685350;
      border-bottom: 5px solid transparent;
    }
  }
  .content-list {
    padding-bottom: 20px;
    padding: 20px;
    padding-top: 0;
    background: #fff;

    .empty {
      width: 100%;
      height: 300px;
      background: #fff;
      color: rgba(104, 83, 80, 0.7);
      font-size: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .item {
      display: flex;
      line-height: 1;
      align-items: center;
      justify-content: space-between;
      color: rgba(104, 83, 80, 0.7);
      padding: 20px 0;
      background: #fff;
      border-bottom: 1px solid rgba(75, 75, 75, 0.1);
      position: relative;
      .avatar {
        width: 60px;
        border-radius: 50%;
        margin-right: 20px;
        border: 4px solid #d9dbe5;
      }
      .name {
        text-align: left;
        width: 100%;
      }
      .info {
        flex: none;
        .status {
          line-height: 1.5;
        }
        .time {
          font-size: 24px;
        }
      }
    }
    .btn-more {
      line-height: 80px;
      height: 80px;
      color: rgba(104, 83, 80, 0.7);
    }
  }
}
</style>
