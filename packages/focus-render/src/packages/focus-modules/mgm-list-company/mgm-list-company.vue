<template>
  <div class="mgm-list-company">
    <div :class="['bg-title', activeParentIndex === 0 ? 'newParent' : 'oldParent']">
      <div
        v-for="(item, index) of titleList"
        :key="item.titleKey"
        @click="() => titleClick(index)"
        :class="['bg-title-item', { active: activeParentIndex === index }]"
      />
    </div>
    <div class="content">
      <div :class="['sub-title-box', activeParentIndex === 0 ? 'sub-new-title' : 'sub-old-title']">
        <div
          v-for="(item, index) of showSubTitleList"
          :key="item.subTitleKey"
          v-text="item.subTitleValue"
          @click="() => subTitleClick(index)"
          :class="[
            'sub-title',
            {
              active: activeSubIndex === index,
            },
          ]"
        />
      </div>
      <div v-if="(currentData.showList || []).length">
        <div class="content-item" v-for="(cont, idx) of currentData.showList" :key="idx">
          <div class="content-main">
            <img class="img" :src="cont.headImgUrl" alt="" />
            <span class="nickName">{{ cont.nickName }}</span>
          </div>
          <div class="content-extra">
            <div class="status">
              <div class="statusLabel">{{ statusLabel(Number(cont.userStatus)) }}</div>
              <div class="certifiedLabel" v-if="Number(cont.userStatus) === 3">- {{ cont.certifiedCompanyName }}</div>
            </div>
            <div class="time">{{ cont[activeSubTitleKey] }}</div>
          </div>
        </div>
      </div>
      <div class="noRes" v-else>
        <img class="img" src="./imgs/noResult.png" alt="" />
        <span>这里还是空的哦~</span>
      </div>
      <div v-if="showBtn" class="btn" @click="() => loadMoreOrPutAway()">
        {{ currentData.isEnd ? '收起' : '展示更多' }}
        <img :class="['icon', currentData.isEnd ? 'up' : 'down']" src="./imgs/icon.png" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { useModuleDataResolve } from '@/packages/hooks'
const props = defineProps<{
  moduleData: TModuleData
}>()

const { configData, styleData, dynamicData } = useModuleDataResolve<
  NMgmListCompany.configData,
  NMgmListCompany.dynamicData
>(props.moduleData, ['mgmListNew', 'mgmListOld'])

// const MgmDataOrigin = ref({})
const limitNum = ref(3)
const limitNumStep = 10
const activeParentIndex = ref(0)
const activeSubIndex = ref(0)
const activeSubTitleKey = ref('inviteTime')

type TitleInter = {
  titleKey: string
  titleValue: string
  subTitle: {
    subTitleKey: string
    subTitleValue: string
    subTitleCode: number
    subTitleNameKey: string
  }[]
}

const titleList: TitleInter[] = [
  {
    titleKey: 'NEW_USER',
    titleValue: '新用户',
    subTitle: [
      {
        subTitleKey: 'inviteTime',
        subTitleValue: '未开户',
        subTitleCode: 1,
        subTitleNameKey: 'unAccount',
      },
      {
        subTitleKey: 'registerTime',
        subTitleValue: '已开户',
        subTitleCode: 2,
        subTitleNameKey: 'accounted',
      },
      {
        subTitleKey: 'certifiedTime',
        subTitleValue: '已认证',
        subTitleCode: 3,
        subTitleNameKey: 'certified',
      },
      {
        subTitleKey: 'completeTime',
        subTitleValue: '已达标',
        subTitleCode: 4,
        subTitleNameKey: 'reached',
      },
    ],
  },
  {
    titleKey: 'OLD_USER',
    titleValue: '老用户',
    subTitle: [
      {
        subTitleKey: 'registerTime',
        subTitleValue: '已开户',
        subTitleCode: 2,
        subTitleNameKey: 'accountedOld',
      },
      {
        subTitleKey: 'completeTime',
        subTitleValue: '已达标',
        subTitleCode: 4,
        subTitleNameKey: 'reachedOld',
      },
    ],
  },
]

// const coverMgmList = () => {
//   MgmDataOrigin.value = coverOriginData(['mgmListNew', 'mgmListOld'])
// }

const MgmDataOrigin = computed(() => {
  return coverOriginData(['mgmListNew', 'mgmListOld'])
})

const coverOriginData = (typeArray: ('mgmListNew' | 'mgmListOld')[]) => {
  let unAccountList: NMgmListCompany.listItemConfig[] = []
  let accountedList: NMgmListCompany.listItemConfig[] = []
  let certifiedList: NMgmListCompany.listItemConfig[] = []
  let reachedList: NMgmListCompany.listItemConfig[] = []

  let accountedListOld: NMgmListCompany.listItemConfig[] = []
  let reachedListOld: NMgmListCompany.listItemConfig[] = []

  typeArray.forEach((dataKeyType) => {
    // 新用户
    dynamicData.value[dataKeyType] &&
      dynamicData.value[dataKeyType].map((item) => {
        switch (Number(item.userStatus)) {
          case 1:
            unAccountList = [...unAccountList, item]
            break
          case 2:
            if (dataKeyType === 'mgmListNew') {
              accountedList = [...accountedList, item]
            } else {
              accountedListOld = [...accountedListOld, item]
            }
            break
          case 3:
            certifiedList = [...certifiedList, item]
            break
          case 4:
            if (dataKeyType === 'mgmListNew') {
              reachedList = [...reachedList, item]
            } else {
              reachedListOld = [...reachedListOld, item]
            }
            break
          default:
            break
        }
      })
  })

  return {
    mgmListNew: {
      unAccount: unAccountList,
      accounted: accountedList,
      certified: certifiedList,
      reached: reachedList,
    },
    mgmListOld: {
      accountedOld: accountedListOld,
      reachedOld: reachedListOld,
    },
  }
}

const loadMoreOrPutAway = () => {
  const { isEnd, orginList } = currentData.value
  const orginListLength = orginList.length
  // 如果超过数据加载完成以后 点击【收起】初始化为三条数据
  if (isEnd) {
    limitNum.value = 3
  } else {
    limitNum.value += limitNumStep
    if (limitNum.value >= orginListLength) {
      limitNum.value = orginListLength
    }
  }
}

const currentData = computed(() => {
  const tagetParentKey = activeParentIndex.value === 0 ? 'mgmListNew' : 'mgmListOld'
  const tagetSub = titleList[activeParentIndex.value].subTitle[activeSubIndex.value]
  // 已经处理完数据的 code (unAccount、accounted、certified、reached、accountedOld、reachedOld 这几个)
  const tagetSubKey = titleList[activeParentIndex.value].subTitle[activeSubIndex.value].subTitleNameKey
  const originList = MgmDataOrigin.value[tagetParentKey]?.[tagetSubKey] || []
  const showList = originList?.slice(0, limitNum.value) || []

  // 当前展示的 数据
  return {
    isEnd: originList.length === showList.length,
    orginList: originList,
    showList: showList,
    subTitleCode: tagetSub.subTitleCode,
    subTitleKey: tagetSub.subTitleKey,
    subTitleNameKey: tagetSub.subTitleNameKey,
    subTitleValue: tagetSub.subTitleValue,
  }
})

const showSubTitleList = computed(() => {
  return activeParentIndex.value === 0 ? titleList[0].subTitle : titleList[1].subTitle
})

const titleClick = (parentIndex: number) => {
  activeParentIndex.value = parentIndex
  activeSubIndex.value = 0
  // 切换一级 tab 重置列表
  limitNum.value = 3
}

const subTitleClick = (subIndex: number) => {
  activeSubIndex.value = subIndex
  // 切换二级 tab 重置列表
  limitNum.value = 3
}

// watch(
//   () => dynamicData.value,
//   (newVal) => {
//     console.log(newVal, '🚀 ~ newVal - page mgm-list-company')
//     coverMgmList()
//   }
// )

const showBtn = computed(() => currentData.value.orginList.length > 3)
const statusLabel = (status: number) => {
  switch (status) {
    case 1:
      activeSubTitleKey.value = 'inviteTime'
      return '未开户'
    case 2:
      activeSubTitleKey.value = 'registerTime'
      return '已开户'
    case 3:
      activeSubTitleKey.value = 'certifiedTime'
      return '已认证'
    case 4:
      activeSubTitleKey.value = 'completeTime'
      return '已达标'
    default:
      break
  }
}
</script>

<style lang="scss" scoped>
.mgm-list-company {
  padding: 0 20px;
  background-color: #5bc4fc;
  .bg-title {
    margin: 0 auto;
    width: 710px;
    height: 114px;
    background: url('./imgs/title-bg.png') no-repeat;
    background-size: contain;
    display: flex;
    &.newParent {
      .active {
        background: url('./imgs/new-active.png') no-repeat;
        background-size: contain;
      }
    }
    &.oldParent {
      .active {
        background: url('./imgs/old-active.png') no-repeat;
        background-size: contain;
      }
    }
    .bg-title-item {
      position: relative;
      top: 4px;
      width: 365px;
      height: 114px;
      font-size: 32px;
    }
  }
  .content {
    border: 2px solid #39b3ff;
    border-top: unset;
    border-radius: 0 0 20px 20px;
    background-color: #fff;
    .sub-title-box {
      padding-top: 40px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      .sub-title {
        font-size: 28px;
        line-height: 28px;
        background-size: 100% 100%;
        border-radius: 10px;
        color: #4f758a;
        background-color: #e5f3ff;
        border: 2px solid #89cfff;
        &.active {
          color: #ffffff;
          background-image: linear-gradient(to right, #b1b0ff, #4fafff);
        }
      }
      .sub-new-title {
        padding: 15px 31px;
      }
      .sub-new-title {
        padding: 15px 31px;
      }
      &.sub-new-title {
        .sub-title {
          padding: 15px 30px;
        }
      }
      &.sub-old-title {
        .sub-title {
          padding: 15px 108px;
        }
      }
    }
    .content-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 0;
      margin: 0 40px;
      border-bottom: 1px solid #ecf4fb;
      .content-main {
        display: flex;
        align-items: center;
        .img {
          width: 70px;
          height: 70px;
          margin-right: 20px;
        }
        .nickName {
          color: #313131;
          font-size: 30px;
          line-height: 42px;
          text-align: left;
          width: 360px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .content-extra {
        .status {
          font-size: 28px;
          line-height: 40px;
          color: #389cff;
          display: flex;
          white-space: nowrap;
          align-items: center;
          justify-content: flex-end;
        }
        .time {
          font-size: 22px;
          line-height: 30px;
          color: #5878b9;
          text-align: right;
        }
      }
    }
    .noRes {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 342px;
      font-size: 30px;
      color: #12436f;
      .img {
        width: 356px;
        height: 248px;
      }
    }
  }
  .btn {
    font-size: 28px;
    line-height: 40px;
    color: #3ca6ff;
    padding: 24px 0;
    .icon {
      width: 27px;
      height: 27px;
    }
    .icon.up {
      transform: rotate(0deg);
    }
    .icon.down {
      transform: rotate(180deg);
    }
  }
}
</style>
