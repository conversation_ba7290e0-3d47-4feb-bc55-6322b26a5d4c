<template>
  <div class="actcoin-history">
    <div class="item" v-for="(item, index) in listData" :key="index">
      <img :src="item.cover" alt="" class="cover" />
      <div class="info">
        <div class="title">{{ item.title }}</div>
        <div class="desc">{{ item.desc }}</div>
        <div class="time">{{ item.time }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { unref, computed, ref } from 'vue'
import { useModuleDataResolve } from '../hooks/index'
import { focusCore, Mask } from '../focus-render'
import { activityService, jumpService, mgmService } from '../../service'
import { focusStore } from '../../store/index'
import dayjs from '@/utils/time-utils'

const { modalStore } = focusStore.stores
const props = defineProps<{
  moduleData: TModuleData
}>()
const { configData, styleData } = useModuleDataResolve<NFocusActCoinHistory.configData>(props.moduleData)

const listData = ref<any[]>([])
if (configData.value.types && focusCore.hasAccount) {
  activityService.getActCoinsHistory(configData.value.types).then((coinDetailList: any[]) => {
    console.log(
      '🚀 ~ file: focus-actcoin-history.vue ~ line 32 ~ activityService.getActCoinsHistory ~ coinDetailList',
      coinDetailList,
    )
    // const { coinVOS, goodsVOS } = res
    // productList.value = goodsVOS
    // amountList.value = coinVOS
    const data = coinDetailList
    listData.value = data.map((i: any) => {
      return {
        cover: i.virtualCoinIcon,
        desc: i.usageScenarios,
        time: dayjs(i.transTime).format('YYYY年MM月DD日 HH:mm:ss') + '获得',
        title: `${i.coinName}x${i.transNum}`,
      }
    })
  })
}
</script>

<style lang="scss" scoped>
.actcoin-history {
  width: 100%;
  box-sizing: border-box;
  .item {
    margin: 0 auto;
    width: 690px;
    height: 200px;
    background: #fff;
    border-radius: 16px;
    font-size: 30px;
    color: #405080;
    line-height: 45px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-top: 30px;
    .cover {
      width: 80px;
      height: 80px;
      margin-left: 55px;
      margin-right: 45px;
    }
    .info {
      text-align: left;
    }
    .desc {
      font-size: 26px;
      color: #808bab;
      margin-top: 10px;
      margin-bottom: 10px;
    }
    .time {
      font-size: 24px;
      color: #b4bacc;
    }
  }
}
</style>
