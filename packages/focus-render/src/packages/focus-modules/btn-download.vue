<template>
  <div
    class="focus-btn-download isimg"
    :style="curStyles"
    alt="下载按钮"
    @click="clickBtn"
    :data-reportid="`${moduleType}_${moduleId}_${channel}`"
  ></div>
</template>

<script setup lang="ts">
import { UrlParser } from '@/utils'
import { toRefs, toRaw, unref, computed } from 'vue'
import { jumpService } from '../../service'
import { focusStore } from '../../store/index'
import { focusCore } from '../focus-render'
import { useModuleDataResolve } from '../hooks'
const { modalStore } = focusStore.stores

const props = defineProps<{ moduleData: NFocusBtnClick.moduleData }>()

const { configData, styleData, moduleId, moduleType } = useModuleDataResolve<NFocusBtnDownload.configData>(
  props.moduleData
)
const urlQuery = new UrlParser(location.href)

const channel: string = urlQuery?.query?.download_channel

const downLoadLink = computed(() => {
  const { links, androidLink, iosLink } = unref(configData)
  console.log(
    '🚀 ~ file: btn-download.vue ~ line 24 ~ downLoadLink ~ links, androidLink, iosLink',
    links,
    androidLink,
    iosLink
  )
  const curLinks = (links && JSON.parse(links)) || []
  let channelLink =
    curLinks.find((i: { channel: string; androidLink: string; iosLink: string }) => i.channel === channel) || null
  let _iosLink = iosLink
  let _androidLink = androidLink
  if (channelLink) {
    _iosLink = channelLink.iosLink
    _androidLink = channelLink.androidLink
  }
  console.log('🚀 ~ file: btn-download.vue ~ line 39 ~ downLoadLink ~ _iosLink', _iosLink)
  console.log('🚀 ~ file: btn-download.vue ~ line 41 ~ downLoadLink ~ _androidLink', _androidLink)

  return focusCore.env.device === 'ios' ? _iosLink : _androidLink
})
const curStyles = computed(() => {
  const { boxSize, margins, position } = unref(styleData)

  const boxBg = {
    backgroundImage: `url(${configData.value.imgUrl})`,
  }

  return {
    ...boxSize,
    ...margins,
    ...boxBg,
    ...position,
  }
})

const clickBtn = function () {
  if (downLoadLink.value) {
    focusCore.clickLog(`${moduleType.value}_${moduleId.value}_${channel}`, { link: downLoadLink.value })
    location.href = downLoadLink.value
  }
}
</script>

<style lang="scss" scoped>
.focus-btn-download {
  flex: none;
  text-align: center;
  word-break: break-all;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: none;
  width: 100%;
  font-size: 32px;
  background-color: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;

  cursor: pointer;
  &:active {
    opacity: 0.6;
  }
}
</style>
