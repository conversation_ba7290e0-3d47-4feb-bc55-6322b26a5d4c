<template>
  <div class="focus-image" :style="curStyles">
    <img ref="imgRef" @load="onImageLoaded" class="img" :class="{ 'can-scan': isCanScan }" :src="url" alt="m_image" />
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, onUnmounted, unref, watchPostEffect, ref, watch } from 'vue'
import { useModuleDataResolve } from '../hooks/index'
import { StorageKey } from '@/consts/storage'
import waLog from '@/service/focus-core/waLog'
import { focusStore } from '@/store'

const props = defineProps<{
  moduleData: TModuleData
}>()
const { configData, styleData, dynamicData } = useModuleDataResolve<NFocusImage.configData, NFocusImage.dynamicData>(
  props.moduleData,
  ['imgUrl']
)
const imgRef = ref<HTMLImageElement | null>()
const webpReg = /\/__HAS_WEBP__/
let st: number | undefined
const { userStore } = focusStore.stores

const url = computed(() => {
  const oriUrl = dynamicData.value.imgUrl
  const surportWebp = localStorage.getItem(StorageKey.surportWebp) === 'true'
  const hasWebp = webpReg.test(oriUrl)
  const grey = /([e-f]|[0-1]|[E-F])$/.test(userStore.wechatOpenId)
  if (surportWebp && hasWebp && grey) {
    const newUrl = oriUrl.replace(/(.png|.jpg)$/, '.webp')
    return newUrl
  } else {
    return oriUrl
  }
})
watch(imgRef, () => {
  if (imgRef.value && imgRef.value.complete === false) {
    st = Date.now()
  } else {
    st = undefined
  }
})

const onImageLoaded = () => {
  /* 命中缓存，不统计 */
  if (!st) return
  const et = Date.now()
  const dt = et - st
  if (webpReg.test(url.value)) {
    /* 存在webp备份的情况下，对比使用webp和不使用webp的加载时长 */
    const logKey = /.webp$/.test(url.value) ? 'is_webp_image' : 'is_ori_image'
    waLog.eventLog(logKey, String(dt))
  }
}

const isCanScan = configData.value.imgCanScan
const curStyles = computed(() => {
  const { boxSize, bg, margins, position, borderRadius } = unref(styleData)
  return {
    ...boxSize,
    ...bg,
    ...margins,
    ...position,
    ...borderRadius,
  }
})
</script>

<style lang="scss" scoped>
.focus-image {
  flex: none;
  box-sizing: border-box;
  border: none;
  display: block;
  line-height: 0;
  width: 100%;
  font-size: 0;
  overflow: hidden;
  .img {
    width: 100%;
    height: 100%;
    display: block;
    &.can-scan {
      pointer-events: auto;
      -webkit-user-select: all;
      -moz-user-select: all;
      -webkit-user-select: all;
      -o-user-select: all;
      user-select: all;
      -webkit-touch-callout: all;
    }
  }
}
</style>
