<template>
  <div class="focus-swiper" :style="styles">
    <swiper
      :speed="400"
      :slides-per-view="'auto'"
      @slideChange="onCardSlideChange"
      @swiper="cardSwiper"
      class="card-tabs"
      :space-between="16"
      v-if="uiType === '1'"
      :scrollbar="{ draggable: true }"
      :centeredSlides="true"
      :centeredSlidesBounds="true"
    >
      <swiper-slide
        v-for="(item, index) in cardItems"
        :key="index"
        class="card-item"
        @click="clickTabCard(index)"
        :style="{ width: cardItemWidth, height: cardItemHeight }"
        :class="{
          hasimg: (cardItemIndex === index && item.imgUrl2) || (cardItemIndex !== index && item.imgUrl1),
        }"
      >
        <img
          v-show="cardItemIndex === index"
          :style="{ width: cardItemWidth, height: cardItemHeight }"
          :src="item.imgUrl2"
        />
        <img
          v-show="cardItemIndex !== index"
          :style="{ width: cardItemWidth, height: cardItemHeight }"
          :src="item.imgUrl1"
        />
      </swiper-slide>
    </swiper>

    <swiper
      :speed="400"
      :slides-per-view="1"
      :space-between="0"
      @swiper="onSwiper"
      :pagination="{ clickable: true }"
      :scrollbar="{ draggable: true }"
      @slideChange="onSlideChange"
      :loop="uiType === '0'"
      :modules="swiperModules"
      :autoplay="{ delay: 3000, disableOnInteraction: false }"
      :initialSlide="0"
    >
      <swiper-slide v-for="(_module, index) in showChildren" :key="index" class="swiper-item">
        <module-center
          :moduleData="_module"
          :focusFid="props.focusFid || 0"
          :focusRenderCtr="props.focusRenderCtr"
          :canShowTypes="['MBox', 'MText', 'MImage', 'BtnAdding', 'BtnClick']"
        >
        </module-center>
      </swiper-slide>
    </swiper>
    <div class="pagenation" v-if="showChildren.length && uiType === '0'">
      <div
        class="dot"
        v-for="(item, index) in showChildren"
        :class="{ actived: dotActiveIndex === index }"
        :style="dotActiveIndex === index ? `background:${dotActivedColor}` : ''"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, onMounted, unref, ref, toRaw, toRefs } from 'vue'
import { useModuleDataResolve } from '../hooks/index'
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue'
import { Autoplay } from 'swiper'
import moduleCenter from '../focus-render/src/modue-center.vue'
import 'swiper/swiper.min.css'

const props = defineProps<{
  moduleData: TModuleData
  childrenModules: Array<TModuleData>
  focusFid: number
  focusRenderCtr: any
}>()

const { configData, styleData } = useModuleDataResolve<NFocusSwiperBox.configData>(props.moduleData)
console.log('🚀 ~ file: focus-swiper.vue:85 ~ styleData:', styleData)

const { childrenModules } = toRefs(props)
const showChildren = computed(() => {
  return (childrenModules.value && childrenModules.value.slice(0, 6)) || []
  // return []
})
// console.log('🚀 ~ file: focus-swiper.vue:82 ~ showChildren ~ showChildren:', showChildren.value)
const styles = computed(() => {
  const margins = styleData.value.margins
  console.log('🚀 ~ file: focus-swiper.vue:95 ~ styles ~ margins:', margins)
  return {
    ...margins,
  }
})
const uiType = computed(() => {
  console.log('🚀 ~ file: focus-swiper.vue:60 ~ uiType ~ configData.uiType:', configData.value.uiType)
  return configData.value.uiType
})

const cardItems = computed(() => {
  const imgs = configData.value.cardImgs
  let result = []
  try {
    result = JSON.parse(imgs)
  } catch (err) {}
  return result.slice(0, 6)
})

const px2vw = function (px: number): string {
  const viewPort = 750
  const vw = ((Number(px) / viewPort) * 100).toFixed(3)
  return `${vw}vw`
}

const ready = ref(false)
const activeIndex = ref(0)
const cardItemIndex = ref(0)
const swiperModules = computed(() => {
  return uiType.value === '0' && configData.value.autoPlay === '0' ? [Autoplay] : []
})
const cardItemWidth = computed(() => {
  const width = configData.value.cardWidth
  return px2vw(Number(width))
})
const cardItemHeight = computed(() => {
  const height = configData.value.cardHeight
  return px2vw(Number(height))
})

const dotActivedColor = computed(() => {
  return configData.value.dotColor
})

const dotActiveIndex = ref(0)

onMounted(() => {
  setTimeout(() => {
    ready.value = true
  }, 300)
})

const swiperCtl = ref<any>(null)
const swiperCardCtl = ref<any>(null)
const onSwiper = (swiper: any) => {
  console.log('🚀 ~ file: focus-swiper.vue:41 ~ onSwiper ~ swiper:', swiper)
  swiperCtl.value = swiper
}

const cardSwiper = (swiper: any) => {
  console.log('🚀 ~ file: focus-swiper.vue:147 ~ cardSwiper ~ swiper:', swiper)
  swiperCardCtl.value = swiper
}

const onSlideChange = (data: any) => {
  console.log('slide change')
  // console.log('🚀 ~ file: focus-swiper.vue:45 ~ onSlideChange ~ data:', data)
  const swiperData = toRaw(data)
  activeIndex.value = swiperData.activeIndex
  dotActiveIndex.value = swiperData.realIndex
  // console.log('🚀 ~ file: focus-swiper.vue:63 ~ onSlideChange ~ activeIndex.value:', activeIndex.value)
  if (swiperCardCtl.value) {
    swiperCardCtl.value.slideTo(swiperData.realIndex)
    cardItemIndex.value = swiperData.realIndex
  }
}

const onCardSlideChange = (data) => {
  console.log('🚀 ~ file: focus-swiper.vue:86 ~ onCardSlideChange ~ data:', data)
  const swiperData = toRaw(data)

  // cardItemIndex.value = swiperData.activeIndex
}

function clickTabCard(index: number) {
  swiperCtl.value.slideTo(index)
  cardItemIndex.value = index
  swiperCardCtl.value.slideTo(index)
}

// swiper.on('slideChange', function () {
//   console.log('slide changed')
// })
</script>

<style lang="scss" scoped>
.focus-swiper {
  width: 100%;
}
.focus-swiper-slide {
  width: 654px;
  height: 300px;
  background: red;
  font-size: 40px;
  line-height: 1.5;
  color: yellow;
  margin: 0 auto;
}
.pagenation {
  width: 100%;
  height: 12px;
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  .dot {
    width: 12px;
    height: 12px;
    background: #f0f0f0;
    margin-right: 10px;
    border-radius: 12px;
    &.actived {
      width: 24px;
      background: #ffac6a;
    }
  }
}
.card-tabs {
  padding-left: 24px;
  padding-right: 24px;
  width: 100%;
}
.card-item {
  width: 174px;
  height: 88px;
  margin-bottom: 18px;
  background: rgb(199, 199, 199);
  &.hasimg {
    background: none;
  }
}
.swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
