<template>
  <div
    class="btn-mgm"
    :style="curStyles"
    @click="handleCick"
    :class="btnDisabled ? 'disabled' : ''"
    role="button"
    aria-label="分享给好友"
  >
    <img :src="showImg" :style="{ ...styleData.boxSize }" aria-hidden="true" />
  </div>
</template>
<script lang="ts">
export default {
  name: 'btn-mgm',
}
</script>
<script setup lang="ts">
import { inject, provide, toRefs, watch, ref, watchEffect, unref, isRef, isProxy, toRaw, markRaw, computed } from 'vue'
import { focusStore } from '../../store/index'
import { jumpService, mgmService, activityService } from '../../service'
import { UrlParser } from '../../utils/url'
import { useModuleDataResolve } from '../hooks'
import { focusCore } from '../focus-render'
const props = defineProps<{
  moduleData: TModuleData
}>()
const { userStore } = focusStore.stores

const urlQuery = new UrlParser(window.location.href).query
const { configData, styleData, moduleId } = useModuleDataResolve<NFocusBtnMgm.configData>(props.moduleData)

// const shareData = computed<TShareConfig>(() => {
//   const data = focusCore.focusRenderCtr?.getShareConfigByConfigId(configData.value.configId) || {}
//   console.log('🚀 ~ file: focus-btn-mgm.vue ~ line 28 ~ data', data)
//   let shareUrl = (data && data.shareUrl) || ''
//   console.log('🚀 ~ file: focus-btn-mgm.vue ~ line 32 ~ shareUrl', shareUrl)

//   if (configData.value.fromAid && shareUrl && shareData) {
//     shareUrl = new UrlParser(shareUrl).appendQuery({ fromAid: configData.value.fromAid }).fullPath
//     data.shareUrl = shareUrl || data.shareUrl
//   }
//   return data
// })

const curStyles = computed(() => {
  const { boxSize, margins, position } = unref(styleData)
  return {
    ...boxSize,
    ...margins,
    ...position,
  }
})
const isAppEquityContrl = ref(-1) // -1 不受控制；0 已解锁（可点击）；1 未解锁（不可点击）
const btnDisabled = computed(() => {
  return isAppEquityContrl.value === 1
})

const showImg = computed(() => {
  if (btnDisabled.value) {
    return configData.value.imgUrl3
  }
  return userStore.hasAccount ? configData.value.imgUrl2 : configData.value.imgUrl1
})

const { isLocked = '' } = urlQuery

// 在app里面给个isLock代表不能点击该按钮
if (isLocked.toString()) {
  if (isLocked.toString() === '1') {
    isAppEquityContrl.value = 1
  } else {
    isAppEquityContrl.value = 0
  }
}

function getShareData() {
  const data = focusCore.focusRenderCtr?.getShareConfigByConfigId(configData.value.configId) || {}
  console.log('🚀 ~ file: focus-btn-mgm.vue ~ line 28 ~ data', data)
  let shareUrl = (data && data.shareUrl) || ''
  console.log('🚀 ~ file: focus-btn-mgm.vue ~ line 32 ~ shareUrl', shareUrl)

  if (configData.value.fromAid && shareUrl && data) {
    shareUrl = new UrlParser(shareUrl).appendQuery({ fromAid: configData.value.fromAid }).fullPath
    data.shareUrl = shareUrl || data.shareUrl
  }
  return data
}

const handleCick = function () {
  if (btnDisabled.value) {
    return false
  }

  if (!userStore.hasAccount && !configData.value.noAccountCanShare) {
    return jumpService.commonUse.appHome()
  }
  /* 参与活动 */
  if (configData.value.joinAid) {
    activityService.setWrInfo(configData.value.joinAid).then((res) => {})
  }

  const shareData = getShareData()

  console.log('🚀 ~ file: focus-btn-mgm.vue ~ line 37 ~ handleCick ~ shareData', shareData)
  shareData && mgmService.clickShare(`BtnMGM_${moduleId.value}`, shareData)
}
</script>

<style lang="scss" scoped>
.btn-mgm {
  // flex: none;
  // box-sizing: border-box;
  // width: 750px;
  cursor: pointer;
  &.disabled {
    // opacity: 0.6;
  }
}
</style>
