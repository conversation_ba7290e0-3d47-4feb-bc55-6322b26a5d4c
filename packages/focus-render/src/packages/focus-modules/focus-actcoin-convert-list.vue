<template>
  <!-- 代币兑换商品列表 -->
  <div class="actcoin-convert-list" v-show="amountList.length">
    <div class="amount-list">
      <div class="item" v-for="(item, index) in amountList" :key="index">
        <img class="cover" :src="item.coinUrl" />
        x{{ item.amount }}
      </div>
    </div>
    <div class="product-list">
      <div class="item" v-for="(item, index) in productList" :key="index">
        <img class="cover" :src="item.showUrl" />
        <div class="info">
          <div class="name">{{ item.name }}</div>
          <div class="coins">
            需要消耗:
            <div class="coin_item" v-for="(coin, i) in item.price" :key="i">
              {{ i >= 1 ? '+' : '' }}
              <img :src="coin.coinUrl" alt="" />
              x{{ coin.amount }}
            </div>
          </div>
        </div>
        <div
          class="btn"
          :class="{ disabled: !item.moneyEnough || item.userCanbuy <= 0 || isConverting }"
          @click="startConvert(item)"
          :data-reportid="`convert_btn_${moduleId}_${index}`"
        >
          <img src="../../assets/img/actcoin-convert-list/btn-active.png" alt="" />
          <p>(还可兑换{{ item.userCanbuy }}个)</p>
        </div>
      </div>
    </div>
  </div>
  <dialog-mask :show="successDialogFlag">
    <template #dialog_contain>
      <div class="success-dialog contain">
        <div class="btn-close" @click="hideDialog">
          <img src="../../assets/img/common/btn-close.png" alt="" />
        </div>
        <div class="title">
          兑换成功<span>{{ rewardData.name }}</span>
        </div>
        <img :src="rewardData.rewardUrl" alt="" class="cover" />
        <p class="desc">点击“去看看”在“我的奖品”中查看</p>
        <div class="btns">
          <div class="share" @click="share">炫耀一下</div>
          <div class="jump" @click="jump">去查看</div>
        </div>
      </div>
    </template>
  </dialog-mask>
  <!-- <Mask class="success-dialog" v-show="successDialogFlag">
    <div class="contain">
      <div class="btn-close" @click="hideDialog">
        <img src="../../assets/img/common/btn-close.png" alt="" />
      </div>
      <div class="title">
        兑换成功<span>{{ rewardData.name }}</span>
      </div>
      <img :src="rewardData.rewardUrl" alt="" class="cover" />
      <p class="desc">点击“去看看”在“我的奖品”中查看</p>
      <div class="btns">
        <div class="share" @click="share">炫耀一下</div>
        <div class="jump" @click="jump">去查看</div>
      </div>
    </div>
  </Mask> -->
</template>

<script setup lang="ts">
import { unref, computed, ref } from 'vue'
import { useModuleDataResolve } from '../hooks/index'
import { focusCore, Mask } from '../focus-render'
import { activityService, mgmService } from '../../service'
import { focusStore } from '../../store/index'
import type { NConvertItem } from '../../service/activityService'
import DialogMask from '../../components/DialogMask.vue'

const { modalStore } = focusStore.stores
const props = defineProps<{
  moduleData: TModuleData
}>()
const { configData, styleData, moduleId } = useModuleDataResolve<NFocusActCoinConvertList.configData>(props.moduleData)

const amountList = ref<Array<NConvertItem.ItemPrice>>([])
const productList = ref<Array<NConvertItem.itemGoods & { userCanbuy: number; moneyEnough: boolean }>>([])
const successDialogFlag = ref(false)
const rewardData = ref({
  name: '',
  rewardUrl: '',
})

const isConverting = ref(false) // 正在兑换

init()
function init() {
  if (configData.value.aid && focusCore.hasAccount) {
    modalStore.loadingStart('get_convert_list')
    activityService.getConvertList(configData.value.aid).then((res) => {
      console.log('🚀 ~ file: focus-actcoin-convert-list.vue ~ line 19 ~ activityService.getConvertList ~ res', res)
      const { coinVOS = [], goodsVOS = [] } = res
      productList.value = goodsVOS.map((goodItem) => {
        const { price } = goodItem
        const moneyEnough = !price.some((priceItem) => {
          const target = coinVOS.find((_i) => _i.coinType === priceItem.coinType)
          if (target) {
            return target?.amount < priceItem.amount
          }
          return true
        })
        return {
          ...goodItem,
          moneyEnough,
        }
      })
      amountList.value = coinVOS
      modalStore.loadingEnd('get_convert_list')
    })
  }
}

function share() {
  const shareData: TShareConfig =
    (focusCore.focusRenderCtr && focusCore.focusRenderCtr.getShareConfigByConfigId(configData.value.shareConfigId)) ||
    {}

  console.log('🚀 ~ file: focus-actcoin-convert-list.vue ~ line 79 ~ share ~ shareData', shareData)
  mgmService.clickShare('btn-convertlist-share', shareData)
}

function jump() {
  location.href = configData.value.myRewardLink
}

function hideDialog() {
  console.log('1111', console.log(successDialogFlag.value))
  successDialogFlag.value = false
}

function startConvert(data: any) {
  console.log('🚀 ~ file: focus-actcoin-convert-list.vue ~ line 65 ~ startConvert ~ data', data)
  // successDialogFlag.value = true
  // console.log(
  //   '🚀 ~ file: focus-actcoin-convert-list.vue ~ line 143 ~ startConvert ~ successDialogFlag.value',
  //   successDialogFlag.value
  // )

  // const { goodsCode } = data
  if (!data.moneyEnough || data.userCanbuy <= 0 || isConverting.value) {
    return false
  }
  // 间隔300ms 后才能点击
  setTimeout(() => {
    isConverting.value = false
  }, 300)
  focusCore.invokeApp.showPassword({
    success: (res) => {
      console.log('🚀 ~ file: password返回', res)

      const { passwordRSA } = res
      convert(data, passwordRSA)
    },
    error: (err: any) => {
      console.log('🚀 ~ file: focus-actcoin-convert-list.vue ~ line 74 ~ error ~ msg', err)
      modalStore.loadingEnd('convert_password')
    },
    emitEvent: (data: any) => {
      console.log('🚀 ~ file: focus-actcoin-convert-list.vue ~ line 121 ~ startConvert ~ data', data)
    },
  })
}

function convert(goodData: any, userPwd: string) {
  modalStore.loadingStart('start_convert')

  const { goodsCode, exchangeSuccUrl, name } = goodData
  activityService.convertGoodByActCoins(configData.value.aid, goodsCode, userPwd).then((res) => {
    console.log(
      '🚀 ~ file: focus-actcoin-convert-list.vue ~ line 79 ~ activityService.convertGoodByActCoins ~ res',
      res
    )
    modalStore.loadingEnd('start_convert')
    init()
    const { status, errorMsg } = res
    if (status) {
      rewardData.value = {
        name: name,
        rewardUrl: exchangeSuccUrl,
      }
      successDialogFlag.value = true
    } else {
      const contents = ['兑换失败！', errorMsg || '']

      modalStore.confirmContrl({
        show: true,
        contents,
        btnCancelText: '知道了',
        hideConfirm: true,
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.actcoin-convert-list {
  margin: 0 auto;
  width: 690px;
  box-sizing: border-box;
  padding: 58px 30px 30px 30px;
  background: #3570f8;
  border-radius: 16px;
  .amount-list {
    display: flex;
    align-items: center;
    justify-content: center;
    .item {
      width: 100%;
      flex: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      color: #fff;
      .cover {
        width: 40px;
        height: 40px;
      }
    }
  }
  .product-list {
    background-image: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(200, 249, 255, 0.9) 100%);
    width: 630px;
    margin: 0 auto;
    margin-top: 50px;
    padding-bottom: 60px;
    border-radius: 10px;
    .item {
      height: 134px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding-left: 30px;
      padding-right: 30px;
      position: relative;
      &::after {
        content: '';
        width: 570px;
        position: absolute;
        bottom: 0;
        left: 30px;
        background: #9dc7e1;
        opacity: 0.1;
        height: 1px;
      }
      .cover {
        width: 80px;
        height: 80px;
      }
      .info {
        margin-left: 10px;
        flex: auto;
        .name {
          font-size: 28px;
          color: #2d3b75;
          line-height: 42px;
          // font-weight: 700;
          text-align: left;
        }
        .coins {
          font-size: 22px;
          color: #7583bc;
          line-height: 33px;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .coin_item {
            img {
              width: 26px;
              height: 26px;
            }
          }
        }
      }
      .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        img {
          width: 164px;
          height: 74px;
        }
        p {
          font-size: 20px;
          color: #7583bc;
          line-height: 33px;
          font-weight: 400;
        }
        &:active {
          opacity: 0.7;
        }
        &.disabled {
          img {
            opacity: 0.5;
          }
          opacity: 1;
        }
      }
    }
  }
}
.success-dialog {
  &.contain {
    width: 560px;
    height: 630px;
    box-sizing: border-box;
    padding-top: 60px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    background: #fff;
    border-radius: 20px;
    padding-bottom: 50px;
    padding-left: 50px;
    padding-right: 50px;
    position: relative;
    margin-top: -100px;
    .title {
      font-size: 34px;
      color: #405080;
      letter-spacing: 0;
      line-height: 51px;
      font-weight: 500;
      span {
        color: #f05446;
      }
    }
    .desc {
      font-size: 26px;
      color: #405080;
      opacity: 0.8;
      line-height: 1;
    }
    .btn-close {
      width: 60px;
      height: 60px;
      position: absolute;
      right: 0;
      top: -110px;
    }
    .cover {
      flex: auto;
      margin-top: 30px;
      height: 260px;
      width: 290px;
      margin-bottom: 30px;
    }
    .btns {
      flex: none;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 30px;
      width: 100%;
      .share,
      .jump {
        width: 210px;
        height: 72px;
        line-height: 72px;
        color: #fff;
        background-color: #456ce6;
        text-align: center;
        font-size: 28px;
        color: #ffffff;
        text-align: center;
        font-weight: 400;
        border: 2px solid #456ce6;
        border-radius: 36px;
        &:active {
          color: #456ce6;
          background: #fff;
          border: 2px solid #456ce6;
        }
      }
      .jump {
        color: #456ce6;
        background: #fff;
        border: 2px solid #456ce6;
        &:active {
          color: #ffffff;
          background-color: #456ce6;
        }
      }
    }
  }
}
</style>
