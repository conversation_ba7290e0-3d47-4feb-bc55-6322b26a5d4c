<template>
  <div class="my-reward-list">
    <div class="empty" v-show="!listData.length && isReady">
      <img class="cover" src="../../assets/img/focus-lottery/reward-list-empty.png" alt="" />
      <p>空空如也</p>
      <p>赶紧参与活动获取奖品吧</p>
    </div>
    <div class="list-item" v-for="(item, index) in listData" :key="index">
      <img class="cover" :src="item.imgUrl" alt="奖品图片" />
      <div class="info">
        <p class="title">{{ item.title }}</p>
        <p class="desc" v-if="!item.coinData.length">{{ item.desc }}</p>
        <div class="coin-desc" v-else>
          <div class="desc-item" v-for="(coinItem, index) in item.coinData" :key="index">
            {{ index > 0 ? ' + ' : '' }}<img class="icon" :src="coinItem.coinUrl" alt="" />x{{ coinItem.amount }}
          </div>
          兑换
        </div>
        <p class="time">{{ item.time }}获得</p>
      </div>
      <div class="btn" @click="handleClick(item)" role="button">去查看</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, provide, toRefs, watch, ref, watchEffect, unref, isRef, isProxy, toRaw, markRaw, computed } from 'vue'
import { focusStore } from '../../store/index'
import { jumpService, mgmService, activityService, lotteryService } from '../../service'
import { UrlParser } from '../../utils/url'
import { useModuleDataResolve, handleClickByClickEvent } from '../hooks'
import { focusCore } from '../focus-render'
import type { TRewardListItem } from '../../service/lotteryService'
interface TUiListItem {
  cardId: string
  imgUrl: string
  title: string
  desc: string
  clickEvent: any
}

type TListDataItem = TUiListItem & TRewardListItem

const props = defineProps<{
  moduleData: TModuleData
}>()
const { userStore } = focusStore.stores

const urlQuery = new UrlParser(window.location.href).query
const { configData, styleData } = useModuleDataResolve<NFocusMyRewardList.configData>(props.moduleData)
const isReady = ref(false)
const listData = ref<Array<TListDataItem>>([])

if (focusCore.hasAccount && configData.value.aids) {
  lotteryService.getMyRewardList(configData.value.aids).then((res) => {
    console.log('🚀 ~ file: focus-my-reward-list.vue ~ line 36 ~ lotteryService.getMyRewardList ~ res', res)
    const UiData: Array<TUiListItem> = JSON.parse(configData.value.listData || '[]')
    listData.value = res
      .map((i) => {
        const { cardId, time, activityId, coinData } = i

        const target = UiData.find((i) => i.cardId.toString() === cardId.toString())

        if (!target) {
          return null
        }
        const { title, imgUrl, desc, clickEvent } = target
        return {
          title,
          imgUrl,
          desc,
          clickEvent,
          time,
          cardId,
          activityId,
          coinData,
        }
      })
      .filter((i) => i) as Array<TListDataItem>
    isReady.value = true
  })
}

function handleClick(data: TListDataItem) {
  console.log('🚀 ~ file: focus-my-reward-list.vue ~ line 44 ~ handleClick ~ data', data)
  const { cardId, activityId, clickEvent } = data
  handleClickByClickEvent(clickEvent, 'myrewardbtn', cardId)
}
</script>

<style lang="scss" scoped>
.my-reward-list {
  padding-top: 30px;
  padding-bottom: 50px;
  .empty {
    background: #fce9cb;
    width: 690px;
    height: 950px;
    border-radius: 15px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .cover {
      width: 300px;
      height: 260px;
      margin-bottom: 40px;
    }
    p {
      font-size: 32px;
      line-height: 48px;
      color: #b9542a;
      font-weight: bold;
    }
  }
  .list-item {
    width: 710px;
    height: 205px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    .cover {
      width: 165px;
      height: 165px;
      margin-right: 20px;
    }
    .info {
      text-align: left;
      line-height: 1.5;
      flex: auto;
      .title {
        font-size: 30px;
        color: #405080;
        letter-spacing: 0;
        line-height: 45px;
      }
      .desc {
        font-size: 26px;
        color: #808bab;
        letter-spacing: 0;
        line-height: 39px;
      }
      .coin-desc {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 26px;
        color: #7583bc;
        line-height: 33px;
        .desc-item {
          font-weight: 700;
          display: flex;
          align-items: center;
          justify-content: flex-start;
        }
        .icon {
          width: 26px;
          height: 26px;
        }
      }
      .time {
        font-size: 24px;
        color: #b4bacc;
        line-height: 36px;
      }
    }
    .btn {
      flex: none;
      width: 156px;
      height: 60px;
      border-radius: 65px;
      // background: linear-gradient(to right, #ff3e2e, #ff9356);
      background: #456ce6;
      line-height: 60px;
      text-align: center;
      font-weight: bold;
      font-size: 30px;
      color: #ffffff;
    }
  }
}
</style>
