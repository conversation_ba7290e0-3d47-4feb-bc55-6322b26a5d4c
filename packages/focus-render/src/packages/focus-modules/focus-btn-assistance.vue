<template>
  <div class="btn-assistance" @click="clickBtn" :style="curStyles">助力按钮</div>
</template>

<script setup lang="ts">
import { toRefs, toRaw, unref, computed, ref } from 'vue'
import { jumpService, mgmService } from '../../service'
import { focusStore } from '../../store/index'
import { focusCore } from '../focus-render'

import { useModuleDataResolve, handleClickByClickEvent } from '../hooks'
import BtnBase from '@/components/BtnBase.vue'
import { UrlParser } from '../../utils/url'

const props = defineProps<{ moduleData: NFocusBtnAssistance.moduleData }>()
const { moduleData } = toRefs(props)
const { modalStore } = focusStore.stores
const urlQuery = new UrlParser(window.location.href).query

const { configData, styleData, moduleType, moduleId } = useModuleDataResolve<NFocusBtnAssistance.configData>(moduleData)
console.log('🚀 ~ file: focus-btn-assistance.vue ~ line 21 ~ configData', configData)

const isHelped = ref<boolean>(false)
const imgUrl = computed(() => {
  return isHelped.value ? configData.value.imgUrl2 : configData.value.imgUrl1
})
const { sid } = urlQuery
if (focusCore.isLogined) {
  mgmService.checkM2HelpedM1({ mgmAid: configData.value.urlMgmAid, m1Sid: sid }).then((flag: 0 | 1 | -1) => {
    if (flag === -1) {
      // 是m1
      return jumpService.jump({ path: configData.value.jumpUrl }, true)
    }
    isHelped.value = !!flag
  })
}

const curStyles = computed(() => {
  const { boxSize, margins, position } = unref(styleData)

  const boxBg = {
    backgroundImage: `url(${imgUrl.value})`,
  }

  const result = {
    ...boxSize,
    ...margins,
    ...boxBg,
    ...position,
  }

  return result
})

const clickBtn = function () {
  if (isHelped.value) {
    return jumpService.jump({ path: configData.value.jumpUrl2 }, true)
  }
  modalStore.loadingStart('setM2helperM1')
  mgmService.setM2helperM1({ mgmAid: configData.value.urlMgmAid, m1Sid: sid }).then((flag) => {
    modalStore.loadingEnd('setM2helperM1')

    if (flag) {
      modalStore.toastShow('助力成功！')
    } else {
      modalStore.toastShow('助力失败！')
    }
    isHelped.value = flag
  })
}
</script>

<style lang="scss" scoped>
.btn-assistance {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}
</style>
