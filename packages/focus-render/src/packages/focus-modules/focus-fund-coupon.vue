<template>
  <div class="fund-coupon" v-if="dataIsReady && (oneCouponData.coupon_config_id || couponsData.length > 1)">
    <div class="middle-area">
      <div class="title-img">
        <img v-if="allCouponIsClaimed" src="../../assets/img/foucs-fund-coupon/title-after.png" alt="恭喜获得福利券" />
        <img v-else src="../../assets/img/foucs-fund-coupon/title-before.png" alt="学知识，领福利" />
      </div>
      <div class="tip" v-show="tipText && !allCouponIsClaimed"><span>小知识：</span>{{ tipText }}</div>
      <!-- 如果只有1个券 -->
      <div class="one-coupon" :class="{ 'is-claimed': oneCouponData.isClaimed }" v-if="couponsData.length === 1">
        <p class="card-title">{{ oneCouponData.cardTitle }}</p>
        <div class="info">
          <div class="money">
            <span class="top" v-show="oneCouponData.isShowTop"><span>最</span><span>高</span></span>
            <div class="number-box">
              <span class="number">{{ oneCouponData.targetDiscountMoney }}</span>
              <span class="yuan">元</span>
            </div>
          </div>
          <p class="desc">
            购买满{{ oneCouponData.targetLevel }}元可用
            {{ oneCouponData.isClaimed ? oneCouponData.overTimeText : '学知识可领取' }}
          </p>
        </div>
      </div>

      <!-- 如果有2个券 -->
      <div class="two-coupons" v-if="couponsData.length === 2">
        <div class="card" v-for="(item, index) in couponsData" :key="index" :class="{ 'is-claimed': item.isClaimed }">
          <div class="card-bg">
            <p class="title">{{ item.cardTitle }}</p>
            <div class="money">
              <span class="top" v-show="item.isShowTop"><span>最</span><span>高</span></span>
              <div class="number-box">
                <span class="number">{{ item.targetDiscountMoney }}</span>
                <span class="yuan">元</span>
              </div>
            </div>

            <div class="desc">
              <p>购买满{{ item.targetLevel }}元可用</p>
            </div>
          </div>
          <div
            class="btn"
            @click="clickTargetCouponAfterClaim(item.coupon_config_id)"
            v-focuslog.click="{
              definedValue: `fundCoupon-btn-go_${moduleId}`,
              definedInfo: {
                curId: item.coupon_config_id,
                showIds: allShowedIds,
              },
            }"
          >
            {{ item.isClaimed ? '去使用' : '学知识可领' }}
          </div>
        </div>
      </div>

      <!-- 如果超过2个券 2-6 -->

      <div
        class="multi-coupons"
        v-if="couponsData.length > 2"
        :class="{ 'shadow-left': showBoxShadow.left, 'shadow-right': showBoxShadow.right }"
      >
        <swiper
          :slides-per-view="'auto'"
          :space-between="8"
          :scrollbar="{ draggable: true }"
          :free-mode="{ momentumBounce: false, momentum: false }"
          :centeredSlidesBounds="false"
          :modules="[FreeMode]"
          class="swiper-wrap"
          @swiper="onSwiper"
          @touch-move="swiperTouchEnd"
        >
          <swiper-slide
            v-for="(item, index) in couponsData"
            :key="`${item.coupon_config_id}_${item.isClaimed}`"
            class="swiper-item"
          >
            <div class="card" :class="{ 'is-claimed': item.isClaimed }">
              <div class="card-bg">
                <p class="title">{{ item.cardTitle }}</p>

                <div class="money">
                  <span class="top" v-show="item.isShowTop"><span>最</span><span>高</span></span>
                  <div class="number-box">
                    <span class="number">{{ item.targetDiscountMoney }}</span>
                    <span class="yuan">元</span>
                  </div>
                </div>
                <div class="desc">
                  <p>购买满{{ item.targetLevel }}元可用</p>
                </div>
              </div>
              <div
                class="btn"
                @click="clickTargetCouponAfterClaim(item.coupon_config_id)"
                v-focuslog.click="{
                  definedValue: `fundCoupon-btn-go_${moduleId}`,
                  definedInfo: {
                    curId: item.coupon_config_id,
                    showIds: allShowedIds,
                  },
                }"
              >
                <p v-show="item.isClaimed">去使用</p>
                <p v-show="!item.isClaimed">学知识可领</p>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>

    <div class="bottom-area" :class="{ 'is-all-claimed': allCouponIsClaimed && couponsData.length >= 2 }">
      <!-- 一个券的按钮 -->
      <div
        class="btn"
        v-if="couponsData.length < 2"
        :style="{ ...(oneCouponData.btnBg || {}) }"
        @click="claimOneCoupon"
        v-focuslog.click="{
          definedValue: `fundCoupon-btn-claimed-one_${moduleId}`,
          definedInfo: {
            curId: oneCouponData.coupon_config_id,
            isClaimed: oneCouponData.isClaimed,
          },
        }"
      >
        {{ !oneCouponData.isClaimed ? '已学习，立即领取' : oneCouponData.afterClaimText || '去使用' }}
      </div>

      <!-- 多个券的按钮 -->
      <div
        class="btn"
        v-if="couponsData.length >= 2 && !allCouponIsClaimed"
        :style="bgForOneBtnClaimAll"
        @click="clickBtnMore"
        v-focuslog.click="{
          definedValue: `fundCoupon-btn-claimed-more_${moduleId}`,
          definedInfo: {
            notClaimedIds: curNotClaimedIds,
            showIds: allShowedIds,
          },
        }"
      >
        已学习，一键领取{{
          couponsData.length > 2 ? (curNotClaimedIds.length ? `(${curNotClaimedIds.length})张` : '') : ''
        }}
      </div>
    </div>

    <dialog-mask :show="!!(showPopupData.show && showPopupData.targetDiscountMoney && dataIsReady)">
      <template #dialog_contain>
        <div
          class="coupon-dialog-contain"
          aria-modal="true"
          role="dialog"
          aria-live="assertive"
          aria-labelledby="dilaog-title"
          @click="showPopupData.show = false"
          v-focuslog.click="{
            definedValue: `coupon-dialog`,
            definedInfo: {
              showPopupData,
              ids: allShowedIds,
              moduleId,
            },
          }"
        >
          <div class="money">
            <span class="top" v-show="showPopupData.isShowTop"><span>最</span><span>高</span></span>
            <div class="number-box">
              <span class="number">{{ showPopupData.targetDiscountMoney }}</span>
              <span class="yuan">元</span>
            </div>
          </div>
          <p class="tip"><span>小知识：</span> {{ showPopupData.tip }}</p>

          <div class="btn-close" @click="showPopupData.show = false" role="button" aria-label="关闭"></div>
        </div>
      </template>
    </dialog-mask>
  </div>
</template>

<script setup lang="ts">
import {
  toRefs,
  toRaw,
  unref,
  computed,
  ref,
  Ref,
  watchEffect,
  onMounted,
  reactive,
  nextTick,
  ReactiveEffect,
} from 'vue'
import { couponService, jumpService } from '../../service'
import { focusStore } from '../../store/index'
import { focusCore } from '../focus-render'
import { useModuleDataResolve, handleClickByClickEvent } from '../hooks'
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue'
import { FreeMode } from 'swiper'
import 'swiper/swiper.min.css'
import 'swiper/modules/free-mode/free-mode.min.css'
import { UrlParser, dayjs } from '@/utils'
import DialogMask from '@/components/DialogMask.vue'
import type { TCouponData } from '@/service/couponService'
const { modalStore, fundCouponStore } = focusStore.stores

const props = defineProps<{ moduleData: NFocusFundCoupon.moduleData }>()
const { moduleData } = toRefs(props)
const urlPased = new UrlParser()
interface TConfigListData {
  btnBg1: string
  btnBg2: string
  btnText2: string
  clickEvent: any
  couponId: string
}

const { configData, styleData, moduleType, moduleId } = useModuleDataResolve<NFocusFundCoupon.configData>(
  props.moduleData
)
console.log('🚀 ~ configData:....', configData)
const tipText = ref('')
const showPopupData = reactive<
  {
    tip: string
    show: boolean
  } & TCouponData
>({
  tip: '',
  show: false,
})
const swiperInstance = ref<any>(null)

const onSwiper = (swiper) => {
  console.log('🚀 ~ onSwiper ~ swiper:', swiper)
  swiperInstance.value = swiper // 通过事件获取实例
}

const curConfigCouponListData = computed(() => {
  const { listData, popupTip, claimBtnBg, tip } = configData.value
  tipText.value = tip
  let data: TConfigListData[] = []
  try {
    data = JSON.parse(listData)
  } catch (err) {
    console.log('🚀 ~ curConfigCouponListData ~ err:', err)
  }

  return data.map((i) => {
    return {
      ...i,
      couponId: i.couponId.toString(),
    }
  })
})

const beforeClickIds = ref<string[]>([])
const dataIsReady = computed(() => {
  return fundCouponStore.dataIsReady
})
const couponsData: Ref<TCouponData[]> = computed(() => {
  const ids = curConfigCouponListData.value.map((i) => {
    return i.couponId
  })
  const idList = Array.from(new Set([...beforeClickIds.value, ...ids]))

  let result: TCouponData[] = []
  result = idList
    .map((id) => {
      return fundCouponStore.allCouponsData[id] || null
    })
    .filter((i) => i && (i.userCouponStatus === '0' || !i.userCouponStatus))
  if (beforeClickIds.value.length) {
    return result
  }
  return result.sort((x, y) => {
    if (x.isClaimed === y.isClaimed) {
      if (Number(x.targetDiscountMoney) > Number(y.targetDiscountMoney)) {
        return -1
      } else if (Number(x.targetDiscountMoney) < Number(y.targetDiscountMoney)) {
        return 1
      }
      return 0
    }
    if (x.isClaimed === false) return -1
    return 1
  })
})

const curNotClaimedIds = computed(() => {
  return couponsData.value
    .filter((i) => !i.isClaimed)
    .map((i) => {
      return i.coupon_config_id
    })
})

const allShowedIds = computed(() => {
  return couponsData.value.map((i) => i.coupon_config_id)
})

const allConfigIds = computed(() => {
  return curConfigCouponListData.value.map((i) => i.couponId)
})

const showBoxShadow = reactive({
  left: false,
  right: true,
})

const canAutoClaimed = computed(() => {
  return new UrlParser().query.autoClaim === '1'
})

getAllCouponsIdInPage()

watchEffect(() => {
  if (dataIsReady.value) {
    focusCore.eventLog('module_show', `FundCoupon_${moduleId.value}`, {
      showIds: allShowedIds.value,
      configIds: allConfigIds.value,
    })
  }
})

onMounted(() => {
  focusCore.onRedirectToSpecialPath('/coupon_toast', () => {
    modalStore.toastShow('恭喜获得福利券，可在当前页面使用')
  })

  // setTimeout(() => {
  //   showPopupData.targetDiscountMoney = 111
  //   showPopupData.isShowTop = true
  //   showPopupData.show = true
  // }, 1000)
})

function swiperTouchEnd(swiper) {
  const swiperData = toRaw(swiper)
  // console.log('🚀 ~ swiperTouchEnd ~ swiperData:', swiperData)
  const translate = swiperInstance?.value?.translate
  // console.log('🚀 ~ swiperTouchEnd ~ translate:', translate)
  // 获取最小和最大允许偏移量
  const minTranslate = swiperInstance?.value?.minTranslate()
  // console.log('🚀 ~ swiperTouchEnd ~ minTranslate:', minTranslate)
  const maxTranslate = swiperInstance?.value?.maxTranslate()
  // console.log('🚀 ~ swiperTouchEnd ~ maxTranslate:', maxTranslate)

  // 判断是否贴边（左右边界）
  const isLeftEdge = translate >= minTranslate
  const isRightEdge = translate <= maxTranslate

  if (isLeftEdge) {
    console.log('滑动到左侧贴边')
    showBoxShadow.right = true
    showBoxShadow.left = false
  } else if (isRightEdge) {
    console.log('滑动到右侧贴边')
    showBoxShadow.right = false
    showBoxShadow.left = true
  } else {
    showBoxShadow.right = true
    showBoxShadow.left = true
  }
}

const oneCouponData: Ref<
  {
    btnBg?: any
    afterClaimText?: string
  } & TCouponData
> = computed(() => {
  console.log('🚀 ~ couponsData.value.length: moduleid', moduleId.value)
  console.log('🚀 ~ couponsData.value.length:', couponsData.value.length)
  if (couponsData.value && couponsData.value.length === 1) {
    const couponData = couponsData.value[0]
    console.log('🚀 ~ couponData:', couponData)

    const targetData = curConfigCouponListData.value.find(
      (i) => i.couponId.toString() === couponData.coupon_config_id?.toString()
    )
    console.log('🚀 ~ targetData:', targetData)
    if (targetData) {
      const beforeClaimBg = targetData.btnBg1 || '#FC505D-#FF9D63'
      const afterCliamBg = targetData.btnBg2 || '#FC505D-#FF9D63'
      const _bg = couponData.isClaimed ? afterCliamBg : beforeClaimBg
      console.log('🚀 ~ _bg:', _bg)

      let btnBg: any = { background: `${_bg}` }
      if (_bg.indexOf('-') > -1) {
        const arr = _bg.split('-')
        btnBg = { 'background-image': `linear-gradient(270deg, ${arr[0]}, ${arr[1]})` }
      }
      return {
        ...couponData,
        btnBg,
        afterClaimText: targetData.btnText2 || '去使用',
        overTimeText: couponData.overTime && couponData.overTime > 0 ? `${couponData.overTime}天后过期` : `今天过期`,
      }
    }
    return {}
  }
  return {}
})

const allCouponIsClaimed = computed(() => {
  return couponsData.value && couponsData.value.length >= 1 && !couponsData.value.some((i) => !i.isClaimed)
})

// 一键领取多个券的按钮UI
const bgForOneBtnClaimAll = computed(() => {
  const { claimBtnBg } = configData.value
  const _bg = claimBtnBg || '#FC505D-#FF9D63'

  let btnBg: any = { background: `${_bg}` }
  if (_bg.indexOf('-') > -1) {
    const arr = _bg.split('-')
    btnBg = { 'background-image': `linear-gradient(270deg, ${arr[0]}, ${arr[1]})` }
  }
  return btnBg
})

function clickBtnMore() {
  clickClaimBtn()
}

function getAllCouponsIdInPage() {
  return new Promise(() => {
    if (focusCore.focusRenderCtr) {
      console.log('...focusCore.focusRenderCtr.moduleTree', focusCore.focusRenderCtr.moduleTree)
      const allMCouponModules = focusCore.focusRenderCtr.findModulesByType('FundCoupon')

      // 找到最小的moduleId
      let minMouldeId = moduleId.value
      let allIds: string[] = []
      const popupTips: any = {}

      allMCouponModules.forEach(({ moduleId, data }) => {
        minMouldeId = minMouldeId > moduleId ? moduleId : minMouldeId
        const { listData, popupTip } = data
        popupTips[moduleId] = popupTip
        try {
          const _data = JSON.parse(listData) || []
          _data.forEach((i) => {
            allIds.push(i.couponId.toString())
          })
        } catch (err) {
          console.log('🚀 ~ allMCouponModules.forEach ~ err:', err)
        }
      })

      console.log('🚀 ~ returnnewPromise ~ minMouldeId:', minMouldeId)
      console.log('🚀 ~ returnnewPromise ~ allIds:', allIds)
      console.log('当前这个就是最小的组件,选最小的组件来拉配置，其他的等待结果就好')
      if (minMouldeId === moduleId.value) {
        getCouponsData(allIds).then((list) => {
          checkShowPopupClaimAllCoupons(popupTips, list)
        })
      }
    }
  })
}

function checkShowPopupClaimAllCoupons(popupTips: any, couponsList: TCouponData[]) {
  if (canAutoClaimed.value && focusCore.hasAccount) {
    console.log('检测到自动领取的字段，要自动领券了！')
    const arr = Object.keys(popupTips).sort((a, b) => Number(a) - Number(b))
    if (arr[0]) {
      showPopupData.tip = popupTips[arr[0]] || ''
      console.log('🚀 ~ returnnewPromise ~ showPopupTip.value:', showPopupData.tip)
      const notClaimedIds = couponsList.filter((i) => !i.isClaimed).map((i) => i.coupon_config_id || '')
      console.log('🚀 ~ 所有未领取的券id在这里', notClaimedIds)
      // 领取所有可领取的id
      if (notClaimedIds.length && showPopupData.tip) {
        modalStore.loadingStart('claimCouponsForPopup')
        claimCouponByIds(notClaimedIds).then((res) => {
          modalStore.loadingEnd('claimCouponsForPopup')
          if (res) {
            showPopupData.show = true
          }
        })
      }
    }
  }
}

function getCouponsData(ids: string[]): Promise<TCouponData[]> {
  modalStore.loadingStart('getCouponsData')

  return new Promise((resolve) => {
    couponService
      .getCoupons(ids)
      .then((res) => {
        console.log('🚀 ~ couponService.getCoupons ~ res:', res)
        fundCouponStore.updatedCouponsData(res.couponList)
        if (canAutoClaimed.value) {
          showPopupData.isShowTop = res?.maxData?.isShowTop
          showPopupData.targetDiscountMoney = res?.maxData?.targetDiscountMoney
        }
        return resolve(res.couponList)
      })
      .finally(() => {
        modalStore.loadingEnd('getCouponsData')
      })
  })
}

function claimCouponByIds(ids: string[] = []): Promise<boolean> {
  modalStore.loadingStart('claimCoupons')
  return new Promise((resolve) => {
    couponService
      .claimCoupons(ids)
      .then((res) => {
        console.log('🚀 ~ couponService.claimCoupons ~ res:', res)
        getCouponsData(ids).then(() => {
          if (res.status) {
            return resolve(true)
          }
        })
        if (res.status) {
          beforeClickIds.value = ids
          modalStore.toastShow('领取成功')
        }
      })
      .catch((err) => {
        modalStore.toastShow('领取失败，请稍后重试')
        const { fid } = urlPased.query

        const key = `autoClaimed_fail_${fid}`

        focusCore.getLocalData(key).then((d) => {
          console.log('🚀 ~ focusCore.getLocalData ~ d:', d)
          const now = dayjs()
          let times = 1
          const _date = now.format('YYYYMMDD')
          if (d) {
            const t = d.value[_date]
            console.log(t)
            if (t && Number(t) < 3) {
              times = Number(t) + 1
            } else if (t && Number(t) >= 3) {
              modalStore.errorMaskContrl(key, '网络开了会小差，下次再来参与吧~', `${_date}_${times}`)
              return
            }
            const data = {}
            data[_date] = times
            focusCore.setLocalData(key, data)
            setTimeout(() => {
              console.log('准备重启啦！')
              location.reload()
            }, 2000)
          } else {
          }
        })
      })
      .finally(() => {
        modalStore.loadingEnd('claimCoupons')
      })
  })
}

function claimOneCoupon() {
  if (oneCouponData.value.isClaimed) {
    clickTargetCouponAfterClaim(oneCouponData.value.coupon_config_id || '')
  } else {
    clickClaimBtn()
  }
}

function clickClaimBtn() {
  if (!focusCore.hasAccount) {
    focusCore.env
      .checkIsInWxMini()
      .then(() => {
        jumpService.jump({
          path: '/pages/index/index?specialJump=activityOpenAccount',
          method: 'mini',
        })
      })
      .catch(() => {
        jumpService.commonUse.appHome()
      })
    return
  }
  const ids = couponsData.value.filter((i) => !i.isClaimed).map((i) => i.coupon_config_id || '')
  claimCouponByIds(ids)
}

function clickTargetCouponAfterClaim(id: string = '') {
  const targeCoupon = couponsData.value.find((i) => i.coupon_config_id === id)
  if (!targeCoupon || !targeCoupon?.isClaimed) {
    return
  }

  const targetConfig = curConfigCouponListData.value.find((i) => i.couponId === id)
  handleClickByClickEvent(targetConfig?.clickEvent || {}, 'FundCoupon', `${moduleId.value}_${id}`)
}
</script>

<style scoped lang="scss">
.money {
  width: 100%;
  color: #f24c3d;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 34px;
  font-size: 28px;
  .number-box {
    height: 50px;
    width: auto;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 2px;
  }
  span.number {
    line-height: 54px;
    font-size: 56px;
    display: block;
    height: 48px;
  }
  span.yuan {
    line-height: 1;
    display: block;
  }
  span.top {
    width: 32px;
    height: 56px;
    box-sizing: border-box;
    line-height: 1;
    color: #f24c3d;
    font-size: 20px;
    position: relative;
    font-weight: normal;
    span {
      z-index: 2;
      display: block;
      width: 32px;
      height: 24px;
      position: absolute;
      left: 0;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      &:nth-child(1) {
        top: 4px;
      }
      &:nth-child(2) {
        bottom: 4px;
      }
    }
    &::before,
    &::after {
      content: '';
      width: 32px;
      height: 32px;
      display: block;
      border-radius: 100%;
      background: #fcd1ca;
      position: absolute;
      z-index: 1;
      left: 0;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
  }
}

.card {
  width: 310px;
  height: 310px;
  box-sizing: border-box;
  position: relative;
  &.is-claimed {
    .card-bg {
      &::before {
        content: '';
        width: 88px;
        height: 80px;
        background: url('../../assets/img/foucs-fund-coupon/icon-isclaimed.png') no-repeat;
        background-size: 100% 100%;
        position: absolute;
        right: 0;
        top: 0;
      }
    }
    .btn {
      &::after {
        display: block;
        content: '';
        width: 30px;
        height: 30px;
        background: url('../../assets/img/foucs-fund-coupon/icon_rightarrow.png') no-repeat;
        background-size: 100%;
      }
    }
  }
  &::after {
    content: '';
    background: #ff4549;
    border-radius: 16px;
    width: 100%;
    height: 262px;
    position: absolute;
    left: 0;
    bottom: 0;
  }
  .card-bg {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    border-radius: 16px;
    background: #fff5f3;
    position: relative;
    padding-top: 24px;
    box-sizing: border-box;
    margin: 0 auto;
    width: 280px;
    height: 244px;
    z-index: 2;

    .title {
      color: #37456e;
      font-size: 32px;
      letter-spacing: 0;
      text-align: center;
      line-height: 40px;
      font-weight: 500;
      z-index: 3;
    }
    .desc {
      height: 70px;
      width: 100%;
      font-size: 20px;
      color: #9b431e;
      letter-spacing: 0;
      text-align: center;
      line-height: 24px;
      font-weight: 400;
      border-top: 1px dashed #ff4549;
      position: relative;
      z-index: 3;
      line-height: 70px;
      overflow: visible;
      p {
        width: 100%;
        overflow: hidden; /* 隐藏溢出内容 */
        white-space: nowrap; /* 强制文本不换行 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
        box-sizing: border-box;
        padding: 0 10px;
      }

      &::before,
      &::after {
        width: 16px;
        height: 16px;
        background: #ff4549;
        border-radius: 100%;
        content: '';
        position: absolute;
        top: -8px;
      }
      &::before {
        left: -8px;
      }
      &::after {
        right: -8px;
      }
    }
  }
  .btn {
    width: 100%;
    height: 64px;
    line-height: 64px;
    font-size: 28px;
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.tip {
  font-size: 28px;
  color: #9da2b3;
  letter-spacing: 0;
  line-height: 42px;
  font-weight: 400;
  margin-top: -16px;
  margin-bottom: 20px;
  width: 100%;
  text-align: left;
  span {
    font-size: 28px;
    color: #37456e;
    letter-spacing: 0;
    line-height: 42px;
    font-weight: 500;
  }
}

.coupon-dialog-contain {
  position: relative;
  width: 560px;
  height: 630px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  background: url('../../assets/img/foucs-fund-coupon/bg-dialog.png') no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 280px 40px 0 40px;
  .money {
    .number-box {
      padding-bottom: 0;
    }
    span.number {
      font-size: 64px;
      line-height: 62px;
      height: 56px;
    }
    span.yuan {
      font-size: 28px;
    }
  }
  .tip {
    padding-top: 40px;
  }
  .btn-close {
    position: absolute;
    width: 60px;
    height: 60px;
    top: -90px;
    right: 0px;
    background: url('../../assets/img/common/btn-close.png') no-repeat;
    background-size: contain;
    z-index: 222;
  }
}

.fund-coupon {
  width: 702px;
  height: auto;
  position: relative;
  &::before {
    content: '';
    display: block;
    width: 100%;
    height: 40px;
    background: url('../../assets/img/foucs-fund-coupon/bg-contan-top.png') no-repeat;
    background-size: 100% 100%;
  }

  .middle-area {
    width: 100%;
    min-height: 280px;
    background: url('../../assets/img/foucs-fund-coupon/bg-contain-middle.png') repeat-y;
    background-size: 100%;
    box-sizing: border-box;
    padding: 0 32px;
    padding-bottom: 10px;
    .title-img {
      width: 100%;
      height: 40px;
      text-align: left;
      img {
        width: auto;
        height: 40px;
      }
      margin-bottom: 32px;
    }

    .one-coupon {
      width: 638px;
      height: 200px;
      background: url('../../assets/img/foucs-fund-coupon/bg-one_coupon.png') no-repeat;
      background-size: 100% 100%;
      position: relative;
      &.is-claimed {
        &::before {
          content: '';
          width: 88px;
          height: 80px;
          background: url('../../assets/img/foucs-fund-coupon/icon-isclaimed.png') no-repeat;
          background-size: 100% 100%;
          position: absolute;
          right: 16px;
          top: 16px;
        }
      }
      .card-title {
        font-size: 40px;
        color: #ffebcc;
        letter-spacing: 0;
        text-align: center;
        line-height: 48px;
        font-weight: 500;
        position: absolute;
        left: 32px;
        top: 50%;
        transform: translate3D(0, -50%, 0);
        margin-top: -4px;
      }
      .info {
        width: 380px;
        height: 112px;
        position: absolute;
        right: 38px;
        bottom: 48px;

        .desc {
          width: 100%;
          font-size: 24px;
          color: #9b431e;
          letter-spacing: 0;
          text-align: center;
          line-height: 30px;
          font-weight: 400;
          margin-top: 10px;
          overflow: hidden; /* 隐藏溢出内容 */
          white-space: nowrap; /* 强制文本不换行 */
          text-overflow: ellipsis; /* 超出部分显示省略号 */
        }
      }
    }

    .two-coupons {
      height: 310px;
      width: 638px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .multi-coupons {
      width: 638px;
      height: 324px;
      position: relative;
      margin-top: -14px;
      &::after,
      &::before {
        width: 40px;
        height: 324px;
        content: '';
        background: url('../../assets/img/foucs-fund-coupon/shadow-swiper.png') no-repeat;
        background-size: 100% 100%;
        position: absolute;
        z-index: 2;
        opacity: 0;
      }
      &::before {
        left: 0;
        top: -30px;
        bottom: 0;
        transform: scale(-1);
      }
      &::after {
        right: 0;
        top: -30px;
        bottom: 0;
      }
      &.shadow-left {
        &::before {
          opacity: 1;
        }
      }
      &.shadow-right {
        &::after {
          opacity: 1;
        }
      }
      .swiper-wrap {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        padding-top: 14px;
        .swiper-item {
          height: 100%;
          width: 250px;
          .card {
            width: 100%;

            .card-bg {
              width: 218px;
              height: 244px;
              .money {
                .number {
                  line-height: 46px;
                  font-size: 48px;
                  height: 42px;
                }
                span.top {
                  height: 52px;
                }
              }
            }
          }
        }
      }
    }
  }

  .bottom-area {
    width: 702px;
    height: 172px;
    background: url('../../assets/img/foucs-fund-coupon/bg-contain-bottom.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    z-index: 2;
    margin-top: -1px;
    padding-bottom: 32px;
    box-sizing: border-box;

    &.is-all-claimed {
      background-image: url('../../assets/img/foucs-fund-coupon/bg-contan-top.png');
      transform: scale(-1);
      height: 40px;
      background-size: 100% 100%;
      margin-top: -10px;
      padding-bottom: 0;
    }
    .btn {
      width: 638px;
      height: 96px;
      font-size: 32px;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      line-height: 40px;
      font-weight: 500;
      line-height: 96px;
      border-radius: 96px;
      margin: 0 auto;
    }
  }
}
</style>
