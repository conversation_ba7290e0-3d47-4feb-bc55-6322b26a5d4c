<template>
  <div class="focus-video" :style="curStyles" v-if="videoSrc">
    <video
      controls
      :poster="posterImg"
      preload="auto"
      :autoplay="isAutoPlay"
      :title="videoTitle"
      playsinline="true"
      webkit-playsinline="true"
      x5-playsinline="true"
      ref="videoDom"
      muted
    >
      <source :src="videoSrc" type="video/mp4" />
    </video>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, onUnmounted, unref, watchPostEffect, ref, watch, onMounted, watchEffect } from 'vue'
import { useModuleDataResolve } from '../hooks/index'
import { focusCore } from '../focus-render'
const props = defineProps<{
  moduleData: TModuleData
}>()
const { configData, styleData, dynamicData } = useModuleDataResolve<NFocusMVideo.configData>(props.moduleData)

const videoSrc = computed(() => {
  return configData.value.videoUrl || ''
})
const posterImg = computed(() => {
  return configData.value.imgUrl || ''
})
const isAutoPlay = computed(() => {
  return configData.value.autoPlay || false
})
const videoTitle = computed(() => {
  return configData.value.title || ''
})
const isAdding = ref(false)
const videoDom = ref<HTMLVideoElement | null>(null)

watchEffect(() => {
  console.log('🚀 ~ file: focus-video.vue ~ line 45 ~ watchEffect ~ videoDom.value', videoDom.value)
  if (videoDom.value && !isAdding.value) {
    console.log('🚀 ~ file: focus-video.vue ~ line 46 ~ watchEffect ~ videoDom.value', videoDom.value)
    isAdding.value = true
    videoDom.value.addEventListener('play', () => {
      onPlay()
    })
    videoDom.value.addEventListener('pause', () => {
      onPause()
    })
  }
})
onMounted(() => {
  setTimeout(() => {
    if (isAutoPlay.value && videoDom.value) {
      videoDom.value?.play()
    }
  }, 3000)
})
const curStyles = computed(() => {
  const { boxSize, margins } = unref(styleData)
  return {
    ...boxSize,
    ...margins,
  }
})
function onPlay() {
  console.log('playvideo')
  focusCore.clickLog(`${props.moduleData.moduleId}`, { step: 'play', src: configData.value.videoUrl })
}
function onPause() {
  console.log('pausevideo')
  focusCore.clickLog(`${props.moduleData.moduleId}`, { step: 'pause', src: configData.value.videoUrl })
}
</script>

<style lang="scss" scoped>
.focus-video {
  video {
    width: 100%;
    height: 100%;
  }
}
</style>
