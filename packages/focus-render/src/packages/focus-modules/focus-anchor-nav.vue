<template>
  <div class="focus-anchor-nav" :style="curStyles" :class="{ fixed: canFixedTop && flag }" ref="anchorList">
    <div class="bar" :style="curStyles">
      <div class="item" v-for="(item, index) in listData" @click="goAnchor(item, index)">
        <img :src="item.imgUrl1" alt="" v-show="activeIndex !== index" />
        <img :src="item.imgUrl2" alt="" v-show="activeIndex === index" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { toRefs, toRaw, unref, computed, ref, getCurrentInstance, nextTick, onUnmounted, onMounted } from 'vue'
import { jumpService, mgmService } from '../../service'
import { focusCore } from '../focus-render'
import { throttle, AnimateController } from '../../utils'
import { useModuleDataResolve } from '../hooks'
interface IItemListData {
  taskId: string
  imgUrl1: string
  imgUrl2: string
  targetId: string
}

const props = defineProps<{ moduleData: NFocusAnchorNav.moduleData; focusFid: number }>()
const { moduleData } = toRefs(props)
// console.log('🚀 ~ file: focus-anchor-nav.vue ~ line 26 ~ moduleData', moduleData.value)

const { configData, styleData, moduleType, moduleId } = useModuleDataResolve<NFocusAnchorNav.configData>(
  props.moduleData
)
const activeIndex = ref(-1)

const listData = computed<IItemListData[]>(() => {
  let data: any = configData.value.listData
  try {
    data = JSON.parse(data || '[]')
  } catch (err) {}
  return data.map((i: IItemListData) => {
    return {
      ...i,
      targetId: `#anchor_${props.focusFid}_${i.taskId}`,
    }
  })
})

const canFixedTop = computed(() => {
  console.log(
    '🚀 ~ file: focus-anchor-nav.vue ~ line 50 ~ canFixedTop ~ configData.value.topping',
    configData.value.topping
  )
  return configData.value.topping
})

const curStyles = computed(() => {
  const { boxSize } = unref(styleData)

  const result = {
    ...boxSize,
  }

  return result
})

const btnScrollTop = ref<number>(0) //挂载后获取到导航条距离顶部的距离
const btnScrollHeight = ref<number>(0) // 挂载后获取到导航栏的高度
const viewHeight = window.screen.height //屏幕高度
const flag = ref(false) // 导航高度是否超过滚动的距离
const isScrolling = ref(false)
const anchorList = ref()
const targetList = ref<
  {
    index: number
    top: number
    height: number
    taskId: string
  }[]
>([]) // 目标组件距离顶部的距离；数组
const watchScroll = ref<any>(() => {})
const timer = ref<any>(null)

onMounted(() => {
  nextTick(() => {
    init()
  })
})
onUnmounted(() => {
  document.removeEventListener('scroll', watchScroll.value)
})

function init() {
  const anchorListDom: any = anchorList.value
  console.log('🚀 ~ file: focus-anchor-nav.vue ~ line 87 ~ init ~ anchorListDom', anchorListDom)

  btnScrollTop.value = (anchorListDom as HTMLElement)?.offsetTop
  btnScrollHeight.value = (anchorListDom as HTMLElement)?.offsetHeight
  const dom = document.querySelector(listData.value[0].targetId)
  // 第一个在可视范围内的时候，展示
  let firstTop = (dom && (dom as HTMLElement).offsetTop) || 0
  if (dom && firstTop < viewHeight) {
    updateActiveInde(0)
  }

  const scrollAmimate = new AnimateController()
  watchScroll.value = () => {
    scrollAmimate.once(() => {
      handleScroll()
    })
  }

  // 添加滚动监听事件
  document.addEventListener('scroll', watchScroll.value)
}

function goAnchor(data: IItemListData, index: number) {
  //清除定时器
  clearTimeout(timer)
  timer.value = null
  let taskID = `#anchor_${props.focusFid}_${data.taskId}` //需要跳转的id
  updateActiveInde(index)
  const dom = document.querySelector(taskID)
  if (dom) {
    timer.value = setTimeout(() => {
      const dom = document.querySelector(taskID)

      let top = (dom as HTMLElement).offsetTop
      if (canFixedTop.value) {
        startScroll(top - btnScrollHeight.value)
      } else {
        // 不置顶
        startScroll(top)
      }
    }, 200)
  }
}
function startScroll(top: number) {
  const target = top
  const now = document.documentElement.scrollTop || document.body.scrollTop

  const animate = new AnimateController()
  isScrolling.value = true

  animate.documentScrollYTo(now, target, 300)
  animate.onAnimateDone(() => {
    isScrolling.value = false
  })
}

function updateActiveInde(id: number) {
  if (!isScrolling.value) {
    activeIndex.value = id
  }
}

function handleScroll() {
  // 滚动的时候再获取一次屏幕高度
  // viewHeight = window.screen.height //屏幕高度
  let target = document.documentElement.scrollTop || document.body.scrollTop // 获取滚动的距离
  flag.value = target > btnScrollTop.value ? true : false

  let bodyDom = document.querySelector('body')
  if (!(targetList.value && targetList.value.length)) {
    listData.value.forEach((element, index) => {
      const dom = document.querySelector(element.targetId)
      targetList.value.push({
        index: index,
        top: (dom && (dom as HTMLElement))?.offsetTop || 0,
        height: (dom && (dom as HTMLElement))?.offsetHeight || 0,
        taskId: `#anchor_${props.focusFid}_${element.taskId}`,
      })
    })
  }
  // 按照目标模块距离顶部高度从小到大排列
  let list = targetList.value.sort((a, b) => {
    let value1 = a['top']
    let value2 = b['top']
    return value1 - value2
  })
  // 判断前后的极值情况
  if (target + viewHeight + 1 >= (bodyDom?.scrollHeight || 0)) {
    if (list[list.length - 1].top < viewHeight + target && list[list.length - 1].top >= target) {
      updateActiveInde(list.length - 1)
    } else {
      console.log('不在可视范围内')
    }
  } else {
    for (let v of list) {
      if (canFixedTop.value) {
        // 置顶
        if (target + 1 < v.top - btnScrollHeight.value + v.height) {
          if (!flag.value && target + 1 >= v.top - btnScrollHeight.value) {
            updateActiveInde(0)
            break
          } else if (target + 1 >= v.top - btnScrollHeight.value) {
            updateActiveInde(v.index)
            break
          } else if (target + viewHeight > v.top) {
            // 滚动到最后，在一屏幕
            updateActiveInde(v.index)
            break
          } else {
            updateActiveInde(-1)
          }
        } else {
          updateActiveInde(-1)
        }
      } else {
        // 不置顶
        if (target >= v.top && target < v.top + v.height) {
          // 超过滚动距离
          updateActiveInde(v.index)
          break
        } else {
          if (targetList.value[0].top < viewHeight) {
            // 表示为第一个模块
            updateActiveInde(0)
            break
          } else {
            updateActiveInde(-1)
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.focus-anchor-nav {
  width: 100%;
  &.fixed {
    z-index: 5000 !important;
    .bar {
      position: fixed;
      top: 0;
      left: 50%;
      right: 0;
      z-index: 5000 !important;
      transform: translateX(-50%);
    }
  }
  .bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    .item {
      flex: auto;
      height: 100%;
      width: 100%;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
