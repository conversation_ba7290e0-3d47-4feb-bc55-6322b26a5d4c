<template>
  <div class="task-list" v-show="taskList.length">
    <div class="header">
      <div class="left">{{ taskListVer }}</div>
      <div class="right">{{ expireTime }}过期</div>
    </div>
    <div class="muti-task" v-if="taskFinishDetail.needTaskNumber">
      <p>
        完成{{ taskFinishDetail.needTaskNumber }}个任务得{{ taskFinishDetail.taskPoints }}积分({{
          taskFinishDetail.currFinishTask
        }}/{{ taskFinishDetail.needTaskNumber }})
      </p>
      <div
        class="btn"
        :class="`status_${taskFinishDetail.moreTaskFinishStatus}`"
        @click="getRewardByMutiDone"
        role="button"
      >
        {{ taskFinishDetail.btnText }}
      </div>
    </div>
    <div class="list">
      <div class="item" v-for="(item, index) in taskList" :key="item.taskId">
        <img class="bg-cover" src="../../assets/img/task-list/bg-task.png" aria-hidden="true" />
        <div class="title">
          <span>{{ item.title }}</span>
          <span class="progress" v-if="item.maxProgress"
            >({{ item.curProgress ? item.curProgress : '--' }}/{{ item.maxProgress }})</span
          >
          <img
            class="tip"
            src="../../assets/img/task-list/tip.png"
            role="button"
            alt="提示"
            v-if="item.taskLotteryImgUrl"
            @click="clickTip(item.taskLotteryImgUrl)"
          />
        </div>
        <div class="line-bottom">
          <div class="info">
            <div class="desc">
              {{ item.taskLotteryDesc }}
            </div>
            <div class="time">{{ item.showTimeText }}</div>
            <div class="lottery-times">
              抽奖机会<span>+{{ item.lotteryTimesChange }}</span>
              <span class="set-change" v-if="item.lotteryChanceDesc">
                {{ item.lotteryChanceDesc }}
              </span>
            </div>
          </div>
          <div
            class="btn"
            v-bind:data-reportid="item.reportid"
            :class="item.isDone ? 'disabled' : ''"
            @click="clickTask(item, index)"
            :style="{ opacity: item.btnDisabled ? '0.5' : '1' }"
            role="button"
            :aria-disabled="item.isDone || item.btnDisabled ? true : false"
          >
            {{ item.btnText }}
          </div>
        </div>
      </div>
    </div>
  </div>
  <MaskVue v-show="tipImg" class="dialog">
    <div class="contain">
      <div class="btn-close" @click="clickTip('')"></div>
      <img class="cover" :src="tipImg" />
      <div class="btn" @click="clickTip('')">知道了</div>
    </div>
  </MaskVue>

  <MaskVue v-show="multiTaskPrice.show" class="dialog">
    <div class="contain price-dialog">
      <div class="btn-close" @click="showPrice({ show: false })"></div>
      <div class="title">恭喜获得</div>
      <div class="title-price">{{ multiTaskPrice.title }}</div>
      <img class="cover" :src="multiTaskPrice.priceImgUrl" alt="奖品" />
      <p class="desc">可在积分页面查看详情并使用</p>
      <div class="btn-share" @click="clickSharePrice" role="button">邀好友一起参与</div>
    </div>
  </MaskVue>
</template>

<script setup lang="ts">
import MaskVue from '@/components/Mask.vue'
import { focusStore } from '../../store/index'
import { focusService, jumpService, mgmService, taskService } from '../../service'
import { ref, onMounted } from 'vue'
import { nowIsBoforeTarget } from '../../utils/time-utils'
import { useModuleDataResolve } from '../hooks/index'
import { focusCore } from '../focus-render'
import taskListService from '@/service/taskListService'

const { taskStore, modalStore } = focusStore.stores
const props = defineProps<{
  moduleData: TModuleData
}>()
const { configData, styleData } = useModuleDataResolve<{ configId: string; multiPriceShareId: string }>(
  props.moduleData
)

const taskList = ref<any[]>([])
const taskListVer = ref('')
const expireTime = ref('')
const taskFinishDetail = ref({
  needTaskNumber: 0,
  taskPoints: 0,
  currFinishTask: 0,
  btnText: '',
  moreTaskFinishStatus: 0,
  periodNo: 0,
})
const multiTaskPrice = ref({
  show: false,
})

getTaskListData()
function getTaskListData() {
  if (configData.value.configId && focusCore.hasAccount) {
    taskService.getTaskProgressByConfigId(configData.value.configId).then((res: any) => {
      console.log('🚀 ~ file: focus-task-list.vue ~ line 78 ~ taskService.getTaskProgressByConfigId ~ res', res)
      if (res && res.taskList && res.taskList.length) {
        taskList.value = res.taskList
        taskListVer.value = res.taskListVer
        expireTime.value = res.expireTime
        taskFinishDetail.value = res.taskFinishDetail
      }
    })
  }
}

const tipImg = ref('')

const clickTip = (_tipImg: string) => {
  tipImg.value = _tipImg
}

const clickTask = (data: any, index: number) => {
  const {
    placeId,
    jumpPath,
    isDone,
    isShare,
    mgmAid,
    reportid,
    taskPerfectibilityTime,
    btnDisabled,
    shareData = {},
    jumpParams = {},
  } = data
  console.log('🚀 ~ file: focus-task-list.vue:142 ~ clickTask ~ jumpParams:', jumpParams)

  if (isDone || btnDisabled) {
    return false
  }
  if (taskPerfectibilityTime && !nowIsBoforeTarget(taskPerfectibilityTime)) {
    taskList.value[index] = {
      ...taskList.value[index],
      isDone: true,
      btnText: '已结束',
    }
    return false
  }
  if (isShare) {
    mgmService.clickShare(reportid, {
      ...shareData,
      shareTitle: shareData.shareTitle || '邀你一起参加微众银行这个App热门活动',
      shareDesc: shareData.shareDesc || '好福利，一起享',
    })
  } else {
    jumpService.jump({ path: jumpPath, query: jumpParams })
  }
}

onMounted(() => {
  focusCore.onPageVisibilityChange(
    'tasklist',
    () => {
      setTimeout(() => {
        // 延迟300毫秒执行，避免状态没更新好
        getTaskListData()
      }, 300)
    },
    'show'
  )
})

function getRewardByMutiDone() {
  if (taskFinishDetail.value.moreTaskFinishStatus !== 1) {
    return false
  }
  modalStore.loadingStart('getaward')
  taskListService
    .getRewardByMultiTaskDone(configData.value.configId, taskFinishDetail.value.periodNo)
    .then(
      (res: {
        getAwardStatus: 0 | 1 | 2 | null // 0 领取失败；1 领取成功；2 已领取过；null 其他异常
        awardPopupUrl: string // 奖励弹窗图片
      }) => {
        if (res.getAwardStatus === 1) {
          modalStore.toastShow('领取成功！')
          showPrice({
            show: true,
            title: `${taskFinishDetail.value.taskPoints}积分`,
            priceImgUrl: res.awardPopupUrl,
          })
        } else {
          modalStore.toastShow('领取失败！请重新尝试！')
        }
      }
    )
    .finally(() => {
      modalStore.loadingEnd('getaward')

      setTimeout(() => {
        getTaskListData()
      }, 300)
    })
}

function showPrice(data: any) {
  multiTaskPrice.value = data
}

function clickSharePrice() {
  const shareData =
    (focusCore.focusRenderCtr &&
      focusCore.focusRenderCtr.getShareConfigByConfigId(configData.value.multiPriceShareId)) ||
    {}

  if (shareData.shareUrl) {
    mgmService.clickShare('multiTaskShare', shareData)
  }
  showPrice({ show: false })
}
</script>

<style lang="scss" scoped>
.task-list {
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 0 30px;
  line-height: 1.5;
  .header {
    font-size: 28px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 20px 0;
    .left {
    }
    .right {
      font-size: 26px;
    }
  }
  .muti-task {
    width: 100%;
    background: linear-gradient(to bottom, #63ddff, #47abff, #6eddff);
    border-radius: 20px;
    color: #fff;
    font-size: 30px;
    // font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 0px 10px 10px #6eeeff inset;
    .btn {
      width: 148px;
      height: 60px;
      border-radius: 60px;
      font-size: 26px;
      line-height: 58px;
      text-align: center;
      background: #ee7a52;

      &.status_0 {
        background: transparent;
        border: #fff 1px solid;
      }
      &.status_2 {
        background: #f8d4c3;
      }
    }
  }
  .list {
    width: 100%;
    padding-bottom: 20px;
    .item {
      width: 100%;
      border-radius: 20px;
      background-image: linear-gradient(to bottom, #ffa45940 0%, #fff 96%);
      background-color: #fff;
      border: 3px solid #fff;
      padding: 30px;
      color: #405080;

      margin-bottom: 20px;
      position: relative;
      .bg-cover {
        position: absolute;
        bottom: 0;
        right: 157px;
        z-index: 0;
        width: 178px;
        height: 97px;
      }
      .title {
        text-align: left;

        font-size: 30px;
        img {
          &.tip {
            width: 35px;
            height: 35px;
            margin-left: 5px;
            pointer-events: auto;
            -webkit-user-select: all;
            -moz-user-select: all;
            -webkit-user-select: all;
            -o-user-select: all;
            user-select: all;
            -webkit-touch-callout: all;
          }
        }
        .progress {
          color: #db826c;
        }
      }
      .line-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 10px;
        position: relative;
        z-index: 1;
      }
      .info {
        flex: auto;
        text-align: left;

        .desc {
          font-size: 24px;
          color: #808bab;
          letter-spacing: 0;
          line-height: 39px;
          font-weight: 300;
        }
        .time {
          font-size: 24px;
          color: #808bab;
          letter-spacing: 0;
          line-height: 39px;
          font-weight: 300;
          margin-top: 10px;
        }
        .lottery-times {
          font-size: 26px;
          margin-top: 10px;
          color: #808bab;
          font-weight: 300;
          span {
            color: #cc5623;
          }
          .set-change {
            background-image: linear-gradient(90deg, #ffe8e5 0%, #ffe6e3 99%);
            padding: 1px 8px;
            border-radius: 6px;
            margin-left: 8px;
          }
        }
        .progress {
          font-size: 24px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .bar {
            width: 100px;
            height: 16px;
            border-radius: 16px;
            background-color: #fdf0d1;
            margin-right: 10px;
            overflow: hidden;
            .bar-item {
              width: 50%;
              height: 100%;

              background: #f0c383;

              z-index: 22;
            }
          }
          span {
            color: #808bab;
          }
        }
      }
      .btn {
        flex: none;
        // width: 156px;
        width: 132px;
        // padding: 0 30px;
        height: 60px;
        background: #4b6ede;
        color: #fff;
        font-size: 24px;
        line-height: 60px;
        border-radius: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        &.disabled {
          background: #bec7e7;
        }
      }
    }
  }
}
.dialog {
  .contain {
    width: 560px;
    height: 630px;
    position: relative;
    .cover {
      width: 100%;
      height: 100%;
    }
    .btn {
      position: absolute;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      width: 420px;
      height: 80px;
      line-height: 80px;
      font-size: 32px;
      color: #fff;
      border-radius: 80px;
      background: #2a63ed;
      border-radius: 40px;
      border-radius: 40px;
      &:active {
        opacity: 0.7;
      }
    }
    .btn-close {
      position: absolute;
      width: 60px;
      height: 60px;
      top: -90px;
      right: 0px;
      background: url('../../assets/img/common/btn-close.png') no-repeat;
      background-size: contain;
      z-index: 222;
    }
  }
}

.price-dialog {
  &.contain {
    width: 560px;
    height: auto;
    background: #fff;
    border-radius: 20px;
    position: relative;
    // box-sizing: border-box;
    padding-top: 60px;
    padding-bottom: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    color: #405080;
    text-align: center;
    .title {
      font-size: 43px;
      font-weight: bold;
      line-height: 1.5;
    }
    .title-price {
      font-size: 43px;
      font-weight: bold;
      color: #f05649;
      line-height: 1.5;
    }
    .cover {
      width: 290px;
      height: 260px;
      margin-top: 40px;
    }
    .desc {
      font-size: 24px;
      margin-top: 30px;
      padding: 0 30px;
      line-height: 1.5;
    }
    .btn-share {
      width: 300px;
      height: 72px;
      line-height: 70px;
      background: #456ce6 100%;
      font-size: 28px;
      color: #fff;
      margin-top: 30px;
      border-radius: 72px;
      cursor: pointer;
      position: relative;
      z-index: 222;
    }
    .btn-close {
      position: absolute;
      width: 60px;
      height: 60px;
      top: -90px;
      right: 0px;
      background: url($cdnPrefix + '/img/common/btn-close.png') no-repeat;
      background-size: contain;
      z-index: 222;
    }
  }
}
</style>
