<template>
  <div class="focus-counttime" @click="backPage" v-if="canShow && maxTime && browsingKey">
    <div class="main">
      <ring :maxTime="maxTime" @change="timeChange"></ring>
      <div class="time-text" :class="{ isdone: isDone }">
        {{ timeText }}
      </div>
    </div>
    <div class="icon-wrap">
      <img src="../../../assets/img/focus-counttime/back.png" alt="" class="icon" v-show="isDone" />
      <img src="../../../assets/img/focus-counttime/time.png" alt="" class="icon" v-show="!isDone" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch, watchEffect } from 'vue'
import { focusCore, focusStore, Mask, focusServices } from '../../focus-render'
import Ring from './Ring.vue'
import { UrlParser } from '@/utils'
const { taskService } = focusServices
const { modalStore } = focusStore.stores
const timeText = ref('')
const isDone = ref(false)
const maxTime = ref(0)
const browsingKey = ref('')
const { taskId = '', browsingId = '', browsingTime = '', viewPageTask = 0 } = new UrlParser().query
const canShow = !!(taskId && browsingId && browsingTime && viewPageTask.toString() === '1')
console.log('🚀 ~ file: focus-counttime.vue:31 ~ canShow:', canShow)

if (canShow) {
  taskService.taskViewPage('1', browsingId, Number(browsingTime), Number(taskId)).then((key) => {
    console.log(
      '🚀 ~ file: focus-counttime.vue:37 ~ taskService.taskViewPage ~  Number(browsingTime):',
      Number(browsingTime)
    )
    browsingKey.value = key
    maxTime.value = Number(browsingTime)
  })
}
function timeChange(time: number) {
  if (time === 0) {
    console.log('请求后台')
    taskService.taskViewPage('2', browsingId, Number(browsingTime), Number(taskId), browsingKey.value).catch(() => {
      modalStore.toastShow('浏览页面任务失败！')
    })
  }
  if (time < 0) {
    timeText.value = '完成'
    isDone.value = true
    return
  }
  timeText.value = time.toString()
}

onMounted(() => {})

function backPage() {
  if (isDone.value) {
    history.back()
    // location.replace(backUrl)
  }
}
</script>

<style scoped lang="scss">
.focus-counttime {
  position: fixed;
  right: 8px;
  top: 300px;
  padding: 0 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 80px;
  height: 100px;
  z-index: 66666;
  padding-bottom: 5px;
  .main {
    width: 66px;
    height: 66px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 100%;
    background-color: #fff;
    position: relative;

    .time-text {
      width: 100%;
      position: absolute;
      left: 50%;
      top: 10px;
      transform: translateX(-50%);
      font-size: 24px;
      line-height: 1.5;
      color: #fa5b21;
      text-align: center;
      font-weight: normal;
      margin-top: 2px;
      &::after {
        content: 's';
        font-size: 20px;
      }
      &.isdone {
        font-size: 22px;
        margin-top: 4px;
        &::after {
          content: none;
        }
      }
    }
  }
  .icon-wrap {
    width: 84px;
    height: 32px;
    position: relative;
    z-index: 2;
    top: -24px;
    .icon {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
