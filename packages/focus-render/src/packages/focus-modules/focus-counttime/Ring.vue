<template>
  <div class="time-count-x">
    <svg :viewBox="`0 0 ${viewBoxWidth} ${viewBoxWidth}`" id="svg-el">
      <defs>
        <linearGradient x1="1" y1="0" x2="0" y2="0" id="gradient1">
          <stop offset="0%" :stop-color="ringColor[0]"></stop>
          <stop offset="100%" :stop-color="ringColor[1]"></stop>
        </linearGradient>
        <linearGradient x1="1" y1="0" x2="0" y2="0" id="gradient2">
          <stop offset="0%" :stop-color="ringColor[0]"></stop>
          <stop offset="100%" :stop-color="ringColor[1]"></stop>
        </linearGradient>
      </defs>
      <g :transform="`matrix(0,-1,1,0,0,${viewBoxWidth})`">
        <!-- 底部灰色 -->
        <circle
          :cx="viewBoxWidth / 2"
          :cy="viewBoxWidth / 2"
          :r="ringR"
          :stroke-width="strokeWidth"
          stroke="#ffe8dc"
          fill="none"
          :stroke-dasharray="`${ringSrokeDasharray} ${ringSrokeDasharray}`"
        ></circle>
        <!-- 左半边渐变 -->
        <circle
          :cx="viewBoxWidth / 2"
          :cy="viewBoxWidth / 2"
          :r="ringR"
          :stroke-width="strokeWidth"
          stroke="url('#gradient1')"
          stroke-linecap="round"
          fill="none"
          :stroke-dasharray="ringStrokeDasharrayStr1"
        ></circle>
        <!-- 右半边渐变 -->
        <circle
          :cx="viewBoxWidth / 2"
          :cy="viewBoxWidth / 2"
          :r="ringR"
          :stroke-width="strokeWidth"
          stroke="url('#gradient2')"
          stroke-linecap="round"
          fill="none"
          :stroke-dasharray="ringStrokeDasharrayStr2"
        ></circle>
      </g>
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, toRefs, watchEffect } from 'vue'
const props = defineProps({
  maxTime: Number,
})

const { maxTime } = toRefs(props)
const ringR = 32
const ringSrokeDasharray = Math.PI * 2 * ringR + 1
const strokeWidth = 4
const viewBoxWidth = (ringR + strokeWidth) * 2
const ringColor = ['#FF834C', '#FF4C25']
// const maxTime = 30
let timerTimeCount = 0
let lastTime = ref(props.maxTime || 0)
const emit = defineEmits(['change'])

const percent = computed(() => {
  if (!props.maxTime) {
    return 0
  }
  let result = 1 - lastTime.value / props.maxTime
  return result ? result : 0
})

const ringStrokeDasharrayStr1 = computed(() => {
  return `${ringSrokeDasharray * percent.value} ${ringSrokeDasharray}`
})
const ringStrokeDasharrayStr2 = computed(() => {
  return percent.value <= 0.5
    ? `${ringSrokeDasharray * percent.value} ${ringSrokeDasharray}`
    : `${ringSrokeDasharray / 2} ${ringSrokeDasharray}`
})

watchEffect(() => {
  if (props.maxTime) {
    timeCount()
  }
})

function timeCount() {
  if (timerTimeCount) {
    return
  }

  const count = () => {
    emit('change', lastTime.value)

    if (lastTime.value < 0) {
      clearInterval(timerTimeCount)
      return
    }
    timerTimeCount = setTimeout(() => {
      lastTime.value--
      count()
    }, 1000)
  }

  count()
}
// onMounted(() => {
//   timeCount()
// })
</script>

<style scoped lang="scss">
#svg-el {
  transform: rotate(-0.05deg);
  width: 100%;
  height: 100%;
}
circle {
  transition: stroke-dasharray 0.2s;
}
.time-count-x {
  line-height: 1.5;
  position: relative;
  width: 66px;
  height: 66px;
  transform: scaleX(-1);
  transform-origin: center center;
}
</style>
