<script lang="ts">
import { computed, defineProps, unref, inject, Slots, h, render, toRefs, defineComponent, onMounted } from 'vue'
import type { PropType } from 'vue'
import { useModuleDataResolve } from '../hooks/index'

export default defineComponent({
  props: {
    moduleData: {
      type: Object as PropType<TModuleData>,
      default: () => {
        return {}
      },
    },
  },
  setup(props) {
    const focusSlots = inject<Slots>('focus_slots')
    const slotMountedCb = inject<any>('slotMountedCb')

    const { configData, styleData, moduleId } = useModuleDataResolve<NFocusPlaceHolder.configData>(props.moduleData)

    const slotName = configData.value.text

    const target = focusSlots && focusSlots[slotName]
    console.log(configData.value.params)
    let extendParams = configData.value.params.length ? configData.value.params : []

    const { boxSize, bg, margins, position, borderRadius } = unref(styleData)
    const curStyles = {
      ...margins,
      minHeight: boxSize.height,
      minWidth: boxSize.width,
    }

    if (target) {
      onMounted(() => {
        console.log('加载slot！！！！')
        if (slotMountedCb && slotMountedCb[slotName] && typeof slotMountedCb[slotName] === 'function') {
          try {
            slotMountedCb[slotName](extendParams, moduleId.value)
          } catch {
            console.error('slotcb 有问题！', slotName)
            console.log('slotMountedCb....', slotMountedCb)
          }
        }
      })
      return () =>
        h(
          'div',
          {
            class: `focus-place-holder box_${slotName}`,
            style: curStyles,
          },
          target({
            extendParams,
            configData: configData.value,
            styles: { boxSize },
          })
        )
    }
    return null
  },
})
</script>

<style></style>
