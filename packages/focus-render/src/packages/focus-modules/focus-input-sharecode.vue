<template>
  <div class="input-sharecode">
    <div class="input-bar" :style="{ borderColor: configData.colorInputBox }">
      <input v-model.trim="text" />
      <p class="placeholder" :style="{ color: configData.colorPlaceHolder }" v-show="!text">请输入邀请码</p>
    </div>
    <div class="btn" @click="submit" :style="btnStyle">提交</div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, onMounted, unref, ref, toRaw, toRefs, watchEffect, watch } from 'vue'
import { useModuleDataResolve } from '../hooks/index'
import 'swiper/swiper.min.css'
import { mgmV2 } from '../../service/mgmService'
import { focusStore } from '../../store/index'

const { modalStore } = focusStore.stores

const props = defineProps<{
  moduleData: TModuleData
}>()

const { configData, styleData } = useModuleDataResolve<NFocusInputShareCode.configData>(props.moduleData)
const strMinLen = 6
const strManLen = 6

const text = ref('')
watch(text, (newData, oldData) => {
  if (newData.length) {
    text.value = text.value.replace(/[^A-Za-z0-9]/g, '').slice(0, strManLen) // 正则过滤[2,8](@ref)
  }
})

const btnStyle = computed(() => {
  // linear-gradient(to right, #ff9659, #ff645f)
  let bgBtn = configData.value.bgBtn
  if (bgBtn.indexOf(',') > -1) {
    const [firstColor, secondColor] = configData.value.bgBtn?.split(',')
    if (secondColor) {
      bgBtn = `linear-gradient(to right, ${firstColor},  ${secondColor})`
    } else {
      bgBtn = firstColor
    }
  }
  return {
    color: configData.value.colorBtnText,
    background: bgBtn,
  }
})
function submit() {
  if (text.value.length < strMinLen) {
    return
  }
  modalStore.loadingStart('submit_sharecode')
  mgmV2.submitShareCode(text.value).then(({ status, statusMsg = '' }) => {
    console.log('🚀 ~ mgmV2.submitShareCode ~ statusMsg:', statusMsg)
    console.log('🚀 ~ mgmV2.submitShareCode ~ status:', status)
    modalStore.loadingEnd('submit_sharecode')
    modalStore.toastShow(statusMsg)
  })
  return
}
</script>

<style lang="scss" scoped>
.input-sharecode {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 60px;
  box-sizing: border-box;
  padding: 15px;

  .input-bar {
    border: 2px solid #ffebb3;
    border-radius: 15px;
    width: 480px;
    height: 60px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    box-sizing: border-box;
  }
  input {
    padding: 0; /* 内边距 */
    font-size: 36px;
    font-weight: bold;
    margin: 0;
    color: #e8492c; /* 文本颜色 */
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 2;
    background: transparent;
    text-align: center;
  }
  p.placeholder {
    font-size: 36px;
    position: relative;
    z-index: 1;
    padding: 0;
    bottom: 0;
  }

  /* 设置 placeholder 的颜色 */
  input::placeholder {
    color: #888; /* placeholder 颜色 */
  }

  /* 可选：设置输入框获得焦点时的样式 */
  input:focus {
    outline: none; /* 去掉默认的焦点轮廓 */
  }
  .btn {
    width: 180px;
    height: 60px;
    background: linear-gradient(to right, #ff9659, #ff645f);
    font-size: 24px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 60px;
    cursor: pointer;
    &:active {
      opacity: 0.7;
    }
  }
}
</style>
