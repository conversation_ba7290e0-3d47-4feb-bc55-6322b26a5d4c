<template>
  <p class="focus-text" :style="curStyles" :class="{ isgray: isGrayText }">
    {{ dynamicData.text }}
  </p>
</template>

<script setup lang="ts">
import { computed, defineProps, onMounted, unref, watchEffect } from 'vue'
import { useModuleDataResolve } from '../hooks/index'
import { focusCore } from '../focus-render'

const props = defineProps<{
  moduleData: TModuleData
}>()

const { configData, styleData, dynamicData } = useModuleDataResolve<NFocusText.configData, NFocusText.dynamicData>(
  props.moduleData,
  ['text']
)

watchEffect(() => {
  if (dynamicData.value.text) {
    if (/(_shareConfig_)|(_api_)/.test(configData.value.text)) {
      focusCore.eventLog('text_show', `${dynamicData.value.text}`, {
        originText: configData.value.text,
        moduleId: props.moduleData.moduleId,
      })
    }
  }
})
const curStyles = computed(() => {
  const { boxSize, bg, margins, position, borderRadius, fontStyles, fontSize } = unref(styleData)
  const { changeStyles = {} } = props.moduleData
  let color = fontStyles.color
  if (configData.value.colorMode === '1') {
    const colors = {
      zero: '#2D3859',
      lessZero: '#12B38A',
      moreThenZero: '#F24C3D',
    }
    const text = dynamicData.value.text
    if (text === '0' || text === '0.00%') {
      color = colors.zero
    } else if (/^\-.*\%$/.test(text)) {
      color = colors.lessZero
    } else if (/\%$/.test(text)) {
      color = colors.moreThenZero
    }
  }
  return {
    ...boxSize,
    ...bg,
    ...margins,
    ...fontStyles,
    ...fontSize,
    ...position,
    ...changeStyles,
    color,
  }
})

// 需要强制置灰的文本
const isGrayText = computed(() => {
  if (!configData.value.grayTexts) {
    return false
  }
  const strs = configData.value.grayTexts
  console.log('🐬 ~ file: focus-text.vue:61 ~ isGrayText ~ configData.value.grayTexts:', configData.value.grayTexts)
  const arr = strs.split(';')
  if (!arr.length) {
    return false
  }
  // 在此之前dynamicData.value.text如果是undefined就已经被赋值为''了，所以没问题
  const text = dynamicData.value.text.toString()
  console.log('🐬 ~ file: focus-text.vue:60 ~ isGrayText ~ text:', text)
  return arr.includes(text)
})

const isNumberText = computed(() => {
  return /^[0-9,.%]+$/.test(dynamicData.value.text)
})
</script>

<style lang="scss" scoped>
.focus-text {
  line-height: 1.5;
  &.isgray {
    color: rgba(108, 108, 108, 0.8) !important;
    font-size: 24px !important;
  }
}
</style>
