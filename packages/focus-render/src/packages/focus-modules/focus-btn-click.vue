<template>
  <BtnBase
    :moduleData="moduleData"
    @click="clickBtn"
    :clickAction="clickBtn"
    :text="btnShowText"
    :img="btnShowImg"
    :noclick="!canClick"
    v-if="!hideBtn"
  ></BtnBase>
</template>

<script setup lang="ts">
import { toRefs, toRaw, unref, computed, ref, watchEffect, onMounted } from 'vue'
import { jumpService, mgmService } from '../../service'
import { focusStore } from '../../store/index'
import { focusCore } from '../focus-render'
import { useModuleDataResolve, handleClickByClickEvent } from '../hooks'
import BtnBase from '@/components/BtnBase.vue'
import { UrlParser } from '@/utils'
const { modalStore } = focusStore.stores

const props = defineProps<{ moduleData: NFocusBtnClick.moduleData }>()
const { moduleData } = toRefs(props)

const { configData, styleData, moduleType, moduleId } = useModuleDataResolve<NFocusBtnClick.configData>(
  props.moduleData
)
const showText = computed<boolean>(() => {
  return configData.value.uiType === 'text'
})
const isTransText = ref(false)
const btnShowText = ref('')
const btnShowImg = ref('')
const canClick = ref(true)
const hideBtn = ref(false)

const curStyles = computed(() => {
  const { boxSize, bg, margins, fontStyles, fontSize, position } = unref(styleData)

  const boxBg = {
    backgroundImage: showText.value ? bg?.backgroundImage : 'none',
    backgroundColor: showText.value ? bg?.backgroundColor : 'transperent',
    borderRadius: showText.value ? boxSize?.borderRadius : 'none',
  }

  let textStyles: any = {
    ...fontStyles,
    ...fontSize,
  }
  if (!showText.value) {
    textStyles = {}
  }
  const result = {
    ...boxSize,
    ...margins,
    ...boxBg,
    ...textStyles,
    ...position,
  }
  if (bg.backgroundColor && showText.value) {
    const reg = /rgba\(.*0\)$/
    isTransText.value = reg.test(bg.backgroundColor)
  }

  return result
})

onMounted(() => {
  checkCalendarAlarmHadClick()
})
// 检查订阅按钮被点啦
function checkCalendarAlarmHadClick() {
  const { effect } = configData.value.clickEvent
  switch (effect) {
    case 'calendarAlarm':
      if (focusCore.env.isInWx) {
        console.log('...日历按钮在微信隐藏')
        hideBtn.value = true
        console.log('🚀 ~ file: focus-btn-click.vue:81 ~ checkCalendarAlarmHadClick ~ hideBtn.value:', hideBtn.value)
      }
      console.log('calendarAlarm....', configData.value.clickEvent)
      const {
        caltitle = '',
        calnotes = '',
        calstartTime = '',
        calremindBefore = '',
        caltext = '',
        calimg = '',
      } = configData.value.clickEvent
      const { fid } = new UrlParser().query
      let id = `${fid}-${moduleId.value}-${effect}`
      focusCore.getLocalData(id).then(({ value }) => {
        console.log('🚀 ~ focusCore.getLocalData ~ value:', value)
        if (value && value.title) {
          btnShowText.value = caltext
          btnShowImg.value = calimg
          canClick.value = false
        }
      })

      break
  }
}
const clickBtn = function () {
  console.error('现在按钮不能点啦！')
  if (!canClick.value) {
    return false
  }
  handleClickByClickEvent(configData.value.clickEvent, moduleType.value, moduleId.value.toString()).then((res) => {
    console.log('🚀 ~ file: focus-btn-click.vue:66 ~ handleClickByClickEvent ~ res:', res)
    const { flag } = res || {}
    switch (flag) {
      case 'calendarAlarm_succ':
        const { text = '', img = '' } = res
        btnShowText.value = text
        btnShowImg.value = img
        canClick.value = false
        break
    }
  })
}
</script>

<style lang="scss" scoped>
.focus-btn-click {
  flex: none;
  text-align: center;
  word-break: break-all;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: none;
  width: 100%;
  font-size: 32px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
  background-repeat: no-repeat;
  background-size: 100%;
  cursor: pointer;
  .img {
    width: 100%;
    height: 100%;
  }
  &:active {
    opacity: 0.6;
  }
  &.isimg {
    background-color: none;
    box-shadow: none;
  }
  &.noshadow {
    box-shadow: none;
  }
}
</style>
