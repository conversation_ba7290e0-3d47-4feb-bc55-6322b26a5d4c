import { ref, isRef, unref, watchEffect, computed, effect, inject, toRaw, isProxy, toRefs, toValue } from 'vue'
import type { Ref, ComputedRef, ReactiveEffect } from 'vue'
import { <PERSON>Handler } from './StyleHandler'
import { dynimicDataService, jumpService } from '@/service'
import { focusCore } from '../focus-render/index'
import { focusStore } from '../focus-render/index'
import { UrlParser } from '@/utils'
import useClipboard from 'vue-clipboard3'

// 全局公共节流标志

const timeSpace = 500 // 执行间隔 1s
let isDoing = false

export function handleClickByClickEvent(
  clickEvent: NClickEvent.item,
  moduleType: string,
  moduleId: string
): Promise<{
  flag?: string
  text?: string
  img?: string
}> {
  const { modalStore, dynimicDataStore } = focusStore.stores

  const doClickEvent = (
    params: {
      vueRouterRedirect?: {
        routerInstance: any
        rootPath: string
        hashList: string[]
      }
      clickEvent?: NClickEvent.item
    } = {},
    cb: Function
  ) => {
    console.log('🚀 ~ clickEvent:', clickEvent)
    if (isDoing) {
      console.error('正在执行，你别点啦！')
      return false
    }
    isDoing = true
    setTimeout(() => {
      isDoing = false
      console.error(`已经过了${timeSpace},可以点击了`)
    }, timeSpace)
    console.log('🚀 ~ file: index.ts ~ line 21 ~ handleClickByClickEvent ~ params', params)
    const {
      path,
      query = {},
      jumpType,
      effect,
      popid,
      miniUsername,
      miniEnv,
      jumpAppPath,
      copyStr,
    } = (params && params.clickEvent) || clickEvent
    let logData: any = {
      path,
      method: jumpType,
      query,
      copyStr,
    }
    if (jumpType === 'mini') {
      logData = {
        ...logData,
        miniUsername,
        miniEnv,
      }
    }
    focusCore.clickLog(`${moduleType}_${moduleId}`, logData)

    switch (effect) {
      case 'popup':
        modalStore.setPopupViewId(popid)
        break
      case 'jump':
        if (jumpType === 'custom') {
          focusCore.emitRedirectToSpecialPath(path)
          return
        }

        jumpService.jump({
          path,
          method: jumpType,
          query,
          miniUsername,
          miniEnv,
          vueRouterRedirect: params && params.vueRouterRedirect,
          jumpAppPath,
        })
        break
      case 'calendarAlarm':
        console.log(clickEvent)
        const {
          caltitle = '',
          calnotes = '',
          calstartTime = '',
          calremindBefore = '',
          caltext = '',
          calimg = '',
        } = clickEvent

        window.hjCoreIab.setCalendarEvent(
          {
            title: caltitle,
            notes: calnotes,
            startTime: calstartTime,
            remindBefore: Number(calremindBefore),
          },
          (info: any) => {
            // this.$toast('设置成功')
            modalStore.toastShow('设置成功')
            const { fid } = new UrlParser().query
            let id = `${fid}-${moduleId}-${clickEvent.effect}`
            focusCore.setLocalData(id, {
              title: caltitle,
              notes: calnotes,
              startTime: calstartTime,
            })
            // localStorage.setItem(
            //   id,
            //   JSON.stringify({
            //     title: caltitle,
            //     notes: calnotes,
            //     startTime: calstartTime,
            //   })
            // )
            cb && cb({ flag: 'calendarAlarm_succ', text: caltext, img: calimg })
            console.log('set calendar event success %o', info)
          },
          (err: any) => {
            console.log('set calendar event fail %o', err)
            if (err.message === '日历权限禁止') {
              modalStore.toastShow('请先手动打开日历权限，再点此设置提醒')
            } else {
              modalStore.toastShow('请确认系统日历权限开启后再操作订阅')
            }
          }
        )
        break
      case 'copyText':
        console.log('copy!!!')
        console.log('🚀 ~ copyStr:', copyStr)
        const text = copyStr && dynimicDataStore.useData[copyStr]
        if (!text) {
          modalStore.toastShow(`复制失败`)
          return
        }
        console.log('🚀 ~ text:', text)
        const { toClipboard } = useClipboard()
        toClipboard(text)
          .then((res) => {
            modalStore.toastShow(`复制成功`)
          })
          .catch((err) => {
            console.log('🚀 ~ toClipboard ~ err:', err)
            modalStore.toastShow(`复制失败`)
          })

        break
    }
  }
  return new Promise((resolve) => {
    focusCore.emitBtnClick({
      clickEvent,
      moduleType,
      moduleId,
      next: (params: any) => {
        doClickEvent(params, (arg: any) => {
          resolve(arg)
        })
      },
    })
  })
}

export function useModuleDataResolve<TConfigData, TDynamicData = any>(
  moduleData: Ref<TModuleData> | TModuleData,
  dynamicKeys?: string[]
): {
  styleData: Ref<TModuleStylesResolved>
  configData: Ref<TConfigData>
  dynamicData: ComputedRef<TDynamicData>
  moduleId: Ref<number>
  moduleType: Ref<string>
} {
  const styleData: Ref<TModuleStylesResolved> = ref({})
  const configData: Ref<any> = ref({})
  const moduleId: Ref<number> = ref(0)
  const moduleType: Ref<string> = ref('')
  const dynamicData = computed<TDynamicData>(() => {
    const data: any = {}

    dynamicKeys &&
      dynamicKeys.forEach((key) => {
        // console.log('🚀 ~ dynamicKeys.forEach ~ key:', key)
        const apiKey = configData.value.apiKeyIndex
        const keyName = apiKey ? `${apiKey}_${key}` : configData.value[key] || ''
        // console.log('🚀 ~ dynamicKeys.forEach ~ keyName:', keyName)
        // console.log('🚀 ~ dynamicKeys.forEach ~ keyName:', keyName)
        const result = dynimicDataService.getDynimicDataByApiKey(keyName)
        // console.log('🚀 ~ dynamicKeys.forEach ~ result:', result)
        data[key] = result === undefined ? '' : result
      })

    return data
  })

  if (isRef(moduleData)) {
    watchEffect(() => {
      const changeData = moduleData.value.changeData || {}
      styleData.value = new StyleHandler(unref(moduleData)).allStyles
      configData.value = Object.assign({}, moduleData.value.data, changeData)
      moduleId.value = moduleData.value.moduleId
      moduleType.value = moduleData.value.moduleType
    })
  } else {
    styleData.value = new StyleHandler(moduleData).allStyles
    configData.value = moduleData.data
    moduleId.value = moduleData.moduleId
    moduleType.value = moduleData.moduleType
  }

  return {
    styleData,
    configData,
    dynamicData,
    moduleId,
    moduleType,
  }
}
