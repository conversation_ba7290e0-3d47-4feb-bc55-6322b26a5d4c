import { inject, Ref } from 'vue'

export class StyleHandler {
  styles: TModuleStyles
  moduleType: any
  moduleId: number
  constructor(moduleData: TModuleData) {
    const { styles, moduleType, moduleId } = moduleData
    this.styles = styles || {}
    this.moduleType = moduleType
    this.moduleId = moduleId
  }

  get allStyles() {
    return {
      boxSize: this.boxSize,
      bg: this.bg,
      margins: this.margins,
      fontStyles: this.fontStyles,
      fontSize: this.fontSize,
      position: this.position,
      borderRadius: this.borderRadius,
    }
  }

  get boxSize() {
    if (!this.styles.width && !this.styles.height) {
      return {}
    }
    let { width = 750, height = 420 } = this.styles

    width = width >= 750 ? 750 : width
    const result: any = {
      height: height === 'auto' || height == '0' ? 'auto' : height === '100%' ? '100%' : this.px2vw(height),
    }

    const isWidth0Text = this.moduleType === 'MText' && width == '0'
    if (width == '100%' || width == '0' || height == '100%' || height == '0') {
      result.flex = 'auto'
      if (isWidth0Text || this.moduleType === 'MBox') {
        delete result.flex
      }
    }
    const _width = width == '100%' || width == '0' ? (isWidth0Text ? 'auto' : '100%') : this.px2vw(width)

    result.width = _width
    if (this.moduleType === 'BtnClick' || this.moduleType === 'BtnAdding') {
      result.borderRadius = this.px2vw(Number(height) / 2)
    }

    return result
  }

  get borderRadius() {
    const { borderRadius } = this.styles
    if (borderRadius) {
      return { borderRadius: this.px2vw(borderRadius) }
    }

    return {}
  }

  get bg() {
    const { bg = '' } = this.styles
    if (!bg) {
      return null
    }
    if (bg && bg.indexOf('http') > -1) {
      return {
        backgroundImage: `url(${bg})`,
        backgroundColor: 'transparent',
      }
    }
    const colorArr = bg.split('-')
    if (colorArr.length > 1) {
      return {
        backgroundImage: `linear-gradient(to bottom,${colorArr[0]},${colorArr[1]}`,
      }
    }
    return { backgroundImage: 'none', backgroundColor: bg }
  }

  get margins() {
    const margin = this.styles.margin || '0,0,0,0'
    const marginMap = ['marginTop', 'marginRight', 'marginBottom', 'marginLeft']
    const marginObj: {
      [key: string]: any
    } = {}
    margin.split(',').forEach((mar: { toString: () => string }, index: number) => {
      if (mar.toString() != '0') {
        marginObj[marginMap[index]] = this.px2vw(Number(mar))
      }
    })
    return { ...marginObj }
  }

  get fontStyles(): {
    color: string
    textAlign: string
    fontWeight?: string
  } {
    let font = this.styles.font || ''
    const _font = font.split(',')
    const result: {
      color: string
      textAlign: string
      fontWeight?: string
    } = {
      color: _font[3],
      textAlign: _font[2],
    }
    if (_font[1] === '1') {
      result.fontWeight = 'bold'
    }

    return result
  }

  get fontSize() {
    const fonsizes = [48, 36, 32, 28, 24, 60, 22, 20]
    const font = this.styles.font || '3'
    const fontArr = font.split(',')
    const sizeIndex = Number(font[0]) - 1
    return {
      fontSize: this.px2vw(fonsizes[sizeIndex]),
    }
  }

  get position() {
    let data = this.styles.position || null
    if (!data) {
      return { position: 'none' }
    }
    let position = {}
    switch (data) {
      case 'fixed-t':
        position = {
          isPT: true,
        }
        break
      case 'fixed-b':
        position = {
          isPB: true,
        }

        break
      case 'none':
        break
    }
    if (data.indexOf('absolute') > -1) {
      const _data = data.split(',')
      position = {
        position: 'absolute',
        top: this.px2vw(_data[1]),
        left: this.px2vw(_data[2]),
        zIndex: `3${this.moduleId}`,
      }
    }

    return position
  }

  px2vw(px: number | string) {
    const viewPort = 750
    const vw = ((Number(px) / viewPort) * 100).toFixed(3)
    return `${vw}vw`
  }
}
