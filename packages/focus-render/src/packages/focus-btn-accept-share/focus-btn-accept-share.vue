<template>
  <div class="btn-accept-share" :style="{ ...curStyles }" @click="handleClick" :class="disabled ? 'disabled' : ''">
    <img :src="showImg" />
  </div>
</template>

<script setup lang="ts">
import familyDialog from './family-dialog.vue'
import { storeToRefs } from 'pinia'
import { focusStore } from '../../store/index'
import { UrlParser } from '../../utils/index'
import { jumpService, mgmService } from '../../service'
import { ref, toRefs, h, render, unref, computed } from 'vue'
import { focusCore } from '../focus-render'
import { useModuleDataResolve } from '../hooks'

console.log('🚀 ~ file: focus-btn-accept-share.vue ~ line 15 ~ focusCore', focusCore)

const { taskStore, modalStore } = focusStore.stores

const { taskList, taskListVer, expireTime } = storeToRefs(taskStore)
const props = defineProps<{
  moduleData: NFocusBtnAcceptShare.moduleData
}>()

const { configData, styleData } = useModuleDataResolve<NFocusBtnAcceptShare.configData>(props.moduleData)
const curStyles = computed(() => {
  const { boxSize, margins } = unref(styleData)
  return {
    ...boxSize,
    ...margins,
  }
})

const showImg = ref(configData.value.imgUrl1)
const localhostPared = new UrlParser(window.location.href)
const m1Sid = localhostPared.query.sid
const disabled = ref(true)
if (m1Sid) {
  modalStore.loadingStart('btn-accept_sid')
  mgmService.checkCurSidIsSameUrlSid(configData.value.mgmAid, m1Sid).then((curSidIsM1) => {
    modalStore.loadingEnd('btn-accept_sid')

    disabled.value = curSidIsM1
  })
} else {
  console.log('没有sid，不给点！')

  disabled.value = true
}

const handleClick = function () {
  if (disabled.value) {
    return false
  }
  if (configData.value.functionType === 'my_family') {
    acceptFamilyShare()
  } else {
    acceptShare()
  }
}

const acceptFamilyShare = function () {
  modalStore.loadingStart('acceptFamilyShare')

  mgmService
    .acceptFamilyShare({
      m1Sid: m1Sid,
      mgmAid: configData.value.mgmAid,
    })
    .then((res) => {
      if (!focusCore.hasAccount) {
        return jumpService.commonUse.appHome()
      }
      const el = document.createElement('div')
      el.setAttribute('id', 'family')
      document.body.appendChild(el)

      const familyDialogvNode = h(familyDialog, {
        familyDialogData: {
          isSuccess: true,
          msg: '已接受组建家庭邀请，请等待对方确认',
          handleCancel: function () {
            modalStore.setPopupViewId(-1)
            document.body.removeChild(el)
          },
        },
      })
      render(familyDialogvNode, el)
    })
    .catch((errMsg) => {
      const el = document.createElement('div')
      el.setAttribute('id', 'family')
      document.body.appendChild(el)

      const familyDialogvNode = h(familyDialog, {
        familyDialogData: {
          isSuccess: false,
          msg: errMsg,
          handleCancel: function () {
            modalStore.setPopupViewId(-1)
            document.body.removeChild(el)
          },
        },
      })
      render(familyDialogvNode, el)
    })
    .finally(() => {
      modalStore.loadingEnd('acceptFamilyShare')
    })
}

const acceptShare = function () {
  mgmService.M2AcceptMGM({ m1Sid, mgmAid: configData.value.mgmAid }).catch((err) => {
    modalStore.confirmContrl({
      show: true,
      contents: ['接受邀请关系失败，请重试'],
      hideConfirm: true,
      btnCancelText: '知道了',
    })
  })
}

const isM1Confirm = function () {
  modalStore.confirmContrl({
    show: true,
    contents: ['您无法接受自己的邀请哦！'],
    hideConfirm: true,
    btnCancelText: '知道了',
  })
}
</script>

<style lang="scss" scoped>
.btn-accept-share {
  font-size: 30px;
  &.disabled {
    opacity: 0.5;
  }
}
.dialog {
  width: 630px;
  position: relative;
  background: linear-gradient(140deg, #fffdf6, #c2f0fd);
  height: 320px;
  border-radius: 10px;
  font-size: 28px;
  color: #5d3013;
  text-align: center;
  height: 720px;
  background: url($cdnPrefix + '/img/family/M2_dialog_accept.png') no-repeat;
  background-size: 100%;
  .contain {
    margin-top: 60px;
    font-weight: bold;
  }
  .btns {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    position: absolute;
    left: 0;
    bottom: 60px;
    .btn-cancel {
      width: 250px;
      height: 80px;
      border-radius: 40px;
      line-height: 76px;
      font-size: 32px;
      border: 5px solid #ffc6ae;
      text-align: center;
      color: #ffc6ae;
      font-weight: bold;
      &:active {
        background: #ffc6ae;
        color: #fff;
      }
    }
    .btn-confirm {
      flex: none;
      width: 250px;
      height: 80px;
      border-radius: 40px;
      line-height: 80px;
      font-size: 32px;
      font-weight: bold;
      text-align: center;
      color: #fff;
      background: linear-gradient(to bottom, #ffc773, #f58a5a);
      &:active {
        background: #f58a5a;
        color: #fff;
      }
    }
  }
}
</style>
