<template>
  <Mask class="family-dialog">
    <div class="contain">
      <p class="title">{{ msg }}</p>
      <img :src="imgUrl" alt="" />
      <div class="btn-cancel" @click="clickBtn">知道了</div>
    </div>
  </Mask>
</template>

<script setup lang="ts">
import Mask from '../../components/Mask.vue'
import { computed, toRefs } from 'vue'
const cdnPrefix = CDN_PREFIX_FOCUS_RENDER

const props = defineProps({
  familyDialogData: Object,
})
const isSuccess = computed(() => {
  return props.familyDialogData?.isSuccess
})
const msg = computed(() => {
  return props.familyDialogData?.msg
})
const imgUrl = computed(() => {
  if (props.familyDialogData?.isSuccess) {
    return `${cdnPrefix}/img/family/accept-succ.png`
  }
  return `${cdnPrefix}/img/family/accept-fail.png`
})
const clickBtn = function () {
  props.familyDialogData?.handleCancel()
}
</script>

<style lang="scss" scoped>
.family-dialog {
  background: rgba($color: #000000, $alpha: 0.8);
  z-index: 99999;
  .contain {
    width: 630px;
    position: relative;
    background: linear-gradient(to bottom, #f4f5f4, #d5f9ff);
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    box-sizing: border-box;
    padding: 60px;
    padding-bottom: 60px;
    border-radius: 10px;

    .title {
      margin-bottom: 40px;
      color: #642d09;
      font-weight: bold;
      font-size: 32px;
      text-align: left;
    }
    img {
      width: 390px;
      height: 288px;
    }
    .btn-cancel {
      width: 490px;
      height: 80px;
      border-radius: 40px;
      line-height: 76px;
      font-size: 32px;
      border: 2px solid #f58a5a;
      text-align: center;
      color: #f58a5a;
      margin-top: 60px;
      &:active {
        background: #f58a5a;
        color: #fff;
      }
    }
  }
}
</style>
