import { defineStore } from 'pinia'
import { userService } from '../service'
// useStore could be anything like useUser, useCart
// the first argument is a unique id of the store across your application
import { focusStore } from '../packages/focus-render/index'
import { EnumConditionMap } from './conditionStore'

interface State {
  isFetching: Array<string>
  isSubscribe: boolean
  hasAccount: boolean
  isLogined: boolean
  isSurportWebp: boolean
  wechatOpenId: string
}

export const userStore = defineStore('user', {
  // other options...
  state: (): State => {
    return {
      isFetching: [],
      isSubscribe: false,
      hasAccount: false,
      isLogined: false,
      isSurportWebp: false,
      wechatOpenId: '',
    }
  },
  actions: {
    setAccountStatus(status: boolean) {
      const { conditionStore } = focusStore.stores

      this.hasAccount = status
      console.log('🚀 ~userStore:', this)
      conditionStore.setCondition(EnumConditionMap.hasAccount, status ? 2 : 1)
    },

    /**
     * 用来管理公共的接口状态，可以之请求一次就让其他组件能使用
     * 使用时候需要通过store来获取对应数据
     */
    signUserData(key: string, forceUpdate = false) {
      const keyList = ['subscribe', 'sid']
      if (this.isFetching.indexOf(key) > -1 && !forceUpdate) {
        console.log('已经在请求拉，别着急！')
        return false
      }
      const canDoFetch = keyList.indexOf(key) > -1
      if (!canDoFetch) {
        console.log(`传入的key: ${key}不在管理范围内`)
        return false
      }
      this.isFetching.push(key)
      switch (key) {
        case 'subscribe':
          // 获取公众号关注状态
          userService.checkSubscribeStatus().then((isSubscribe: boolean) => {
            console.log('🚀 ~ 关注结果：', isSubscribe)

            this.isSubscribe = isSubscribe
            // this.isFetching.splice(this.isFetching.findIndex(key), 1)
          })
          break
        case 'wxinfo':
        case 'sid':
      }
    },
  },
})

export default userStore
