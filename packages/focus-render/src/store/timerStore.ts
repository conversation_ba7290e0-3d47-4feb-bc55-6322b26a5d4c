import focusCore from '@/service/focus-core'
import { defineStore } from 'pinia'
import { dayjs } from '../utils/index'
// useStore could be anything like useUser, useCart
// the first argument is a unique id of the store across your application

// 公共的timer，全局只有一个定时器
let timerCtrl: any = 0

// 是一个对象，key是moduleId，value是Timer实例
const timerSettings = {}

let isTimeFetched = false

export const timerStore = defineStore('timer', {
  state: (): {
    timerText: any
  } => ({
    timerText: {}, // 是一个对象，key是moduleId，value是倒计时时间文本展示
  }),
  actions: {
    async initTimer(moduleId: number, datetime: string, uiType: '1' | '2' | '3', afterCountEnd?: Function) {
      // 注册倒计时
      const newTimer = new Timer(dayjs(datetime).valueOf(), uiType)
      await newTimer.setNowTime()
      newTimer.init()
      console.log('🐬 ~ file: timerStore.ts:26 ~ initTimer ~ newTimer:', newTimer)
      this.timerText[`${moduleId}`] = newTimer.timeText
      // 如果一进入页面就结束，就不需要注册timerSettings和倒计时功能了
      if (!newTimer.isEnd) {
        timerSettings[`${moduleId}`] = newTimer
        this.timeCounter(afterCountEnd)
      }
    },
    // 实现倒计时功能
    timeCounter(afterCountEnd?: Function) {
      let countfrequency = 0 // 计数器
      let syncStartTime = performance.now()
      const counterFunction = () => {
        Object.keys(timerSettings).forEach((key) => {
          const timer = timerSettings[key]
          timer.count()
          this.timerText[key] = timer.timeText
          console.log('🐬 ~ file: timerStore.ts:55 ~ Object.keys ~ timer.timeText:', timer.timeText)
          if (timer.isEnd) {
            try {
              afterCountEnd && afterCountEnd()
            } catch (err) {
              console.log('🚀 ~ keyList.forEach ~ err:', err)
            }
            delete timerSettings[key]
          }
        })
      }
      // counter函数会清除定时器，所以每个倒计时组件在初始状态时执行了它的timeCounter函数并执行到counter函数后，在下面设置完定时器后，就会被下个倒计时组件执行counter函数所清除，所以这样就只会最后一个倒计时组件的setTimeOut生效，此后递归的是最后一个倒计时组件的counter函数
      const counter = async () => {
        // 清除定时器
        clearTimeout(timerCtrl)

        let timeFlag = true
        if (timeFlag && countfrequency >= 300) {
          timeFlag = false
          countfrequency = 0
          // 这里一定不要用forEach，因为forEach会忽略await
          for (const key of Object.keys(timerSettings)) {
            if (!isTimeFetched) {
              isTimeFetched = true
              const now = await timerSettings[key].setNowTime()
              syncStartTime = performance.now()
              for (const key of Object.keys(timerSettings)) {
                timerSettings[key].now = now
              }
            }
            timerSettings[key].init()
            this.timerText[key] = timerSettings[key].timeText
          }
          isTimeFetched = false
        }
        // 处理偏移时间
        const syncEndTime = performance.now()
        let interval = 1000
        const offset = syncEndTime - (syncStartTime + interval * countfrequency)
        const nextTime = interval - offset

        countfrequency++

        timerCtrl = setTimeout(() => {
          counterFunction()
          counter()
        }, nextTime)
      }
      counter()
    },
    async visibleTimer(moduleId: number, afterCountEnd?: Function) {
      if (timerSettings[moduleId]) {
        await timerSettings[moduleId].setNowTime()
        timerSettings[moduleId].init()
        this.timerText[moduleId] = timerSettings[moduleId].timeText
        if (!timerSettings[moduleId].isEnd) {
          this.timeCounter(afterCountEnd)
        }
      }
    },
    clearTimer() {
      clearTimeout(timerCtrl)
    },
  },
})

class Timer {
  constructor(datetime: number, uiType: string) {
    this.datetime = datetime
    this.uiType = uiType
  }
  datetime: number = 0
  uiType: string = ''
  _days: number = 0
  _hours: number = 0
  _minutes: number = 0
  _seconds: number = 0
  now: number = 0
  isEnd: boolean = false
  async init() {
    const { days, hours, minutes, seconds } = this.getRemainingTime(this.datetime)
    console.log('🚀 ~ Timer ~ init ~ days, hours, minutes, seconds:', days, hours, minutes, seconds)
    this._days = days
    this._hours = hours
    this._minutes = minutes
    this._seconds = seconds
  }

  async getSystemTime() {
    try {
      const startTime = performance.now()
      const response = await focusCore.getNowTime()
      const endTime = performance.now()
      // 请求时间差
      const requestTime = endTime - startTime
      const now = response.valueOf() + Math.floor(requestTime / 2)
      return Promise.resolve(now)
    } catch (err) {
      console.log('🐬 ~ file: timerStore.ts:147 ~ Timer ~ getSystemTime ~ err:', err)
      return Promise.resolve(dayjs().valueOf())
    }
  }

  async setNowTime() {
    this.now = await this.getSystemTime()
    return Promise.resolve(this.now)
  }

  get timeText() {
    // 他们的值不可能为负数
    const days = this._days.toString()
    const hours = this._hours.toString().padStart(2, '0')
    const minutes = this._minutes.toString().padStart(2, '0')
    const seconds = this._seconds.toString().padStart(2, '0')
    switch (this.uiType) {
      case '1': {
        const showHours = (this._hours + this._days * 24).toString().padStart(2, '0')
        return `${showHours}:${minutes}:${seconds}`
      }
      case '2': {
        return `${days}天${hours}:${minutes}`
      }
      case '3': {
        return `${days}天${hours}:${minutes}:${seconds}`
      }
    }
  }

  count() {
    // 每一秒都更新
    if (this._seconds <= 0 && this._minutes <= 0 && this._hours <= 0 && this._days <= 0) {
      this.isEnd = true
      return
    }
    this._seconds--
    if (this._seconds < 0) {
      this._seconds = 59
      this._minutes--
      if (this._minutes < 0) {
        this._minutes = 59
        this._hours--
        if (this._hours < 0) {
          this._hours = 23
          this._days--
        }
      }
    }
  }

  getRemainingTime(targetDate: number): {
    days: number
    hours: number
    minutes: number
    seconds: number
    isEnd?: boolean
  } {
    const remainingTime = targetDate - this.now // 计算剩余时间（毫秒）
    console.log('🐬 ~ file: timerStore.ts:192 ~ Timer ~ getRemainingTime ~ remainingTime:', remainingTime)
    if (remainingTime <= 0) {
      this.isEnd = true
      return {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
      }
    }
    const seconds = Math.round((remainingTime / 1000) % 60)
    const minutes = Math.floor((remainingTime / 1000 / 60) % 60)
    const hours = Math.floor((remainingTime / (1000 * 60 * 60)) % 24)
    const days = Math.floor(remainingTime / (1000 * 60 * 60 * 24))
    return {
      days,
      hours,
      minutes,
      seconds,
    }
  }
}

export default timerStore
