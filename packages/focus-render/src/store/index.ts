import taskStore from './taskStore'
import userStore from './userStore'
import modalStore from './modalStore'
import conditionStore from './conditionStore'
import dynimicDataStore from './dynimicDataStore'
import activityStore from './activityStore'
import timerStore from './timerStore'
import fundCouponStore from './fundCouponStore'
export interface TStoreContrl {
  getStores: (keys: Array<string>) => any
}
class StoreContrl {
  $pinia: any
  setPinia($pinia: any) {
    // 只能拥有一个实例，无法变更
    if (!this.$pinia) {
      this.$pinia = $pinia
    }
  }

  get stores(): {
    taskStore: ReturnType<typeof taskStore>
    userStore: ReturnType<typeof userStore>
    modalStore: ReturnType<typeof modalStore>
    conditionStore: ReturnType<typeof conditionStore>
    dynimicDataStore: ReturnType<typeof dynimicDataStore>
    activityStore: ReturnType<typeof activityStore>
    timerStore: ReturnType<typeof timerStore>
    fundCouponStore: ReturnType<typeof fundCouponStore>
  } {
    return {
      taskStore: taskStore(this.$pinia),
      userStore: userStore(this.$pinia),
      modalStore: modalStore(this.$pinia),
      conditionStore: conditionStore(this.$pinia),
      dynimicDataStore: dynimicDataStore(this.$pinia),
      activityStore: activityStore(this.$pinia),
      timerStore: timerStore(this.$pinia),
      fundCouponStore: fundCouponStore(this.$pinia),
    }
  }
}
export const focusStore = new StoreContrl()
export default StoreContrl
