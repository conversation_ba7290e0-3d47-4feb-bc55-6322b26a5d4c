import { defineStore } from 'pinia'
import type { TCouponData } from '../service/couponService'
export const fundCouponStore = defineStore('fundCoupon', {
  // other options...
  state: (): {
    allCouponsData: {
      [k: string]: TCouponData
    }
    dataIsReady: boolean
  } => ({
    allCouponsData: {},
    dataIsReady: false,
  }),
  getters: {
    getCouponsDataList: (store): TCouponData[] => {
      console.log('🚀 ~ store:', store)
      console.log('🚀 ~ Object.values(store.allCouponsData):', Object.values(store.allCouponsData))
      return Object.values(store.allCouponsData)
    },
  },
  actions: {
    updatedCouponsData(data: TCouponData[]) {
      console.log('🚀 ~ updatedCouponsData ~ data:', data)

      data.forEach((_d) => {
        if (_d.coupon_config_id) {
          this.allCouponsData[_d.coupon_config_id] = _d
        }
      })
      if (!this.dataIsReady) {
        this.dataIsReady = true
      }
    },
  },
})

export default fundCouponStore
