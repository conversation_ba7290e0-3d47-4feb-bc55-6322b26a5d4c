import { defineStore } from 'pinia'
import type { TActivityStatus } from '../service/activityService'

export const activityStore = defineStore('activity', {
  // other options...
  state: (): {
    addingAids: number[]
    aidCheckStatus: {
      [k: string]: TActivityStatus
    }
  } => ({
    addingAids: [],
    aidCheckStatus: {},
  }),
  getters: {},
  actions: {
    updatedAids(aid: number) {
      this.addingAids.push(aid)
    },
    updateAidsStatus(data: TActivityStatus) {
      this.aidCheckStatus[data.activityId] = data
    },
  },
})

export default activityStore
