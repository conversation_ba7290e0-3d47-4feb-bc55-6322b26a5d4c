import { defineStore } from 'pinia'
import waLog from '../service/focus-core/waLog'
// useStore could be anything like useUser, useCart
// the first argument is a unique id of the store across your application
export interface TConfirmDialog {
  show: Boolean
  contents: Array<string>
  btnConfirmText?: string
  btnCancelText?: string
  btnConfirmJumpConfig?: {
    path: string
    method?: string
    query?: any
  }
  title?: string
  contentAlign?: string
  hideConfirm?: Boolean
  hideCancel?: Boolean
  alwaysShow?: Boolean
  confirmCb?: Function
}
enum EnumErrorType {
  NOT_IN_PAGE_GRAY = 'not_in_page_gray',
  SAFE_LOCK = 'safe_lock',
}

const errorCodeMsg = {
  '20530069': ['此次活动已结束~'],
}

export const modalStore = defineStore('msg', {
  // other options...
  state: (): {
    confirmDialog: TConfirmDialog
    errorMaskMsgs: Array<string>
    errorMaskShowBtn: Boolean
    errorMaskFillDark: Boolean
    showShareMesk: Boolean
    loadingContrlList: Array<string>
    loadingKeyTimes: {
      firstStartTime: number
      loadingEndTime: number
      fist5SecKeys: any[]
    }
    popupViewId: number
    toastMsg: string
  } => ({
    confirmDialog: {
      show: false,
      title: '',
      contentAlign: '',
      contents: [],
      btnConfirmText: '',
      btnCancelText: '取消',
      hideConfirm: false,
      hideCancel: false,
      alwaysShow: false,
    },
    errorMaskMsgs: [],
    errorMaskShowBtn: false,
    errorMaskFillDark: false,
    showShareMesk: false,
    loadingContrlList: [],
    popupViewId: 0,
    toastMsg: '',
    loadingKeyTimes: {
      firstStartTime: 0,
      loadingEndTime: 0,
      fist5SecKeys: [],
    },
  }),
  getters: {
    showLoading(): Boolean {
      const show = !!this.loadingContrlList.length
      return show
    },
  },
  actions: {
    confirmContrl(confirmData: TConfirmDialog) {
      console.log('🚀 ~ file: modalStore.ts ~ line 26 ~ confirmContrl ~ confirmData', confirmData)
      const status = confirmData.show ? 'show' : 'hide'
      const title = this.confirmDialog.title || confirmData.title || ''
      let contents = this.confirmDialog.contents || []
      if (!contents.length) {
        contents = confirmData.contents
      }
      waLog.eventLog(`confirm-modal`, `${status}`, {
        title,
        contents: contents,
        btnConfirmText: this.confirmDialog.btnConfirmText || confirmData.btnConfirmText || '',
      })

      this.confirmDialog = confirmData
    },
    errorMaskContrl(
      contrlKey: string,
      msgs?: string | Array<string>,
      logDefinedValue?: string,
      errorMaskFillDark?: boolean
    ) {
      console.error('触发错误浮层的是', contrlKey)
      console.log('🚀 ~ msgs:', msgs)
      if (contrlKey === EnumErrorType.SAFE_LOCK) {
        this.errorMaskShowBtn = true
      } else {
        this.errorMaskShowBtn = false
      }
      this.errorMaskFillDark = !!errorMaskFillDark
      let _msgs = msgs
      if (!_msgs || !_msgs.length) {
        _msgs = ['网络开了会小差，', '再试一次哦~']
      }

      if (typeof _msgs === 'string') {
        this.errorMaskMsgs = errorCodeMsg[_msgs] || ['网络开了会小差，', '再试一次哦~']
      } else {
        this.errorMaskMsgs = _msgs
      }
      const definedValue = logDefinedValue ? `${contrlKey}_${logDefinedValue}` : contrlKey
      waLog.eventLog(`error.errorMask`, definedValue)
    },
    shareMaskContrl(status: Boolean) {
      this.showShareMesk = status
    },
    loadingStartNoMask(key: string) {},
    loadingStart(key: string) {
      console.log('🚀 ~ file: modalStore.ts ~ line 66 ~ loadingStart ~ key', key)
      if (!key) {
        console.log('调用loading必须传key！！！')
        return
      }
      const now = Date.now()
      this.loadingContrlList.push(key)
      this.loadingKeyTimes.fist5SecKeys.push(key)
      waLog.eventLog(`loading`, `show_${key}`)
      if (this.loadingKeyTimes.firstStartTime === 0) {
        // 如果firstStartTime 为0，说明还没任何组件调用过loading
        this.loadingKeyTimes.firstStartTime = now
        // 5秒后将当前的loading状态上报上去
        setTimeout(() => {
          waLog.eventLog(
            `first_5s_loading`,
            this.loadingKeyTimes.loadingEndTime.toString(),
            this.loadingContrlList.join('__')
          )
        }, 5000)
      }
    },
    loadingEnd(key: string, forceEnd?: boolean) {
      if (!key) {
        return false
      }

      if (forceEnd) {
        this.loadingContrlList = []
        return false
      }
      const now = Date.now()
      this.loadingKeyTimes.loadingEndTime = now - this.loadingKeyTimes.firstStartTime
      const targetIndex = this.loadingContrlList.findIndex((i) => i === key)
      this.loadingContrlList.splice(targetIndex, 1)
      waLog.eventLog(`loading`, `hide_${key}_${this.loadingContrlList.length}`)
      console.log('loading_end...', this.loadingContrlList)
    },
    setPopupViewId(index: number) {
      this.popupViewId = index
    },
    toastShow(msg: string) {
      this.toastMsg = msg
      setTimeout(() => {
        this.toastMsg = ''
      }, 3000)
    },
  },
})

export default modalStore
