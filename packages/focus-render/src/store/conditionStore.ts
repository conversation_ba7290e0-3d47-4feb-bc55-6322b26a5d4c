import { defineStore } from 'pinia'
import waLog from '../service/focus-core/waLog'
// useStore could be anything like useUser, useCart
// the first argument is a unique id of the store across your application
export interface TConditions {
  dc01: 1 | 2
  dc02: 1 | 2
  dc06: 1 | 2
  dc07: 1 | 2
}
export enum EnumConditionMap {
  isSbuscribe = 'dc01',
  hasAccount = 'dc02',
  hadInGrayId = 'dc03',
  dynamicDataCondition = 'dc05',
  isiOS = 'dc06',
  isInApp = 'dc07',
}

export const conditionStore = defineStore('condition', {
  // other options...
  state: (): {
    conditions: TConditions
  } => ({
    conditions: {
      dc01: 1,
      dc02: 1,
      dc06: 1,
      dc07: 1,
    },
  }),
  getters: {},
  actions: {
    setCondition(key: EnumConditionMap | string, value: any) {
      const targetKey = key
      console.log('🚀 ~ 更新渲染条件', targetKey, value)
      if (!targetKey) {
        return false
      }
      this.conditions[targetKey] = value
      console.log('条件更新完成，目前', this.conditions)
    },
  },
})

export default conditionStore
