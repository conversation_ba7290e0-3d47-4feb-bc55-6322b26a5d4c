import { defineStore } from 'pinia'
import waLog from '../service/focus-core/waLog'
// useStore could be anything like useUser, useCart
// the first argument is a unique id of the store across your application

export enum EnumApiKey {
  M1Info = '_api_01',
  UserInfo = '_api_08',
}

export const dynimicDataStore = defineStore('dynimicData', {
  // other options...
  state: (): any => ({
    useData: {},
    numberData: {},
  }),
  getters: {},
  actions: {
    setDynimicDataByApiKey(apiKey: EnumApiKey, dataIndex: number, values: any = {}) {
      console.log('🚀 ~ 更新动态数据！', values)
      console.log('🚀 ~ 更新动态数据！ ~ dataIndex:', dataIndex)
      console.log('🚀 ~ 更新动态数据！ ~ apiKey:', apiKey)
      const str = `${apiKey}_${dataIndex}`
      Object.keys(values).forEach((keyName) => {
        this.useData[`${str}_${keyName}`] = typeof values[keyName] !== 'undefined' ? values[keyName] : '--'
      })
      console.log(this.useData)
    },
    setDynimicDataByCustomKey(customKey: string, value: any) {
      if (!/^_custom_/.test(customKey)) {
        console.error('customKey 必须是 _custom_ 开头')
        return
      }
      this.useData[customKey] = value
    },
    setNumberDynimicData(apiKey: EnumApiKey, dataIndex: number, values: any = {}) {
      const str = `${apiKey}_${dataIndex}`
      Object.keys(values).forEach((keyName) => {
        const data = Number(values[keyName])
        this.numberData[`${str}_${keyName}`] = isNaN(data) ? 0 : data
      })
    },
  },
})

export default dynimicDataStore
