import { defineStore } from 'pinia'

// useStore could be anything like useUser, useCart
// the first argument is a unique id of the store across your application
export const taskStore = defineStore('task', {
  // other options...
  state: (): {
    taskList: any[]
    expireTime: string
    taskListVer: number
  } => ({
    taskList: [],
    expireTime: '',
    taskListVer: 0,
  }),
  actions: {
    setTaskIsDone(index: number) {
      this.taskList[index] = {
        ...this.taskList[index],
        isDone: true,
        btnText: '已结束',
      }
    },
  },
})

export default taskStore
