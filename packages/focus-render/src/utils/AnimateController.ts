export default class AnimateController {
  clear: (id: number) => void
  animateId: number = 0
  animateDoneCbList: Function[] = []
  onceDoneCbList: Function[] = []
  constructor() {
    this.clear = !window.cancelAnimationFrame
      ? function (id: number) {
          clearTimeout(id)
        }
      : window.cancelAnimationFrame
  }

  once(callback: FrameRequestCallback, noEmitOnceDone?: boolean): number {
    let createFn = window.requestAnimationFrame

    if (!createFn) {
      let lastTime = 0
      const vendors = ['ms', 'moz', 'webkit', 'o']
      for (let x = 0; x < vendors.length && !createFn; ++x) {
        createFn = window[vendors[x] + 'RequestAnimationFrame']
        createFn = window[vendors[x] + 'CancelAnimationFrame'] || window[vendors[x] + 'CancelRequestAnimationFrame']
      }
      if (!createFn) {
        createFn = function (callback: FrameRequestCallback): number {
          const currTime = new Date().getTime()
          const timeToCall = Math.max(0, 16 - (currTime - lastTime))
          const id = window.setTimeout(function () {
            callback(currTime + timeToCall)
          }, timeToCall)
          lastTime = currTime + timeToCall
          return id
        }
      }
    }

    return createFn((time: number) => {
      callback(time)
      if (noEmitOnceDone) {
        return
      }
      this.emitOnceDoneList()
    })
  }

  documentScrollYTo(now: number, target: number, animateTime: number = 500): void {
    const diss = target - now > 0 ? target - now : now - target
    // console.log('🚀 ~ 绝对距离', diss)
    const stepTimes = animateTime !== 0 ? 60 / (1000 / animateTime) + 1 : 1 // 次数
    // console.log('🚀 ~ 预估次数', stepTimes)
    const stepDiss = diss / stepTimes // 每次多少
    // console.log('每次距离', stepDiss)

    this.multi((times: number) => {
      // console.log('执行到第几次了', times)
      let direction = 0
      if (target - now > 0) {
        direction = now + (times + 1) * stepDiss
        direction = direction > target ? target : direction
      } else {
        direction = now - (times + 1) * stepDiss
        direction = direction < target ? target : direction
      }
      // console.log('🚀 ~ 本次运动到了', direction)

      if (document.documentElement.scrollTop) {
        document.documentElement.scrollTop = direction
      } else if (document.body.scrollTop) {
        document.body.scrollTop = direction
      } else {
        //这个else指以上两种值均为0的状态，有一者是恒为0的，另一者可能因为回到顶部等操作被置为0，便会出现这种状况
        document.documentElement.scrollTop = direction
        document.body.scrollTop = direction
      }
    }, stepTimes)
  }

  multi(stepFn: Function, animateTimes: number = 0): void {
    this.stop()
    if (!animateTimes) {
      this.once(() => {
        stepFn()
      })
      return
    }
    let done = false
    let i = 0 // 执行次数
    const step = (timestamp: number) => {
      if (i >= animateTimes) {
        done = true
        this.emitAnimateDoneList()
      }
      if (!done) {
        i++
        stepFn(i)
        this.animateId = this.once((lastTime) => {
          step(lastTime)
        }, false)
      }
    }
    this.animateId = this.once((lastTime) => {
      step(lastTime)
    }, false)
  }
  stop(): void {
    if (this.animateId) {
      this.clear(this.animateId)
    }
  }
  onAnimateDone(cb: Function): void {
    if (typeof cb === 'function') {
      this.animateDoneCbList.push(cb)
    }
  }
  onOnceDone(cb: Function): void {
    if (typeof cb === 'function') {
      this.onceDoneCbList.push(cb)
    }
  }
  emitOnceDoneList(): void {
    if (this.onceDoneCbList.length) {
      this.onceDoneCbList.forEach((i) => {
        i && i()
      })
    }
  }
  emitAnimateDoneList(): void {
    if (this.animateDoneCbList.length) {
      this.animateDoneCbList.forEach((i) => {
        i && i()
      })
    }
  }
}
