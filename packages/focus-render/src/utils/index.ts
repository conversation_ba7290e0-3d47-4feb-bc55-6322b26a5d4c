import { Url<PERSON>arser } from './url'
import dayjs from './time-utils'
import { formatAmount } from './formate-money'
import AnimateController from './AnimateController'
export { utf16toEntities, entitiestoUtf16 } from './formate-name'
import { Base64 } from 'js-base64'
export function base64encode(s: string): string {
  return Base64.encode(s)
}
// export function base64encode(str: string): string {
//   const base64EncodeChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
//   let out, i, c1, c2, c3
//   const len = str.length

//   i = 0
//   out = ''
//   while (i < len) {
//     c1 = str.charCodeAt(i++) & 0xff
//     if (i === len) {
//       out += base64EncodeChars.charAt(c1 >> 2)
//       out += base64EncodeChars.charAt((c1 & 0x3) << 4)
//       out += '=='
//       break
//     }
//     c2 = str.charCodeAt(i++)
//     if (i === len) {
//       out += base64EncodeChars.charAt(c1 >> 2)
//       out += base64EncodeChars.charAt(((c1 & 0x3) << 4) | ((c2 & 0xf0) >> 4))
//       out += base64EncodeChars.charAt((c2 & 0xf) << 2)
//       out += '='
//       break
//     }
//     c3 = str.charCodeAt(i++)
//     out += base64EncodeChars.charAt(c1 >> 2)
//     out += base64EncodeChars.charAt(((c1 & 0x3) << 4) | ((c2 & 0xf0) >> 4))
//     out += base64EncodeChars.charAt(((c2 & 0xf) << 2) | ((c3 & 0xc0) >> 6))
//     out += base64EncodeChars.charAt(c3 & 0x3f)
//   }
//   return out
// }

/**
 * 函数节流
 */
export function throttle(fn: Function, delay: number = 200): Function {
  let lastTime: number = 0
  let timer: any = null
  return function (..._args: any[]) {
    const args: any = _args
    // 记录当前函数触发的时间
    const nowTime = Date.now()
    if (lastTime && nowTime - lastTime < delay) {
      clearTimeout(timer)
      timer = setTimeout(() => {
        // 记录上一次函数触发的时间
        lastTime = nowTime
        // 修正this指向问题
        fn(...args)
      }, delay)
    } else {
      lastTime = nowTime
      fn(...args)
    }
  }
}

export { dayjs, UrlParser, formatAmount, AnimateController }
