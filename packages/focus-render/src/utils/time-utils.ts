import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import isToday from 'dayjs/plugin/isToday'
import isYesterday from 'dayjs/plugin/isYesterday'

// 加载插件
dayjs.extend(isYesterday)
dayjs.extend(isBetween)
dayjs.extend(isToday)
function nowIsBoforeTarget(timeStr: string): boolean {
  return dayjs().isBefore(dayjs(Number(timeStr)))
}
export { nowIsBoforeTarget }
export default dayjs
