export function formatAmount(money = '--', withFix2?: boolean): string {
  let tempMoney: string | number = money.toString()
  if (!tempMoney.length) {
    return '--'
  }
  if (tempMoney === '--') return '--'
  let symbol = ''
  if (tempMoney.indexOf('-') > -1) {
    symbol = '-'
    tempMoney = Math.abs(Number(tempMoney))
  }
  if (withFix2) {
    if (tempMoney.toString().indexOf('.') > -1) {
      tempMoney = parseFloat(tempMoney.toString()).toFixed(2)
    } else {
      const numStr = ((Number(tempMoney) * 1000) / 1000).toString()
      tempMoney = parseFloat(parseInt(numStr).toString()).toFixed(2)
    }
  }
  const moneyArr = tempMoney.toString().split('.')
  let moneyLeft = moneyArr[0].toString()
  let result = moneyArr[1] ? '.' + moneyArr[1] : ''
  while (moneyLeft.length > 3) {
    result = ',' + moneyLeft.slice(-3) + result
    moneyLeft = moneyLeft.slice(0, -3)
  }
  return symbol + moneyLeft + result
}

// /**
//  * 展示2位小数的数字
//  * 该方法仅限展示，别用来做计算！！
//  * @param money
//  * @param withFix2
//  * @param fixOption  默认 floor: 直接去尾; round: 四舍五入
//  * @returns
//  */

// export function formatAmount(money = '--', withFix2?: boolean, fixOption?: 'floor' | 'round'): string {
//   let tempMoney: string | number = money.toString()

//   if (tempMoney === '--') return '--'
//   let symbol = ''
//   if (tempMoney.indexOf('-') > -1) {
//     symbol = '-'
//     tempMoney = Math.abs(Number(tempMoney))
//   }
//   if (withFix2) {
//     const _fixOption = fixOption || 'floor'
//     const tempStr = tempMoney.toString()
//     if (tempStr.indexOf('.') > -1) {
//       const bigNum = parseInt((Number(tempStr) * 1000).toString()).toString()
//       const leftNum = bigNum.substring(0, bigNum.length - 3)
//       const rightNum = bigNum.substring(bigNum.length - 3, bigNum.length - 1)
//       const rightLastNum = bigNum.substring(bigNum.length - 1)

//       // 默认去尾巴
//       let rightNumResult = Number(rightNum)
//       // 四舍五入
//       if (_fixOption === 'round') {
//         // tempMoney = parseFloat((bigNum / 100).toString())
//         rightNumResult = Number(rightLastNum) >= 5 ? Number(rightNum) + 1 : Number(rightNum)
//       }
//       tempMoney = `${leftNum}.${rightNumResult}`
//       // const numStr = (parseInt((Number(tempStr) * 1000).toString()) / 1000).toString()
//     } else {
//       tempMoney = tempStr + '.00'
//     }
//   }
//   const moneyArr = tempMoney.toString().split('.')
//   let moneyLeft = moneyArr[0].toString()
//   let result = moneyArr[1] ? '.' + moneyArr[1] : ''
//   while (moneyLeft.length > 3) {
//     result = ',' + moneyLeft.slice(-3) + result
//     moneyLeft = moneyLeft.slice(0, -3)
//   }
//   return symbol + moneyLeft + result
// }
