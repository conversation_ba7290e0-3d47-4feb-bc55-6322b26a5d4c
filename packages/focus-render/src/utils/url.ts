const qs = require('query-string')
/**
 * @description 解析url
 * @param {String} url
 * @param {String} key
 * @return {Object} parsed[key]
 */

export function obj2QueryStr(obj: { [key: string]: any }) {
  return qs.stringify(obj)
}

export class UrlParser {
  href: string
  path: string
  hash: string
  query: {
    [key: string]: string
  }

  constructor(_href?: string) {
    this.href = _href || window.location.href
    const { path, query, hash } = this.parse(this.href)
    // console.log('🚀 ~ file: url.ts:24 ~ UrlParser ~ constructor ~ ath, query, hash:', path, query, hash)
    this.path = path
    this.query = query
    this.hash = hash
  }
  get fullPath() {
    let parmas = ''
    if (this.queryStr) {
      parmas = `?${this.queryStr}`
    }
    if (this.hash) {
      parmas += `#${this.hash}`
    }
    return `${this.path}${parmas}`
  }

  get hrefUseCurHost() {
    const urlArr = (this.fullPath && this.fullPath.split('?')) || []
    let pathOld = urlArr[0]
    const hjHost = []
    if (
      /(webank\.com)|(webankwealth\.com)|(webankapp\.com)/.test(pathOld) &&
      /(personal)|(m\.test)|(m\.)/.test(pathOld)
    ) {
      pathOld = pathOld.replace('https://', '').replace('http://', '')
      const pathOldArr = pathOld.split('/')
      pathOldArr[0] = `https://${location.host}`
      const pathNew = pathOldArr.join('/')
      urlArr[0] = pathNew
      return urlArr.join('?')
    }
    return this.fullPath
  }

  get queryStr() {
    return qs.stringify(this.query)
  }

  get h5InWxMiniJumpToWxMiniPath() {
    if (!this.href) {
      return ''
    }
    const str = this.href.indexOf('/') !== 0 ? '/' + this.href : this.href
    let arr = str.split('?')
    arr[0] = arr[0].replace('.html', '')

    return arr.join('?')
  }

  private parse(url: string): {
    href: string
    path: string
    hash: string
    query: {
      [key: string]: string
    }
  } {
    if (!url) {
      console.error('UrlParser 没有传入url！')
      return {
        href: '',
        path: '',
        query: {},
        hash: '',
      }
    }
    // 先处理hash，# 前是 path + search ,# 后是 hash
    const hashArr = url.split('#')
    let hash = hashArr[1] || ''
    // console.log('🚀 ~ file: url.ts:93 ~ UrlParser ~ parse ~ hash:', hash)
    const pathAndSearch = hashArr[0] || url
    let search = pathAndSearch.split('?')[1]
    // console.log('🚀 ~ file: url.ts:94 ~ UrlParser ~ parse ~ search:', search)
    let path = pathAndSearch.split('?')[0]
    // console.log('🚀 ~ file: url.ts:95 ~ UrlParser ~ parse ~ path:', path)
    const query = qs.parse(search) || {}

    // 处理一个额外情况，wb_safe_code 这个东西，如果原链接有 #xxx，会直接拼接，解析的不好。
    if (hash.indexOf('wb_safe_code') > -1) {
      console.log('1111', hash)
      // 只把wb_safe的参数放到query里面
      const arr = hash.split('&')
      const keys = ['wb_safe_code', 'wb_safe_msg', 'wb_safe_rule_action']
      arr.forEach((i) => {
        if (keys.some((_i) => i.indexOf(_i) > -1)) {
          const key = i.split('=') && i.split('=')[0]
          const val = i.split('=') && i.split('=')[1]
          query[key] = val
        }
      })
    }

    // &wb_safe_code=null&wb_safe_msg=null&wb_safe_rule_action=null

    // console.log('🚀 ~ file: url.ts:105 ~ UrlParser ~ parse ~ query:', query)
    // if (path.indexOf('#') > -1) {
    //   // const newPath = path.replace()
    //   const temp = path.split('#')
    //   path = temp[0]
    //   hash = temp[1]
    // }
    // Object.keys(query).forEach((key) => {
    //   const data = query[key]
    //   if (data.indexOf('#') > -1) {
    //     const arr = data.split('#')
    //     hash = arr[1] || ''
    //     query[key] = arr[0]
    //     return
    //   }
    // })
    this.path = path
    this.query = query
    this.hash = hash
    return this
  }

  appendQuery(
    keys: {
      [key: string]: any
    } = {}
  ) {
    const _query = JSON.parse(JSON.stringify(this.query))
    Object.keys(keys).forEach((key) => {
      _query[key] = keys[key]
    })
    this.query = _query
    return this
  }
  removeQuery(keys: string[]) {
    const _query = JSON.parse(JSON.stringify(this.query))
    keys.forEach((key) => {
      if (Object.prototype.hasOwnProperty.call(_query, key)) {
        delete _query[key]
      }
    })
    this.query = _query
    return this
  }
}

// export { UrlParser, parseUrl };
export default UrlParser
