const address = require('address')
const nodejs_argv = require('nodejs-argv')
const path = require('path')
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
const pkgConfig = require('./package.json')
const focusCoreVer = pkgConfig.version
const { NODE_ENV, VUE_APP_BUILD_MODE, VUE_APP_IMG_URL, VUE_APP_CDN_PREFIX } = process.env
const argv = nodejs_argv.new()
argv.option([
  ['-p', '--page', 'str=', '项目名'],
  ['-d', '--dev', 'str=', 'dev模式'],
  ['-log', '--log', 'str=', '不屏蔽log'],
  ['-t', '--target', 'str=', '打包模式'],
  ['-vc', '--vconsole', 'str=', 'vconsole是否隐藏'],
])
try {
  argv.parse()
} catch (e) {
  console.log('Error:', e)
}
console.log('🚀 ~ file: vue.config.js:9 ~ argv:', argv)

let pageName = 'client'
let isLib = false
let showVConsole = true // 测试环境默认展示vc
if (argv.get('-t')) {
  console.log('...........')
  console.log(argv.get('-t'))
  isLib = argv.get('-t') === 'lib'
}
if (argv.get('-p')) {
  pageName = argv.get('-p')
  console.log('🚀 ~ 打包项目', pageName)
}
console.log("🚀 ~ file: vue.config.js:36 ~ argv.get('--vconsole'):", argv.get('--vconsole'))
if (argv.get('--vconsole')) {
  showVConsole = argv.get('--vconsole') !== 'hide'
  console.log('🚀 ~ 是否展示vconsle', showVConsole)
}
if (!pageName) {
  console.error('没有目录名！')
  return
}
const isDev = argv.get('--dev') === 'dev'
const showLog = isDev || argv.get('--log') === 'log'
const serverIsTest = VUE_APP_BUILD_MODE === 'test'
const isPreview = VUE_APP_BUILD_MODE === 'preview'
const loaclIp = address.ip()

console.log(`NODE_ENV=${NODE_ENV}`)
console.log(`打包的环境参数VUE_APP_BUILD_MODE=${VUE_APP_BUILD_MODE}`)
const ajaxBaseUrl = serverIsTest ? `https://personal.test.webank.com` : `https://personal.webank.com`
const pages = {
  client: {
    // page 的入口
    // 模板来源
    entry: './src/views/client/main.ts',
    // 在 dist/index.html 的输出
    filename: './client/index.html',
    // 当使用 title 选项时，
    // 模板来源
    template: './src/views/client/index.html',
    // 当使用 title 选项时，
    // template 中的 title 标签需要是 <title><%= htmlWebpackPlugin.options.title %></title>
    title: '微众银行',
    // 在这个页面中包含的块，默认情况下会包含
    // 提取出来的通用 chunk 和 vendor chunk。
    chunks: ['log-resource', 'chunk-vendors', 'chunk-common', 'client'],
  },
}

const buildOptions = (() => {
  const targetPages = {}
  if (pageName) {
    targetPages[pageName] = pages[pageName]
  }
  const publicPath = isPreview ? VUE_APP_CDN_PREFIX + '/hj/focus2-preview' : VUE_APP_CDN_PREFIX + '/hj/focus2'
  return {
    // baseUrl: `https://personal${isTest}.webank.com/s/hj/op2/${pageName}`,
    targetPages: targetPages,
    outputDir: serverIsTest ? 'client-test' : isPreview ? 'client-preview' : `client-prod`,
    publicPath: publicPath,
  }
})()

const { targetPages, outputDir, publicPath } = buildOptions
console.log('🚀 ~ publicPath: ', publicPath)
console.log('🚀 ~ outputDir: ', outputDir)
const cdnPrefixFocusRender = publicPath + '/focus-render'
console.log('🚀 ~ cdnPrefixFocusRender: ', cdnPrefixFocusRender)

const devDir = {
  'client-test': path.resolve(__dirname, '/client-test/'),
}

module.exports = {
  lintOnSave: false,
  pages: targetPages,
  publicPath: publicPath,
  outputDir: isLib ? 'build-lib' : outputDir,
  assetsDir: `./${pageName}/`,
  // filenameHashing: VUE_APP_BUILD_MODE === 'prod' ? true : false,
  filenameHashing: true,
  runtimeCompiler: true,
  productionSourceMap: serverIsTest ? true : false,
  css: {
    extract: {
      filename: `client/css/[name].[hash:8].${focusCoreVer}.css`,
      chunkFilename: `client/css/[name].[hash:8].${focusCoreVer}.css`,
    },
    loaderOptions: {
      scss: {
        // @/ 是 src/ 的别名
        // 所以这里假设你有 `src/variables.scss` 这个文件
        prependData: `@import "./src/styles/variables.scss";@import "./src/styles/variables.${VUE_APP_BUILD_MODE}.scss";`,
        implementation: require('sass'), // This line must in sass option
      },
    },
  },
  // css: {
  //   loaderOptions: {
  //     sass: {
  //       implementation: require('sass'), // This line must in sass option
  //     },
  //   },
  // }

  // devServer: {
  //   host: '127.0.0.1',
  //   disableHostCheck: true,
  //   port: 2222,
  //   https: true,
  //   proxy: {
  //     'https://m.test.webank.com/s/hj/focus2/': {
  //       changeOrigin: true,
  //       target: devDir['client-test'],
  //     },
  //     'https://dbd.test.webankwealthcdn.net/wm-resm/hj/focus2/': {
  //       changeOrigin: true,
  //       target: devDir['client-test'],
  //     },
  //     // '/common/systimeinfo': {
  //     //   changeOrigin: true,
  //     //   target: `${loaclIp}:3008`,
  //     // },
  //     // '/welfare/queryactivityinfo': {
  //     //   changeOrigin: true,
  //     //   target: `${loaclIp}:3008`,
  //     // },
  //   },
  // },

  chainWebpack: (config) => {
    config.module
      .rule('images')
      .use('url-loader')
      .tap((options) => {
        options = {
          ...options,
          limit: 5 * 1024,
          fallback: {
            ...options.fallback,
            options: {
              name: `client/img/[name].[hash:8].[ext]`,
              publicPath: publicPath,
              outputPath: `./`,
            },
          },
        }

        return options
      })
    // 增加全局变量
    config.plugin('define').tap((args) => {
      args[0].BUILD_MODE = `"${VUE_APP_BUILD_MODE}"`
      args[0].LOCAL_IP = `"${loaclIp}"`
      args[0].IMG_URL = `"${VUE_APP_IMG_URL}"`
      args[0].CDN_PREFIX_FOCUS_RENDER = `"${cdnPrefixFocusRender}"`
      args[0].AJAX_BASE_URL = `"${ajaxBaseUrl}"`
      args[0].BUILD_TEST = serverIsTest
      args[0].FOCUS_CORE_VER = `"${focusCoreVer}"`
      args[0].SHOW_VCONSOLE = showVConsole
      return args
    })

    if (VUE_APP_BUILD_MODE === 'test') {
      config.optimization.minimize(false)
    }

    if (!isLib) {
      config.plugin(`preload-${pageName}`).tap((options) => {
        options[0].include.entries.unshift('log-resource')
        return options
      })
    }

    config.when(VUE_APP_BUILD_MODE === 'prod', (config) => {
      config.optimization.minimizer('terser').tap((args) => {
        args[0].terserOptions.compress.drop_console = true
        return args
      })
    })

    config.module.rule('ts').use('ts-loader')
  },
  configureWebpack: (config) => {
    // 添加构建完成后打印自定义文本的插件
    config.plugins.push({
      apply: (compiler) => {
        compiler.hooks.done.tap('BuildCompletePlugin', (stats) => {
          setTimeout(() => {
            // 检测本地127.0.0.1:8899服务是否开启
            const net = require('net');
            const client = new net.Socket();
            let isServerRunning = false;

            client.connect(8899, '127.0.0.1', () => {
              isServerRunning = true;
              client.destroy();
            });

            client.on('error', () => {
              client.destroy();
            });

            client.on('close', () => {
              // 定义颜色和样式常量
              const GREEN = '\x1b[32m';
              const RED = '\x1b[31m';
              const CYAN = '\x1b[36m';
              const YELLOW = '\x1b[33m';
              const BOLD = '\x1b[1m';
              const RESET = '\x1b[0m';
              const SEPARATOR = `${GREEN}=====================================${RESET}`;

              // 组织提示信息
              const messages = [
                '',
                SEPARATOR,
                `${BOLD}${CYAN}步骤 1: 配置代理服务${RESET}`,
                `${GREEN}  1. 安装 whisle 插件: npm install -g whistle${RESET}`,
                `${GREEN}  2. 启动服务: w2 start${RESET}`,
                `${GREEN}  3. 添加以下代理规则:${RESET}`,
                `${GREEN}     - https://m.test.webank.com/s/hj/focus2/client/index.html  file://${__dirname}/client-test/client/index.html${RESET}`,
                `${GREEN}     - https://dbd.test.webankwealthcdn.net/wm-resm/hj/focus2/client/  file://${__dirname}/client-test/client/${RESET}`,
                '',
                isServerRunning ?
                  `${GREEN}✅ 检测到 whisle 服务 (127.0.0.1:8899) 已开启${RESET}` :
                  `${RED}❌ 检测到 whisle 服务 (127.0.0.1:8899) 未开启，请先启动服务${RESET}`,
                '',
                `${BOLD}${CYAN}步骤 2: 配置微信开发者工具网络代理${RESET}`,
                `${GREEN}  1. 打开[微信开发者工具]${RESET}`,
                `${GREEN}  2. 进入设置 > 代理设置 > 手动设置代理${RESET}`,
                `${GREEN}  3. 输入: 127.0.0.1:8899${RESET}`,
                '',
                `${BOLD}${CYAN}步骤 3: 获取访问链接${RESET}`,
                `${YELLOW}  focus管理端测试环境: ${BOLD}http://10.107.97.66/focusv3/#/${RESET}`,
                `${GREEN}  从管理端获取有效的fid参数${RESET}`,
                '',
                `${BOLD}${CYAN}步骤 4: 在微信开发者工具中访问${RESET}`,
                `${GREEN}  使用以下格式的地址:${RESET}`,
                `${BOLD}${GREEN}  https://m.test.webank.com/s/hj/focus2/client/index.html?fid=xxx${RESET}`,
                `${GREEN}  (将xxx替换为从管理端获取的实际fid值)${RESET}`,
                SEPARATOR,
                ''
              ];

              // 一次性打印所有消息
              console.log(messages.join('\n'));
            });
          }, 100);
        });
      }
    });
    if (isLib) {
      console.log('...config.externals', config.externals)
      config.externals = {
        axios: 'axios',
        pinia: 'pinia',
      }
    }

    // 配置输出文件名格式
    config.output = {
      ...config.output,
      filename: `client/js/[name].[hash:8].${focusCoreVer}.js`,
      chunkFilename: `client/js/[name].[hash:8].${focusCoreVer}.js`,
    }

    if (config.entry.client) {
      config.entry = {
        'log-resource': [path.resolve(__dirname, 'src/service/focus-core/log-resource.ts')],
        ...config.entry,
      }
    }
  },
}
