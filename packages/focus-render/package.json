{"name": "@focus/render", "version": "2.25.0820", "scripts": {"build:test-lib": "node ../../scripts/update-version.js && vue-cli-service build --mode test -p client --target lib --name focus-render src/packages/focus-render/index.ts && tsc --emitDeclarationOnly", "watch:test-lib": "vue-cli-service build --mode test --watch -p client --target lib --name focus-render src/packages/focus-render/index.ts && tsc --emitDeclarationOnly", "build:test": "vue-cli-service build -p client --mode test ", "build:prod-lib": "node ../../scripts/update-version.js && vue-cli-service build --mode prod -p client --target lib --name focus-render src/packages/focus-render/index.ts", "devtest": "node ../../scripts/update-version.js && vue-cli-service build -p client --dev dev --mode test --watch --vconsole hide", "dev": " vue-cli-service build  --mode test --watch -p", "build:prod": "vue-cli-service build -p client --mode prod --no-clean", "build:prod-log": "vue-cli-service build -p client --mode prod --no-clean --log log", "build:preview": " vue-cli-service build  --mode preview -p client"}, "main": "./build-lib/focus-render.umd.js", "dependencies": {"@babel/plugin-proposal-optional-chaining": "^7.20.7", "@focus/activity": "workspace:^0.1.0", "@webank/wa-sdk": "1.16.0", "axios": "^0.26.1", "core-js": "^3.22.2", "dayjs": "1.11.5", "install": "^0.13.0", "isBetween": "link:dayjs/plugin/isBetween", "js-base64": "^3.7.7", "js-md5": "^0.7.3", "pinia": "^2.0.13", "query-string": "^7.1.1", "swiper": "8.4.7", "vconsole": "3.15.1", "vue": "^3.2.33", "vue-clipboard3": "^2.0.0", "vue3-video-play": "^1.3.1-beta.6"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/js-md5": "^0.4.3", "@types/node": "^14.14.31", "@types/vue": "^2.0.0", "@types/webpack-env": "^1.16.3", "@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-plugin-typescript": "^4.5.15", "@vue/cli-plugin-vuex": "4", "@vue/cli-service": "4", "@vue/compiler-sfc": "^3.2.33", "address": "^1.1.2", "cssnano-preset-advanced": "^4.0.8", "nodejs-argv": "^1.0.2", "postcss-aspect-ratio-mini": "^1.1.0", "postcss-cssnext": "^3.1.1", "postcss-px-to-viewport": "^1.1.1", "postcss-write-svg": "^3.0.1", "regenerator-runtime": "^0.13.9", "sass": "^1.49.11", "sass-loader": "^8.0.2", "ts-loader": "^9.2.8", "typescript": "~4.1.6", "webpack-bundle-analyzer": "^4.5.0"}, "peerDependencies": {"pinia": "^2.0.13", "vue": ">=3.0.0"}, "typings": "./build-lib/index.d.ts"}