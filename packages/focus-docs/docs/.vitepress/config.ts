import { defineConfig } from "vitepress";

export default defineConfig({
  lang: "zh-CN",
  title: "Focus-页面配置平台",
  description: "Focus in your Focus",
  lastUpdated: true,

  themeConfig: {
    docsDir: "docs",
    docsBranch: "main",
    lastUpdated: "Last Updated",
    nav: [
      { text: "指南", link: "/guide/index", activeMatch: "^/$|^/guide/" },
      {
        text: "组件和功能（H5）",
        link: "/comps&function/index",
        activeMatch: "^/$|^/comps&function/",
      },
      {
        text: "业务场景配置样例",
        link: "/config-reference/index",
        activeMatch: "^/config-reference/",
      },
      {
        text: "更新日志",
        link: "/change-log/index",
        activeMatch: "^/change-log/",
      },
    ],

    sidebar: {
      "/guide/": "auto",
      "/config-reference/": "auto",
      "/change-log/": "auto",
      "/comps&function/": getSidebarComps(),
    },
  },
});

function getGuideSidebar() {
  return [
    {
      text: "介绍",
      children: [
        { text: "营销业务流程", link: "/guide/getting-started" },
        {
          text: "FOCUS管理端",
          link: "/guide/index",
        },
      ],
    },
    {
      text: "Advanced",
      children: [
        { text: "Frontmatter", link: "/guide/frontmatter" },
        { text: "Theming", link: "/guide/theming" },
        { text: "API Reference", link: "/guide/api" },
        {
          text: "Differences from Vuepress",
          link: "/guide/differences-from-vuepress",
        },
      ],
    },
  ];
}

function getSidebarComps() {
  return [
    {
      text: "组件",
      collapsable: true,
      children: [
        { text: "简介", link: "/comps&function/index" },
        {
          text: "UI组件",
          children: [
            { text: "盒子组件", link: "/comps&function/MBox" },
            { text: "图片组件", link: "/comps&function/MImage" },
            { text: "文字组件", link: "/comps&function/MText" },
            { text: "视频组件", link: "/comps&function/MVideo" },
            { text: "背书组件", link: "/comps&function/MLogo" },
          ],
        },
        {
          text: "功能组件",
          children: [
            { text: "点击按钮", link: "/comps&function/BtnClick" },
            { text: "锚点按钮", link: "/comps&function/BtnAnchor" },
            { text: "锚点导航组件", link: "/comps&function/AnchorNav" },
            { text: "条件渲染盒子", link: "/comps&function/conditionBox" },
          ],
        },
        {
          text: "业务组件",
          children: [
            {
              text: "参与活动按钮",
              link: "/comps&function/BtnAdding",
            },
            {
              text: "MGM分享按钮",
              link: "/comps&function/BtnShare",
            },
            {
              text: "企业购分享按钮",
              link: "/comps&function/BtnShareCompany",
            },
          ],
        },
      ],
    },
    {
      text: "页面功能",
      children: [
        {
          text: "自动参与活动",
          link: "/comps&function/page-func/auto-join",
        },
        {
          text: "分享能力",
          link: "/comps&function/page-func/page-share",
        },
        {
          text: "标签&灰度ID显示",
          link: "/comps&function/page-func/gray-list",
        },
        {
          text: "MGM-建立邀请关系",
          link: "/comps&function/page-func/mgm",
        },
        {
          text: "数据源配置",
          link: "/comps&function/page-func/data-source",
        },
        {
          text: "弹出层配置",
          link: "/comps&function/page-func/click-popup",
        },
        {
          text: "接口功能配置",
          link: "/comps&function/page-func/service-config",
          children: [
            {
              text: "拉取营销弹窗",
              link: "/comps&function/page-func/service-config",
            },
          ],
        },
      ],
    },
  ];
}
