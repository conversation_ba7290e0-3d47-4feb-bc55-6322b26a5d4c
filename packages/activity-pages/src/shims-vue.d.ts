/* eslint-disable */
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}
declare module '@webank/wa-sdk'
declare interface Window {
  hjCoreIab: any
  isProWebView: Boolean
  wx: any
  hjCoreIab: any
  FOCUS_WA: any // wa实例
  WAFocusIsWatchingClickEvent: boolean // wa自动点击上报
}
declare const BUILD_TEST: boolean
declare const CDN_PREFIX_FOCUS_RENDER: string
declare const SHOW_VCONSOLE: boolean
/**
 * cgi相关
 */
declare const enum EnumMethod {
  GET = 'get',
  POST = 'post',
}

declare module 'svga'
// global.d.ts
declare module '*.svga'
declare module 'gsap'
declare module 'animate.css'
