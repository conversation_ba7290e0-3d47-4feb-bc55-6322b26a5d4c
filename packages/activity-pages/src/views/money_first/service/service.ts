import { focusStore, focusServices, focusCore, focusUtils } from '@focus/render'
console.log('🚀 ~ file: service.ts ~ line 2 ~ focusUtils', focusUtils)
const { dayjs, formatAmount } = focusUtils
const { mgmService, kvService } = focusServices

const cgis = {
  propertyHistoryList: {
    url: '/wm-htrserver/cop/hj/asset_reach/queryAssetHistoryDetail',
    name: '历史资产明细',
  },
  moneyFirstTaskProgress: {
    url: '/wm-htrserver/cop/hj/asset_reach/queryAssetProgressDetail',
    name: '资产达标进度-已开户',
  },
  awardList: {
    url: '/wm-htrserver/cop/hj/query_user_award',
    name: '资产达标奖励',
  },
  moneyFirstTaskProgressNoAccount: {
    url: '/wm-htrserver/cop/hj/asset_reach/queryAssetConfigWithoutOpen',
    name: '资产达标进度-未开户',
  },
  checkIsCarActive: {
    name: '检查用户是否是未激活的车贷通户',
    url: '/wm-htrserver/cop/hj/asset_reach/checkCarAccountActiveFlag',
  },
  //
}

class MoneyFirstService {
  /**
   * 倒序返回资产列表，近12个月
   * @returns
   */
  getPropertyHistoryList(): Promise<
    Array<{ dateId: string; date: string; amountMonth: number; dayList: Array<any>; dsIsLast: boolean }>
  > {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.propertyHistoryList)
        .then((res: any) => {
          console.log('🚀 ~ file: service.ts ~ line 23 ~ MoneyFirstService ~ focusCore.request ~ res', res)
          const { assetHistoryDetailList = [] } = res
          const monthList = assetHistoryDetailList.map((i: any) => {
            const { dsMonth, assetJson, monthlyAverageAum, dsDate } = i
            const monthData = JSON.parse(assetJson || '{}')
            const dayList =
              Object.keys(monthData)
                .sort((a, b) => {
                  if (Number(a || 0) < Number(b || 0)) {
                    return 1
                  }
                  return -1
                })
                .map((time: string) => {
                  const amount = monthData[time]
                  const year = time.substr(0, 4)
                  const month = time.substr(4, 2)
                  const day = time.substr(6, 2)
                  return {
                    date: `${month}月${day}日`,
                    amount,
                  }
                }) || []
            const year = dsMonth.toString().substr(0, 4)
            const month = dsMonth.toString().substr(4, 2)

            return {
              dateId: `${year}${month}`,
              date: `${year}年${month}月`,
              amountMonth: monthlyAverageAum,
              dsIsLast: dayjs(`${dsDate} 00:00:00`).add(1, 'd').format('D') === '1',
              dayList,
            }
          })
          console.log('当前月日均资产列表', monthList)
          resolve(monthList)
        })
        .catch((err: any) => {
          console.log(err)
          resolve([])
        })
    })
  }

  getProgressData(aid: number) {
    console.log('....focusCore,', focusCore.hasAccount)
    if (focusCore.hasAccount) {
      return focusCore.request(cgis.moneyFirstTaskProgress, { activityId: aid })
    }
    return new Promise((resolve) => {
      focusCore.request(cgis.moneyFirstTaskProgressNoAccount, { activityId: aid, productId: 'HJ' }).then((res: any) => {
        console.log('🚀 ~ file: service.ts:91 ~ MoneyFirstService ~ focusCore.request ~ res:', res)
        return resolve({
          ...res,
          assetProgressDetail: {
            remainingDays: res.remainDays || 0,
          },
        })
      })
    })
  }

  getMoneyFirstTaskProgress(aid: number) {
    return new Promise((resolve, reject) => {
      this.getProgressData(aid)
        .then(async (res: any) => {
          console.log('🚀 ~ file: service.ts:103 ~ MoneyFirstService ~ .then ~ res:', res)
          const {
            assetReachActivityConfig = {
              newUserActivityUrl: '',
            },
            assetProgressDetail = {
              // 这里的数据是由大数据推送的
              overchargeId: 0, //加码任务配置ID
              assetExtra: '{}',
              startlevelamt: null,
              userCurrentStatus: '2', // 用户当前状态  '1':新人；'2':非新人
              remainingDays: 0, //新手状态剩余天数
              overchargeLevelAmt: 0,
              userTomorrowStatus: null, //用户次日状态	1:新人用户，2:非新人用户，后台没值时给null
            },
            first_reg_day,
            rewardDays, // 新手状态剩余天数(后台计算)
            rewardStatus = '2', //用户当前状态 '1':新人；'2':非新人
            assetTagInfo = {
              currentMonthAvgAum: 0,
              fDate: 0,
            },
            assetOverChargeItem = {
              stallConfigPlus: '[]', //加码配置	json字符串
            },
            dsDate = '',
          } = res
          console.log('🚀 ~ file: service.ts ~ line 83 ~ MoneyFirstService ~ .then ~ res', res)
          console.log('🚀 ~ file: service.ts:131 ~ MoneyFirstService ~ .then ~ dsDate:', dsDate)
          let canUseNewUser = true // 放开限制
          // try {
          //   canUseNewUser = await this.checkCanUseNewUser()
          //   // canUseNewUser = true
          // } catch (err) {
          //   console.log('🚀 ~ file: service.ts:118 ~ MoneyFirstService ~ .then ~ err:', err)
          // }

          const { stallConfigPlus = '[]' } = assetOverChargeItem || {}
          const { newUserActivityUrl = '', stallConfig = '[]' } = assetReachActivityConfig || {}
          const {
            assetExtra,
            startlevelamt = null,
            userCurrentStatus = '2',
            remainingDays = 0,
            overchargeLevelAmt = 0,
          } = assetProgressDetail

          // 0326版本更换字段 userCurrentStatus 为 rewardStatus
          let isNewUser = rewardStatus === '1'
          // 未开户用户就是新人用户
          if (!focusCore.hasAccount) {
            isNewUser = true
          }
          // 0326版本，更换 remainingDays 为 rewardDays，用后台实时计算的新手到期
          const newUserCountTime = isNewUser ? Number(rewardDays) : 0

          const { conditionStore } = focusStore.stores
          conditionStore.setCondition('dc04', isNewUser ? 2 : 1)

          const curStartLevelAmt = startlevelamt === 0 ? 0 : !startlevelamt ? 0 : startlevelamt

          console.log('🚀 ~ 用户是不是新人', isNewUser)

          console.log('🚀 ~ 新人状态倒计时', newUserCountTime)
          console.log(
            '🚀 ~ file: service.ts ~ line 84 ~ MoneyFirstService ~ .then ~ assetProgressDetail',
            assetProgressDetail
          )

          let taskPlusConfig: {
            stallAmount: number
            allPriceAmount: number
            data: {
              taskType: string
              amt: number
            }[]
          }[] = []

          let propertyList: {
            dateId: string
            date: string
            amountMonth: number
            dayList: Array<any>
            dsIsLast: boolean
          }[] = []
          try {
            propertyList = await this.getPropertyHistoryList()
          } catch (err) {
            console.log(err)
          }
          try {
            taskPlusConfig = JSON.parse(stallConfigPlus) || []
          } catch (err) {
            console.log(err)
          }
          console.log('🚀 ~ file: service.ts:139 ~ MoneyFirstService ~ .then ~ taskPlusConfig:', taskPlusConfig)

          // 确定新手任务加码范围
          const taskPlusConfigObj = {}
          taskPlusConfig = taskPlusConfig.filter((i) => {
            if (isNewUser) {
              return true
            }
            return i.stallAmount <= overchargeLevelAmt
          })
          taskPlusConfig.forEach((i) => {
            const { data, stallAmount, allPriceAmount } = i
            data.forEach((_i, index) => {
              if (_i.amt && Number(_i.amt)) {
                // const nameKey = `taskType_${_i.taskType}_${stallAmount}_rewardName`
                const countKey = `taskType_${_i.taskType}_${stallAmount}_amt`
                // taskPlusConfigObj[nameKey] = data.rewardName
                taskPlusConfigObj[countKey] = Number(_i.amt)
              }
            })
          })
          console.log(
            '🚀 ~ file: service.ts:216 ~ MoneyFirstService ~ data.forEach ~ taskPlusConfigObj:',
            taskPlusConfigObj
          )

          const originStallConfig: Array<{
            stallAmount: number
            allPriceAmount: number
            allPriceAmount3: number
            allPriceAmount2: number
            dataList: Array<{
              taskType: string
              id: number
              taskTitle: string
              taskDone?: boolean
              data: {
                rewardCount: string
                rewardDesc: string
                rewardName: string
                rewardPic: string
                rewardScen: string
                rewardType: string
              }
            }>
          }> = JSON.parse(stallConfig || '[]') || []

          // 选出可展示的档位配置，档位
          const configList = originStallConfig.filter((item, index) => {
            return item.stallAmount > curStartLevelAmt
          })
          console.log('🚀 ~ 可展示的配置', configList)

          let taskProgressData: {
            lastDayAum?: string | number
            curMonthAum?: string | number
          } = {}
          try {
            taskProgressData = JSON.parse(assetExtra || '{}')
          } catch (err) {
            console.log('🚀 ~ file: service.ts ~ line 56 ~ MoneyFirstService ~ focusCore.request ~ err', err)
          }
          const userIsAdding = !!Object.keys(taskProgressData).length
          const lastDayAum = formatAmount(taskProgressData.lastDayAum || 0, true)
          const curMonthAum = formatAmount(taskProgressData.curMonthAum || assetTagInfo.currentMonthAvgAum || 0, true)
          const lastDayAumNumber = Number(taskProgressData.lastDayAum || 0)
          const curMonthAumNumber = Number(taskProgressData.curMonthAum || 0)
          let maxPriceAmount = 0 // 有4个任务时，所有档位加起来的奖励
          let leftPriceAmount = 0
          let maxPriceAmountFor3 = 0 // 有3个任务时，所有档位加起来的奖励
          let maxPriceAmountFor2 = 0 // 有2个任务时，所有档位加起来的奖励
          const configData = configList.map((i, index) => {
            const { stallAmount, dataList, allPriceAmount = 0, allPriceAmount2 = 0, allPriceAmount3 = 0 } = i

            const noWan = Number(stallAmount) < 10000
            const amountFormate = noWan ? stallAmount : Number(stallAmount) / 10000
            const unit = !noWan ? '万元' : '元'
            //当前档位的加码总奖励
            const targetNewUser: any = taskPlusConfig.find((_i) => _i.stallAmount === stallAmount) || {}

            // 当前单个档位的总奖励，分别是有4个任务、有3个任务、有2个任务
            let curStallAllPriceAmount = (allPriceAmount || 0) + (targetNewUser.allPriceAmount || 0)
            let curStallAllPriceAmount3 =
              ((allPriceAmount3 || 0) * 100 + (targetNewUser.allPriceAmount || 0) * 100) / 100
            let curStallAllPriceAmount2 =
              ((allPriceAmount2 || 0) * 100 + (targetNewUser.allPriceAmount || 0) * 100) / 100

            // 累计到当前档位的总奖励，分别是有4个任务、有3个任务、有2个任务
            let curStallCountingAllPriceAmount = 0
            let curStallCountingAllPriceAmount3 = 0
            let curStallCountingAllPriceAmount2 = 0
            for (let i = 0; i < index + 1; i++) {
              // 需要到当前档位为止
              const target = configList[i] || {}
              // console.log('🚀 ~ file: service.ts:295 ~ MoneyFirstService ~ configData ~ target:', target)

              const targetNewUser: any = taskPlusConfig.find((_i) => _i.stallAmount === target.stallAmount) || {}

              curStallCountingAllPriceAmount += (target.allPriceAmount || 0) + (targetNewUser.allPriceAmount || 0)

              // 放大100倍规避浮点计算
              curStallCountingAllPriceAmount3 +=
                (target.allPriceAmount3 || 0) * 100 + (targetNewUser.allPriceAmount || 0) * 100
              curStallCountingAllPriceAmount2 +=
                (target.allPriceAmount2 || 0) * 100 + (targetNewUser.allPriceAmount || 0) * 100

              if (index === configList.length - 1) {
                maxPriceAmount = curStallCountingAllPriceAmount
                maxPriceAmountFor3 = curStallCountingAllPriceAmount3 / 100
                maxPriceAmountFor2 = curStallCountingAllPriceAmount2 / 100
              }
            }
            // 缩小100倍恢复原来数值
            curStallCountingAllPriceAmount3 = curStallCountingAllPriceAmount3 / 100
            curStallCountingAllPriceAmount2 = curStallCountingAllPriceAmount2 / 100
            const taskList = dataList.map((i) => {
              const { taskTitle, id, data } = i
              const taskStatusKeyName = `taskType_${id}_${stallAmount}` // 档位完成状态
              const taskProgressKeyName = `taskType_${id}_${stallAmount}_progress` // 档位进度
              const batchNoKeyName = `taskType_${id}_${stallAmount}_batchNo` // 档位的发奖id
              const newUserRewardNameKey = `taskType_${id}_${stallAmount}_rewardName` // 加码档位名称
              const newUserRewardCountKey = `taskType_${id}_${stallAmount}_amt` // 加码档位名称

              const batchNo = taskProgressData[batchNoKeyName] || 0
              const taskStatus = taskProgressData[taskStatusKeyName] || '0'
              let isPlusTask = false // 这个任务是否加码
              let progressPercent = '0%'
              let progressText = id === 3 ? '0/3' : id === 4 ? '0/6' : '0%'
              let propertyText =
                taskStatus === '0' ? (id === 1 ? `昨日资产 ${lastDayAum}元` : `当月日均资产 ${curMonthAum}元`) : ''
              if (taskStatus === '0') {
                let curProgress: any = ''
                // 昨日资产的进度
                if (id === 1) {
                  curProgress =
                    (Number(parseInt((lastDayAumNumber * 1000).toString(), 10)) / 1000 / Number(stallAmount)) * 100
                  curProgress =
                    curProgress >= 100 ? '100' : (parseInt((curProgress * 100).toString(), 10) / 100).toString()
                }
                if (id === 2) {
                  //月日均资产进度
                  curProgress =
                    (Number(parseInt((curMonthAumNumber * 1000).toString(), 10)) / 1000 / Number(stallAmount)) * 100
                  curProgress =
                    curProgress >= 100 ? '100' : (parseInt((curProgress * 100).toString(), 10) / 100).toString()
                }
                if (id === 3) {
                  // 连续3个月
                  let times = 0

                  propertyList
                    .map((i) => i)
                    .reverse()
                    .forEach((i, index) => {
                      if (index === propertyList.length - 1 && !i.dsIsLast) {
                        // 如果最后一个不是最后一天结算，就不看了
                        return
                      }

                      if (i.amountMonth >= Number(stallAmount)) {
                        times += 1
                      } else {
                        times = 0
                      }
                      // console.log(times)
                    })
                  curProgress = `${times}/3`
                }
                if (id === 4) {
                  // 累计6个月
                  let times = 0
                  propertyList.forEach((i, index) => {
                    if (!index && !i.dsIsLast) {
                      // 如果第一个不是最后一天结算，就不看了
                      return
                    }

                    if (i.amountMonth >= Number(stallAmount)) {
                      times += 1
                    }
                  })
                  times = times >= 6 ? 6 : times
                  curProgress = `${times}/6`
                }

                if (curProgress) {
                  // console.error(`${taskProgressKeyName}现在有进度拉`, curProgress)
                  const nums = curProgress.split('/')
                  progressPercent = nums.length > 1 ? (nums[0] / nums[1]) * 100 + '%' : curProgress + '%'
                  progressText = nums.length <= 1 ? progressPercent : curProgress
                }
              } else {
                if (id === 1) {
                  progressPercent = '100%'
                  progressText = '100%'
                }
                if (id === 3) {
                  progressPercent = '100%'
                  progressText = '3/3'
                }
                if (id === 4) {
                  progressPercent = '100%'
                  progressText = '6/6'
                }
              }
              // 任务2隐藏进度
              if (id === 2) {
                progressText = ''
              }

              let newUserRewardName = ''
              let newUserRewardCount = 0
              let rewardMore = 0
              if (taskPlusConfigObj && taskPlusConfigObj[newUserRewardCountKey]) {
                const total = taskPlusConfigObj[newUserRewardCountKey] + data.rewardCount
                newUserRewardName = total + `${data.rewardType === '0' ? '积分' : '元京东卡'}`
                newUserRewardCount = taskPlusConfigObj[newUserRewardCountKey]
                isPlusTask = true
              }

              return {
                taskId: id,
                taskTitle: `${taskTitle}`,
                amountFormate,
                unit,
                rewardName: data.rewardName,
                rewardIcon: data.rewardPic,
                rewardScene: data.rewardScen,
                rewardDesc: data.rewardDesc,
                rewardMore,
                rewardTypeName: data.rewardType === '0' ? '积分' : '京东卡',
                taskDone: taskStatus !== '0',
                taskStatus,
                propertyText,
                progressPercent,
                progressText,
                batchNo,

                // 新户数据
                isPlusTask,
                isNewUser,
                newUserRewardName,
                newUserRewardCount,
                newUserCountTime,
              }
            })
            const taskAllDone = !taskList.some((i) => !i.taskDone)

            return {
              amount: Number(stallAmount),
              noWan,
              amountFormate,
              unit,
              taskList,
              taskAllDone,
              userIsAdding,
              monthAumFromTag: formatAmount(assetTagInfo.currentMonthAvgAum || 0, true),
              lastDayAumNumber,

              curStallCountingAllPriceAmount,
              curStallCountingAllPriceAmount3,
              curStallCountingAllPriceAmount2,

              curStallAllPriceAmount,
              curStallAllPriceAmount3,
              curStallAllPriceAmount2,
            }
          })

          focusCore.getNowTime().then((now: any) => {
            const todayIsMonthFristDay = now.format('D').toString() === '1'
            const userIsRegInThisMonth =
              !first_reg_day ||
              (first_reg_day &&
                dayjs(first_reg_day).isSame(now, 'month') &&
                first_reg_day &&
                dayjs(first_reg_day).isSame(now, 'year'))

            console.log(
              '🚀 ~ file: service.ts ~ line 165 ~ MoneyFirstService ~ focusCore.getNowTime ~ first_reg_day',
              first_reg_day
            )
            console.log(
              '🚀 ~ file: service.ts ~ line 165 ~ MoneyFirstService ~ focusCore.getNowTime ~ userIsRegInThisMonth',
              userIsRegInThisMonth
            )
            // const dataReadyInMonthFirstDay =  dsDate && dayjs(dsDate).add(1, 'd').format('D') === '1' // 数据日期 在 1号的前一天

            console.log('🚀 ~ 原始的 startlevelamt', startlevelamt)
            const dataReadyInMonthFirstDay = startlevelamt === null || startlevelamt === '' ? false : true // 只要 startlevelamt 不为 null 就说明数据已更新
            console.log(
              '🚀 ~ file: service.ts ~ line 285 ~ MoneyFirstService ~ focusCore.getNowTime ~ dataReadyInMonthFirstDay',
              dataReadyInMonthFirstDay
            )

            resolve({
              configData,
              todayIsMonthFristDay,
              dataReadyInMonthFirstDay,
              userIsAdding,
              newUserActivityUrl: canUseNewUser ? '' : userIsRegInThisMonth ? newUserActivityUrl : '', // 因为放开了灰度，所以这里永远为空；
              isOverTopLevel: !configList.length,
              isNewUser,
              maxPriceAmount: parseInt(maxPriceAmount.toString(), 10),
              maxPriceAmountFor2: parseInt(maxPriceAmountFor2.toString(), 10),
              maxPriceAmountFor3: parseInt(maxPriceAmountFor3.toString(), 10),
              dsDateFormat: dsDate ? dayjs(dsDate).format('YYYY-MM-DD') : '暂无数据',
            })
          })
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: service.ts ~ line 172 ~ MoneyFirstService ~ focusCore.request ~ err', err)
          reject()
        })
    })
  }
  getAwardList(params: { aids?: any[]; batchNos?: any[] }) {
    const _aid = params.aids && params.aids.join(',')
    const _batchNos = params.batchNos && params.batchNos.join(',')
    return new Promise((resolve) => {
      focusCore.request(cgis.awardList, { activityId: _aid, batchNo: _batchNos }).then((res: any) => {
        console.log('🚀 ~ file: service.ts ~ line 39 ~ MoneyFirstService ~ focusCore.request ~ res', res)
        // activityId: 8728
        // awardDesc: "积分ss"
        // awardName: "积分"
        // awardTime: 1675249608000
        // awardUrl: "http://www.baidu.com"
        // batchNo: "10001820221208000100000002"
        // bizNo: "2212080KA02596RollTkCw9Ho9bixAcL"
        // cardNo: "6236330070680815808"
        // createTime: 1674376887000
        // dataStatus: "1"
        // dataType: "1"
        // ecifNo: "0999960255224459"
        // id: 2
        // issueSuccNumber: 19
        // lastDataStatusDesc: "奖励发放成功"
        // lastMsgStatusDesc: "发送短信消息ecif或手机号为空|微信账户信息open为空|或者没有配置"
        // msgStatus: "2"
        // orderId: "2212080KA02419Ee5loXEDNoePxZ62YK"
        // productId: "HJ"
        // showStatus: "0"
        // transScenarioId: 100552
        // updateTime: 1675336584000
        const { hjUserAwardList = [] } = res
        const batchNos: string[] = []
        const rewardList = hjUserAwardList.map(
          (i: {
            awardNameGroup: string
            awardDesc: string
            awardTime: number
            awardUrl: string
            batchNo: string
            dataType: string
          }) => {
            const { awardDesc, awardNameGroup, awardTime, awardUrl, batchNo, dataType } = i
            batchNos.push(batchNo)
            return {
              awardDesc,
              awardName: awardNameGroup,
              awardTime: dayjs(awardTime).format('YYYY年MM月DD日 HH:mm发放'),
              awardUrl,
              dataType,
            }
          }
        )
        resolve({ rewardList, batchNos })
      })
    })
  }
  /**
   * 检查是否能用新户加码逻辑
   *
   */
  checkCanUseNewUser(): Promise<boolean> {
    if (!focusCore.hasAccount) {
      return Promise.resolve(true)
    }
    const grayId = [{ id: 663, subId: '2023xinhu' }]
    return new Promise((resolve) => {
      const { grayService } = focusServices
      grayService.checkBussinessGray(grayId).then((list: any) => {
        console.log('🚀 ~ file: service.ts:483 ~ MoneyFirstService ~ grayService.checkBussinessGray ~ list:', list)
        return resolve(!!list.length)
      })
    })
  }
  checkIsCarActiveAccount(): Promise<boolean> {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.checkIsCarActive)
        .then((res: any) => {
          console.log('🚀 ~ file: service.ts:524 ~ MoneyFirstService ~ focusCore.request ~ res:', res)
          resolve(!!res.carActiveFlag)
        })
        .catch(() => {
          resolve(false)
        })
    })
  }
}
export default new MoneyFirstService()
