<template>
  <div class="money-task-list">
    <swiper
      class="nav"
      :speed="400"
      :class="{ isnotfirst: isNotFirst }"
      :slides-per-view="'auto'"
      :centeredSlides="true"
      @slideChange="onSlideChange"
      @swiper="setSwiper"
    >
      <swiper-slide
        class="item"
        :class="`${dataIsReady ? 'cover_mask' : 'cover_mask data_not_ready'}`"
        v-for="(item, index) in configList"
        :key="index"
        @click="switchNav(index)"
      >
        <p class="amount">
          {{ item.amountFormate }}<span>{{ item.noWan ? '元' : '万元' }}</span>
        </p>
        <p class="total-price">
          累计奖励价值<span>{{
            showTaskLevel === 1
              ? item.curStallCountingAllPriceAmount3
              : showTaskLevel === 2
              ? item.curStallCountingAllPriceAmount2
              : item.curStallCountingAllPriceAmount
          }}</span
          >元
        </p>
        <div class="tag">
          <img src="../img/tip_task-done.png" alt="" v-show="dataIsReady && isAdding && item.taskAllDone" />
          <img src="../img/tip_task-notdone.png" alt="" v-show="dataIsReady && isAdding && !item.taskAllDone" />
        </div>
      </swiper-slide>
    </swiper>
    <div class="task-list" :class="`${isNewUser ? 'is-new-user' : ''}`">
      <div class="stall-all-price" :class="`${dataIsReady ? 'cover_mask' : 'cover_mask data_not_ready'}`">
        达标本档位所有挑战，奖励价值 <span>{{ curStallAllPriceAmount }}</span
        >元
      </div>
      <div class="task-item" v-for="(item, index) in taskList" :key="index">
        <div class="index-tip" :class="`${dataIsReady ? 'cover_mask' : 'cover_mask data_not_ready'}`">
          <p>{{ taskTip[index] }}</p>
          <div class="tip_newuser" v-if="item.newUserCountTime && item.newUserRewardName">
            <div class="bg">新客加码倒计时：{{ item.newUserCountTime }}天</div>
          </div>
          <div class="btn-diss" @click="clickBtnDiss(item, index)">{{ taskTipNum[index] }}</div>
        </div>

        <div class="task-content" :class="`${dataIsReady ? 'cover_mask' : 'cover_mask data_not_ready'}`">
          <div class="reward">
            <img class="icon" :src="item.rewardIcon" />
            <!-- 非加码任务 -->
            <p v-if="!item.isPlusTask">达标赢{{ item.rewardName }}</p>
            <!-- 加码任务 -->
            <p v-if="item.isPlusTask">
              新客赚{{ item.newUserRewardName
              }}<span>(多赚{{ item.newUserRewardCount }}{{ item.rewardTypeName }})</span>
            </p>
            <img class="task-status" src="../img/task-done.png" alt="" v-if="isAdding && item.taskStatus === '1'" />
            <img
              class="task-status"
              src="../img/task-reward.png"
              alt=""
              v-else-if="isAdding && item.taskStatus === '2'"
            />
            <img
              class="task-status"
              src="../img/task-notdone.png"
              alt=""
              v-else-if="isAdding && item.taskStatus === '0'"
            />
          </div>
          <div class="info">
            <p class="title">
              {{ item.taskTitle }}达 <span>{{ item.amountFormate }}</span
              >{{ item.unit }}
            </p>

            <!-- 单日任务的进度控制 -->
            <p class="property" v-if="index === 0 && isAdding && item.propertyText">
              {{ canShowDailyProgress ? item.propertyText : '单日资产达标挑战中' }}
            </p>
            <div class="flex progress-bar" v-if="index === 0 && item.progressText && canShowDailyProgress">
              <div class="bar-bg">
                <div class="bar-color" :style="{ width: item.progressPercent }"></div>
              </div>
              <span>{{ item.progressText }}</span>
            </div>

            <!-- 非单日任务控制 -->
            <p class="property" v-if="index !== 0 && isAdding && item.propertyText">
              {{ item.propertyText }}
            </p>
            <div class="flex progress-bar" v-if="index !== 0 && isAdding && item.progressText">
              <div class="bar-bg">
                <div class="bar-color" :style="{ width: item.progressPercent }"></div>
              </div>
              <span>{{ item.progressText }}</span>
            </div>
            <div
              class="btn"
              :class="{ disabled: item.taskStatus === '1' || !isAdding }"
              @click="clickTask(item.taskStatus)"
            >
              {{ item.taskStatus === '1' ? '待发奖' : item.taskStatus === '2' ? '查看奖励' : '去完成' }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <DialogMask :show="showDialog" :justifyContent="'flex-end'" :canScroll="'.content-wrap'">
      <template #dialog_contain>
        <div class="price-diss">
          <div class="header flex">
            <div class="btn-close" @click="hideDialog"></div>
            <p class="title">{{ dialogData.title }}</p>
          </div>
          <div class="content-wrap">
            <div class="dialog-title">
              {{ dialogData.dialogTitle }}
            </div>
            <div class="dialog-task-list">
              <div class="item flex">
                <div class="left">首次达标</div>
                <div class="center">奖励内容</div>
                <div class="right">当前状态</div>
              </div>
              <div class="item flex" v-for="(item, index) in rewardDissList" :key="index">
                <div class="left">{{ item.moneyFormateText }}</div>
                <div class="center">
                  {{ item.newUserRewardName ? item.newUserRewardName : item.rewardName }}
                  <img v-if="item.newUserRewardName" src="../img/tip_newuser_diss.png" alt="" />
                </div>
                <div
                  class="right flex"
                  :class="{ taskdone: item.taskStatus === '1', taskreward: item.taskStatus === '2' }"
                >
                  <span>{{ item.taskStatus === '2' ? '已发奖' : item.taskStatus === '1' ? '已达标' : '达标中' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </DialogMask>
  </div>
</template>

<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue'
import { EffectFade, Controller } from 'swiper'
import 'swiper/swiper.min.css'
import { ref, toRefs, toRaw, computed, inject, watchEffect, Ref, nextTick } from 'vue'
import { focusUtils, focusCore, focusServices, DialogMask } from '@focus/render'
import { useRouter } from 'vue-router'
import moneyFirstService from '../service/service'

const { jumpService, mgmService } = focusServices
const router = useRouter()
let swiperCtrl: any = null

const props = defineProps({
  showTaskLevel: {
    require: true,
    type: Number,
    default: 0, // 0 展示所有；1 展示任务 1-3；2 展示任务1-2
  },
  isAdding: {
    require: true,
    type: Boolean,
    default: false,
  },
  taskConfigList: {
    require: true,
    default(): Array<any> {
      return []
    },
  },
  canShowDailyProgress: {
    require: true,
    type: Boolean,
    default: false,
  },
  isNewUser: {
    require: true,
    type: Boolean,
    default: false,
  },
  hideTask3: {
    require: true,
    type: Boolean,
    default: false,
  },
  hideTask4: {
    require: true,
    type: Boolean,
    default: false,
  },
})

const { isAdding, canShowDailyProgress, isNewUser, hideTask3, hideTask4, showTaskLevel } = toRefs(props)
const taskTip = ['挑战一', '挑战二', '挑战三', '挑战四']
const taskTipNum = ['奖励对比①', '奖励对比②', '奖励对比③', '奖励对比④']

const configList: any = ref<any[]>([
  { fakeData: true },
  { fakeData: true },
  { fakeData: true },
  { fakeData: true },
  { fakeData: true },
  { fakeData: true },
  { fakeData: true },
  { fakeData: true },
  { fakeData: true },
  { fakeData: true },
])

const activeAid = inject<Ref<number>>('activeAid')
const batchNoList = ref<Array<string>>([])

const curStallAllPriceAmount = computed(() => {
  const targetAmountData = configList.value[activeIndex.value]
  if (props.showTaskLevel === 1) {
    return (targetAmountData && targetAmountData.curStallAllPriceAmount3) || 0
  } else if (props.showTaskLevel === 2) {
    return (targetAmountData && targetAmountData.curStallAllPriceAmount2) || 0
  }
  return (targetAmountData && targetAmountData.curStallAllPriceAmount) || 0
})

const taskList = computed(() => {
  const targetAmountData = configList.value[activeIndex.value]
  if (targetAmountData && !targetAmountData.fakeData) {
    return targetAmountData.taskList
      .map((i) => {
        if (batchNoList.value.length) {
          return {
            ...i,
            taskStatus: batchNoList.value.indexOf(i.batchNo) > -1 ? '2' : i.taskStatus,
          }
        }
        return i
      })
      .filter((i) => {
        if (hideTask3.value && i.taskId === 3) {
          return false
        }
        if (hideTask4.value && i.taskId === 4) {
          return false
        }
        return true
      })
  }
  return [{}, {}, {}, {}]
})

const rewardDissList = ref<Array<any>>([])

const showDialog = ref(false)

const dialogData = ref({
  title: '',
  dialogTitle: '',
})

const dataIsReady = ref(false)
// getDataList()

function clickTask(taskStatus: string) {
  if (!props.isAdding || taskStatus === '1') {
    return false
  }
  if (taskStatus === '2') {
    router.push({
      path: '/rewardlist',
      query: {
        rewardAids: activeAid && activeAid.value,
      },
    })
  } else {
    jumpService.commonUse.transDialog()
    // /X_OTHER_BANK_TRANS_IN_DIALOG
  }
}
const isAddingStatus = ref(false)
watchEffect(() => {
  if (props.isAdding && !isAddingStatus.value) {
    isAddingStatus.value = true
    moneyFirstService.getAwardList({ aids: [activeAid && activeAid.value] }).then((res: any) => {
      const { batchNos = [] } = res
      batchNoList.value = batchNos
    })
  }
  if (props.taskConfigList.length && !dataIsReady.value) {
    const index = props.taskConfigList.findIndex((i) => !i.taskAllDone)
    configList.value = props.taskConfigList
    dataIsReady.value = true
    nextTick(() => {
      switchNav(index)
    })
  }
})

// function getDataList() {
//   if (activeAid) {
//     moneyFirstService.getMoneyFirstTaskProgress(activeAid).then((res: any) => {
//       const { configData, first_reg_day, todayIsMonthFristDay, dataReadyInMonthFirstDay } = res
//       configList.value = configData
//       if (todayIsMonthFristDay && dataReadyInMonthFirstDay && showDataNotReady && !props.isAdding) {
//         showDataNotReady.value = true
//       }
//     })
//   }
// }

function hideDialog() {
  showDialog.value = false
}

function clickBtnDiss(data: any, index: number) {
  const { taskTitle, rewardTime, taskId } = data
  let getRewardTime = ''
  rewardDissList.value = configList.value.map((i) => {
    const { amountFormate, taskList } = i
    const targetTask = taskList?.find((_i: any) => _i.taskId === taskId)

    const { rewardName, taskDone, batchNo, unit, rewardDesc, taskStatus, newUserRewardName } = targetTask || {}

    if (!getRewardTime) {
      getRewardTime = rewardDesc
    }
    return {
      moneyFormateText: amountFormate + unit,
      rewardName,
      rewardDesc,
      taskDone,
      newUserRewardName,
      taskStatus: batchNoList.value.indexOf(batchNo) > -1 ? '2' : taskStatus,
    }
  })

  showDialog.value = true
  const dialogTitle = taskTipNum[index]
  dialogData.value = {
    title: dialogTitle,
    dialogTitle: `参与挑战的用户，${taskTitle}达到任务要求金额，即可获得奖励，${getRewardTime}。`,
  }
}

const activeIndex = ref(0)

function setSwiper(swiper: any) {
  console.log('🚀 ~ file: moneyTaskList.vue ~ line 214 ~ setSwiper ~ swiper', swiper)
  swiperCtrl = swiper
}
const onSlideChange = function (data: any) {
  const swiperData = toRaw(data)
  activeIndex.value = swiperData.activeIndex
}

const isNotFirst = computed(() => {
  return activeIndex.value !== 0
})

function switchNav(index: number) {
  const swiper = toRaw(swiperCtrl)
  swiper && swiper.slideTo(index)
}
</script>

<style lang="scss">
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.cover_mask {
  position: relative;
  &::before {
    content: '';
    width: 100%;
    height: 100%;
    background: #c7c7c7;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    transition: opacity 0.3s;
    opacity: 0;
    z-index: -1;
    border-radius: 10px;
  }
  &.data_not_ready {
    animation: shink 0.7s ease infinite;
    &::before {
      opacity: 1;
      z-index: 22;
    }
  }
}

@keyframes shink {
  from {
    opacity: 0.9;
  }
  to {
    opacity: 1;
  }
}

.money-task-list {
  width: 100%;
  min-height: 500px;
  position: relative;
  .nav {
    width: 100%;
    height: 163px;
    position: relative;
    z-index: 22;
    .item {
      box-sizing: border-box;
      font-size: 38px;
      font-weight: bold;
      line-height: 1;
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;
      text-align: center;
      flex-direction: column;
      position: relative;
      color: #845b37;
      padding-left: 30px;
      padding-bottom: 30px;
      span {
        font-size: 22px;
        margin-top: 12px;
        color: #e54434;
      }
      p.amount {
        color: #e54434;
        span {
          color: #845b37;
        }
      }
      p.total-price {
        line-height: 1.5;
        font-size: 24px;
        color: #845b37;
        letter-spacing: 0;
        font-weight: 500;
        text-align: left;
        span {
          font-size: 24px;
          color: #e54434;
          letter-spacing: 0;
          font-weight: 700;
        }
      }

      .tag {
        position: absolute;
        right: 5px;
        top: 5px;
        width: 80px;
        height: 30px;
        font-size: 0;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .swiper-wrapper {
      width: 100%;
      height: 100%;
      margin-left: -215px;
      display: flex;
      align-items: flex-end;
      position: relative;
    }
    &.isnotfirst {
      .swiper-wrapper {
        margin-left: -190px;
      }
    }
    .swiper-slide-active.item {
      width: 300px;
      height: 163px;
      background: url('../img/bg_item_big.png') no-repeat;
      background-size: 100% 100%;
      font-size: 48px;
      position: relative;
      span {
        font-size: 28px;
        margin-top: 14px;
      }
    }
  }
  .swiper-slide {
    width: 300px;
    height: 126px;
    margin: 0 3px;
    background: url('../img/bg_item_small.png') no-repeat;
    background-size: 100% 100%;
  }

  .task-list {
    width: 100%;
    background: #fff7ec;
    margin-top: 28px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    line-height: 1.5;
    padding-bottom: 30px;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 40px;
      height: 16px;
      background: url('../img/icon_triangle.png') no-repeat;
      background-size: contain;
      margin-left: -251px;
      transform: translateX(-50%);
      left: 50%;
      top: -16px;
    }

    .stall-all-price {
      width: 690px;
      height: 60px;
      background-image: linear-gradient(90deg, #ffd6ab 0%, #ffe9d0 20%, #ffd6ab 100%);
      box-shadow: 0 2px 12px 0 rgba(187, 136, 100, 0.14);
      border-radius: 10px;
      font-size: 26px;
      color: #845b37;
      letter-spacing: -1.3px;
      line-height: 1;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      box-sizing: border-box;
      padding-left: 20px;
      margin-top: 20px;
      span {
        color: #e54434;
      }
    }
    .task-item {
      padding: 30px 30px 0 30px;

      .index-tip {
        color: #867b6f;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 30px;
        margin-bottom: 22px;
        .tip_newuser {
          height: 42px;
          margin-left: 20px;
          flex: auto;
          line-height: 1;
          .bg {
            width: 299px;
            height: auto;
            background: url('../img/tip_newuser.png') no-repeat;
            background-size: 100% 100%;
            line-height: 42px;
            font-size: 22px;
            color: #fff;
            text-align: left;
            box-sizing: border-box;
            padding-left: 55px;
          }
        }
        .btn-diss {
          font-size: 24px;
          cursor: pointer;
        }
      }
      .task-content {
        border-radius: 0 0 20px 20px;
        position: relative;
        min-height: 100px;
        .reward {
          font-size: 30px;
          color: #725d47;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: 690px;
          height: 105px;
          background: url('../img/bg_reward-title.png') no-repeat;
          background-size: 100% 100%;
          font-weight: bold;
          p {
            span {
              font-size: 20px;
              padding-top: 10px;
            }
          }
        }
        .icon {
          width: 100px;
          height: 65px;
          margin-left: 18px;
          margin-right: 25px;
        }
        .task-status {
          width: 112px;
          height: 85px;
          position: absolute;
          right: 0;
          top: 20px;
        }
      }
      .info {
        width: 690px;
        background: #fff;
        border-radius: 0 0 20px 20px;
        padding: 30px;
        box-sizing: border-box;
        font-size: 28px;
        color: #725d47;
        text-align: left;
        padding-left: 30px;
        position: relative;
        box-shadow: 0px 2px 12px 0px rgba(187, 136, 100, 0.14);
        .title {
          font-weight: bold;
          span {
            color: #e54434;
          }
        }
        .property {
          font-size: 24px;
          font-weight: 400;
          color: #bababa;
          line-height: 33px;
          margin-top: 10px;
          margin-bottom: 10px;
        }
        .progress-bar {
          justify-content: flex-start;
          .bar-bg {
            width: 228px;
            height: 10px;
            background: #f0f0f0;
            border-radius: 8px;
            overflow: hidden;
            margin-right: 10px;
            .bar-color {
              height: 100%;
              width: 0;
              background: #ffc864;
            }
          }
          span {
            font-size: 24px;
            color: #d8a17c;
            line-height: 36px;
          }
        }
        .btn {
          width: 138px;
          height: 60px;
          background: linear-gradient(145deg, #538dff 0%, #4fa1ff 100%);
          border-radius: 30px;
          color: #fff;
          line-height: 60px;
          position: absolute;
          text-align: center;
          right: 30px;
          top: 50%;
          transform: translateY(-50%);
          &.disabled {
            background: linear-gradient(145deg, #dcdcdc, #bfbfbf);
          }
        }
      }
    }
  }
}
.price-diss {
  width: 100%;
  height: auto;
  max-height: 80vh;
  background-color: #fdf9f2;
  position: relative;
  padding-top: 105px;
  overflow: hidden;
  .header {
    height: 105px;
    width: 100%;
    color: #725d47;
    font-size: 30px;
    font-weight: bold;
    text-align: center;
    z-index: 2;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    &::after {
      display: block;
      content: '';
      height: 1px;
      width: 100%;
      transform: scaleY(0.5);
      background-color: #f2efeb;
      position: absolute;
      left: 0;
      bottom: 0;
    }
    .btn-close {
      width: 28px;
      height: 28px;
      position: absolute;
      left: 20px;
      top: 20px;
      transform: rotate(45deg);
      &::before,
      &::after {
        content: '';
        width: 28px;
        height: 3px;
        background: #725d47;
        position: absolute;
      }
      &::before {
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      &::after {
        height: 28px;
        width: 3px;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
  .content-wrap {
    height: 100%;
    width: 100%;
    padding-bottom: 30px;
    overflow-y: auto;
  }
  .dialog-title {
    font-size: 26px;
    font-weight: 400;
    color: #725d47;
    line-height: 39px;
    padding: 30px;
  }
  .dialog-task-list {
    width: 690px;
    margin: 0 auto;
    border-radius: 8px;
    box-shadow: 0px 2px 12px 0px rgba(187, 136, 100, 0.14);
    overflow: hidden;
    background-color: #fff;

    .item {
      width: 100%;
      box-sizing: border-box;
      height: 80px;
      font-size: 26px;
      color: #725d47;
      line-height: 80px;
      text-align: center;

      .left,
      .center,
      .right {
        flex: auto;
        border: 1px solid #f0f0f0;
        border-top: none;
        height: 100%;
      }
      .left {
        text-align: left;
        border-right: none;
        padding-left: 25px;
        width: 174px;
        flex: none;
        box-sizing: border-box;
      }
      .right {
        flex: none;
        border-left: none;
        width: 228px;
        span {
          display: block;
          width: 92px;
          height: 40px;
          background: #fff4ea;
          border-radius: 6px;
          border: 2px solid rgba(254, 165, 63, 0.6);
          color: #fc9f46;
          font-size: 24px;
          line-height: 38px;
        }
        &.taskdone {
          span {
            background: rgba(108, 211, 137, 0.15);
            border: 2px solid #6cd389;
            color: #66b97e;
          }
        }
        &.taskreward {
          span {
            background: rgba(108, 194, 211, 0.15);
            border: 2px solid #55b5ff;
            color: #55b5ff;
          }
        }
      }

      .center {
        border-top: none;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding-left: 25px;
        flex: auto;
        box-sizing: border-box;
        line-height: 1.5;

        img {
          width: 100px;
          height: 30px;
          margin-left: 10px;
        }
      }
      &:first-child {
        height: 94px;
        background: #d8d8d8 linear-gradient(180deg, #ffe8c8 0%, #ffe0a3 100%);
        border: none;
        border-radius: 8px 8px 0 0;
        line-height: 94px;
        .left,
        .center,
        .right {
          font-weight: bold;
          border-color: #f0f0f088;
        }
      }
    }
  }
}
</style>
