<template>
  <div class="product-list">
    <swiper
      class="item-wrap"
      :speed="400"
      :loop="true"
      :centeredSlides="true"
      slidesPerView="auto"
      v-if="productList.length"
      @slideChange="onSlideChange"
    >
      <swiper-slide class="prod-item" v-for="(item, index) in productList" :key="index">
        <!-- 未体验 -->
        <div class="content no_exp" @click="goAct">
          <div class="header">
            <img class="icon" src="../img/prod_status_0.png" alt="" />
            {{ item.pStatusText }}
          </div>
          <p class="name">{{ item.pName }}</p>
          <p class="rate" :class="{ up: item.unit === '+', down: item.unit === '-' }">{{ item.pRate }}</p>
          <p class="rate-text">{{ item.pRateText }}</p>
          <div class="btn dot-line">免费体验</div>
        </div>
      </swiper-slide>
    </swiper>
    <div class="dots">
      <div
        class="dot"
        :class="{ actived: activeIndex === index }"
        v-for="(item, index) in productList"
        :key="index"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { toRaw, ref, onMounted } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices } from '@focus/render'
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue'
import 'swiper/swiper.min.css'

const { productService, jumpService } = focusServices
const { modalStore } = focusStore.stores
const props = defineProps({
  propsData: {
    reqire: true,
    default() {
      return {
        configData: {
          params: [],
        },
      }
    },
  },
})
const activeIndex = ref(0)
const productList = ref([])

const onSlideChange = function (data: any) {
  const swiperData = toRaw(data)
  activeIndex.value = swiperData.realIndex
}

function goAct() {
  if (props.propsData && props.propsData.configData && props.propsData.configData.params) {
    const mainActPage = props.propsData.configData.params[1]
    jumpService.jump({ path: mainActPage })
  }
}

onMounted(() => {
  getProdList()
})
function getProdList() {
  modalStore.loadingStart('getprodlist')
  if (props.propsData && props.propsData.configData && props.propsData.configData.params) {
    const prodCodes: string = props.propsData.configData.params[0] || ''
    console.log('🚀 ~ file: M2HelperBtn.vue ~ line 88 ~ getProdList ~ prodCodes', prodCodes)
    const prodCodeList = prodCodes && prodCodes.split(',')
    productService.getProductListByCodes(prodCodeList).then((res: any) => {
      console.log('🚀 ~ file: M2HelperBtn.vue ~ line 91 ~ productService.getProductListByCodes ~ res', res)
      modalStore.loadingEnd('getprodlist')

      productList.value = res.map((i: any) => {
        const { product_name, product_code, rate_value, extra_info } = i
        const { rate_desc } = extra_info

        return {
          pName: product_name,
          pCode: product_code,
          pRate: rate_value === '--' ? '--' : rate_value.replace('-', ''),
          unit: rate_value === '--' ? '' : rate_value.indexOf('-') > -1 ? '-' : '+',
          pRateText: rate_desc,
        }
      })
    })
    // jumpService.jump({ path: m1HelpListPage, method: 'url' })
  }
}
</script>

<style lang="scss" scoped>
.dot-line {
  position: relative;
  &::before {
    position: absolute;
    content: '';
    left: -10px;
    right: -10px;
    width: 590px;
    top: -30px;
    opacity: 0.5;
    height: 1px;
    background: linear-gradient(to left, transparent 0%, transparent 50%, #777 50%, #777 100%);
    background-size: 16px 1px;
    background-repeat: repeat-x;
  }
}
.product-list {
  width: 100%;
  height: 500px;
  padding-top: 20px;

  .dots {
    margin-top: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    .dot {
      width: 10px;
      height: 10px;
      opacity: 0.3;
      background: #b4bacc;
      margin: 0 5px;
      border-radius: 100%;
      &.actived {
        opacity: 1;
      }
    }
  }
  .item-wrap :deep() {
    height: 100%;
    width: 650px;

    overflow: visible;
    .prod-item {
      font-size: 80px;
      color: red;
      height: 484px;
      padding-left: 10px;
      padding-right: 10px;
      .content {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #ffffff 0%, #fffdfc 100%);
        box-shadow: 0px 2px 16px 0px rgba(175, 98, 90, 0.17);
        border-radius: 10px;
        border-radius: 10px;

        .header {
          width: 100%;
          height: 56px;
          position: relative;
          background-image: linear-gradient(270deg, #ffccba 0%, #ffe3d5 98%);
          font-size: 24px;
          color: #93582e;
          letter-spacing: 0;
          line-height: 54px;
          border-radius: 10px 10px 0 0;
          font-weight: bold;
          box-sizing: border-box;
          padding-left: 180px;
          text-align: left;

          .icon {
            width: 168px;
            height: 90px;
            position: absolute;
            left: 10px;
            top: -10px;
          }
        }
        .name {
          font-size: 30px;
          color: #405080;
          text-align: center;
          line-height: 45px;
          margin-top: 30px;
          font-weight: bold;
        }
        .rate {
          font-size: 60px;
          color: #f05446;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
          line-height: 1.5;
          margin-top: 10px;
          &.up {
            &::before {
              content: '+';
              color: #f05446;
              font-size: 60px;
              letter-spacing: 0;
              text-align: center;
              font-weight: 500;
            }
            &::after {
              content: '%';
              color: #f05446;
              font-size: 30px;
              letter-spacing: 0;
              text-align: center;
              font-weight: 400;
            }
          }

          &.down {
            color: #30c09c;
            &::before {
              content: '-';
              color: #30c09c;
              font-size: 60px;
              letter-spacing: 0;
              text-align: center;
              font-weight: 500;
            }
            &::after {
              content: '%';
              color: #30c09c;
              font-size: 30px;
              letter-spacing: 0;
              text-align: center;
              font-weight: 400;
            }
          }
        }
        .rate-text {
          font-size: 24px;
          color: #405080;
          text-align: center;
          line-height: 36px;
        }
        .btn {
          background-image: linear-gradient(180deg, #ff9e8f 0%, #f78070 25%, #ff3824 71%, #ff8f85 100%);
          box-shadow: 0px 8px 9px 0px rgba(255, 207, 200, 1);
          border-radius: 44px;
          width: 570px;
          height: 88px;
          line-height: 86px;
          font-size: 28px;
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
          margin: 0 auto;
          margin-top: 50px;
          position: relative;
          &:active {
            opacity: 0.7;
          }
        }

        &.exp {
          .header {
            background-image: linear-gradient(270deg, #f9cfae 0%, #ffe2c0 100%);
          }
          .name {
            text-align: left;
            padding-left: 30px;
          }
          .detail {
            padding: 0 30px;
            width: 100%;
            box-sizing: border-box;
            margin-top: 50px;
            line-height: 1.5;
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            .left,
            .center,
            .right {
              width: 33.3%;
              text-align: left;

              .titles {
                font-size: 24px;
                color: #808bab;
                line-height: 36px;
              }
              .money {
                font-size: 24px;
                color: #808bab;
                line-height: 54px;
                &.up {
                  color: #f05446;
                }
                &.down {
                  color: #30c09c;
                }
              }
            }
            .center {
              text-align: center;
            }
            .right {
              text-align: right;
              .money {
                font-size: 36px;
                color: #f05446;
                font-weight: 500;
              }
            }
          }
          .buttom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 0 auto;
            margin-top: 50px;
            width: 570px;
            .profit-rate {
              background: #808bab36;
              border-radius: 4px;
              padding: 3px 10px;
              font-size: 20px;
              color: #f05446;
              line-height: 1.5;
              background: #f0544630;
            }
            .btn {
              width: 190px;
              margin: 0;
              &.btn-again {
                box-shadow: none;
                box-sizing: border-box;
                border: 2px solid #fe3925;
                color: #fe3925;
                background: #fff;
                &:active {
                  opacity: 0.7;
                }
                &.disabled {
                  opacity: 0.5;
                  &:active {
                    opacity: 0.5;
                  }
                }
              }
            }
          }
        }
        &.exp-end {
          .header {
            background-image: linear-gradient(270deg, #ffdd96 0%, #ffedcb 99%);
          }
        }
        .tip {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding-left: 30px;
          margin-top: 20px;
          .icon-tip {
            width: 36px;
            height: 40px;
            margin-right: 10px;
          }
          font-size: 22px;
          color: #93582e;
          line-height: 33px;
        }
      }
    }
  }
}
</style>
