<template>
  <div class="tip">
    <p>我正参与基金体验金活动</p>
    <p>点击下方按钮，帮我增加体验金，感恩的心～</p>
  </div>
  <div class="btns">
    <img class="hand" src="../img/icon_finger.png" :class="{ place2: M2IsHelpedM1 }" />

    <div class="btn_helper" @click="setHelper" :class="{ disabled: M2IsHelpedM1 }">
      {{ M2IsHelpedM1 ? '助力成功' : '帮TA+100元体验金' }}
    </div>
    <div class="btn_go" @click="goActPage">我也要体验金</div>
  </div>
  <div class="prod-list"></div>
</template>

<script setup lang="ts">
import { provide, readonly, ref, toRaw, computed, nextTick } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices } from '@focus/render'

const { jumpService, mgmService, productService } = focusServices
const { modalStore } = focusStore.stores
const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { sid, mgmAid } = locationHrefPared.query
const M2IsHelpedM1 = ref(false)

const props = defineProps({
  propsData: {
    reqire: true,
    default() {
      return {
        configData: {
          params: [],
        },
      }
    },
  },
})

if (sid && mgmAid) {
  modalStore.loadingStart('m2helperbtn')
  mgmService
    .checkM2HelpedM1({
      mgmAid,
      m1Sid: sid,
    })
    .then((status: number) => {
      if (status === -1) {
        // 是m1进来了，要跳走
        jumpToM1Page()
        return
      }
      modalStore.loadingEnd('m2helperbtn')

      M2IsHelpedM1.value = !!status
    })
}
function jumpToM1Page() {
  if (props.propsData && props.propsData.configData && props.propsData.configData.params) {
    const m1HelpListPage = props.propsData.configData.params[0]
    jumpService.jump({ path: m1HelpListPage })
  }
}

function setHelper() {
  if (M2IsHelpedM1.value) {
    return
  }
  mgmService.checkCurSidIsSameUrlSid(mgmAid, sid).then((flag) => {
    if (!flag) {
      modalStore.loadingStart('sethelper')
      mgmService.setM2helperM1({ mgmAid, m1Sid: sid }).then((status: any) => {
        console.log('🚀 ~ file: M2HelperBtn.vue ~ line 72 ~ mgmService.setM2helperM1 ~ status', status)
        modalStore.loadingEnd('sethelper')
        if (status) {
          modalStore.toastShow('助力成功！')
          M2IsHelpedM1.value = true
        } else {
          modalStore.toastShow('助力失败！')
        }
      })
    } else {
      modalStore.toastShow('M1不能给自己助力哦！')
    }
  })
}

function goActPage() {
  if (props.propsData && props.propsData.configData && props.propsData.configData.params) {
    const mainActPage = props.propsData.configData.params[1]
    jumpService.jump({ path: mainActPage, method: 'url' })
  }
}
</script>

<style lang="scss" scoped>
.tip {
  width: 100%;
  p {
    font-size: 28px;
    color: #405080;
    text-align: center;
    line-height: 42px;
  }
}
.btns {
  width: 100%;
  margin-top: 30px;
  position: relative;
  .hand {
    width: 200px;
    height: 200px;
    position: absolute;
    right: 50px;
    top: 50px;
    z-index: 22;
    animation: flow 0.5s infinite ease;
    &.place2 {
      top: 150px;
    }
  }
  .btn_helper {
    background-image: linear-gradient(180deg, #ff9e8f 0%, #f78070 25%, #ff3824 71%, #ff8f85 100%);
    box-shadow: 0px 8px 9px 0px rgba(255, 207, 200, 1);
    border-radius: 44px;
    width: 600px;
    height: 88px;
    line-height: 86px;
    font-size: 28px;
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
    margin: 0 auto;
    position: relative;
    &.disabled {
      opacity: 0.7;
    }
  }
  .btn_go {
    font-size: 28px;
    color: #ff422f;
    letter-spacing: 0;
    text-align: center;
    border: 2px solid rgba(254, 57, 37, 1);
    border-radius: 44px;
    line-height: 86px;
    height: 88px;
    width: 600px;
    margin: 0 auto;
    margin-top: 20px;
  }
}
@keyframes flow {
  from {
    transform: translate(0, 0);
  }
  to {
    transform: translate(-5px, -8px);
  }
}
</style>
