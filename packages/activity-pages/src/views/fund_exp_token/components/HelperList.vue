<template>
  <div class="fund-helper-list">
    <div class="item" v-for="(item, index) in listData" :key="index">
      <img :src="item.m2HeadImgUrl" alt="" class="avator" />
      <p class="name">{{ item.m2NickName }}</p>
      <!-- <div class="desc">+100元 体验金</div> -->
      <p class="time">{{ item.time }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, Ref, watchEffect } from 'vue'
import { focusStore, focusServices } from '@focus/render'

const { jumpService, mgmService } = focusServices
const { modalStore } = focusStore.stores

const props = defineProps({
  propsData: {
    reqire: true,
    default() {
      return {
        configData: {
          params: [],
        },
      }
    },
  },
})
watchEffect(() => {
  console.log('helpers......', props.propsData)
  if (props.propsData && props.propsData.configData && props.propsData.configData.params) {
    const mgmAid = props.propsData.configData.params[0]
    console.log('🚀 ~ file: HelperList.vue ~ line 31 ~ watchEffect ~ mgmAid', mgmAid)
    if (mgmAid) {
      getList(mgmAid)
    }
  }
})
const listData = ref([])
function getList(mgmAid: string) {
  modalStore.loadingStart('queryM1HelpersList')
  mgmService.queryM1HelpersList(mgmAid, 0, 1000).then((list: any) => {
    console.log('🚀 ~ file: HelperList.vue ~ line 60 ~ mgmService.queryM1HelpersList ~ res', list)
    listData.value = list
    modalStore.loadingEnd('queryM1HelpersList')
  })
}
</script>

<style lang="scss" scoped>
.fund-helper-list {
  width: 100%;
  .item {
    padding: 22px 30px;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    &::before {
      content: '';
      height: 1px;
      background: rgba(147, 49, 46, 0.26);
      opacity: 0.5;
      position: absolute;
      left: 30px;
      right: 30px;
      top: 0;
      transform: scaleY(0.5);
    }
    .avator {
      width: 70px;
      height: 70px;
      border-radius: 100%;
      background: gray;
      margin-right: 20px;
    }
    .name {
      font-size: 26px;
      color: #364a95;
      font-weight: bold;
      color: #364a95;
      flex: none;
      margin-right: 10px;
      width: 30%;
      text-align: left;
      line-height: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .desc {
      flex: none;
      background-image: linear-gradient(270deg, #ffd2afc6 0%, #ffe1c0d0 100%);
      border-radius: 8px;
      font-size: 24px;
      color: #93582e;
      line-height: 1.5;
      height: 36px;
      width: 178px;
      text-align: center;
      flex: none;
    }
    .time {
      line-height: 1.5;
      text-align: right;
      flex: auto;
      font-size: 24px;
      color: #868fb8;
      color: #868fb8;
    }
  }
}
</style>
