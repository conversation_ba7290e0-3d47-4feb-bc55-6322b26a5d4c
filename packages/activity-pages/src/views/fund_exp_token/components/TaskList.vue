<template>
  <div class="task-list">
    <div class="btn-close" @click="hide"></div>
    <img class="icon" src="../img/header_task.png" alt="" />
    <div class="tip" v-if="!isLimit">
      <b>完成下方任务即可增加相应体验金</b>，体验金上限总额为50000，达到该上限时则不继续增加体验金。
    </div>
    <div class="tip" v-if="isLimit">
      <b>您已达到体验金上限50000；</b>可继续分享邀请好友了解基金活动，但不新增体验金。
    </div>
    <div class="list">
      <div class="item" v-for="(item, index) in taskList" :key="index">
        <div class="info">
          <p class="title">{{ item.taskName }}</p>
          <div class="desc" v-html="item.taskDesc"></div>
        </div>
        <div class="btn" @click="clickTask(item)">{{ item.buttonText }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, Ref, watchEffect } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices } from '@focus/render'
const { jumpService, mgmService } = focusServices

const props = defineProps({
  hide: {
    require: true,
    default() {
      return () => {}
    },
  },
  taskList: {
    require: true,
    default(): any {
      return []
    },
  },
  isLimit: {
    require: true,
    default: false,
  },
})

const tipMsg = ref('完成下方任务即可增加相应体验金，体验金上限总额为50000，达到该上限时则不继续增加体验金。')

function hideDialog() {
  props.hide()
}

function clickTask(data: { isShare: boolean; jumpPath: string }) {
  if (data.isShare) {
    const shareData = focusCore.focusRenderCtr?.getShareConfigByConfigId(1) || {}

    mgmService.clickShare('jijin-task', shareData)
  } else {
    jumpService.jump({ path: data.jumpPath })
  }
  props && props.hide()
}
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.task-list {
  line-height: 1;
  font-size: 30px;
  width: 100%;
  background: red;
  background-image: linear-gradient(180deg, #ff665b 1%, #ffcac6 41%, #ffffff 100%);
  border-radius: 40px 40px 0px 0px;
  width: 100%;
  padding-top: 94px;
  position: relative;
  .icon {
    width: 510px;
    height: 230px;
    position: absolute;
    left: 50%;
    top: -145px;
    transform: translateX(-50%);
  }
  .btn-close {
    position: absolute;
    left: 20px;
    top: -80px;
    width: 60px;
    height: 60px;
    background: url('../img/btn-close.png') no-repeat;
    background-size: 100% 100%;
    z-index: 222;
  }

  .tip {
    background-image: linear-gradient(270deg, #ffd2af 0%, #ffe1c0 100%);
    border-radius: 10px;
    font-size: 24px;
    color: #93582e;
    line-height: 33px;
    width: 724px;
    height: 96px;
    box-sizing: border-box;
    padding: 15px 30px;
    margin: 0 auto;
  }
  .list {
    width: 726px;
    margin: 0 auto;
    margin-top: 20px;
    background-image: linear-gradient(135deg, rgba(255, 252, 252, 0.9) 0%, rgba(255, 247, 245, 0.9) 100%);
    box-shadow: 0px 2px 8px 0px rgba(175, 98, 90, 0.17);
    border-radius: 20px 20px 0px 0px;
    padding-bottom: 40px;
    .item {
      width: 100%;
      box-sizing: border-box;
      padding: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      &::after {
        content: '';

        position: absolute;
        background: #93312e3f;
        transform: scaleY(0.5);
        width: 666px;
        height: 1px;
        left: 30px;
        bottom: 0;
      }
      .info {
        flex: auto;
        .title {
          font-size: 28px;
          color: #405080;
          line-height: 42px;
          font-weight: 500;
        }
        .desc {
          font-size: 24px;
          color: #808bab;
          line-height: 36px;
          margin-right: 20px;
          // display: flex;
          // align-items: center;
          // justify-content: flex-start;
          & :deep() {
            .coin {
              width: 22px;
              height: 22px;
            }
          }
        }
      }
      .btn {
        background-image: linear-gradient(180deg, #ff9e8f 0%, #f78070 25%, #ff3824 71%, #ff8f85 100%);
        box-shadow: 0px 5px 7px 0px rgba(255, 207, 200, 1);
        border-radius: 44px;
        color: #fff;
        font-size: 28px;
        width: 170px;
        height: 70px;
        line-height: 68px;
        text-align: center;
        font-weight: bold;
        flex: none;
        &:active {
          opacity: 0.7;
        }
        &.disabled {
          opacity: 0.5;
          &:active {
            opacity: 0.5;
          }
        }
      }
    }
  }
}
</style>
