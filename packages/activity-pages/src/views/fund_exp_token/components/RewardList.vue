<template>
  <div class="reward-list">
    <div class="nav" :class="{ actived: activeIndex === 2 }">
      <div class="btn" @click="clickNav(1)">
        <p>待领取</p>
        <p class="point">
          <span>{{ pointWait }}</span> 积分
        </p>
      </div>
      <div class="btn" @click="clickNav(2)">
        <p>已领取</p>
        <p class="point">
          <span>{{ pointNow }}</span> 积分
        </p>
      </div>
      <div class="history-list"></div>
    </div>
    <div class="tip" v-if="rewardHistoryList.length && activeIndex === 1" @click.prevent="goFund">
      购买本活动中的指定产品满100元，您获得的待领取积分，即可在确认份额后的下个月初自动发放到账（指定产品为本活动中可供体验的产品）
    </div>
    <div class="tip status_2" v-if="rewardHistoryList.length && activeIndex === 2" @click.prevent="goPoint">
      <img src="../img/tip-2.png" alt="" />
    </div>
    <div class="history" v-show="rewardHistoryList.length">
      <div class="item" v-for="(item, index) in rewardHistoryList" :key="index">
        <div class="info">
          <div class="time">{{ item.time }}</div>
          <div class="name">{{ item.name }}</div>
          <div class="desc">
            <p>体验盈亏</p>
            <p class="money" :class="{ up: item.isUp, down: item.isDown }">{{ item.totalEarnings }}</p>
            <p>奖励积分</p>
            <p class="money up">{{ item.pointEarnings }}</p>
          </div>
        </div>
        <div class="btn" @click="clickBuy(item.pCode)">立即购买</div>
      </div>
    </div>
    <div class="history empty" v-show="!rewardHistoryList.length">
      <img src="../img/list-empty.png" alt="" />
      <p>您还没有参加基金体验金活动哦</p>
      <p>快去参加吧~</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, Ref, watchEffect, onMounted } from 'vue'
import { focusStore, focusServices, focusCore } from '@focus/render'
import fundExpService from '../service/service'

const { jumpService, mgmService } = focusServices
const { modalStore } = focusStore.stores

const props = defineProps({
  propsData: {
    reqire: true,
    default() {
      return {
        configData: {
          params: [],
        },
      }
    },
  },
})

const pointWait = ref('0')
const pointNow = ref('0')
const activeIndex = ref(1)
const rewardHistoryList = ref([])

function goFund() {
  jumpService.jump({ path: '/fund/HomeTabScene' })
}

onMounted(() => {
  focusCore.updateDynamicData('_custom_userGetPoint', '0', true)
})
watchEffect(() => {
  console.log('helpers......', props.propsData)
  if (props.propsData && props.propsData.configData && props.propsData.configData.params) {
    const actAid = props.propsData.configData.params[0]

    if (actAid) {
      getList(actAid)
    }
  }
})
function goPoint() {
  jumpService.commonUse.userPoint()
}
// const { configData } = btnData
//   const { params } = configData
function clickBuy(pCode: string) {
  jumpService.jump({ path: pCode, method: 'productCode' })
}

function clickNav(index: number) {
  activeIndex.value = index
}
function getList(actAid: string) {
  modalStore.loadingStart('getHistoryList')
  fundExpService.getHistoryList(actAid).then((res: any) => {
    const { historyList, totalPoint, notGetPoint, gettedPoint } = res
    rewardHistoryList.value = historyList
    const userGetPoint = totalPoint
    pointWait.value = notGetPoint
    pointNow.value = gettedPoint
    focusCore.updateDynamicData('_custom_userGetPoint', userGetPoint, true)
    modalStore.loadingEnd('getHistoryList')
  })
}
</script>

<style lang="scss" scoped>
.reward-list {
  width: 690px;
  margin: 0 auto;
  background-image: linear-gradient(180deg, #ffebd6 0%, #fcfcfc 14%, #fffdfc 100%);
  box-shadow: 0px 2px 6px 0px rgba(245, 146, 137, 0.17);
  border-radius: 14px 14px;
  padding-bottom: 30px;
  padding-top: 138px;
  position: relative;
  border: 1px solid #f5928937;
  box-sizing: border-box;
  .tip {
    background-image: linear-gradient(135deg, rgba(255, 237, 233, 0.9) 0%, rgba(255, 233, 212, 0.9) 100%);
    border: 3px solid rgba(255, 252, 251, 1);
    width: 630px;
    // height: 140px;
    box-sizing: border-box;
    padding: 16px 30px;
    font-size: 24px;
    line-height: 1.5;
    color: #405080;
    font-weight: 400;
    text-align: left;
    margin: 0 auto;
    margin-top: 10px;
    &.status_2 {
      width: 662px;
      height: 171px;
      padding: 0;
      border: none;
      background: none;
      margin-top: 0;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .btn-go {
      font-size: 24px;
      color: #456ce6;
      display: inline-block;
      cursor: pointer;
      &:active {
        opacity: 0.7;
      }
    }
  }
  .nav {
    width: 705px;
    height: 141px;
    background-image: url('../img/tab-02.png');

    background: url('../img/tab-01.png') no-repeat;
    background-size: 100% 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -10px;
    left: -10px;
    cursor: pointer;
    &.actived {
      background-image: url('../img/tab-02.png');
    }

    .btn {
      width: 50%;
      cursor: pointer;

      p {
        font-size: 28px;
        color: #405080;
        text-align: center;
        line-height: 42px;
        font-weight: bold;
        &.point {
          font-size: 26px;
          color: #f05446;
          line-height: 54px;
          font-weight: 400;
          span {
            font-size: 36px;
            font-weight: bold;
          }
        }
      }
    }
  }
  .history {
    width: 100%;
    &.empty {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 100%;
      height: 400px;
      img {
        width: 160px;
        height: 100px;
        margin-bottom: 10px;
      }
      p {
        font-size: 24px;
        color: #808bab;
        text-align: center;
        line-height: 36px;
      }
    }
    .item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 30px;
      box-sizing: border-box;
      text-align: left;
      position: relative;
      &::after {
        content: '';
        position: absolute;
        left: 30px;
        right: 30px;
        bottom: 0;
        height: 1px;
        transform: scaleY(0.5);
        background-color: #93312e;
        opacity: 0.5;
      }
      .info {
        flex: auto;
        font-size: 24px;
        color: #405080;
        line-height: 36px;
        .name {
          font-weight: bold;
          margin-bottom: 15px;
          margin-top: 15px;
        }
        .desc {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          color: #808bab;
          .money {
            margin-left: 10px;
            margin-right: 40px;
            &.down {
              color: #30c09c;
            }
            &.up {
              color: #f05446;
            }
          }
        }
      }
      .btn {
        background-image: linear-gradient(180deg, #ff9e8f 0%, #f78070 25%, #ff3824 71%, #ff8f85 100%);
        box-shadow: 0px 5px 7px 0px rgba(255, 207, 200, 1);
        border-radius: 44px;
        width: 150px;
        height: 60px;
        line-height: 58px;
        color: #fff;
        font-size: 26px;
        text-align: center;
        font-weight: 500;
      }
    }
  }
}
</style>
