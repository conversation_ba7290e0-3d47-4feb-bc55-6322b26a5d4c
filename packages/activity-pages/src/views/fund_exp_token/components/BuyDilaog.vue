<template>
  <div class="buying">
    <div class="header">
      <div class="btn-close" @click="hideDialog"></div>
      <p class="title">选择体验金</p>
    </div>
    <div class="content">
      <p class="top">请选择体验金额</p>
      <div class="list">
        <div
          class="item"
          v-for="(item, index) in moneyList"
          :key="index"
          @click="selectPrice(item)"
          :class="{
            active: selectedPrice === item.value,
            disabled: userExpBalance < item.value || userExpBalance === 0,
          }"
        >
          {{ item.money }}
        </div>
      </div>
    </div>
    <div class="footer">
      <div class="btn" @click="clickBuy" :class="{ disabled: !selectedPrice }">确认</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, Ref } from 'vue'
const props = defineProps({
  hide: {
    require: true,
    default() {
      return () => {}
    },
  },
  userExpBalance: {
    require: true,
    type: Number,
    default: 0,
  },
  doBuy: {
    require: true,
    type: Function,
    default() {
      return () => {}
    },
  },
})
const moneyList = ref([
  { money: '1,000元', value: 1000 },
  { money: '5,000元', value: 5000 },
  { money: '10,000元', value: 10000 },
  { money: '15,000元', value: 15000 },
  { money: '20,000元', value: 20000 },
  { money: '50,000元', value: 50000 },
  { money: '全部体验金', value: 'all' },
])

const selectedPrice = ref<string | number>('')
function selectPrice(data: { money: string; value: number | string }) {
  if (props.userExpBalance < data.value || props.userExpBalance === 0) {
    return
  }
  selectedPrice.value = data.value || 0
}
function clickBuy() {
  if (!selectedPrice.value) {
    return false
  }
  props.doBuy(selectedPrice.value)
}
function hideDialog() {
  selectedPrice.value = 0
  props.hide()
}
</script>

<style lang="scss" scoped>
.buying {
  background: #fff;
  width: 100%;
  .header {
    height: 88px;
    width: 100%;
    border-bottom: 1px solid #f0f0f0;
    font-size: 32px;
    line-height: 86px;
    color: #405080;
    text-align: center;
    position: relative;
    .btn-close {
      background: url('../img/btn-close-line.png') no-repeat;
      background-size: 100% 100%;
      height: 28px;
      width: 28px;
      position: absolute;
      left: 30px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .content {
    padding-top: 30px;
    p {
      color: #405080;
      font-size: 32px;
      text-align: left;
      padding-left: 30px;
    }
    .list {
      width: 100%;
      box-sizing: border-box;
      padding: 20px 30px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: wrap;
      margin-left: -20px;
      padding-right: 0;
      padding-bottom: 230px;

      .item {
        width: 217px;
        height: 98px;
        // margin-right: 10px;
        margin-left: 20px;
        line-height: 96px;
        font-size: 28px;
        color: #808bab;
        background: rgba(218, 221, 230, 0.2);
        border-radius: 4px;
        text-align: center;
        margin-top: 20px;
        box-sizing: border-box;
        border: 2px solid rgba(218, 221, 230, 0.2);
        &.active {
          background: #fdf4ef;
          border: 2px solid rgba(242, 150, 97, 1);
          border-radius: 4px;
          color: #f29661;
        }
        &.disabled {
          background: rgba(218, 221, 230, 0.08);
          border: 2px solid rgba(218, 221, 230, 0.08);
          color: #dadde6;
        }
      }
    }
  }
  .footer {
    border-top: 1px solid #f0f0f0;
    height: 148px;
    padding: 30px;
    .btn {
      width: 690px;
      height: 88px;
      line-height: 86px;
      font-size: 32px;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      background: #f29661;
      border-radius: 44px;
      &:active {
        opacity: 0.8;
      }
      &.disabled {
        background: #aeaeae;
        &:active {
          opacity: 1;
        }
      }
    }
  }
}
</style>
