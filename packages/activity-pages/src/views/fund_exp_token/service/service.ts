import { focusStore, focusServices, focusCore, focusUtils } from '@focus/render'
console.log('🚀 ~ file: service.ts ~ line 2 ~ focusUtils', focusUtils)
const { dayjs, formatAmount } = focusUtils
const { mgmService, kvService } = focusServices

const cgis = {
  getActInfo: {
    url: '/wm-htrserver/hj/experienceGold/GetActivityPageInfo',
    name: '体验金信息',
  },
  expBuyFund: {
    url: '/wm-htrserver/hj/experienceGold/ExperienceBuy',
    name: '体验金购买基金',
  },
  awardList: {
    url: '/wm-htrserver/cop/hj/query_user_award',
    name: '资产达标奖励',
  },
  rewardHistoryList: {
    url: '/wm-htrserver/hj/experienceGold/GetAwardPageInfo',
    name: '基金体验金-奖励记录',
  },
  getShareTimes: {
    url: '/hjop/welfare/query_user_share_count',
    name: '获取分享次数',
  },
}

class FunExpTokenService {
  getActInfo(tokenCode: string) {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getActInfo, {
          ccy_code: tokenCode,
        })
        .then((res: any) => {
          const {
            product_list = [],
            experience_gold_list = [],
            residue_quota = 0,
            user_coin_sum = 0,
            coin_all_limit = 0, // 总上限
            coin_day_limit = 0,
          } = res
          const expList = experience_gold_list.sort((a: any, b: any) => {})
          resolve({
            isLimitCoins: user_coin_sum === coin_all_limit, // 用户最高的体验金数量
            expToken: formatAmount(residue_quota, true),
            userExpBalance: residue_quota,
            productList: product_list
              .map((i: any) => {
                const { product_name, product_code, rate_value, extra_info } = i
                const { rate_desc } = extra_info
                const expTarget =
                  expList.find(
                    (i: {
                      balance: number // 体验的金额
                      ccyCode: string
                      endDate: string
                      experienceRate: number // 体验的收益率
                      experienceStatus: string // '1' 体验中； '2' 体验结束
                      lastDayEarnings: number //昨日收益
                      productCode: string
                      startDate: string
                      totalEarnings: number //总收益
                    }) => {
                      return i.productCode === product_code
                    }
                  ) || {}
                const { experienceStatus, lastDayEarnings, totalEarnings, experienceRate, balance, endDate } = expTarget
                console.log(
                  '🚀 ~ file: service.ts ~ line 71 ~ FunExpTokenService ~ .map ~ experienceRate',
                  experienceRate
                )
                const noRateData = experienceRate === undefined
                const isUp = totalEarnings > 0
                let pStatusText = ''
                if (experienceStatus === '1') {
                  pStatusText = dayjs(endDate).format('M月D日结束')
                }

                const totalIsUp = totalEarnings > 0
                const totalIsDown = totalEarnings < 0
                if (experienceStatus === '2') {
                  if (totalIsUp) {
                    pStatusText = '太棒啦，快去领取奖励吧 >'
                  } else {
                    pStatusText = '市场有涨有跌，逢低加仓是制胜秘诀'
                  }
                }
                const yestodayIsDown = experienceStatus === '1' && lastDayEarnings < 0
                const yestodayIsUp = experienceStatus === '1' && lastDayEarnings > 0
                const yestodayProfitLoss =
                  experienceStatus === '2'
                    ? '体验已结束'
                    : yestodayIsUp
                    ? '+' + formatAmount(lastDayEarnings)
                    : formatAmount(lastDayEarnings)
                const totalProfitLoss = totalIsUp ? '+' + formatAmount(totalEarnings) : formatAmount(totalEarnings)
                const tipText = totalIsDown ? '市场有跌也有涨，逢低加仓、低买高卖是制胜秘诀~' : ''
                return {
                  pName: product_name,
                  pCode: product_code,
                  pRate: rate_value === '--' ? '--' : rate_value.replace('-', ''),
                  pStatus: experienceStatus ? experienceStatus : '0',
                  pRateText: rate_desc,
                  unit: rate_value === '--' ? '' : rate_value.indexOf('-') > -1 ? '-' : '+',
                  yestodayProfitLoss,
                  totalProfitLoss,
                  profRate: `${!noRateData ? experienceRate : '--'}%`,
                  expAmount: formatAmount(balance, true),
                  pStatusText,
                  totalIsUp,
                  totalIsDown,
                  yestodayIsDown,
                  yestodayIsUp,
                  noRateData,
                  tipText,
                }
              })
              .sort((a: any, b: any) => {
                console.log('🚀 ~ 第一个', a.pStatus)
                console.log('🚀 ~ 第二个', b.pStatus)
                if (a.pStatus === '1' && b.pStatus !== '1') {
                  return -1
                }
                if (a.pStatus !== '1' && b.pStatus === '1') {
                  return 1
                }
                return 0
              }),
          })
        })
        .catch(() => {
          resolve({
            expToken: '0.00',
            userExpBalance: 0,
            productList: [],
          })
        })
    })
  }

  expBuyFund(productCode: string, tokenCode: string, amount: number) {
    return new Promise((resolve, reject) => {
      focusCore
        .request(cgis.expBuyFund, {
          ccyCode: tokenCode,
          channel: 'HJ',
          productCode: productCode,
          transAmount: amount,
        })
        .then((res: any) => {
          console.log('🚀 ~ file: service.ts ~ line 81 ~ FunExpTokenService ~ .then ~ res', res)
          resolve(res)
        })
        .catch(() => {
          reject()
        })
    })
  }

  getShareTimes() {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getShareTimes, { share_type: 'FUND_EXPERIENCE_SHARE_PRODUCT' })
        .then((res: any) => {
          const { currShareCount = 0 } = res
          return resolve(currShareCount)
        })
        .catch(() => {
          resolve(0)
        })
    })
  }

  getHistoryList(aid: string) {
    return new Promise((resolve, reject) => {
      focusCore
        .request(cgis.rewardHistoryList, {
          ccy_code: 'TYJ2023',
          activity_id: aid,
        })
        .then((res: any) => {
          console.log('🚀 ~ file: service.ts ~ line 115 ~ FunExpTokenService ~ returnnewPromise ~ res', res)
          const { experience_gold_list = [], points_issued = 0, total_point = 0 } = res
          const notGetPoint = total_point - points_issued
          return resolve({
            totalPoint: total_point,
            notGetPoint,
            gettedPoint: points_issued,
            historyList: experience_gold_list.map((i: any) => {
              const { balance, startDate, endDate, productDesc, pointEarnings = 0, totalEarnings, productCode } = i
              const isDown = !!(totalEarnings < 0)
              const isUp = !!(totalEarnings > 0)
              return {
                balance,
                time: `${dayjs(startDate).format('YYYY-MM-DD')}~${dayjs(endDate).format('YYYY-MM-DD')}`,
                name: productDesc || '啦啦啦测试',
                isDown,
                isUp,
                totalEarnings: totalEarnings > 0 ? `+${totalEarnings}` : totalEarnings,
                pointEarnings,
                pCode: productCode,
              }
            }),
          })
        })
        .catch(() => {
          reject()
        })
    })
  }
}
export default new FunExpTokenService()
