import homeVue from '../pages/home.vue'
import subpageVue from '../pages/subpage.vue'
import { createRouter, createWebHashHistory } from 'vue-router'
import { focusCore, focusServices } from '@focus/render'
const { loginService } = focusServices
const routes = [
  {
    path: '/',
    redirect: '/home',
    meta: {
      title: '基金体验金',
      noNeedLogin: true,
    },
  },
  {
    path: '/home',
    component: homeVue,
    meta: {
      title: '基金体验金',
      noNeedLogin: true,
    },
  },
  {
    path: '/subpage',
    component: subpageVue,
    meta: {
      title: '',
    },
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes, // `routes: routes` 的缩写
})
router.beforeEach((to, from, next) => {
  console.log('🚀 ~ file: index.ts ~ line 33 ~ router.beforeEach ~ to', to)
  const { query } = to
  const { title, noNeedLogin = false } = to.meta
  focusCore.setPageTitle(title)
  if (noNeedLogin && to.path !== '/home') {
    loginService
      .checkLogined()
      .then((res: any) => {
        const { error } = res
        if (error === '401') {
          return next({
            path: '/home',
            query: {
              fid: query.fid,
            },
          })
        }
        next()
      })
      .catch(() => {
        next('/home')
      })
  } else {
    next()
  }
})
router.afterEach((to, from, next) => {})

export default router
