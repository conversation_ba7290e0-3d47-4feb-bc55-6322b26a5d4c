<template>
  <focus-render v-if="focusFid" :focusFid="focusFid" @onLoginSucc="afterLogin" @onFocusConfigSucc="setFocus">
    <!-- M1的助力列表 -->
    <template #fund_helper_list="slotProps" v-if="isLogined">
      <HelperList :propsData="slotProps"></HelperList
    ></template>

    <!-- 奖励列表 -->
    <template #fund_reward_list="slotProps" v-if="isLogined">
      <RewardList :propsData="slotProps"></RewardList
    ></template>

    <!-- m2助力按钮 -->
    <template #fund_helper="slotProps" v-if="isLogined"> <M2HelperBtn :propsData="slotProps"></M2HelperBtn></template>

    <!-- m2 助力页的产品列表 -->
    <template #fund_prod_list="slotProps" v-if="isLogined">
      <ProductList :propsData="slotProps"></ProductList
    ></template>
  </focus-render>
</template>

<script setup lang="ts">
import { ref, provide } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import HelperList from '../components/HelperList.vue'
import RewardList from '../components/RewardList.vue'
import M2HelperBtn from '../components/M2HelperBtn.vue'
import ProductList from '../components/ProdList.vue'
const { modalStore } = focusStore.stores
const { activityService, taskService, jumpService } = focusServices

const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { pageid, aid, fid } = locationHrefPared.query
const focusFid = fid || pageid

const isLogined = ref(false)

function afterLogin(loginData: any) {
  if (focusCore.isLogined) {
    isLogined.value = true
  }
}

function setFocus(data: any) {}
</script>

<style lang="scss" scoped></style>
