<template>
  <focus-render v-if="focusFid" :focusFid="focusFid" @onLoginSucc="afterLogin" @onFocusConfigSucc="setFocus">
    <template #fund_exp_token="slotProps">
      {{ setSlotData(slotProps) }}
      <div class="product-list">
        <swiper
          class="item-wrap"
          :speed="400"
          :loop="true"
          :centeredSlides="true"
          slidesPerView="auto"
          v-if="productList.length"
          @slideChange="onSlideChange"
        >
          <swiper-slide class="prod-item" v-for="(item, index) in productList" :key="index">
            <!-- 体验中 -->
            <div class="content exp" v-if="item.pStatus === '1'" @click="goFund(item.pCode)">
              <div class="header">
                <img class="icon" src="../img/prod_status_1.png" alt="" />
                {{ item.pStatusText }}
              </div>
              <p class="name">{{ item.pName }}</p>
              <div class="detail">
                <div class="left">
                  <p class="titles">昨日盈亏</p>
                  <p class="money" :class="{ up: item.yestodayIsUp, down: item.yestodayIsDown }">
                    {{ item.noRateData ? '待更新' : item.yestodayProfitLoss }}
                  </p>
                </div>
                <div class="center">
                  <p class="titles">体验盈亏</p>
                  <p class="money" :class="{ up: item.totalIsUp, down: item.totalIsDown }">
                    {{ item.noRateData ? '待更新' : item.totalProfitLoss }}
                  </p>
                </div>
                <div class="right">
                  <p class="titles">体验金额</p>
                  <p class="money">{{ item.expAmount }}</p>
                </div>
              </div>
              <div class="dot-line buttom">
                <p class="profit-rate" :class="{ up: item.totalIsUp, down: item.totalIsDown }">
                  体验收益率{{ item.profRate }}
                </p>
                <div class="btn" @click.stop="goFund(item.pCode)">直接购买</div>
              </div>
            </div>

            <!-- 体验结束 -->
            <div class="content exp exp-end" v-if="item.pStatus === '2'" @click="goFund(item.pCode)">
              <div class="header">
                <img class="icon" src="../img/prod_status_2.png" alt="" />
                {{ item.pStatusText }}
              </div>
              <p class="name">{{ item.pName }}</p>
              <div class="detail">
                <div class="left">
                  <p class="titles">昨日盈亏</p>
                  <p class="money" :class="{ up: item.yestodayIsUp, down: item.yestodayIsDown }">
                    {{ item.noRateData ? '待更新' : item.yestodayProfitLoss }}
                  </p>
                </div>
                <div class="center">
                  <p class="titles">体验盈亏</p>
                  <p class="money" :class="{ up: item.totalIsUp, down: item.totalIsDown }">
                    {{ item.noRateData ? '待更新' : item.totalProfitLoss }}
                  </p>
                </div>
                <div class="right">
                  <p class="titles">体验金额</p>
                  <p class="money">{{ item.expAmount }}</p>
                </div>
              </div>
              <div class="dot-line buttom">
                <p class="profit-rate" :class="{ up: item.totalIsUp, down: item.totalIsDown }">
                  体验收益率{{ item.profRate }}
                </p>
                <div class="btn btn-again" @click.stop="clickProduct(item)" :class="{ disabled: !userExpBalance }">
                  再次体验
                </div>
                <div class="btn" @click.stop="goFund(item.pCode)">直接购买</div>
              </div>
              <!-- <div class="tip" v-if="item.tipText">
                <img src="../img/icon_tip.png" alt="" class="icon-tip" />{{ item.tipText }}
              </div> -->
            </div>

            <!-- 未体验 -->
            <div class="content no_exp" v-if="item.pStatus === '0'" @click="goFund(item.pCode)">
              <div class="header">
                <img class="icon" src="../img/prod_status_0.png" alt="" />
                {{ item.pStatusText }}
              </div>
              <p class="name">{{ item.pName }}</p>
              <p class="rate" :class="{ up: item.unit === '+', down: item.unit === '-' }">{{ item.pRate }}</p>
              <p class="rate-text">{{ item.pRateText }}</p>
              <div class="btn dot-line" @click.stop="clickProduct(item)">免费体验</div>
            </div>
          </swiper-slide>
        </swiper>
        <div class="dots">
          <div
            class="dot"
            :class="{ actived: activeIndex === index }"
            v-for="(item, index) in productList"
            :key="index"
          ></div>
        </div>
      </div>
    </template>
  </focus-render>

  <DialogMask :show="showTaskDialog" :justifyContent="'flex-end'">
    <template #dialog_contain>
      <task-list :hide="hideTaskList" :taskList="curTaskList" :isLimit="isLimit"></task-list>
    </template>
  </DialogMask>
  <DialogMask :show="showBuyDialog" :justifyContent="'flex-end'">
    <template #dialog_contain>
      <BuyDialog :hide="hideBuyDialog" :userExpBalance="userExpBalance" :doBuy="doBuy"></BuyDialog>
    </template>
  </DialogMask>
</template>

<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue'
import { EffectFade, Controller } from 'swiper'
import 'swiper/swiper.min.css'
import { provide, readonly, ref, toRaw, computed, nextTick } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import fundExpService from '../service/service'
import TaskList from '../components/TaskList.vue'
import BuyDialog from '../components/BuyDilaog.vue'
const { modalStore } = focusStore.stores
const { activityService, taskService, jumpService } = focusServices

const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { pageid, aid, fid } = locationHrefPared.query
const focusFid = fid || pageid
let focusRenderCtr: any = null
const showTaskDialog = ref(false)
const showBuyDialog = ref(false)
const activityId = 9148
const tokenCode = 'TYJ2023'
let taskConfigId = 6
if (BUILD_TEST) {
  taskConfigId = 5
}
const userExpBalance = ref(0)
const curTaskList = ref([])
const coinUrl = require('../img/icon_coin.png')
const isLimit = ref(false)
const productList = ref<
  {
    pName: string
    pCode: string
    pRate: string
    pStatus: string
    pStatusText: string
    pRateText: string
    yestodayProfitLoss?: string
    totalProfitLoss?: string
    profRate?: string
    expAmount?: string
    unit?: string
    totalIsUp: boolean
    totalIsDown: boolean
    yestodayIsDown: boolean
    yestodayIsUp: boolean
    noRateData: boolean
    tipText: string
  }[]
>([])
const activeIndex = ref(0)

const clickProductCode = ref('')

function hideTaskList() {
  showTaskDialog.value = false
}
function hideBuyDialog() {
  showBuyDialog.value = false
}

const onSlideChange = function (data: any) {
  const swiperData = toRaw(data)
  activeIndex.value = swiperData.realIndex
}

function clickProduct(prodData: { pName: string; pCode: string; pRate: string }) {
  console.log('🚀 ~ file: home.vue ~ line 54 ~ clickProduct ~ prodData', prodData)
  if (userExpBalance.value === 0) {
    showTaskDialog.value = true
    return
  }
  showBuyDialog.value = true
  clickProductCode.value = prodData.pCode
}

function setSlotData(slotData: any) {
  console.log('🚀 ~ file: home.vue ~ line 200 ~ setSlotData ~ data', slotData)
  if (slotData && slotData.configData && slotData.configData.params) {
    const btnTaskModuleId = slotData.configData.params[0]
    focusCore.onBtnClick(btnTaskModuleId, (data: { clickEvent: { path: string } }) => {
      // 拉起浮层
      showTaskDialog.value = true
    })
  }
}

function afterLogin(data: any) {
  modalStore.loadingStart('setwrinfo')

  if (focusCore.isLogined) {
    activityService.setWrInfo(activityId).then((aid: number) => {
      if (aid) {
      }
      modalStore.loadingEnd('setwrinfo')
      setTimeout(() => {
        getActInfo()
      }, 200)
    })
  }
}

function setFocus(_focusRenderCtr: any) {
  focusRenderCtr = _focusRenderCtr
  focusCore.updateDynamicData('_custom_expToken', '0.00', true)
}

function getActInfo() {
  modalStore.loadingStart('getActInfo')

  // 获取活动信息
  fundExpService
    .getActInfo(tokenCode)
    .then((res: any) => {
      console.log('🚀 ~ file: home.vue ~ line 36 ~ fundExpService.getActInfo ~ res', res)
      const { expToken = '0.00', isLimitCoins } = res
      focusCore.updateDynamicData('_custom_expToken', expToken, true)
      productList.value = res.productList
      userExpBalance.value = res.userExpBalance || 0
      isLimit.value = isLimitCoins
    })
    .catch(() => {
      modalStore.errorMaskContrl('getactinfo')
    })
    .finally(() => {
      modalStore.loadingEnd('getActInfo')
    })

  taskService.getTaskProgressByConfigId(taskConfigId).then(async (res: any) => {
    const { taskList = [] } = res || {}
    console.log('🚀 ~ file: home.vue ~ line 162 ~ taskService.getTaskProgressByConfigId ~ res', res)
    if (!taskList.length) {
      return false
    }
    const shareTimes = await getShareTimes()
    const heightLight = (string: string, curCoin: string) => {
      const style = 'color=#ec6b7b;'
      const str = string
        .replace('{coin}', `<img class="coin" src="${coinUrl}" />`)
        .replace('{userGetAmt}', `{${curCoin}}`)
        .replace('{shareTimes}', `{${shareTimes}}`)
        .replace(/{/g, '<span class="height-light" style="color:#ec6b7b;">')
        .replace(/}/g, '</span>')

      return `${str}`
    }
    curTaskList.value = taskList.map((i) => {
      const { taskName, taskLotteryDesc, buttonText, isShare, jumpPath, currTransCoin } = i

      return {
        taskName,
        taskDesc: heightLight(taskLotteryDesc, currTransCoin),
        isShare,
        jumpPath,
        buttonText,
      }
    })
    console.log(
      '🚀 ~ file: home.vue ~ line 171 ~ curTaskList.value=taskList.mpa ~ curTaskList.value',
      curTaskList.value
    )
  })
}

const shareTimes: any = ref(undefined)
async function getShareTimes() {
  if (shareTimes.value !== undefined) {
    return shareTimes.value
  }
  shareTimes.value = await fundExpService.getShareTimes()
  return shareTimes.value
}

function doBuy(amount: number) {
  modalStore.loadingStart('dobuy')

  const productCode = clickProductCode.value
  const _amount: number = amount.toString() === 'all' ? userExpBalance.value : amount
  fundExpService
    .expBuyFund(productCode, tokenCode, _amount)
    .then((res) => {
      console.log('🚀 ~ file: home.vue ~ line 151 ~ fundExpService.expBuyFund ~ res', res)
      modalStore.toastShow('购买成功')

      getActInfo()
    })
    .catch(() => {
      modalStore.toastShow('购买失败！请重试！')
    })
    .finally(() => {
      modalStore.loadingEnd('dobuy')
      showBuyDialog.value = false
    })
}

function goFund(pCode: string) {
  jumpService.jump({ path: pCode, method: 'productCode' })
}
</script>

<style lang="scss" scoped>
.dot-line {
  position: relative;
  &::before {
    position: absolute;
    content: '';
    left: -10px;
    right: -10px;
    width: 590px;
    top: -30px;
    opacity: 0.5;
    height: 1px;
    background: linear-gradient(to left, transparent 0%, transparent 50%, #777 50%, #777 100%);
    background-size: 16px 1px;
    background-repeat: repeat-x;
  }
}
.product-list {
  width: 100%;
  height: 500px;
  padding-top: 20px;

  .dots {
    margin-top: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    .dot {
      width: 10px;
      height: 10px;
      opacity: 0.3;
      background: #b4bacc;
      margin: 0 5px;
      border-radius: 100%;
      &.actived {
        opacity: 1;
      }
    }
  }
  .item-wrap :deep() {
    height: 100%;
    width: 650px;

    overflow: visible;
    .prod-item {
      font-size: 80px;
      color: red;
      height: 484px;
      padding-left: 10px;
      padding-right: 10px;
      .content {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #ffffff 0%, #fffdfc 100%);
        box-shadow: 0px 2px 16px 0px rgba(175, 98, 90, 0.17);
        border-radius: 10px;
        border-radius: 10px;

        .header {
          width: 100%;
          height: 56px;
          position: relative;
          background-image: linear-gradient(270deg, #ffccba 0%, #ffe3d5 98%);
          font-size: 24px;
          color: #93582e;
          letter-spacing: 0;
          line-height: 54px;
          border-radius: 10px 10px 0 0;
          font-weight: bold;
          box-sizing: border-box;
          padding-left: 180px;
          text-align: left;

          .icon {
            width: 168px;
            height: 90px;
            position: absolute;
            left: 10px;
            top: -10px;
          }
        }
        .name {
          font-size: 30px;
          color: #405080;
          text-align: center;
          line-height: 45px;
          margin-top: 30px;
          font-weight: bold;
        }
        .rate {
          font-size: 60px;
          color: #f05446;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
          line-height: 1.5;
          margin-top: 10px;
          &.up {
            &::before {
              content: '+';
              color: #f05446;
              font-size: 60px;
              letter-spacing: 0;
              text-align: center;
              font-weight: 500;
            }
            &::after {
              content: '%';
              color: #f05446;
              font-size: 30px;
              letter-spacing: 0;
              text-align: center;
              font-weight: 400;
            }
          }

          &.down {
            color: #30c09c;
            &::before {
              content: '-';
              color: #30c09c;
              font-size: 60px;
              letter-spacing: 0;
              text-align: center;
              font-weight: 500;
            }
            &::after {
              content: '%';
              color: #30c09c;
              font-size: 30px;
              letter-spacing: 0;
              text-align: center;
              font-weight: 400;
            }
          }
        }
        .rate-text {
          font-size: 24px;
          color: #405080;
          text-align: center;
          line-height: 36px;
        }
        .btn {
          background-image: linear-gradient(180deg, #ff9e8f 0%, #f78070 25%, #ff3824 71%, #ff8f85 100%);
          box-shadow: 0px 8px 9px 0px rgba(255, 207, 200, 1);
          border-radius: 44px;
          width: 570px;
          height: 88px;
          line-height: 86px;
          font-size: 28px;
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
          margin: 0 auto;
          margin-top: 50px;
          position: relative;
          &:active {
            opacity: 0.7;
          }
        }

        &.exp {
          .header {
            background-image: linear-gradient(270deg, #f9cfae 0%, #ffe2c0 100%);
          }
          .name {
            text-align: left;
            padding-left: 30px;
          }
          .detail {
            padding: 0 30px;
            width: 100%;
            box-sizing: border-box;
            margin-top: 50px;
            line-height: 1.5;
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            .left,
            .center,
            .right {
              width: 33.3%;
              text-align: left;

              .titles {
                font-size: 24px;
                color: #808bab;
                line-height: 36px;
              }
              .money {
                font-size: 24px;
                color: #808bab;
                line-height: 54px;
                &.up {
                  color: #f05446;
                }
                &.down {
                  color: #30c09c;
                }
              }
            }
            .center {
              text-align: center;
            }
            .right {
              text-align: right;
              .money {
                font-size: 36px;
                color: #f05446;
                font-weight: 500;
              }
            }
          }
          .buttom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 0 auto;
            margin-top: 50px;
            width: 570px;
            .profit-rate {
              background: #808bab36;
              border-radius: 4px;
              padding: 3px 10px;
              font-size: 20px;
              line-height: 1.5;
              color: #405080;
              &.up {
                color: #f05446;
                background: #f0544630;
              }
              &.down {
                color: #30c09c;
                background: #65f04630;
              }
            }
            .btn {
              width: 190px;
              margin: 0;
              &.btn-again {
                box-shadow: none;
                box-sizing: border-box;
                border: 2px solid #fe3925;
                color: #fe3925;
                background: #fff;
                &:active {
                  opacity: 0.7;
                }
                &.disabled {
                  opacity: 0.5;
                  &:active {
                    opacity: 0.5;
                  }
                }
              }
            }
          }
        }
        &.exp-end {
          .header {
            background-image: linear-gradient(270deg, #ffdd96 0%, #ffedcb 99%);
          }
        }
        .tip {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding-left: 30px;
          margin-top: 20px;
          .icon-tip {
            width: 36px;
            height: 40px;
            margin-right: 10px;
          }
          font-size: 22px;
          color: #93582e;
          line-height: 33px;
        }
      }
    }
  }
}
</style>
