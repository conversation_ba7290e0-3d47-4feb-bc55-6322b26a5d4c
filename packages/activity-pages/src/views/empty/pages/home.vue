<template>
  <focus-render :needLogin="false"></focus-render>
</template>

<script setup lang="ts">
import { provide, readonly, ref, toRaw, computed, nextTick, watch, watchEffect } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import userEquitySetvice from '../service/service'

const { modalStore } = focusStore.stores
const { activityService, jumpService } = focusServices
// const activeAid = ref(0)
// provide('activeAid', activeAid)
const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { fid, ap } = locationHrefPared.query
// const focusFid = fid
focusCore.eventLog('emptypage')
const hasAccount = ref(false)
if (ap) {
  if (!/javascript/.test(ap)) {
    jumpService.jump({
      method: 'appModule',
      path: ap,
    })
  }
}

watchEffect(async () => {
  // console.log('activeAid.value', activeAid.value)
  if (hasAccount.value) {
    // console.log('activeAid.value in true', activeAid.value)
  }
})

function afterLogin(data: any) {
  if (focusCore.isLogined) {
    hasAccount.value = true
  }
}
</script>

<style lang="scss" scoped></style>
