import { formatAmount } from '@/utils'
import { focusStore, focusServices, focusCore, focusUtils } from '@focus/render'
console.log('🚀 ~ file: service.ts ~ line 2 ~ focusUtils', focusUtils)
const { entitiestoUtf16, dayjs } = focusUtils
const { mgmService, kvService } = focusServices
const defaultAvator = require('../img/default_avator.png')
const cgis = {
  getCompanyInviteList: {
    url: '/wm-htrserver/cop/hj/kickoff_activity/queryWhiteCollarInviteList',
    name: '查询白领邀请好友列表',
  },
  getRankList: {
    url: '/wm-htrserver/cop/hj/query_mgm2_colleague_ranking',
    name: '查询白领活动排行榜',
  },
}

class FunExpTokenService {
  getRankList(aid: number): Promise<{
    rankList: {
      rank?: number
      nickName: string
      points: string
    }[]
    myRank: number
  }> {
    return new Promise((resolve, reject) => {
      focusCore
        .request(cgis.getRankList, {
          activityId: aid,
        })
        .then(
          (res: {
            rankTop100: {
              rank: number
              nickName: string
              val: string
            }[]
            myRank: number
          }) => {
            console.log('🚀 ~ file: service.ts ~ line 115 ~ FunExpTokenService ~ returnnewPromise ~ res', res)
            const { rankTop100 = [], myRank = -1 } = res
            const origin = rankTop100.slice(0, 20) || []
            let list =
              (origin.length &&
                origin.map((i) => {
                  return {
                    ...i,
                    nickName: entitiestoUtf16(i.nickName),
                    points: formatAmount(i.val, false),
                  }
                })) ||
              []

            return resolve({
              rankList: list,
              myRank,
            })
          }
        )
        .catch(() => {
          resolve({
            rankList: [],
            myRank: -1,
          })
        })
    })
  }

  getCompanyInviteList(activityId: string): Promise<{
    lists: {
      '101': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '101'
      }[]
      '102': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '102'
      }[]
      '103': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '103'
      }[]
      '104': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '104'
      }[]
      '105': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '105'
      }[]
      '106': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '106'
      }[]
    }
  }> {
    console.log('🚀 ~ file: service.ts:140 ~ KmhService ~ getCompanyInviteList ~ activityId:', activityId)
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getCompanyInviteList, { activityId })
        .then(
          (res: {
            inviteDataPushList: {
              '101': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '101'
              }[]
              '102': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '102'
              }[]
              '103': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '103'
              }[]
              '104': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '104'
              }[]
              '105': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '105'
              }[]
              '106': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '106'
              }[]
            }
          }) => {
            console.log('🚀 ~ file: service.ts:144 ~ KmhService ~ .then ~ res:', res)
            const { inviteDataPushList } = res
            const obj = {
              '101': [],
              '102': [],
              '103': [],
              '104': [],
              '105': [],
              '106': [],
            }
            for (let k in inviteDataPushList) {
              const list = inviteDataPushList[k]
              if (list && list.length) {
                obj[k] = list.map((i: any) => {
                  const { m2NickName, tabType, m2EnterpriseAbbreviation, m2HeadImgUrl, showTime } = i
                  const nickName = (m2NickName && entitiestoUtf16(m2NickName)) || '微众用户'
                  const avator = (m2HeadImgUrl && m2HeadImgUrl.replace('http://', 'https://')) || defaultAvator
                  let desc = '已邀请'
                  switch (tabType) {
                    case '101':
                      break
                    case '102':
                      desc = '已邀请开户'
                      break
                    case '103':
                      desc = `已认证-${m2EnterpriseAbbreviation}`
                      break
                    case '104':
                      desc = `已达标-${m2EnterpriseAbbreviation}`
                      break
                    case '105':
                      desc = '已开户'
                      break
                    case '106':
                      desc = `已认证-${m2EnterpriseAbbreviation}`
                      break
                  }

                  return {
                    avator,
                    nickName,
                    time: dayjs(showTime).format('YYYY-MM-DD'),
                    statusText: desc,
                  }
                })
              }
            }
            return resolve({ lists: obj })
          }
        )
        .catch((err: any) => {
          console.log('🚀 ~ file: service.ts:144 ~ KmhService ~ focusCore.request ~ err:', err)
          return resolve({
            lists: {
              '101': [],
              '102': [],
              '103': [],
              '104': [],
              '105': [],
              '106': [],
            },
          })
        })
    })
  }
}

class KmhService {
  getCompanyInviteList(activityId: string): Promise<{
    lists: {
      '101': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '101'
      }[]
      '102': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '102'
      }[]
      '103': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '103'
      }[]
      '104': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '104'
      }[]
      '105': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '105'
      }[]
      '106': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '106'
      }[]
    }
  }> {
    console.log('🚀 ~ file: service.ts:140 ~ KmhService ~ getCompanyInviteList ~ activityId:', activityId)
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getCompanyInviteList, { activityId })
        .then(
          (res: {
            inviteDataPushList: {
              '101': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '101'
              }[]
              '102': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '102'
              }[]
              '103': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '103'
              }[]
              '104': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '104'
              }[]
              '105': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '105'
              }[]
              '106': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '106'
              }[]
            }
          }) => {
            console.log('🚀 ~ file: service.ts:144 ~ KmhService ~ .then ~ res:', res)
            const { inviteDataPushList } = res
            const obj = {
              '101': [],
              '102': [],
              '103': [],
              '104': [],
              '105': [],
              '106': [],
            }
            for (let k in inviteDataPushList) {
              const list = inviteDataPushList[k]
              if (list && list.length) {
                obj[k] = list.map((i: any) => {
                  const { m2NickName, tabType, m2EnterpriseAbbreviation, m2HeadImgUrl, showTime } = i
                  const nickName = (m2NickName && entitiestoUtf16(m2NickName)) || '微众用户'
                  const avator = (m2HeadImgUrl && m2HeadImgUrl.replace('http://', 'https://')) || defaultAvator
                  let desc = '已邀请'
                  switch (tabType) {
                    case '101':
                      break
                    case '102':
                      desc = '已邀请开户'
                      break
                    case '103':
                      desc = `已认证-${m2EnterpriseAbbreviation}`
                      break
                    case '104':
                      desc = `已达标-${m2EnterpriseAbbreviation}`
                      break
                    case '105':
                      desc = '已开户'
                      break
                    case '106':
                      desc = `已认证-${m2EnterpriseAbbreviation}`
                      break
                  }

                  return {
                    avator,
                    nickName,
                    time: dayjs(showTime).format('YYYY-MM-DD'),
                    statusText: desc,
                  }
                })
              }
            }
            return resolve({ lists: obj })
          }
        )
        .catch((err: any) => {
          console.log('🚀 ~ file: service.ts:144 ~ KmhService ~ focusCore.request ~ err:', err)
          return resolve({
            lists: {
              '101': [],
              '102': [],
              '103': [],
              '104': [],
              '105': [],
              '106': [],
            },
          })
        })
    })
  }
}
export default new FunExpTokenService()
