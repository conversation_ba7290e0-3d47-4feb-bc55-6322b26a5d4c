import { focusStore, focusServices, focusCore, focusUtils } from '@focus/render'
import { PhotoData, ListType, directionType } from '../Types/index'

const { entitiestoUtf16 } = focusUtils
const { userService } = focusServices
// import { EnumMethod } from './focus-core/plugins/Ajax'

const activityId = 11390
const approvingImg = require('../img/photograph/approving.png')
const approveErrImg = require('../img/photograph/approveErr.png')
const defaultImg = require('../img/photograph/loadErr.png')

const defaultData = {
  photoId: '',
  reviewStatus: '0', //是否审核通过
  dayDate: '', //上传时间
  fileUrl: defaultImg,
  title: '',
  likedCount: 0,
  rank: 0,
  headImgUrl: '',
  nickName: '',
  likeFlag: false,
  photoDirection: directionType.Vertical,
}

const cgis = {
  uploadFtp: {
    url: '/wm-hjhtr/file_upload/common',
    name: '上传文件到ftp服务器',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  },
  getUserData: {
    url: '/wm-htrserver/cop/hj/photo_competition/query_my_photo',
    name: '我上传的作品',
  },
  getTimePhotoList: {
    url: '/wm-htrserver/cop/hj/photo_competition/query_new_list',
    name: '上传榜照片列表',
  },
  getLikePhotoList: {
    url: '/wm-htrserver/cop/hj/photo_competition/query_top_list',
    name: '点赞榜照片列表',
  },
  getPhotoDetail: {
    url: '/wm-htrserver/cop/hj/photo_competition/query_detail',
    name: '照片详情',
  },
  likePhotoWithOpen: {
    url: '/wm-htrserver/cop/hj/photo_competition/like_photo',
    name: '开户用户点赞作品',
    method: 'post',
  },
  likePhotoWithoutOpen: {
    url: '/wm-htrserver/cop/hj/photo_competition/like_photo_without_open',
    name: '未开户用户点赞作品',
    method: 'post',
  },
  uploadPhoto: {
    url: '/wm-htrserver/cop/hj/photo_competition/upload_photo',
    name: '上传审核作品',
    method: 'post',
  },
  deletePhoto: {
    url: '/wm-htrserver/cop/hj/photo_competition/delete_photo',
    name: '删除作品',
    method: 'post',
  },
  getLikeCountWithOpen: {
    url: '/wm-htrserver/cop/hj/photo_competition/query_like_count',
    name: '开户用户剩余点赞数',
  },
  getLikeCountWithoutOpen: {
    url: '/wm-htrserver/cop/hj/photo_competition/query_like_count_without_open',
    name: '未开户用户剩余点赞数',
  },
  getFtpFile: {
    url: '/wm-hjhtr/common/download/fps_file',
    name: '获取ftp文件',
  },
}

class AppPhotoHandler {
  filePath: string = ''
  file_id: string = ''
  hash_id: string = ''
  photoDirection: directionType = directionType.Vertical
  photoDirectionHandler = new PhotoDirectionHandler()
  getPhoto(): Promise<{
    file_id: string
    hash_id: string
    previewImg: string
  }> {
    // if (focusCore.env.isInWx) {
    //   return new Promise((resolve) => {
    //     this.getPreviewPhoto(this.file_id, this.hash_id).then(async (previewImg) => {
    //       console.log(
    //         '🚀 ~ file: photoService.ts:30 ~ AppPhotoHandler ~ this.getPreviewPhoto ~ previewImg:',
    //         previewImg
    //       )
    //       this.filePath = previewImg
    //       if (previewImg) {
    //         this.photoDirection = await this.photoDirectionHandler.getImageDirection(previewImg)
    //       }
    //       console.log('hahaha', this.photoDirection, this.filePath)
    //       resolve({
    //         file_id: this.file_id,
    //         hash_id: this.hash_id,
    //         previewImg,
    //       })
    //     })
    //   })
    // }

    return new Promise((resolve, reject) => {
      focusCore.invokeApp.selectPhoto({
        success: (res: { file_id: string; hash_id: string }[]) => {
          const photo = (res && res[0]) || {}
          const { file_id, hash_id } = photo
          console.log('🚀 ~ file: photoService.ts:71 ~ AppPhotoHandler ~ returnnewPromise ~ res:', res)

          this.getPreviewPhoto(file_id, hash_id).then(async (previewImg) => {
            console.log(
              '🚀 ~ file: photoService.ts:30 ~ AppPhotoHandler ~ this.getPreviewPhoto ~ previewImg:',
              previewImg
            )
            this.filePath = previewImg
            if (previewImg) {
              this.photoDirection = await this.photoDirectionHandler.getImageDirection(previewImg)
            }
            resolve({
              file_id,
              hash_id,
              previewImg,
            })
          })
        },
        error: () => {
          return reject()
        },
      })
    })
  }

  getPreviewPhoto(file_id: string, hash_id: string): Promise<string> {
    return new Promise((resolve) => {
      if (!file_id || !hash_id) {
        return resolve('')
      }
      focusCore
        .request(cgis.getFtpFile, {
          file_id,
          file_hash: hash_id,
        })
        .then((res: { datas: string }) => {
          console.log('🚀 ~ file: photoService.ts:47 ~ AppPhotoHandler ~ returnnewPromise ~ res:', res)
          const { datas = '' } = res
          const imgData = datas ? 'data:image/jpg;base64,' + datas : ''
          console.log('🚀 ~ file: photoService.ts:88 ~ AppPhotoHandler ~ .then ~ imgData:', imgData)
          this.file_id = file_id
          this.hash_id = hash_id
          return resolve(imgData)
        })
        .catch(() => {
          resolve('')
        })
    })
  }

  upload(file: File): Promise<{ file_id: string; hash_id: string }> {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('file', file)
      focusCore
        .request(cgis.uploadFtp, {}, formData)
        .then((res: { hash_id: string; file_id: string }) => {
          console.log('🚀 ~ file: photoService.ts:43 ~ AppPhotoHandler ~ .then ~ res:', res)
          const { file_id = '', hash_id = '' } = res
          this.file_id = file_id
          this.hash_id = hash_id
          resolve({ file_id, hash_id })
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: photoService.ts:52 ~ AppPhotoHandler ~ returnnewPromise ~ err:', err)
          reject(null)
        })
    })
  }
}

class PhotoDirectionHandler {
  //direction: directionType = directionType.Vertical

  getImageDirection(url: string): Promise<directionType> {
    console.log('我执行了')
    return new Promise(function (resolve, reject) {
      let image = new Image()
      image.onload = function () {
        const direction = image.width < image.height ? directionType.Vertical : directionType.Horizontal
        resolve(direction)
      }
      image.onerror = function () {
        reject(new Error('error'))
      }
      image.src = url
    })
  }
}
class PhotoListHandler {
  allPhotoList: PhotoData[] = [] //已获取的总照片
  columnLists: PhotoData[][] = [] //已获取的总照片的分列
  listType: ListType = ListType.Time
  columnCount: number = 1 //照片列表分列数
  cgi: any = {}
  onStepSize: number = 100
  constructor(type: ListType, columnCount: number) {
    this.listType = type
    this.cgi = this.listType === ListType.Time ? cgis.getTimePhotoList : cgis.getLikePhotoList
    this.columnCount = columnCount
  }
  initData(): Promise<PhotoData[][]> {
    return new Promise((resolve) => {
      if (this.listType === ListType.Time) {
        resolve(this.initTimePhotoData())
      } else {
        resolve(this.initLikePhotoData())
      }
    })
  }

  initTimePhotoData(): Promise<PhotoData[][]> {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getTimePhotoList, {
          activityId,
          pageSize: this.onStepSize,
          photoId: '',
          createTime: null,
        })
        .then((res: { newList: PhotoData[] }) => {
          console.log('🚀 ~ file: photoService.ts:47 ~ PhotoListHandler ~ returnnewPromise ~ res:', res)
          const { newList = [] } = res
          this.allPhotoList = newList
          let splitList = this.getColumnLists(newList)
          console.log(this.allPhotoList, this.columnLists)
          console.log('🚀 ~ file: photoService.ts:88 ~ PhotoListHandler ~ .then ~ newList:', newList)
          return resolve(splitList)
        })
        .catch(() => {
          resolve([])
        })
    })
  }

  initLikePhotoData(): Promise<PhotoData[][]> {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getLikePhotoList, {
          activityId,
          pageSize: this.onStepSize,
        })
        .then((res: { likeTopList: PhotoData[] }) => {
          console.log('🚀 ~ file: photoService.ts:47 ~ PhotoListHandler ~ returnnewPromise ~ res:', res)
          const { likeTopList = [] } = res
          this.allPhotoList = likeTopList
          const splitList = this.getColumnLists(likeTopList)
          console.log(this.allPhotoList, this.columnLists)
          console.log('🚀 ~ file: photoService.ts:88 ~ PhotoListHandler ~ .then ~ likeTopList:', likeTopList)
          return resolve(splitList)
        })
        .catch(() => {
          resolve([])
        })
    })
  }

  // getLastItem(): PhotoData | null {
  //   if (this.allPhotoList.length <= 0) return null
  //   return this.allPhotoList[this.allPhotoList.length - 1]
  // }

  get lastItem(): PhotoData | any {
    return this.allPhotoList.length ? this.allPhotoList[this.allPhotoList.length - 1] : {}
  }

  //注意：分页返回的不是总数据，而是返回新页的数据
  nextPage(): Promise<PhotoData[][]> {
    //点赞榜不支持分页  后面可以优化弄个黑名单
    if (this.listType === ListType.Like) {
      return Promise.resolve([[]])
    }

    // const lastItem = this.getLastItem()
    const lastItem = this.lastItem

    if (!lastItem.photoId || !lastItem.createTime) {
      return new Promise((resolve) => {
        resolve([[]])
      })
    }
    console.log('最后一个作品', lastItem)
    return new Promise((resolve) => {
      focusCore
        .request(this.cgi, {
          activityId,
          pageSize: this.onStepSize,
          photoId: lastItem.photoId,
          createTime: lastItem.createTime,
        })
        .then((res: { newList: PhotoData[] }) => {
          console.log('🚀 ~ file: photoService.ts:47 ~ AppPhotoHandler ~ returnnewPromise ~ res:', res)
          const { newList = [] } = res
          const newColumnList = this.getColumnLists(newList)
          //分页后更新总数据
          this.allPhotoList.push(...newList)
          return resolve(newColumnList)
        })
        .catch(() => {
          resolve([[]])
        })
    })
  }

  //根据分列数进行分列  目前只支持两列 后续可拓展
  getColumnLists(list: PhotoData[]): PhotoData[][] {
    if (this.columnCount === 2) {
      return this.splitTwoList(list)
    } else {
      return [list]
    }
  }

  splitTwoList(list: PhotoData[]): PhotoData[][] {
    let listResult: PhotoData[][] = [[], []]
    list.forEach((i, index) => {
      const item = i
      item.nickName = entitiestoUtf16(i.nickName)
      if (index % 2 === 0) {
        listResult[0].push(item)
      } else {
        listResult[1].push(item)
      }
    })
    console.log('listResult', listResult)
    return listResult
  }
}

class photoService {
  AppPhotoHandler = new AppPhotoHandler()
  timeListHandlers = new PhotoListHandler(ListType.Time, 2)
  likeListHandlers = new PhotoListHandler(ListType.Like, 2)
  myPhotoData: PhotoData = {} as PhotoData

  initHomePageData = () => {}

  get myPhotoId() {
    return this.myPhotoData.photoId
  }
  get myPhotoDataIsReady() {
    console.log(
      '🚀 ~ file: photoService.ts:361 ~ photoService ~ getmyPhotoDataIsReady ~ this.myPhotoData:',
      this.myPhotoData
    )
    return this.myPhotoData.photoId !== undefined
  }

  setInitHomePageData(fun: (arg?: any) => void) {
    console.log('我是回调函数', fun)
    this.initHomePageData = fun
  }

  /**
   * 返回我的作品信息
   * @returns
   */

  getMyPhotoData(): Promise<PhotoData> {
    return new Promise((resolve, reject) => {
      focusCore
        .request(cgis.getUserData, {
          activityId,
        })
        .then((res: any) => {
          if (/0000$/.test(res.ret_code)) {
            console.log('🚀 ~ file: service.ts ~ line 115 ~ FunExpTokenService ~ returnnewPromise ~ res', res)
            const { myPhoto = defaultData } = res
            //图片兜底
            if (myPhoto.reviewStatus === '0') {
              myPhoto.fileUrl = approvingImg
            } else if (myPhoto.reviewStatus === '1' && myPhoto.fileUrl === '') {
              myPhoto.fileUrl = defaultImg
            } else if (myPhoto.reviewStatus === '2') {
              myPhoto.fileUrl = approveErrImg
            }
            this.myPhotoData = myPhoto
            console.log('🚀 ~ file: photoService.ts:396 ~ photoService ~ .then ~ this.myPhotoData:', this.myPhotoData)
            return resolve(myPhoto)
          } else {
            console.log('🚀 ~ file: service.ts ~ line 115 res', res)
            console.log(this.myPhotoData)
            this.myPhotoData = defaultData
            return resolve(defaultData)
          }
        })
        .catch(() => {
          resolve(defaultData)
        })
    })
  }

  /**
   * 返回照片详情
   * photoId 照片id
   * @returns
   */

  getPhotoDetail(photoId: string): Promise<PhotoData> {
    return new Promise((resolve, reject) => {
      focusCore
        .request(cgis.getPhotoDetail, {
          activityId,
          photoId: photoId || '',
        })
        .then((res: any) => {
          if (/0000$/.test(res.ret_code)) {
            console.log('🚀 ~ file: service.ts ~ line 115 ~ FunExpTokenService ~ returnnewPromise ~ res', res)
            const { photoDetail = defaultData } = res
            const item = photoDetail
            item.nickName = entitiestoUtf16(photoDetail.nickName)
            //图片兜底
            if (item.fileUrl === '') {
              item.fileUrl = defaultImg
            }
            return resolve(item)
          } else {
            console.log('🚀 ~ file: service.ts ~ line 115 res', res)
            return resolve(defaultData)
          }
        })
        .catch(() => {
          resolve(defaultData)
        })
    })
  }

  /**
   * 用户点赞照片
   * photoId 照片id
   * @returns
   */

  likePhoto(photoId: string): Promise<{ status: string; likedCount: number; balanceQuantity: number }> {
    return new Promise((resolve, reject) => {
      let cgi = focusCore.hasAccount ? cgis.likePhotoWithOpen : cgis.likePhotoWithoutOpen
      focusCore
        .request(
          cgi,
          {},
          {
            activityId,
            photoId: photoId,
          }
        )
        .then((res: any) => {
          if (/0000$/.test(res.ret_code)) {
            console.log('🚀 ~ file: service.ts ~ line 115 ~ PhotoService ~ returnnewPromise ~ res', res)
            const { status = '0', likedCount = 0, balanceQuantity = 0 } = res
            return resolve({ status, likedCount, balanceQuantity })
          } else {
            console.log('🚀 ~ file: service.ts ~ line 115 res', res)
            return resolve({ status: '0', likedCount: 0, balanceQuantity: 0 })
          }
        })
        .catch(() => {
          resolve({ status: '0', likedCount: 0, balanceQuantity: 0 })
        })
    })
  }

  /**
   * 审核照片
   * photoId 照片id
   * @returns
   */

  approvePhoto(
    fileId: string,
    fileHash: string,
    title: string,
    headImgUrl: string,
    nickName: string,
    photoDirection: directionType,
    fileType?: string
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      focusCore
        .request(
          cgis.uploadPhoto,
          {},
          {
            activityId,
            fileId,
            fileHash,
            title,
            headImgUrl,
            nickName,
            fileType: fileType || 'JPG',
            photoDirection,
          }
        )
        .then((res: { reviewStatus: string; cdnUrl: string; ret_code: string }) => {
          if (/0000$/.test(res.ret_code)) {
            console.log('🚀 ~ file: service.ts ~ line 115 ~ PhotoService ~ returnnewPromise ~ res', res)
            return resolve(true)
          } else {
            console.log('🚀 ~ file: service.ts ~ line 115 res', res)
            return resolve(false)
          }
        })
        .catch(() => {
          resolve(false)
        })
    })
  }

  /**
   * 删除照片
   * photoId 照片id
   * @returns
   */

  deletePhoto(photoId: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      focusCore
        .request(
          cgis.deletePhoto,
          {},
          {
            activityId,
            photoId: photoId,
          }
        )
        .then((res: { photoId: string; status: string; ret_code: string }) => {
          if (/0000$/.test(res.ret_code)) {
            console.log('🚀 ~ file: service.ts ~ line 115 ~ FunExpTokenService ~ returnnewPromise ~ res', res)
            return resolve(true)
          } else {
            console.log('🚀 ~ file: service.ts ~ line 115 res', res)
            return resolve(false)
          }
        })
        .catch(() => {
          resolve(false)
        })
    })
  }

  /**
   * 我的剩余点赞数
   * @returns
   */

  getMyRemainLikes(): Promise<number> {
    return new Promise((resolve, reject) => {
      let cgi = focusCore.hasAccount ? cgis.getLikeCountWithOpen : cgis.getLikeCountWithoutOpen
      focusCore
        .request(cgi, {
          activityId,
        })
        .then((res: any) => {
          if (/0000$/.test(res.ret_code)) {
            console.log('🚀 ~ file: service.ts ~ line 115 ~ PhotoService ~ returnnewPromise ~ res', res)
            const { balanceQuantity = 0 } = res
            return resolve(balanceQuantity > 0 ? balanceQuantity : 0)
          } else {
            console.log('🚀 ~ file: service.ts ~ line 115 res', res)
            return reject()
          }
        })
        .catch(() => {
          reject()
        })
    })
  }

  /**
   * 提交审核
   * 需要先调用App能力获取fileId和fileHash再调用后端上传接口
   * @returns
   */

  submitPhoto(title: string): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const file_id = this.AppPhotoHandler.file_id
      const hash_id = this.AppPhotoHandler.hash_id
      const photo_direction = this.AppPhotoHandler.photoDirection
      const { headImgUrl, nickName } = await this.getUserInfo()

      console.log('上传结果：', file_id, hash_id)
      console.log('用户信息', nickName)
      if (file_id && hash_id) {
        console.log('上传成功')
        this.approvePhoto(file_id, hash_id, title, headImgUrl, nickName, photo_direction).then((res: boolean) => {
          return resolve(res)
        })
      } else {
        console.log('上传失败')
        return resolve(false)
      }
    })
  }

  getUserInfo(): Promise<{ headImgUrl: string; nickName: string }> {
    return new Promise((resolve, reject) => {
      let headImgUrl = '',
        nickName = ''
      userService.getUserInfoForUpload().then((res: any) => {
        headImgUrl = res.avator || ''
        nickName = res.nickNameEncode || ''
        resolve({ headImgUrl, nickName })
      })
    })
  }
}

export default new photoService()
