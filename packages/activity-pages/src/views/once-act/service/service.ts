import { formatAmount } from '@/utils'
import { focusStore, focusServices, focusCore, focusUtils } from '@focus/render'
console.log('🚀 ~ file: service.ts ~ line 2 ~ focusUtils', focusUtils)
const { entitiestoUtf16, dayjs } = focusUtils
const { mgmService, kvService } = focusServices
const defaultAvator = require('../img/default_avator.png')
const cgis = {
  getRankList: {
    url: '/wm-htrserver/cop/hj/query_mgm2_finance_festival_ranking',
    name: '理财节邀请排名',
  },
  w2kMgmRelationShipList: {
    url: '/wm-htrserver/op/mgm2/query_relation_list',
    name: 'hj邀请we2000的邀请列表',
  },
  getGaoduankaInfo: {
    url: '/wm-htrserver/cop/hj/kickoff_activity/queryUserBindCardAndEquityRank',
    name: '获取用户绑定高端卡信息',
  },
  getCompanyInviteList: {
    url: '/wm-htrserver/cop/hj/kickoff_activity/queryWhiteCollarInviteList',
    name: '查询白领邀请好友列表',
  },
  getRankListV2: {
    url: '/wm-htrserver/cop/hj/query_mgm2_activity_ranking',
    name: '24理财节排行榜',
    canSaveTimes: true,
    cacheTimeOut: 3000,
  },
}

class FunExpTokenService {
  getRankList(aid: number): Promise<{
    rankList: {
      rank?: number
      nickName: string
      points: string
    }[]
    myRank: number
  }> {
    return new Promise((resolve, reject) => {
      focusCore
        .request(cgis.getRankList, {
          activityId: aid,
        })
        .then(
          (res: {
            rankTop10: {
              rank: number
              nickName: string
              points: string
            }[]
            myRank: number
          }) => {
            console.log('🚀 ~ file: service.ts ~ line 115 ~ FunExpTokenService ~ returnnewPromise ~ res', res)
            const { rankTop10 = [], myRank = -1 } = res
            const origin = rankTop10 || []
            let list =
              (origin.length &&
                origin.map((i) => {
                  return {
                    ...i,
                    nickName: entitiestoUtf16(i.nickName),
                    points: formatAmount(i.points, false),
                  }
                })) ||
              []

            return resolve({
              rankList: list,
              myRank,
            })
          }
        )
        .catch(() => {
          resolve({
            rankList: [],
            myRank: -1,
          })
        })
    })
  }

  // 24年理财节排行榜
  getRankListV2(aid: number): Promise<{
    rankList: {
      rank?: number
      nickName: string
      points: string
    }[]
    myRank: number
  }> {
    return new Promise((resolve, reject) => {
      focusCore
        .request(cgis.getRankListV2, {
          activityId: aid,
        })
        .then(
          (res: {
            rankTopList: {
              rank: number
              nickName: string
              rankValue: string
            }[]
            myRank: number
          }) => {
            console.log('🚀 ~ file: service.ts ~ line 115 ~ FunExpTokenService ~ returnnewPromise ~ res', res)
            const { rankTopList = [], myRank = -1 } = res
            const origin = rankTopList || []
            let list =
              (origin.length &&
                origin.map((i) => {
                  return {
                    ...i,
                    nickName: entitiestoUtf16(i.nickName),
                    points: formatAmount(i.rankValue, false),
                  }
                })) ||
              []

            return resolve({
              rankList: list,
              myRank,
            })
          }
        )
        .catch(() => {
          resolve({
            rankList: [],
            myRank: -1,
          })
        })
    })
  }

  getRelationShipList(mgmAid: string): Promise<{
    0: Array<{ nickName: string; dateTime: string; headImgUrl: string; relationStatus: number }>
    1: Array<{ nickName: string; dateTime: string; headImgUrl: string; relationStatus: number }>

    2: Array<{ nickName: string; dateTime: string; headImgUrl: string; relationStatus: number }>
  }> {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.w2kMgmRelationShipList, {
          activityId: mgmAid,
        })
        .then((res: any) => {
          console.log('🚀 ~ file: service.ts:80 ~ FunExpTokenService ~ .then ~ res:', res)
          const { resultList = [] } = res
          console.log('🚀 ~ file: service.ts:83 ~ FunExpTokenService ~ .then ~ resultList:', resultList)
          const result = { 0: [], 1: [], 2: [] }
          resultList.forEach(
            (i: { nickname: string; dateTime: string; headImgUrl: string; relationStatus: number }) => {
              const { headImgUrl, nickname, relationStatus } = i

              result[relationStatus].push({
                ...i,
                headImgUrl: (headImgUrl && headImgUrl.replace('http://', 'https://')) || defaultAvator,
                nickName: (nickname && entitiestoUtf16(nickname)) || '微众用户',
              })
            }
          )
          resolve(result)
        })
        .catch((err: any) => {
          const result = { 0: [], 1: [], 2: [] }
          resolve(result)
        })
    })
  }
}

class GaoduankaService {
  getUserInfo(activityId: string): Promise<{
    isBindCard: boolean
    hasEquity: boolean
    checkStatus: boolean
  }> {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getGaoduankaInfo, { activityId })
        .then((res: { checkStatus: boolean; isBindPremiumCard: boolean; isValidEquityRank: boolean }) => {
          const { isBindPremiumCard = false, isValidEquityRank = false, checkStatus = false } = res
          return resolve({
            checkStatus,
            isBindCard: checkStatus && isBindPremiumCard,
            hasEquity: checkStatus && isValidEquityRank,
          })
        })
        .catch((err: any) => {
          const { modalStore } = focusStore.stores
          modalStore.errorMaskContrl(`getGaoduankaInfo`)
        })
    })
  }
}

class KmhService {
  getCompanyInviteList(activityId: string): Promise<{
    lists: {
      '101': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '101'
      }[]
      '102': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '102'
      }[]
      '103': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '103'
      }[]
      '104': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '104'
      }[]
      '105': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '105'
      }[]
      '106': {
        avator: string
        nickName: string
        time: number
        statusText: string
        tabType: '106'
      }[]
    }
  }> {
    console.log('🚀 ~ file: service.ts:140 ~ KmhService ~ getCompanyInviteList ~ activityId:', activityId)
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getCompanyInviteList, { activityId })
        .then(
          (res: {
            inviteDataPushList: {
              '101': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '101'
              }[]
              '102': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '102'
              }[]
              '103': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '103'
              }[]
              '104': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '104'
              }[]
              '105': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '105'
              }[]
              '106': {
                m2EnterpriseAbbreviation: string
                m2HeadImgUrl: string
                m2NickName: string
                showTime: number
                tabType: '106'
              }[]
            }
          }) => {
            console.log('🚀 ~ file: service.ts:144 ~ KmhService ~ .then ~ res:', res)
            const { inviteDataPushList } = res
            const obj = {
              '101': [],
              '102': [],
              '103': [],
              '104': [],
              '105': [],
              '106': [],
            }
            for (let k in inviteDataPushList) {
              const list = inviteDataPushList[k]
              if (list && list.length) {
                obj[k] = list.map((i: any) => {
                  const { m2NickName, tabType, m2EnterpriseAbbreviation, m2HeadImgUrl, showTime } = i
                  const nickName = (m2NickName && entitiestoUtf16(m2NickName)) || '微众用户'
                  const avator = (m2HeadImgUrl && m2HeadImgUrl.replace('http://', 'https://')) || defaultAvator
                  let desc = '已邀请'
                  switch (tabType) {
                    case '101':
                      break
                    case '102':
                      desc = '已邀请开户'
                      break
                    case '103':
                      desc = `已认证-${m2EnterpriseAbbreviation}`
                      break
                    case '104':
                      desc = `已达标-${m2EnterpriseAbbreviation}`
                      break
                    case '105':
                      desc = '已开户'
                      break
                    case '106':
                      desc = `已认证-${m2EnterpriseAbbreviation}`
                      break
                  }

                  return {
                    avator,
                    nickName,
                    time: dayjs(showTime).format('YYYY-MM-DD'),
                    statusText: desc,
                  }
                })
              }
            }
            return resolve({ lists: obj })
          }
        )
        .catch((err: any) => {
          console.log('🚀 ~ file: service.ts:144 ~ KmhService ~ focusCore.request ~ err:', err)
          return resolve({
            lists: {
              '101': [],
              '102': [],
              '103': [],
              '104': [],
              '105': [],
              '106': [],
            },
          })
        })
    })
  }
}
const gaoduankaService = new GaoduankaService()
const kmhService = new KmhService()
export { gaoduankaService, kmhService }
export default new FunExpTokenService()
