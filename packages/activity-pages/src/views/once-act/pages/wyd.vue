<template>
  <focus-render v-if="focusFid" :focusFid="focusFid" @onFocusConfigSucc="initFocus"> </focus-render>
  <previewTool :focus-render-ctr="focusRenderCtl" v-if="focusCore.isPreview && focusRenderCtl"></previewTool>
</template>

<script setup lang="ts">
import { provide, readonly, ref, toRaw, computed, nextTick, watchEffect, watch, onMounted } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import previewTool from '@/views/preview/components/preview-tool.vue'

const { modalStore, dynimicDataStore, activityStore } = focusStore.stores
const { activityService, taskService, jumpService, mgmService } = focusServices
const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { fid } = locationHrefPared.query
const focusFid = fid
const focusRenderCtl = ref(null)

function showDialog() {
  //   const env: any = {}
  //   if (BUILD_TEST) {
  //     env.miniEnv = 'develop'
  //     env.miniUsername = ''
  //   } else {
  //     env.miniEnv = 'release'
  //     env.miniUsername = ''
  //   }

  //   const minilink = ``

  modalStore.confirmContrl({
    show: true,
    contents: ['即将打开微信“微业贷”小程序'],
    btnConfirmText: '允许',
    confirmCb: () => {
      gotoMini()
    },
    // btnConfirmJumpConfig: {
    //   path: minilink,
    //   method: 'mini',
    //   ...env,
    // },
  })
}

function initFocus(fctl: any) {
  focusRenderCtl.value = fctl

  focusCore.onRedirectToSpecialPath('/wyd_mini', () => {
    gotoMini()
  })
  showDialog()
}

function gotoMini() {
  const env: any = {
    miniEnv: 'release',
    miniUsername: 'gh_89943eb60324',
  }
  //   if (BUILD_TEST) {
  //     env.miniEnv = 'develop'
  //     env.miniUsername = 'gh_89943eb60324'
  //   } else {
  //     env.miniEnv = 'release'
  //     env.miniUsername = 'gh_89943eb60324'
  //   }

  // 账户页 fid= 4894 ;测试 4001
  let minilink = `pages/login/getUserInfo/getUserInfo?portal=3434_0016&advert_id=01623&channel_id=198`

  // 贷款页 fid= 4895 ;测试 4002
  if (focusFid === '4895') {
    minilink = `pages/login/getUserInfo/getUserInfo?portal=3434_0001&advert_id=01623&channel_id=198`
  }

  // 我的页面 fid= 5002;测试4044
  if (focusFid === '5002') {
    minilink = `pages/login/getUserInfo/getUserInfo?portal=3434_0002&advert_id=01623&channel_id=198`
  }

  // 测试环境的贷款页
  if (BUILD_TEST) {
    if (focusFid === '4002') {
      minilink = `pages/login/getUserInfo/getUserInfo?portal=3434_0001&advert_id=01623&channel_id=198`
    }

    // 测试环境的我的
    if (focusFid === '4044') {
      minilink = `pages/login/getUserInfo/getUserInfo?portal=3434_0002&advert_id=01623&channel_id=198`
    }
  }
  jumpService.jump({
    path: minilink,
    method: 'mini',
    ...env,
  })
}
</script>

<style lang="scss" scoped></style>
