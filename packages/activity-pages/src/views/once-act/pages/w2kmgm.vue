<template>
  <focus-render v-if="focusFid" :focusFid="focusFid" @onLoginSucc="afterLogin" :slotMountedCb="slotMountedCb()">
    <template #w2kmgm>
      <div class="mgm-list">
        <div class="headers">
          <div
            class="item"
            v-for="(tab, index) in tabs"
            :key="index"
            @click="switchTabIndex(index)"
            :class="{ actived: tabIndex === index }"
          >
            {{ tab.title }}
          </div>
        </div>
        <div class="list">
          <div class="item" v-for="(item, index) in showList" :key="`${tabIndex}_${index}`">
            <img :src="item.headImgUrl" alt="" class="avator" />
            <div class="name">{{ item.nickName }}</div>
            <div class="time">{{ item.dateTime }}</div>
          </div>
          <div class="item btn-showmore" v-if="showBtnMore" @click="clickBtnMore">查看更多></div>
        </div>
        <div class="empty" v-if="!showList.length">还没有记录，快去邀请吧~</div>
      </div>
    </template>
  </focus-render>
</template>

<script setup lang="ts">
import { provide, readonly, ref, toRaw, computed, nextTick, watchEffect, watch, onMounted, Ref } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import service from '../service/service'

const { modalStore, dynimicDataStore } = focusStore.stores
const { activityService, taskService, jumpService, mgmService } = focusServices

const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { pageid, aid, fid } = locationHrefPared.query
const focusFid = fid || pageid
const mgmAid = ref('')

const tabs = ref([
  {
    index: 0,
    title: '未开户',
  },
  {
    index: 1,
    title: '已开户',
  },
  {
    index: 2,
    title: '已达标',
  },
])

const tabIndex = ref(0)
const originList = ref<{
  0: Array<{ nickName: string; dateTime: string; headImgUrl: string; relationStatus: number }>
  1: Array<{ nickName: string; dateTime: string; headImgUrl: string; relationStatus: number }>
  2: Array<{ nickName: string; dateTime: string; headImgUrl: string; relationStatus: number }>
}>({ 0: [], 1: [], 2: [] })
const maxLenth = ref({
  0: 5,
  1: 5,
  2: 5,
})

const curList = computed(() => {
  return originList.value[tabIndex.value]
})

const curMaxLenth = computed<number>(() => {
  console.log('🚀 ~ file: w2kmgm.vue:78 ~ curMaxLenth ~ curMaxLenth:', maxLenth.value[tabIndex.value])
  return maxLenth.value[tabIndex.value]
})

const showList = computed(() => {
  return curList.value.slice(0, curMaxLenth.value)
})

const showBtnMore = computed(() => {
  console.log('🚀 ~ file: w2kmgm.vue:87 ~ showBtnMore ~ curList.value:', curList.value)
  return curList.value.length > curMaxLenth.value
})

function switchTabIndex(index: number) {
  tabIndex.value = index
}

function clickBtnMore() {
  maxLenth.value[tabIndex.value] = curMaxLenth.value + 10
}

function afterLogin() {
  if (focusCore.hasAccount && mgmAid.value) {
  }
}

watchEffect(() => {
  if (mgmAid.value) {
    if (focusCore.hasAccount) {
      service.getRelationShipList(mgmAid.value).then((listObj) => {
        console.log('🚀 ~ file: w2kmgm.vue:102 ~ service.getRelationShipList ~ listObj:', listObj)
        originList.value = listObj
      })
    }
  }
})

function slotMountedCb() {
  return {
    w2kmgm: (data: any, parentModuleId: number) => {
      console.log('🚀 ~ file: licaijie.vue:125 ~ slotMountedCb ~ parentModuleId:', parentModuleId)
      console.log('啦啦啦 w2kmgm 调用！')
      mgmAid.value = data[0] || ''
    },
  }
}
</script>

<style scoped lang="scss">
.mgm-list {
  width: 700px;
  background: #ffffff;
  border: 3px solid #ffffff;
  box-shadow: 0 2px 6px 0 rgba(245, 146, 137, 0.17);
  border-radius: 14px;
  overflow: hidden;
  margin: 0 auto;
  .empty {
    width: 100%;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    color: #868fb8;
  }
  .headers {
    height: 90px;
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    background-image: linear-gradient(to bottom, rgba(45, 216, 255, 0.16) 0%, rgba(255, 255, 255, 0) 30%);
    padding: 0 24px;
    box-sizing: border-box;
    position: relative;
    &::after {
      content: '';
      width: 640px;
      height: 1px;
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%) scaleY(0.5);
      transform-origin: center 100%;
      z-index: 1;
      background: #dbe1fb;
    }
    .item {
      z-index: 2;
      flex: 1;
      width: 100%;
      text-align: center;
      font-size: 24px;
      color: #405080;
      line-height: 42px;
      font-weight: 400;
      opacity: 0.3;
      vertical-align: bottom;
      padding-bottom: 10px;
      border-bottom: 4px solid transparent;
      border-left: 0;
      border-right: 0;
      &.actived {
        font-size: 28px;
        font-weight: bold;
        color: #3e3f5f;
        border-bottom: 4px solid #22a9cb;
        opacity: 1;
      }
    }
  }
  .list {
    width: 100%;
    box-sizing: border-box;
    padding: 0 24px;
    .item {
      width: 100%;
      height: 120px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      position: relative;
      &.btn-showmore {
        width: 100%;
        height: 50px;
        padding-top: 20px;
        justify-content: center;
        font-size: 24px;
        color: #3566ff;
        padding-bottom: 20px;
        box-sizing: content-box;
      }
      &::after {
        content: '';
        width: 655px;
        height: 1px;
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%) scaleY(0.5);
        z-index: 1;
        background: #dbe1fb;
      }
      &:last-child {
        &::after {
          content: none;
        }
      }
      .avator {
        width: 80px;
        height: 80px;
        box-shadow: 0 5px 5px rgba($color: #000000, $alpha: 0.1);
        border-radius: 100%;
      }
      .name {
        font-size: 24px;
        color: #3e3f5f;
        margin-left: 20px;
        width: 300px;
        height: 40px;
        line-height: 40px;
        text-align: left;
        white-space: nowrap; /* 文本不换行 */
        overflow: hidden; /* 溢出部分隐藏 */
        text-overflow: ellipsis; /* 显示省略号 */
      }
      .time {
        flex: auto;
        font-size: 24px;
        height: 40px;
        line-height: 40px;
        color: #868fb8;
        text-align: right;
      }
    }
  }
}
</style>
