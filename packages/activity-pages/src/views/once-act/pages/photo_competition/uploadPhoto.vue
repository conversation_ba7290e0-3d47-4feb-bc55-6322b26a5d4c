<template>
  <div class="uploadPage-container">
    <div class="photo-img" :class="{ vertical: isVertical }">
      <img :src="photoPath" alt="" />
    </div>
    <div class="photo-detail">
      <div class="detail-body">
        <div class="title-detail">作品标题:</div>
        <textarea
          class="title-edit"
          id="edit"
          rows="2"
          cols="20"
          :maxlength="maxlength"
          v-model.trim="title"
          placeholder="请输入标题内容"
          @input="changeText"
        ></textarea>
        <span>还可以输入{{ remainCounts }}个字符</span>
      </div>
      <div class="detail-footer">
        <div class="approve-btn" @click="approvePhoto()"></div>
        <div class="tips">作品提交后，审核过程中不可修改</div>
      </div>
    </div>
    <DialogMask :show="showDialog" @touchmove.prevent>
      <template #dialog_contain>
        <div class="dialog" :class="{ horizon: isHorizon }">
          <!-- <div class="btn-close" @click="showDialog = false"></div> -->
          <div class="to-home-btn" @click="goHome()"></div>
        </div>
      </template>
    </DialogMask>
    <focus-render :needLogin="false"></focus-render>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, onBeforeRouteLeave } from 'vue-router'
import { focusStore, DialogMask } from '@focus/render'
import photoService from '../../service/photoService'
import { directionType } from '../../Types/index'
const defaultImg = require('../../img/photograph/loadErr.png')

const { modalStore } = focusStore.stores

const maxlength = 40
const showDialog = ref(false)
const title = ref('')
const router = useRouter()
const isHorizon = ref(false)

const photoPath = computed<string>(() => {
  return photoService.AppPhotoHandler.filePath || defaultImg
})

const isVertical = computed(() => {
  return photoService.AppPhotoHandler.photoDirection === directionType.Vertical
})

const remainCounts = computed<number>(() => {
  const counts = maxlength - title.value.length
  return counts > 0 ? counts : 0
})

function approvePhoto() {
  if (title.value.length === 0) {
    modalStore.toastShow('请录入作品标题')
    return
  }
  modalStore.loadingStart('upload')
  photoService
    .submitPhoto(title.value)
    .then((res) => {
      if (res) {
        if (document.documentElement.clientWidth > document.documentElement.clientHeight) {
          isHorizon.value = true
        }
        showDialog.value = true
      } else modalStore.toastShow('提交审核失败，请稍后再试')
    })
    .finally(() => {
      modalStore.loadingEnd('upload')
    })
}

function goHome() {
  router.replace({ path: `/photograph` })
  photoService.initHomePageData()
  showDialog.value = false
}

function changeText() {
  console.log(title)
  // var regex = /[\uD800-\uDFFF].|[\u200D-\uFE0F]/g

  const emojiRegex =
    /[\u{1F300}-\u{1F5FF}\u{1F600}-\u{1F64F}\u{1F680}-\u{1F6FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}\u{1F900}-\u{1F9FF}\u{1F1E0}-\u{1F1FF}\u{1F191}-\u{1F251}\u{1F004}\u{1F0CF}\u{1F170}-\u{1F171}\u{1F17E}-\u{1F17F}\u{1F18E}\u{3030}\u{2B50}\u{2B55}\u{2934}-\u{2935}\u{2B05}-\u{2B07}\u{2B1B}-\u{2B1C}\u{3297}\u{3299}\u{303D}\u{00A9}\u{00AE}\u{2122}\u{23F3}\u{24C2}\u{23E9}-\u{23EF}\u{25B6}\u{23F8}-\u{23FA}]/gu

  if (emojiRegex.test(title.value)) {
    // 如果输入的值包含表情符号，则清除输入框的值
    title.value = title.value.replace(emojiRegex, '')
  }
}

onBeforeRouteLeave((to, from) => {
  showDialog.value = false
})
</script>

<style lang="scss" scoped>
.uploadPage-container {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: black;
  justify-content: space-between;
  .photo-img {
    width: 750px;
    background-color: black;
    overflow: hidden;
    //padding-top: 75%;
    height: 562.5px;
    display: flex;
    align-items: center;
    justify-content: center;
    &.vertical {
      //padding-top: 133.333%;
      height: 1000px;
      img {
        height: 100%;
        width: auto;
      }
    }
    img {
      width: 100%;
      height: auto;
      //position: absolute;
      //left: 50%;
      //top: 50%;
      //transform: translate(-50%, -50%);
    }
  }
  .photo-detail {
    flex: 1;
    background-color: #feecb9;
    width: 100%;
    padding: 60px 20px 30px 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    .detail-body {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      width: 100%;
      position: relative;
      margin-bottom: 60px;
      .title-detail {
        font-size: 40px;
        line-height: 60px;
        font-weight: 600;
        color: #702215;
        background: url('../../img/photograph/active.png') no-repeat;
        background-size: 54%;
        padding-left: 24px;
      }
      .title-edit {
        background: #f8e0a1;
        border-radius: 20px;
        font-size: 40px;
        line-height: 60px;
        font-weight: 600;
        color: #b3805b;
        padding: 22px;
        height: 250px;
        width: 100%;
        margin-top: 32px;
      }
      textarea::-webkit-input-placeholder {
        font-size: 40px;
        color: rgba(#702215, 0.8);
        line-height: 60px;
      }
      span {
        position: absolute;
        bottom: 24px;
        right: 32px;
        opacity: 0.2;
        font-size: 32px;
        color: #702215;
        line-height: 48px;
        font-weight: 400;
      }
    }
    .detail-footer {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .approve-btn {
        background: url('../../img/photograph/approve-btn.png') no-repeat;
        background-size: 100%;
        width: 636px;
        height: 94px;
      }
      .tips {
        font-size: 32px;
        color: #702215;
        text-align: center;
        line-height: 48px;
        font-weight: 400;
        margin-top: 10px;
      }
    }
  }
}
.dialog {
  width: 630px;
  height: 720px;
  background: url('../../img/photograph/releaseSucc.png');
  background-size: 100%;
  border-radius: 20px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  .to-home-btn {
    background: url('../../img/photograph/to-home-btn.png') no-repeat;
    background-size: 100%;
    width: 486px;
    height: 96px;
    margin-bottom: 60px;
  }
  .btn-close {
    background: url('../../img/btn-close.png') no-repeat;
    background-size: 100% 100%;
    height: 60px;
    width: 60px;
    position: absolute;
    top: -32px;
    right: 0;
    transform: translateY(-100%);
  }

  &.horizon {
    width: 84vh;
    height: 96vh;
    .to-home-btn {
      background: url('../../img/photograph/to-home-btn.png') no-repeat;
      background-size: 100%;
      width: 64.8vh;
      height: 12.8vh;
      margin-bottom: 8vh;
    }
  
    .btn-close {
      background: url('../../img/btn-close.png') no-repeat;
      background-size: 100% 100%;
      height: 8vh;
      width: 8vh;
      position: absolute;
      top: 0;
      right: -4.2vh;
      transform: translateX(100%);
    }
  }
}
</style>
