<template>
  <!-- <wxupload v-if="inwx"></wxupload> -->
  <focus-render v-if="focusFid" :focusFid="focusFid" @onLoginSucc="afterLogin">
    <template #auth>
      <div class="auth-container">
        <div v-if="myPhotoData.photoId === ''" class="join-btn" @click="clickBtnUpload"></div>
        <template v-else>
          <img class="my-photo-title" :src="myPhotoTitleImg" alt="" />
          <div class="my-photo">
            <div class="img-container"><img :src="myPhotoData.fileUrl" alt="" /></div>
            <div v-if="myPhotoData.reviewStatus === '1'" class="myphoto-detail">
              <div class="myphoto-msg">
                当前点赞：
                <div class="liked-count">{{ myPhotoData.likedCount }}</div>
              </div>
              <div class="myphoto-msg">
                当前排名：
                <div class="rank">第{{ myPhotoData.rank }}名</div>
              </div>
              <div class="jump-icon" @click="jumpToDetail(myPhotoData.photoId, myPhotoData.photoDirection)"></div>
            </div>
            <div v-else class="myphoto-detail">
              <div class="myphoto-time">上传时间：{{ myPhotoData.dayDate }}</div>
              <div v-if="myPhotoData.reviewStatus === '0'" class="myphoto-state">正在审核中，请耐心等待</div>
              <div v-if="myPhotoData.reviewStatus === '2'" class="myphoto-state">审核不通过，请重新上传</div>
            </div>
          </div>
          <div v-if="myPhotoData.reviewStatus === '2'" class="delete-highlight-btn" @click="reUpload()"></div>
        </template>
      </div>
    </template>
    <template #photograph>
      <div class="photo-container">
        <div class="photo-detail">
          <div class="photo-tab">
            <div
              v-for="tab in tabs"
              class="tab"
              :key="tab.id"
              :class="selectedTabType === tab.type ? 'actived' : ''"
              @click="clickTab(tab.type)"
            >
              <span class="tab-title">{{ tab.title }}</span>
            </div>
          </div>
          <div class="photo-list">
            <div class="column-list" v-for="columnList in columnLists">
              <photo-card
                v-for="item in columnList"
                :key="item.photoId"
                :photoData="item"
                :selectedTab="selectedTabType"
                class="card"
                @click="jumpToDetail(item.photoId, item.photoDirection)"
              >
              </photo-card>
            </div>
          </div>
        </div>
        <div
          v-if="selectedTabType === ListType.Time"
          class="view-more"
          :style="{ display: btnHidden ? 'none' : 'flex' }"
        >
          <div class="view-tips" @click="viewMore()">查看更多作品</div>
        </div>
      </div>
    </template>
  </focus-render>
  <DialogMask :show="showDialog" :justifyContent="'flex-end'" @touchmove.prevent>
    <template #dialog_contain>
      <div class="dialog">
        <img src="../../img/photograph/uploadTips.png" alt="" />
        <div class="upload-btn" @click="uploadPhoto()"></div>
        <div class="back-btn" @click="showDialog = false"></div>
      </div>
    </template>
  </DialogMask>
</template>

<script setup lang="ts">
import { ref, computed, onActivated, toRefs } from 'vue'
import { focusUtils, focusStore, focusServices, DialogMask, focusCore } from '@focus/render'
import PhotoCard from '../../components/photograph/PhotoCard.vue'
import { useRouter, onBeforeRouteLeave } from 'vue-router'
import { PhotoData, ListType, directionType } from '../../Types/index'
import photoService from '../../service/photoService'
import wxupload from '../../components/photograph/wxupload.vue'
const { activityService, taskService, jumpService, mgmService } = focusServices

const { modalStore } = focusStore.stores
const myPhotoTitleImg = require('../../img/photograph/my-photo-title.png')

const { UrlParser } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { pageid, aid, fid } = locationHrefPared.query
const focusFid = fid || pageid

const selectedTabType = ref<ListType>(ListType.Time)
const showDialog = ref(false)
const btnHidden = ref(false)

const router = useRouter()

const myPhotoData = ref<PhotoData>({
  photoId: '',
  reviewStatus: '0', //是否审核通过
  dayDate: '', //上传时间
  fileUrl: '',
  title: '',
  likedCount: 0,
  rank: 0,
  headImgUrl: '',
  nickName: '',
  likeFlag: false,
  photoDirection: directionType.Vertical,
})

const columnLists = ref<PhotoData[][]>([])
const tabs = [
  { id: 0, title: '最新上传', type: ListType.Time },
  { id: 1, title: '点赞最高', type: ListType.Like },
]
const inwx = computed(() => {
  return focusCore.env.isInWx
})

async function initShowPhotoList() {
  columnLists.value = []
  btnHidden.value = false
  modalStore.loadingStart('initShowPhotoList')
  if (selectedTabType.value === ListType.Time) {
    columnLists.value = await photoService.timeListHandlers.initData()
  } else {
    columnLists.value = await photoService.likeListHandlers.initData()
  }
  console.log('columnLists', columnLists.value)
  modalStore.loadingEnd('initShowPhotoList')
}

function getMyPhotoData() {
  modalStore.loadingStart('getUserData')
  photoService
    .getMyPhotoData()
    .then((value) => {
      myPhotoData.value = value
    })
    .finally(() => {
      modalStore.loadingEnd('getUserData')
    })
}

function clickTab(tabType: ListType) {
  selectedTabType.value = tabType
  initShowPhotoList()
}

function jumpToDetail(id: string, direction: string) {
  router.push({
    path: `/photograph/photodetail`,
    query: {
      photoId: id,
      photoDirection: direction,
    },
  })
}

function uploadPhoto() {
  modalStore.loadingStart('uploadPhoto')
  photoService.AppPhotoHandler.getPhoto()
    .then(() => {
      showDialog.value = false
      router.push({ path: `/photograph/uploadPhoto` })
    })
    .catch(() => {
      showDialog.value = false
    })
    .finally(() => {
      modalStore.loadingEnd('uploadPhoto')
    })
}

function reUpload() {
  modalStore.confirmContrl({
    show: true,
    contents: ['作品删除后不可找回，作品相关点赞数将全部作废'],
    btnCancelText: '取消',
    btnConfirmText: '删除',
    title: '确认删除作品',
    confirmCb: () => {
      modalStore.loadingStart('delete')
      photoService
        .deletePhoto(myPhotoData.value.photoId)
        .then((res) => {
          if (res) {
            initData()
            // clickBtnUpload()
            if (focusCore.env.isInApp) {
              showDialog.value = true
            }
          } else modalStore.toastShow('删除失败')
        })
        .finally(() => {
          modalStore.loadingEnd('delete')
        })
    },
  })
}

function viewMore() {
  modalStore.loadingStart('nextPage')
  photoService.timeListHandlers
    .nextPage()
    .then((newData) => {
      if (newData[0].length === 0) {
        modalStore.toastShow('没有更多作品了！')
        btnHidden.value = true
        return
      }
      console.log('查看更多作品', newData, columnLists)
      newData.forEach((item, index) => {
        columnLists.value[index].push(...item)
      })
      console.log('作品', columnLists.value)
    })
    .finally(() => {
      modalStore.loadingEnd('nextPage')
    })
}

function initData() {
  console.log('我执行了')
  initShowPhotoList()
  getMyPhotoData()
}

function afterLogin(loginData: any) {
  console.log('.....login,', loginData)
  if (focusCore.isLogined) {
    initData()
    photoService.setInitHomePageData(initData)
  }
}

function clickBtnUpload() {
  if (focusCore.env.isInWx) {
    const { fid } = locationHrefPared.query
    jumpService.jump({
      path: location.origin + location.pathname + '?' + `fid=${fid}#photograph`,
      method: 'url',
    })
  }
  if (focusCore.env.isInApp) {
    showDialog.value = true
  }
}

onBeforeRouteLeave((to, from) => {
  showDialog.value = false
})
</script>

<style lang="scss" scoped>
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .my-photo {
    display: flex;
    width: 710px;
    background: #f8e0a1;
    border-radius: 20px;
    padding: 20px;
    margin-top: 9px;
    .img-container {
      width: 243px;
      height: 159px;
      margin-right: 32px;
      border-radius: 12px;
      overflow: hidden;
      img {
        width: 243px;
        height: 159px;
        object-fit: cover;
        object-position: center;
      }
    }
    .myphoto-detail {
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      flex: 1;
      .myphoto-msg {
        font-size: 32px;
        color: #702215;
        letter-spacing: 0;
        text-align: justify;
        line-height: 48px;
        font-weight: 400;
        display: flex;
        margin: 6px;
        .liked-count {
          font-weight: 400;
        }
        .rank {
          font-weight: 400;
          color: #ff531c;
        }
      }
      .jump-icon {
        position: absolute;
        right: 32px;
        top: 50%;
        transform: translateY(-50%);
        background: url('../../img/photograph/jump.png');
        background-size: 100% 100%;
        width: 28px;
        height: 28px;
      }
      .myphoto-time {
        font-size: 32px;
        color: #702215;
        letter-spacing: 0;
        text-align: justify;
        line-height: 48px;
        font-weight: 400;
        margin: 6px;
      }
      .myphoto-state {
        font-size: 32px;
        color: #ff531c;
        letter-spacing: 0;
        text-align: justify;
        line-height: 48px;
        font-weight: 400;
        margin: 6px;
      }
    }
  }
}
.photo-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 0 20px;
  .photo-detail {
    width: 100%;
    padding: 32px 17px 0 17px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    border-radius: 20px;
    background-color: #f8e0a1;
    .photo-tab {
      display: flex;
      align-items: center;
      height: 60px;
      width: 100%;
      margin-left: -14px;
      .tab {
        font-size: 40px;
        color: #702215;
        letter-spacing: 0;
        text-align: justify;
        line-height: 60px;
        font-weight: 400;
        padding-left: 20px;
        .tab-title {
          opacity: 0.5;
        }
        &.actived {
          background: url('../../img/photograph/active.png') no-repeat;
          background-size: 100px 55px;
          .tab-title {
            opacity: 1;
            font-weight: 600;
          }
        }

        &:first-child {
          &::after {
            content: '|';
            margin-left: 24px;
          }
        }
      }
    }
    .photo-list {
      width: 100%;
      display: flex;
      margin: 21px 0;
      .column-list {
        width: 50%;
      }
      .card {
        overflow: hidden;
      }
    }
  }
  .view-more {
    width: 710px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-top: 28px;
    opacity: 0.5;
    .view-tips {
      display: flex;
      align-items: center;
      font-size: 32px;
      color: #5d2407;
      line-height: 48px;
      font-weight: 500;
      margin: 0 12px;
      &::before {
        content: '';
        height: 1px;
        width: 203px;
        background: #5d2407;
        opacity: 0.5;
        margin-right: 20px;
      }
      &::after {
        content: '';
        background: url('../../img/photograph/open.png');
        background-size: 100% 100%;
        width: 28px;
        height: 28px;
        margin-left: 10.31px;
        opacity: 1;
      }
    }
    &::after {
      content: '';
      height: 1px;
      width: 203px;
      background: #5d2407;
      opacity: 0.5;
    }
  }
}

.dialog {
  width: 100%;
  height: auto;
  background: #feecb9;
  border-radius: 20px;
  padding: 36px;
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    width: 100%;
    margin-bottom: 61px;
  }
  .upload-btn {
    background: url('../../img/photograph/upload-btn.png') no-repeat;
    background-size: 100%;
    width: 636px;
    height: 94px;
  }
  .back-btn {
    background: url('../../img/photograph/back-btn.png') no-repeat;
    background-size: 100%;
    width: 636px;
    height: 94px;
    margin-top: 24px;
  }
}
.join-btn {
  background: url('../../img/photograph/join-btn.png') no-repeat;
  background-size: 100%;
  width: 662px;
  height: 120px;
}
.delete-highlight-btn {
  background: url('../../img/photograph/delete-highlight-btn.png') no-repeat;
  background-size: 100%;
  width: 662px;
  height: 120px;
  margin-top: 36px;
}
</style>
