<template>
  <div class="photoDetail-container">
    <div class="photo-img" :class="{ vertical: isVertical }">
      <img :src="photoData.fileUrl" alt="" />
    </div>
    <div class="photo-detail">
      <div class="detail-header">
        <div class="auth-detail">
          <img class="auth-avator" :src="photoData.headImgUrl || defaultImg" />
          <div class="auth-msg">
            <div class="auth-name">{{ photoData.nickName }}</div>
            <div class="photo-rank">
              当前排名：
              <div class="rank">第{{ photoData.rank }}名</div>
            </div>
          </div>
        </div>
        <div class="like-detail">
          <div class="photo-likeCount">{{ photoData.likedCount }}</div>
          <img v-if="photoData.likeFlag" class="photo-like" src="../../img/photograph/like.png" alt="" />
          <img v-else class="photo-like" src="../../img/photograph/unlike.png" alt="" />
        </div>
      </div>
      <div class="detail-body">
        <div class="title-detail">{{ photoData.title }}</div>
        <div class="detail-footer">
          <template v-if="isAuth">
            <div class="share-my-btn" @click="sharePhoto()"></div>
            <div class="delete-btn" @click="deletePhoto()"></div>
          </template>
          <template v-else>
            <div class="like-btn" :class="{ disabled: remainlikes === 0 }" @click="likePhoto()"></div>
            <div class="share-btn" @click="sharePhoto()"></div>
            <div class="tips">
              <div class="likes-remain">
                本日您还可点赞<span>{{ remainlikes }}</span
                >次，
              </div>
              <div class="jump-tips" @click="viewMore()">查看所有作品&gt;</div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>

  <DialogMask :show="showDialog" @touchmove.prevent>
    <template #dialog_contain>
      <div class="dialog" :class="{ horizon: isHorizon }">
        <div class="btn-close" @click="showDialog = false"></div>
        <div class="to-join-btn" @click="goHome()"></div>
      </div>
    </template>
  </DialogMask>
  <focus-render :needLogin="true" @onLoginSucc="afterLogin"></focus-render>
</template>

<script setup lang="ts">
import { ref, watch, onActivated, computed, onMounted } from 'vue'
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router'
import { DialogMask, focusServices, focusStore, focusUtils } from '@focus/render'
import { PhotoData, directionType } from '../../Types/index'
import photoService from '../../service/photoService'
import { focusCore } from '@focus/render'

const { jumpService, mgmService } = focusServices
const { modalStore } = focusStore.stores
const { UrlParser } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const defaultImg = require('../../img/photograph/default_avator.png')

const photoData = ref<PhotoData>({
  photoId: '',
  reviewStatus: '0', //是否审核通过
  dayDate: '', //上传时间
  fileUrl: '',
  title: '',
  likedCount: 0,
  rank: 0,
  headImgUrl: '',
  nickName: '',
  likeFlag: false,
  photoDirection: directionType.Vertical,
})

const curUrlParsed = new UrlParser()
console.log('🚀 ~ file: photoDetail.vue:87 ~ curUrlParsed:', curUrlParsed)
const route = useRoute()
const router = useRouter()
const photoId = ref(route.query.photoId || curUrlParsed.query.photoId || '')
const isAuth = ref(photoId.value && photoId.value === photoService.myPhotoId)
const showDialog = ref(false)
const remainlikes = ref(10)
const isHorizon = ref(false)

const isVertical = computed(() => {
  const direction = route.query.photoDirection || curUrlParsed.query.photoDirection || photoData.value.photoDirection
  return direction === directionType.Vertical
})

function getPhotoDetail() {
  modalStore.loadingStart('getPhotoDetail')
  photoService
    .getPhotoDetail(photoId.value)
    .then((value) => {
      photoData.value = value
      setRtConnerShare()
    })
    .finally(() => {
      modalStore.loadingEnd('getPhotoDetail')
    })
}

function getMyRemainLikes() {
  modalStore.loadingStart('getLikeCountWithOpen')
  photoService
    .getMyRemainLikes()
    .then((val) => {
      remainlikes.value = val
    })
    .catch(() => {
      modalStore.toastShow('获取点赞数失败')
    })
    .finally(() => {
      modalStore.loadingEnd('getLikeCountWithOpen')
    })
}
function toastNoLikes() {
  if (!focusCore.hasAccount || (focusCore.hasAccount && photoService.myPhotoDataIsReady && !photoService.myPhotoId)) {
    if (document.documentElement.clientWidth > document.documentElement.clientHeight) {
      isHorizon.value = true
    }
    showDialog.value = true
    return
  }
  modalStore.toastShow('今日点赞数已用完')
}

function likePhoto() {
  if (remainlikes.value === 0) {
    // toastNoLikes()
    return
  }
  modalStore.loadingStart('likePhoto')
  photoService
    .likePhoto(photoId.value)
    .then((res) => {
      if (res.status === '1') {
        getPhotoDetail()
        remainlikes.value = res.balanceQuantity
        modalStore.toastShow(`点赞成功,今日还可以点赞${remainlikes.value}次`)
        if (res.balanceQuantity === 0) {
          toastNoLikes()
        }
      } else if (res.status === '0') {
        modalStore.toastShow('点赞失败')
      } else if (res.status === '2') {
        modalStore.toastShow('今日点赞次数超限')
        remainlikes.value = 0
      }
    })
    .finally(() => {
      modalStore.loadingEnd('likePhoto')
    })
}

function sharePhoto() {
  const shareConfig = getShareConfig()
  mgmService.clickShare(`photo-share`, shareConfig)
}

function deletePhoto() {
  modalStore.confirmContrl({
    show: true,
    contents: ['作品删除后不可找回，作品相关点赞数将全部作废'],
    btnCancelText: '取消',
    btnConfirmText: '删除',
    title: '确认删除作品',
    confirmCb: () => {
      modalStore.loadingStart('delete')
      photoService
        .deletePhoto(photoId.value)
        .then((res) => {
          if (res) {
            router.replace({ path: '/photograph' })
            photoService.initHomePageData()
          } else modalStore.toastShow('删除失败')
        })
        .finally(() => {
          modalStore.loadingEnd('delete')
        })
    },
  })
}

function goHome() {
  if (focusCore.env.isInWx) {
    const { fid } = locationHrefPared.query
    jumpService.jump({
      path: location.origin + location.pathname + '?' + `fid=${fid}#photograph`,
      method: 'url',
    })
  }
  if (focusCore.env.isInApp) {
    const link = location.origin + location.pathname + `?fid=${curUrlParsed.query.fid}&t=${Date.now()}#/photograph`
    location.replace(link)
  }
  showDialog.value = false
}

onBeforeRouteLeave((to, from) => {
  showDialog.value = false
})

function viewMore() {
  const link = location.origin + location.pathname + `?fid=${curUrlParsed.query.fid}&t=${Date.now()}#/photograph`
  location.replace(link)
}

function setIsAuth() {
  console.log(
    '🚀 ~ file: photoDetail.vue:205 ~ setIsAuth ~ photoService.myPhotoDataIsReady:',
    photoService.myPhotoDataIsReady
  )
  if (!focusCore.hasAccount) {
    return
  }
  if (photoService.myPhotoDataIsReady) {
    return
  }
  modalStore.loadingStart('getUserData')
  photoService
    .getMyPhotoData()
    .then((value) => {
      if (value.photoId === photoId.value) {
        isAuth.value = true
      } else {
        isAuth.value = false
      }
    })
    .finally(() => {
      modalStore.loadingEnd('getUserData')
    })
}

function getShareConfig() {
  const link =
    location.origin +
    location.pathname +
    `?fid=${curUrlParsed.query.fid}&photoId=${photoId.value}&photoDirection=${photoData.value.photoDirection}#/photograph/photoDetail`
  const shareConfig = {
    shareTitle: '微众银行的摄影比赛',
    shareDesc: '快来点赞吧！奖品好丰富！',
    shareImage: require('../../img/photograph/share-icon.png'),
    shareUrl: link,
  }
  return shareConfig
}

function setRtConnerShare() {
  const shareConfig = getShareConfig()
  mgmService.setRtConnerShare(`photo-share`, shareConfig)
}

function afterLogin(data: any) {
  if (focusCore.isLogined) {
    setIsAuth()
    getPhotoDetail()
    getMyRemainLikes()
  }
}
</script>

<style lang="scss" scoped>
.photoDetail-container {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: black;
  justify-content: space-between;
  .photo-img {
    //position: relative;
    width: 750px;
    height: 562.5px;
    background-color: black;
    //padding-top: 75%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    &.vertical {
      //padding-top: 133.333%;
      height: 1000px;
      img {
        height: 100%;
        width: auto;
      }
    }
    img {
      width: 100%;
      height: auto;
      //position: absolute;
      //left: 50%;
      //top: 50%;
      //transform: translate(-50%, -50%);
    }
  }
  .photo-detail {
    flex: 1;
    background-color: #feecb9;
    width: 100%;
    padding: 36px 20px;
    display: flex;
    flex-direction: column;
    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // height: 100px;
      .auth-detail {
        display: flex;
        align-items: center;
        .auth-avator {
          width: 120px;
          height: 120px;
          margin-right: 20px;
          border-radius: 50%;
          border: 4px solid #f9c291;
          background: transparent;
        }
        .auth-msg {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          width: 300px;
          .auth-name {
            font-size: 40px;
            color: #702215;
            letter-spacing: 0;
            text-align: justify;
            line-height: 60px;
            font-weight: 600;
            &:empty:before {
              content: '...';
              color: #702215;
            }
          }
          .photo-rank {
            display: flex;
            font-size: 32px;
            color: #702215;
            letter-spacing: 0;
            text-align: justify;
            line-height: 48px;
            font-weight: 400;
            .rank {
              font-size: 32px;
              color: #ff531c;
              // color: linear-gradient(87deg, #FF531C 100%, #FF7B42 85%);
              font-weight: 600;
            }
          }
        }
      }
      .like-detail {
        display: flex;
        height: 100%;
        justify-content: center;
        align-items: center;
        align-self: flex-end;
        margin-bottom: 4px;
        .photo-likeCount {
          margin-right: 20px;
          font-size: 32px;
          color: #b3805b;
          letter-spacing: 0;
          text-align: justify;
          line-height: 38px;
          font-weight: 600;
        }
        .photo-like {
          width: 32px;
          height: 29px;
        }
      }
    }
    .detail-body {
      flex: 1;
      display: flex;
      flex-direction: column;
      // height: 60%;
      justify-content: space-between;
      align-items: flex-start;
      border-top: 1px solid rgba($color: #702215, $alpha: 0.1);
      margin-top: 36px;
      padding-top: 32px;
      .title-detail {
        width: 100%;
        min-height: 120px;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        //-webkit-line-clamp: 2
        font-size: 40px;
        color: #702215;
        letter-spacing: 0;
        text-align: justify;
        line-height: 60px;
        font-weight: 400;
        margin-bottom: 60px;
      }
      .detail-footer {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .share-my-btn {
          background: url('../../img/photograph/share-my.png') no-repeat;
          background-size: 100%;
          width: 662px;
          height: 120px;
        }
        .delete-btn {
          background: url('../../img/photograph/delete-btn.png') no-repeat;
          background-size: 100%;
          width: 636px;
          height: 94px;
        }
        .share-btn {
          background: url('../../img/photograph/share-btn.png') no-repeat;
          background-size: 100%;
          width: 636px;
          height: 94px;
        }
        .like-btn {
          background: url('../../img/photograph/like-btn.png') no-repeat;
          background-size: 100%;
          width: 662px;
          height: 120px;
        }
        .disabled {
          opacity: 0.5;
        }
        .tips {
          display: flex;
          font-size: 32px;
          line-height: 48px;
          margin-top: 8px;
          .likes-remain {
            color: rgba(112, 34, 21, 0.8);
            font-weight: 400;
            span {
              font-weight: 600;
            }
          }
          .jump-tips {
            color: #3474e1;
            font-weight: 500;
          }
        }
      }
    }
  }
}
.dialog {
  width: 630px;
  height: 720px;
  background: url('../../img/photograph/goHome.png');
  background-size: 100%;
  border-radius: 20px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  //overflow: hidden;
  .to-join-btn {
    background: url('../../img/photograph/to-join-btn.png') no-repeat;
    background-size: 100%;
    width: 486px;
    height: 96px;
    margin-bottom: 60px;
  }

  .btn-close {
    background: url('../../img/btn-close.png') no-repeat;
    background-size: 100% 100%;
    height: 60px;
    width: 60px;
    position: absolute;
    top: -32px;
    right: 0;
    transform: translateY(-100%);
  }

  &.horizon {
    width: 84vh;
    height: 96vh;
    .to-join-btn {
      background: url('../../img/photograph/to-join-btn.png') no-repeat;
      background-size: 100%;
      width: 64.8vh;
      height: 12.8vh;
      margin-bottom: 8vh;
    }

    .btn-close {
      background: url('../../img/btn-close.png') no-repeat;
      background-size: 100% 100%;
      height: 8vh;
      width: 8vh;
      position: absolute;
      top: 0;
      right: -4.2vh;
      transform: translateX(100%);
    }
  }
}
</style>
