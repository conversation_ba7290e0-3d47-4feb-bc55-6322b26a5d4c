<template>
  <focus-render
    v-if="focusFid"
    :focusFid="focusFid"
    @onLoginSucc="afterLogin"
    :slotMountedCb="slotMountedCb()"
    @onFocusConfigSucc="setFocus"
  >
    <template #ranklist>
      <div class="rank-list">
        <div class="item header">
          <div class="index">排名</div>
          <div class="name">昵称</div>
          <div class="score">福气值</div>
        </div>
        <div class="empty"></div>
        <div class="item" v-for="(item, index) in showRankList" :key="index">
          <div class="index" :class="`index_${index}`">
            {{ index + 1 }}
          </div>
          <div class="name">{{ item.nickName }}</div>
          <div class="score">{{ item.points }}</div>
        </div>
        <div class="btn-more" v-show="showMoreBtn" @click="jumpMoreRankList">更多排名</div>
      </div>
    </template>

    <!--  -->
    <template #mgmlist="mgmlistProps">
      {{ mgmlistProps }}
      <MgmListCompany :activityId="mgmlistProps.extendParams[0]"></MgmListCompany>
    </template>
  </focus-render>
</template>

<script lang="ts" setup>
import 'swiper/swiper.min.css'
import { provide, readonly, ref, toRaw, computed, nextTick, watchEffect, watch, onMounted } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import service from '../service/service'
import MgmListCompany from '../components/kmh/MgmListCompany.vue'
const { modalStore, dynimicDataStore } = focusStore.stores
const { activityService, taskService, jumpService, mgmService } = focusServices
const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { pageid, aid, fid } = locationHrefPared.query
const focusFid = fid || pageid
const mgmAid = ref(0)
const rankMaxLenth = ref(0)
const moreRankList = ref('')
const myRankIndex = ref(-1)
const rankDataIsReady = ref(false)

const originRankList = ref<{ nickName: string; points: string; rank?: number }[]>([])

const showRankList = computed(() => {
  const data = originRankList.value.slice(0, rankMaxLenth.value)
  return data
})

const showMoreBtn = computed(() => {
  return rankMaxLenth.value < originRankList.value.length
})

watchEffect(() => {
  let { _api_10_0_userScore = 0 } = dynimicDataStore.numberData
  console.log('🚀 ~ file: kmh.vue:39 ~ watchEffect ~ dynimicDataStore.numberData:', dynimicDataStore.numberData)
  updateMyRank(_api_10_0_userScore)

  if (mgmAid.value && focusCore.isLogined) {
    getRankList(mgmAid.value)
  }
})

const userOpenId = ref('')

onMounted(() => {
  focusCore.onRedirectToSpecialPath('/focus_subscribe_click', () => {
    alert('NE65IQi87EXcj17I7YF_Bzv9adrWsVO154bb0KrS2Nw')
    // console.log(shareConfigData.value)
    // showInvite('邀请')
    window.hjCoreIab.sendWxSubscribeMessageReq(
      {
        scene: '1000',
        templateId: 'NE65IQi87EXcj17I7YF_Bzv9adrWsVO154bb0KrS2Nw',
      },
      (res: any) => {
        // action: "confirm"
        // errCode: 0
        // errStr: ""
        // openid: "ozf_BuIr5AcHmgNrl-qURLeBIXAo"
        // reserved: "qwe"
        // scene: 12
        // templateId: "aONvDtCuacZufgjSsVsiBFfjBHTYpQZxLU_jjX5hWjY"
        // type: "SubscribeMsg.Resp"
        // ------------------
        // action: "cancel"
        // errCode: 0
        // errStr: ""
        // openid: ""
        // reserved: "qwe"
        // scene: 12
        // templateId: "aONvDtCuacZufgjSsVsiBFfjBHTYpQZxLU_jjX5hWjY"
        // type: "SubscribeMsg.Resp"
        console.log('sendWxSubscribeMessageReq res', res)
        userOpenId.value = res.openid
        if (res.action === 'confirm') {
          const { openid } = res
          modalStore.confirmContrl({
            show: true,
            btnConfirmText: '好的',
            hideConfirm: true,
            contents: ['一次性订阅消息订阅成功哦！', `openId: ${userOpenId.value}`],
          })
          // this.sendOnceScribeMsg({touser: openid})
          // this.setPopupBottom({
          //   isShow: true,
          //   title: '还差1步即可关注公众号',
          //   type: 1,
          //   btnText: '返回微信',
          //   onConfirm: () => {
          //     hjCoreIab.launchWxApp()
          //     this.hidePopupBottom()
          //   }
          // })
        }
      },
      (err: any) => {
        console.log('sendWxSubscribeMessageReq', err)
        modalStore.confirmContrl({
          show: true,
          btnConfirmText: '好的',
          title: '订阅失败啦！',
          hideConfirm: true,
          contents: JSON.stringify(err),
        })
      }
    )
  })

  focusCore.onRedirectToSpecialPath('/focus_subscribe_send', () => {
    // 发送订阅消息
    focusCore
      .request(
        {
          url: 'https://personal.webank.com/hjop/wechat/template_subscribe',
          name: '发送订阅消息',
          method: 'POST',
        },
        {},
        {
          touser: userOpenId.value,
          template_id: 'aONvDtCuacZufgjSsVsiBFfjBHTYpQZxLU_jjX5hWjY',
          url: location.href,
          scene: 1000,
          title: 'focus测试订阅',
          data: {
            content: {
              value: '只差一步！戳我扫码关注，每周PK赢积分',
            },
          },
        }
      )
      .then((res: any) => {
        modalStore.confirmContrl({
          title: '发送成功',
          content: JSON.stringify(res),
        })
      })
      .catch((err: any) => {
        modalStore.confirmContrl({
          title: '发送失败',
          content: JSON.stringify(err),
        })
      })
  })
})

function afterLogin() {}

function updateMyRank(myRankScore: number) {
  if (myRankScore) {
    let rankIndex = myRankIndex.value === -1 ? '300+' : myRankIndex.value
    focusCore.updateDynamicData('_custom_myRank', rankIndex, true)
  } else {
    focusCore.updateDynamicData('_custom_myRank', '未上榜', true)
  }
}

function setFocus(fc: any) {
  // focusRenderCtr.value = fc
}

function getRankList(aid: number) {
  modalStore.loadingStart('getRankList')
  service.getRankList(aid).then(({ rankList, myRank }) => {
    originRankList.value = rankList
    myRankIndex.value = myRank
    rankDataIsReady.value = true
    modalStore.loadingEnd('getRankList')
  })
}

function jumpMoreRankList() {
  jumpService.jump({
    path: moreRankList.value,
  })
}

function slotMountedCb() {
  return {
    ranklist: (data: any) => {
      console.log('🚀 ~ file: kmh.vue:78 ~ slotMountedCb ~ data:', data)

      mgmAid.value = data[0] || 0
      moreRankList.value = data[1]
      rankMaxLenth.value = Number(data[2]) || 0
    },
  }
}
</script>

<style lang="scss" scoped>
.rank-list {
  width: 100%;
  background: red($color: #000000);
  .item {
    width: 650px;
    height: 110px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: center;
    border-bottom: 1px solid #f2dac8;
    font-size: 28px;
    color: #a26043;
    position: relative;
    margin: 0 auto;
    &.header {
      height: 97px;
      font-size: 30px;
      color: #704633;
      .score {
        display: flex;
        align-items: center;
        justify-content: center;
        &::before {
          content: '';
          width: 42px;
          height: 42px;
          display: inline-block;
          background: url('../img/kmh/icon_score.png') no-repeat;
          background-size: contain;
          background-position: center center;
        }
      }
    }
    .index {
      margin-left: 50px;
      width: 80px;
      height: 80px;
      flex: none;
      background-size: 100% 100%;
      background-position: center center;
      line-height: 80px;
      &.index_0 {
        color: transparent;
        background-image: url('../img/kmh/index_0.png');
      }
      &.index_1 {
        color: transparent;
        background-image: url('../img/kmh/index_1.png');
      }
      &.index_2 {
        color: transparent;
        background-image: url('../img/kmh/index_2.png');
      }
    }
    .name {
      width: 100%;
      flex: 1;
    }
    .score {
      flex: none;
      width: 150px;
      margin-right: 20px;
    }
  }
  .btn-more {
    font-size: 24px;
    color: #456ce6;
    line-height: 42px;
    font-weight: 400;
    height: 84px;
    width: 200px;
    text-align: center;
    line-height: 82px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    &::after {
      content: '';
      width: 13px;
      height: 22px;
      background: url('../img/kmh/icon_right.png') no-repeat;
      background-size: cover;
      background-position: center;
      margin-left: 20px;
    }
  }
}
</style>
