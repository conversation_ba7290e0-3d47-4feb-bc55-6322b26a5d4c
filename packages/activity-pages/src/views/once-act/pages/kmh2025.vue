<template>
  <focus-render v-if="focusFid" :focusFid="focusFid" :slotMountedCb="slotMountedCb()">
    <template #kmh2025>
      <div
        v-if="kmhAidIsAdding === activityState.notJoined"
        :class="`rewards-container-nosigned${whiteSuffix}`"
        :style="{ opacity: dataIsUnReady ? 0 : 1 }"
      ></div>
      <div v-else class="rewards-container" :style="{ opacity: dataIsUnReady ? 0 : 1 }">
        <div :class="`rewards-right${whiteSuffix}`"></div>
        <div class="box-green">
          <div class="box-14">
            <div
              class="item"
              v-for="(item, index) in sixBox"
              :key="index"
              :class="{ actived: days14Index === index }"
            ></div>
          </div>
          <div class="box-28">
            <div
              class="item"
              v-for="(item, index) in sixBox"
              :key="index"
              :class="{ actived: days28Index === index }"
            ></div>
          </div>
        </div>
        <div class="box-day">
          <div class="item" v-for="(days, index) in standardDays" :key="index">
            <div class="itemBox">
              <span class="day" :class="{ gray: days === ' 0' }">{{ days }}</span>
              <span class="tian">天</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </focus-render>
</template>

<script lang="ts" setup>
import { provide, readonly, ref, toRaw, computed, nextTick, watchEffect, watch, onMounted } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import { storeToRefs } from 'pinia'
const { grayService } = focusServices
const { modalStore, dynimicDataStore, activityStore } = focusStore.stores
const { activityService, taskService, jumpService, mgmService } = focusServices
const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { fid } = locationHrefPared.query
const focusFid = fid
// 开门红活动id,参与后才能推数（T+1）
const kmhAid = ref('')
const grayId = ref('')
// 活动参与状态
enum activityState {
  loading = 0,
  notJoined = 1,
  joined = 2,
}
// 灰度判定状态
enum grayState {
  loading = 0,
  notIn = 1,
  in = 2,
}
// 初始值为loading状态正在获取数据时
const kmhAidIsAdding = ref(activityState.loading)
const isInGray = ref(grayState.loading)

const dataIsUnReady = computed(() => {
  // dsDate的结果只有两种情况，一种是无数据为--，一种是有数据
  const { _api_10_0_dsDate } = dynimicDataStore.useData
  console.log('🐬 ~ file: kmh2025.vue:75 ~ dataIsUnReady ~ _api_10_0_dsDate:', _api_10_0_dsDate)
  return kmhAidIsAdding.value === activityState.loading || isInGray.value === grayState.loading || !_api_10_0_dsDate
})

// 样式后缀，区分白领报名、普通报名
// 白领未报名、普通未报名是两张图片展示
const whiteSuffix = computed(() => {
  // 这里默认useData是浅响应式追踪，而明确解析出_api_10_0_userType则会被追踪
  // 也就是useData中有其他字段变化时，这里是不触发计算属性的，只有_api_10_0_userType变化时才会触发
  const { _api_10_0_userType } = dynimicDataStore.useData
  if (_api_10_0_userType === '1') {
    return '-white'
  } else if (_api_10_0_userType === '0') {
    return ''
  }
  if (isInGray.value === grayState.in) {
    return '-white'
  } else {
    return ''
  }
})

// 达标绿色
const days14Index = computed(() => {
  const { _api_10_0_day_14_status } = dynimicDataStore.useData
  return Number(_api_10_0_day_14_status)
})
const days28Index = computed(() => {
  const { _api_10_0_day_28_status } = dynimicDataStore.useData
  return Number(_api_10_0_day_28_status)
})

// 渲染达标绿色的每列6个盒子
const sixBox = new Array(6).fill(0)

watchEffect(() => {
  console.log('activityStore.addingAids...', activityStore.addingAids)
  // 用于处理暂未报名时点击报名后的报名状态的实时更新
  if (
    kmhAidIsAdding.value !== activityState.loading &&
    kmhAid.value &&
    activityStore.addingAids &&
    activityStore.addingAids.some((i: any) => i.toString() === kmhAid.value.toString())
  ) {
    kmhAidIsAdding.value = activityState.joined
  }
})

watchEffect(() => {
  // 用于刚进入页面首次参与活动状态查询和灰度查询
  if (
    kmhAid.value &&
    grayId.value &&
    kmhAidIsAdding.value === activityState.loading &&
    isInGray.value === grayState.loading
  ) {
    modalStore.loadingStart('checkAidIsAdding and checkBussinessGray')
    Promise.all([checkAidIsAdding(), grayService.checkBussinessGray([{ id: grayId.value, subId: '' }], '2')])
      .then(([status, grayIds]) => {
        console.log('🚀 ~ checkAidIsAdding ~ res:', status)
        // 判断活动参与状态
        if (status) {
          kmhAidIsAdding.value = activityState.joined
        } else {
          kmhAidIsAdding.value = activityState.notJoined
        }
        if (grayIds.length && grayIds[0] && grayIds[0].type === grayId.value) {
          isInGray.value = grayState.in
        } else {
          isInGray.value = grayState.notIn
        }
      })
      .catch((err) => {
        console.log('🐬 ~ file: kmh2025.vue:168 ~ watchEffect ~ err:', err)
        kmhAidIsAdding.value = activityState.notJoined
        isInGray.value = grayState.notIn
      })
      .finally(() => {
        modalStore.loadingEnd('checkAidIsAdding and checkBussinessGray')
      })
  }

  // 根据活动参与状态和大数据推数更新用户积分展示
  let { _api_10_0_userPoint, _api_10_0_dsDate } = dynimicDataStore.useData
  if (kmhAidIsAdding.value === activityState.notJoined) {
    focusCore.updateDynamicData('_custom_userPoint', '暂未报名', true)
  } else if (kmhAidIsAdding.value === activityState.joined) {
    let returnStr_userPoint = ''
    if (!_api_10_0_dsDate || _api_10_0_dsDate === '--') {
      returnStr_userPoint = '暂无数据'
    } else {
      returnStr_userPoint = _api_10_0_userPoint === '--' ? '数据更新中' : _api_10_0_userPoint
    }
    focusCore.updateDynamicData('_custom_userPoint', returnStr_userPoint, true)
  }
})

// 达标天数
const standardDays = computed(() => {
  const {
    _api_10_0_level_1_days,
    _api_10_0_level_5_days,
    _api_10_0_level_10_days,
    _api_10_0_level_30_days,
    _api_10_0_level_50_days,
    _api_10_0_level_100_days,
  } = dynimicDataStore.numberData
  const arr = [
    _api_10_0_level_1_days,
    _api_10_0_level_5_days,
    _api_10_0_level_10_days,
    _api_10_0_level_30_days,
    _api_10_0_level_50_days,
    _api_10_0_level_100_days,
  ]
  return arr.map((item) => {
    console.log('🐬 ~ file: kmh2025.vue:63 ~ returnarr.map ~ item:', item)
    if (!item || item === '--') return ' 0'
    else return item.toString().padStart(2, ' ')
  })
})

function checkAidIsAdding(): Promise<boolean> {
  // 这里处理是否参与了活动
  return new Promise((resolve) => {
    // 模拟接口调用
    activityService.checkWrInfoStatus(kmhAid.value).then((status: any) => {
      resolve(status)
    })
  })
}

function slotMountedCb() {
  return {
    kmh2025: (data: any) => {
      console.log('🚀 ~ file: zcjj.vue:78 ~ slotMountedCb ~ data:', data)
      kmhAid.value = data[0]
      grayId.value = data[1]
    },
  }
}
</script>

<style lang="scss" scoped>
.beCommon {
  width: 710px;
  height: 627px;
  background-size: 710px 627px;
}

.container {
  @extend .beCommon;
}

.rewards-container-nosigned {
  @extend .beCommon;
  background-image: url('../img/kmh2025/bg-nosigned.png');
}

.rewards-container-nosigned-white {
  @extend .beCommon;
  background-image: url('../img/kmh2025/bg-white-nosigned.png');
}

.rewards-container {
  @extend .beCommon;
  background-image: url('../img/kmh2025/bg.png');
  font-family: Arial, sans-serif;
  position: relative;

  .rewards-right {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 200;
    @extend .beCommon;
    background-image: url('../img/kmh2025/bg-shuzi.png');
    &-white {
      @extend .rewards-right;
      background-image: url('../img/kmh2025/bg-white-shuzi.png');
    }
  }

  .box-green {
    width: 283px;
    height: 474px;
    position: absolute;
    left: 306px;
    top: 135px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    column-gap: 1px;
    .box-14,
    .box-28 {
      height: 474px;
      width: 141px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      gap: 1px;
      .item {
        height: 78px;
        width: 100%;
        position: relative;
        &.actived {
          background-color: #dfffe2;
          &::after {
            content: '';
            position: absolute;
            right: 5px;
            bottom: 2px;
            width: 34px;
            height: 26px;
            background-image: url('../img/kmh2025/green-mark.png');
            background-size: 34px 26px;
          }
        }
      }
    }
  }
  .box-day {
    width: 100px;
    height: 474px;
    position: absolute;
    right: 20px;
    top: 136px;
    display: flex;
    justify-content: center;
    align-items: space-between;
    column-gap: 1px;
    flex-wrap: wrap;
    .item {
      position: relative;
      width: 100%;
      height: 78px;
      display: flex;
      align-items: center;
      justify-content: center;
      .itemBox {
        width: 60px;
        height: 30px;
        vertical-align: middle;
        display: flex;
        align-items: flex-end;
        .day {
          flex: none;
          width: 35px;
          font-size: 32px;
          color: #f64744;
          line-height: 32px;
          font-weight: 600;
          display: flex;
          justify-content: flex-end;
          &.gray {
            color: rgba(108, 108, 108, 0.8);
            font-weight: 400;
          }
        }
        .tian {
          flex: 1;
          font-size: 24px;
          color: #704633;
          line-height: 30px;
          font-weight: 400;
        }
      }
    }
  }
}
</style>
