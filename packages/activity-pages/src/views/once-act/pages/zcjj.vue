<template>
  <focus-render v-if="focusFid" :focusFid="focusFid" :slotMountedCb="slotMountedCb()">
    <template #zcjj>
      <div class="rewards-container">
        <div class="rewards-left">
          <div class="rewards-text">积分<br />奖励</div>
          <div class="rewards-text">达标<br />档位</div>
        </div>
        <div class="rewards-right">
          <div v-for="(reward, index) in rewards" :key="reward.points" class="rewards-item">
            <span class="points">{{ reward.points }}</span>
            <div class="barChart">
              <div
                class="barChart-item"
                :class="{ 'barChart-highlight': index === userLevel, 'barChart-midlight': index < userLevel }"
              ></div>
            </div>
            <div class="threshold-item">
              <span>{{ reward.amount }}</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </focus-render>
</template>

<script lang="ts" setup>
import { provide, readonly, ref, toRaw, computed, nextTick, watchEffect, watch, onMounted } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
const { modalStore, dynimicDataStore, activityStore } = focusStore.stores
const { activityService, taskService, jumpService, mgmService } = focusServices
const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { fid } = locationHrefPared.query
const focusFid = fid

// 开门红活动id,参与后会才能推数（T+1）
const kmhAid = ref('')
// 活动参与状态
const kmhAidIsAdding = ref(false)
// focus渲染控制器
const focusRenderCtr = ref(null)

// 积分奖励和新增档位数据
const rewards = ref([
  { points: 800, amount: '1万' },
  { points: 4000, amount: '5万' },
  { points: 8000, amount: '10万' },
  { points: 24000, amount: '30万' },
  { points: 40000, amount: '50万' },
  { points: 80000, amount: '100万' },
])

// 用户档位展示
const userLevel = computed(() => {
  const lv = [10000, 50000, 100000, 300000, 500000, 1000000] // 6个财富值档位

  // 用户的新增财富值
  const { _api_10_0_newWealth } = dynimicDataStore.numberData
  console.log('🐬 ~ file: zcjj.vue:71 ~ userLevel ~ _api_10_0_newWealth:', _api_10_0_newWealth)
  let index =
    lv.findIndex((i) => i > (_api_10_0_newWealth === '--' || !_api_10_0_newWealth ? 0 : _api_10_0_newWealth)) - 1
  if (index === -2) index = 5
  return index
})

onMounted(() => {})

watchEffect(() => {
  console.log('activityStore.addingAids...', activityStore.addingAids)
  if (
    !kmhAidIsAdding.value &&
    kmhAid.value &&
    activityStore.addingAids &&
    activityStore.addingAids.some((i: any) => i.toString() === kmhAid.value.toString())
  ) {
    kmhAidIsAdding.value = true
  }

  if (kmhAid.value) {
    modalStore.loadingStart('checkAidIsAdding')
    checkAidIsAdding()
      .then((status) => {
        console.log('🚀 ~ checkAidIsAdding ~ res:', status)
        kmhAidIsAdding.value = status
        if (!status) {
          focusCore.updateDynamicData('_custom_userPoint', '暂未报名', true)
          focusCore.updateDynamicData('_custom_curWealth', '暂未报名', true)
          focusCore.updateDynamicData('_custom_newWealth', '暂未报名', true)
        }
      })
      .finally(() => {
        modalStore.loadingEnd('checkAidIsAdding')
      })
  }

  let {
    _api_10_0_userPoint,
    _api_10_0_dsDate = '',
    _api_10_0_curWealth,
    _api_10_0_newWealth,
  } = dynimicDataStore.useData

  console.log('🚀 ~ watchEffect ~ _api_10_0_userPoint:', _api_10_0_userPoint)
  console.log('🚀 ~ watchEffect ~ _api_10_0_dsDate:', _api_10_0_dsDate)
  console.log('🚀 ~ watchEffect ~ _api_10_0_curWealth:', _api_10_0_curWealth)
  console.log('🚀 ~ watchEffect ~ _api_10_0_newWealth:', _api_10_0_newWealth)
  // 已参与的情况下
  if (kmhAidIsAdding.value) {
    let returnStr_userPoint = ''
    let returnStr_curWealth = ''
    let returnStr_newWealth = ''
    if (!_api_10_0_dsDate || _api_10_0_dsDate === '--') {
      returnStr_userPoint = '暂无数据'
      returnStr_curWealth = '暂无数据'
      returnStr_newWealth = '暂无数据'
    } else {
      returnStr_userPoint = _api_10_0_userPoint === '--' ? '数据更新中' : _api_10_0_userPoint
      returnStr_curWealth = _api_10_0_curWealth === '--' ? '数据更新中' : _api_10_0_curWealth
      returnStr_newWealth = _api_10_0_newWealth === '--' ? '数据更新中' : _api_10_0_newWealth
    }
    focusCore.updateDynamicData('_custom_userPoint', returnStr_userPoint, true)
    focusCore.updateDynamicData('_custom_curWealth', returnStr_curWealth, true)
    focusCore.updateDynamicData('_custom_newWealth', returnStr_newWealth, true)
  }
})

function checkAidIsAdding(): Promise<boolean> {
  // 这里处理是否参与了活动
  return new Promise((resolve) => {
    // 模拟接口调用
    activityService.checkWrInfoStatus(kmhAid.value).then((status: any) => {
      resolve(status)
    })
  })
}

function slotMountedCb() {
  return {
    zcjj: (data: any) => {
      console.log('🚀 ~ file: zcjj.vue:78 ~ slotMountedCb ~ data:', data)
      kmhAid.value = data[0]
    },
  }
}
</script>

<style lang="scss" scoped>
.rewards-container {
  width: 660px;
  height: 225px;
  background-color: #ffffff;
  margin: 0 auto;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-family: Arial, sans-serif;
  padding: 27px 24px 27px 18px;
  box-sizing: border-box;
  border-radius: 10px;
  .rewards-left {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 66px;
    height: 100%;
    flex: none;
    .rewards-text {
      width: 55px;
      height: 60px;
      font-family: PingFangSC-Semibold;
      font-size: 24px;
      color: #61041b;
      letter-spacing: 0;
      line-height: 30px;
      font-weight: 600;
    }
  }

  .rewards-right {
    display: flex;
    justify-content: space-between;
    flex: 1;
    height: 100%;
    .rewards-item {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      flex: 1;
      .points {
        font-family: DINAlternate-Bold;
        font-size: 24px;
        color: #e92d5b;
        letter-spacing: 0;
        line-height: 24px;
        font-weight: 700;
      }
      .barChart {
        width: 100%;
        border-bottom: 1px solid #f1e0d2;
        height: 106px;
        background-color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: flex-end;
        .barChart-item {
          width: 50px;
          background-color: #dad5e3;
          border-radius: 3px 3px 0 0;
        }
        .barChart-highlight {
          background-image: linear-gradient(180deg, #fe9c64 3%, #fe9c64 100%);
        }
        .barChart-midlight {
          background: #ffdda7;
        }
      }
      &:nth-child(1) {
        .barChart-item {
          height: 8px;
        }
      }
      &:nth-child(2) {
        .barChart-item {
          height: 15px;
        }
      }
      &:nth-child(3) {
        .barChart-item {
          height: 30px;
        }
      }
      &:nth-child(4) {
        .barChart-item {
          height: 49px;
        }
      }
      &:nth-child(5) {
        .barChart-item {
          height: 68px;
        }
      }
      &:nth-child(6) {
        .barChart-item {
          height: 90px;
        }
      }
      .threshold-item {
        margin-top: 10px;
        font-family: PingFangSC-Semibold;
        font-size: 24px;
        color: #61041b;
        letter-spacing: 0;
        line-height: 30px;
        font-weight: 600;
      }
    }
  }
}
</style>
