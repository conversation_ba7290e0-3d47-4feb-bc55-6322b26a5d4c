<template>
  <focus-render
    v-if="focusFid"
    :focusFid="focusFid"
    @onLoginSucc="afterLogin"
    :slotMountedCb="slotMountedCb()"
    @onFocusConfigSucc="setFocus"
  >
    <template #ranklist>
      <div class="rank-list">
        <div class="item header">
          <div class="index">排名</div>
          <div class="name">昵称</div>
          <div class="score">人气值</div>
        </div>
        <div class="empty"></div>
        <div class="item" v-for="(item, index) in showRankList" :key="index">
          <div class="index" :class="`index_${index}`">
            {{ index + 1 }}
          </div>
          <div class="name">{{ item.nickName }}</div>
          <div class="score">{{ item.points }}</div>
        </div>
        <div class="btn-more" v-show="showMoreBtn" @click="jumpMoreRankList">更多排名</div>
      </div>
    </template>

    <!--  -->
    <template #mgmlist="mgmlistProps">
      <MgmListCompany></MgmListCompany>
    </template>
  </focus-render>
</template>

<script lang="ts" setup>
import 'swiper/swiper.min.css'
import { provide, readonly, ref, toRaw, computed, nextTick, watchEffect, watch, onMounted } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import service from '../service/nzj'
import MgmListCompany from '../components/nzj/MgmListCompany.vue'
const { modalStore, dynimicDataStore } = focusStore.stores
const { activityService, taskService, jumpService, mgmService } = focusServices
const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { pageid, aid, fid } = locationHrefPared.query
const focusFid = fid || pageid
const mgmAid = ref(0)
const rankMaxLenth = ref(0)
const moreRankList = ref('')
const myRankIndex = ref(-2)
const rankDataIsReady = ref(false)

const originRankList = ref<{ nickName: string; points: string; rank?: number }[]>([])

const showRankList = computed(() => {
  const data = originRankList.value.slice(0, rankMaxLenth.value)
  return data
})

const showMoreBtn = computed(() => {
  return rankMaxLenth.value < originRankList.value.length
})

watchEffect(() => {
  updateMyRank()

  if (mgmAid.value && focusCore.isLogined) {
    getRankList(mgmAid.value)
  }
})

function afterLogin() {}

function updateMyRank() {
  console.log('🚀 ~ file: nzj.vue:76 ~ updateMyRank ~  myRankIndex.value:', myRankIndex.value)
  let rankIndex = myRankIndex.value > 0 ? myRankIndex.value : myRankIndex.value === 0 ? '50+' : '未上榜'
  focusCore.updateDynamicData('_custom_myRank', rankIndex, true)
  // if (myRankScore) {
  // } else {
  //   focusCore.updateDynamicData('_custom_myRank', '未上榜', true)
  // }
}

function setFocus(fc: any) {
  // focusRenderCtr.value = fc
}

function getRankList(aid: number) {
  modalStore.loadingStart('getRankList')
  service.getRankList(aid).then(({ rankList, myRank }) => {
    originRankList.value = rankList
    myRankIndex.value = myRank
    rankDataIsReady.value = true
    modalStore.loadingEnd('getRankList')
  })
}

function jumpMoreRankList() {
  jumpService.jump({
    path: moreRankList.value,
  })
}

function slotMountedCb() {
  return {
    ranklist: (data: any) => {
      console.log('🚀 ~ file: kmh.vue:78 ~ slotMountedCb ~ data:', data)

      mgmAid.value = data[0] || 0
      moreRankList.value = data[1]
      rankMaxLenth.value = Number(data[2]) || 0
    },
  }
}
</script>

<style lang="scss" scoped>
.rank-list {
  width: 100%;
  background: red($color: #000000);
  .item {
    width: 650px;
    height: 110px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: center;
    border-bottom: 1px solid #e9e9e9;
    font-size: 28px;
    color: #7a7a7a;
    position: relative;
    margin: 0 auto;
    &.header {
      height: 97px;
      font-size: 30px;
      color: #5b5a5a;
    }
    .index {
      margin-left: 50px;
      width: 80px;
      height: 80px;
      flex: none;
      background-size: 100% 100%;
      background-position: center center;
      line-height: 80px;
      &.index_0 {
        color: transparent;
        background-image: url('../img/kmh/index_0.png');
      }
      &.index_1 {
        color: transparent;
        background-image: url('../img/kmh/index_1.png');
      }
      &.index_2 {
        color: transparent;
        background-image: url('../img/kmh/index_2.png');
      }
    }
    .name {
      width: 100%;
      flex: 1;
    }
    .score {
      flex: none;
      width: 150px;
      margin-right: 55px;
      text-align: right;
    }
  }
  .btn-more {
    font-size: 24px;
    color: #456ce6;
    line-height: 42px;
    font-weight: 400;
    height: 84px;
    width: 200px;
    text-align: center;
    line-height: 82px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    &::after {
      content: '';
      width: 13px;
      height: 22px;
      background: url('../img/kmh/icon_right.png') no-repeat;
      background-size: cover;
      background-position: center;
      margin-left: 20px;
    }
  }
}
</style>
