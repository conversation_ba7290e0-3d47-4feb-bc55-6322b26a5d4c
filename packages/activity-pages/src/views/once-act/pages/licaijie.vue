<template>
  <focus-render
    v-if="focusFid"
    :focusFid="focusFid"
    @onLoginSucc="afterLogin"
    :slotMountedCb="slotMountedCb()"
    @onFocusConfigSucc="setFocus"
  >
    <!-- 新资金模块 -->
    <template #wealth>
      <div class="wealth">
        <div class="box_status">
          <div class="box box_1" :class="`task_${days14MoneyLevel}_done`"></div>
          <div class="box box_2" :class="`task_${days28MoneyLevel}_done`"></div>
          <div class="box box_3" :class="`task_${days56MoneyLevel}_done`"></div>
        </div>
        <div class="data_list">
          <div class="data" v-for="(item, index) in daysData" :key="index">{{ item }}</div>
        </div>
      </div>
    </template>

    <!-- 邀好友排行榜 -->
    <template #ranklist>
      <div class="rank-list" v-if="rankMaxLenth && originRankList">
        <div class="item header">
          <div class="index">排名</div>
          <div class="name">昵称</div>
          <div class="score">人气值</div>
        </div>
        <div class="empty"></div>
        <div class="item" v-for="(item, index) in showRankList" :key="index">
          <div class="index" :class="`index_${index}`">
            {{ index + 1 }}
          </div>
          <div class="name">{{ item.nickName }}</div>
          <div class="score">{{ item.points }}</div>
        </div>
        <div class="btn-more" v-show="showMoreBtn" @click="showMoreRank">更多排名</div>
      </div>
    </template>
  </focus-render>

  <DialogMask :show="showDialog" @touchmove.prevent>
    <template #dialog_contain>
      <div class="count-aum">
        <div class="btn-close" @click="toggleDialog"></div>
        <div class="contain">
          <div class="list">
            <div class="item" v-for="(item, index) in countAumList" :key="index">
              {{ item.money }}
            </div>
          </div>
          <div class="btn-trans" @click="goTrans"></div>
        </div>
      </div>
    </template>
  </DialogMask>
</template>

<script setup lang="ts">
import 'swiper/swiper.min.css'
import { provide, readonly, ref, Ref, toRaw, computed, nextTick, watchEffect, watch, onMounted } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import service from '../service/service'
import { formatAmount } from '@/utils'

const { modalStore, dynimicDataStore } = focusStore.stores
const { activityService, taskService, jumpService, mgmService, dynimicDataService } = focusServices

const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { pageid, aid, fid } = locationHrefPared.query
const focusFid = fid || pageid
const ranklistdom = ref('')
const mgmAid = ref(0)
const myRankIndex = ref(0)
const rankMaxLenth = ref(0)
const moreRankList = ref('')
const show = ref(false)
const focusRenderCtr = ref(null)
const showDialog = ref(false)
const dataDsDate = ref('暂无数据')

const daysData = ref(['0', '0', '0', '0', '0', '0'])
const days14MoneyLevel = ref(0)
const days28MoneyLevel = ref(0)
const days56MoneyLevel = ref(0)
const countAumList: Ref<{ level: number; money: any }[]> = ref([
  { level: 1, money: 0 },
  { level: 5, money: 0 },
  { level: 10, money: 0 },
  { level: 30, money: 0 },
  { level: 50, money: 0 },
  { level: 100, money: 0 },
])

const myRankKeyName = ref('')

watchEffect(() => {
  if (mgmAid.value && focusCore.isLogined) {
    getRankList(mgmAid.value)
  }
  if (myRankKeyName.value) {
    const myRankScore = dynimicDataStore.numberData[myRankKeyName.value] || '--'
    console.log('🚀 ~ file: licaijie.vue:124 ~ watch ~ myRankScore:', myRankScore)
    updateMyRank(myRankScore)
  }
})

const originRankList = ref<{ nickName: string; points: number; rank?: number }[]>([])

const showRankList = computed(() => {
  const data = originRankList.value.slice(0, rankMaxLenth.value)
  return data
})

const showMoreBtn = computed(() => {
  return rankMaxLenth.value && rankMaxLenth.value < originRankList.value.length && moreRankList.value
})

function afterLogin(data: any) {
  if (focusCore.isLogined) {
    getWealthData()
  }
}
function setFocus(fc: any) {
  focusRenderCtr.value = fc
}

function updateMyRank(myRankScore: string) {
  console.log('🚀 ~ file: licaijie.vue:150 ~ updateMyRank ~ myRankScore:', myRankScore)
  if (myRankScore !== '--') {
    let rankIndex = myRankIndex.value === -1 ? '200+' : myRankIndex.value
    focusCore.updateDynamicData('_custom_myRank', rankIndex, true)
  } else {
    focusCore.updateDynamicData('_custom_myRank', '未上榜', true)
  }
}

function getRankList(aid: number) {
  modalStore.loadingStart('getRankList')

  service.getRankListV2(aid).then(({ rankList, myRank }) => {
    originRankList.value = rankList
    myRankIndex.value = myRank
    show.value = true
    modalStore.loadingEnd('getRankList')
  })
}

function showMoreRank() {
  jumpService.jump({
    path: moreRankList.value,
  })
}

function toggleDialog() {
  showDialog.value = !showDialog.value
}

function goTrans() {
  jumpService.commonUse.transDialog()
}

function getWealthData() {
  dynimicDataService.queryActivityDataPushRecord(14787, '117').then((res: any) => {
    console.log('🚀 ~ file: licaijie.vue:142 ~ dynimicDataService.queryActivityDataPushRecord ~ res:', res)

    const {
      countDays_1 = '0',
      countDays_5 = '0',
      countDays_10 = '0',
      countDays_30 = '0',
      countDays_50 = '0',
      countDays_100 = '0',
      cons_14_max_level = '0',
      cons_28_max_level = '0',
      cons_56_max_level = '0',
      aumGap_1 = '--',
      aumGap_5 = '--',
      aumGap_10 = '--',
      aumGap_30 = '--',
      aumGap_50 = '--',
      aumGap_100 = '--',
      dsDate = '--',
    } = res

    days14MoneyLevel.value = cons_14_max_level
    days28MoneyLevel.value = cons_28_max_level
    days56MoneyLevel.value = cons_56_max_level

    dataDsDate.value = dsDate === '--' ? '暂无数据' : dayjs(dsDate).format('YYYY-MM-DD')

    daysData.value = [countDays_1, countDays_5, countDays_10, countDays_30, countDays_50, countDays_100]
    countAumList.value = countAumList.value.map((i) => {
      const keyName = `aumGap_${i.level}`
      let val = res[keyName] === undefined || res[keyName] === '--' ? '暂无数据' : formatAmount(res[keyName], false)

      const money = val === '0' ? '昨日已达标' : val
      return {
        ...i,
        money,
      }
    })
  })
}

const slotMountedCb = () => {
  console.error('......ranklist')
  return {
    ranklist: (data: any, parentModuleId: number) => {
      console.log('🚀 ~ file: licaijie.vue:221 ~ slotMountedCb ~ data:', data)
      console.log('啦啦啦 ranklist 调用！')
      mgmAid.value = data[0] || 0
      rankMaxLenth.value = Number(data[1]) || 0
      moreRankList.value = data[2]
      myRankKeyName.value = data[3] || ''
    },
  }
}

onMounted(() => {
  focusCore.onRedirectToSpecialPath('/showtrans', () => {
    toggleDialog()
  })
})
</script>

<style lang="scss" scoped>
.count-aum {
  height: 90%;
  width: 700px;
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  flex-direction: column;
  .btn-close {
    width: 60px;
    height: 60px;
    background: url('../img/btn-close.png') no-repeat;
    background-size: contain;
    margin-bottom: 18px;
    flex: none;
    cursor: pointer;
  }
  .contain {
    flex: 1;
    width: 660px;
    margin: 0 auto;
    background: url('../img/licaijie/bg-trans.png') no-repeat;
    background-size: 100% 100%;
    height: 100%;
    max-height: 1094px;
    box-sizing: border-box;
    padding: 0 15px;
    position: relative;
    .list {
      width: 382px;
      height: 467px;
      box-sizing: border-box;
      position: absolute;
      right: 30px;
      top: 293px;
      color: #704633;

      .item {
        font-size: 30px;
        color: #704633;
        letter-spacing: 2px;
        text-align: left;
        font-weight: 700;
        height: 77px;
        position: relative;
        z-index: 2;
        box-sizing: border-box;
        padding-left: 121px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
    }
    .btn-trans {
      position: absolute;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      width: 528px;
      height: 135px;
      background: url('../img/licaijie/btn-trans.png') no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
      &:active {
        opacity: 0.8;
      }
    }
  }
}

.wealth {
  width: 690px;
  height: 612px;
  margin: 0 auto;
  background: url('../img/licaijie/bg_table.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
  &::before {
    content: '';
    width: 100%;
    height: 100%;
    background: url('../img/licaijie/bg_number.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 3;
  }
  .box_status {
    width: 336px;
    position: relative;
    left: 243px;
    top: 134px;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    height: 468px;
    z-index: 2;

    .box {
      width: 112px;
      height: 77px;
      background: rgba(164, 255, 221, 0.5);
      opacity: 0;
      position: relative;
      &::after {
        content: '';
        width: 27px;
        height: 21px;
        position: absolute;
        right: 5px;
        top: 5px;
        background: url('../img/licaijie/task_done.png') no-repeat;
        z-index: 2;
        background-size: 100% 100%;
      }
      &.task_1_done {
        opacity: 1;
        margin-top: 0px;
      }
      &.task_5_done {
        opacity: 1;
        margin-top: 77px;
      }
      &.task_10_done {
        opacity: 1;
        margin-top: 154px;
      }
      &.task_30_done {
        opacity: 1;
        margin-top: 231px;
      }
      &.task_50_done {
        opacity: 1;
        margin-top: 308px;
      }
      &.task_100_done {
        opacity: 1;
        margin-top: 390px;
      }
    }
  }
  .data_list {
    width: 45px;
    height: 468px;
    position: absolute;
    right: 63px;
    top: 134px;
    .data {
      width: 100%;
      height: 78px;
      font-size: 30px;
      color: #f64744;
      line-height: 78px;
      font-weight: 700;
      text-align: right;
      margin-right: 5px;
    }
  }
}
.rank-list {
  width: 100%;
  background: red($color: #000000);
  .item {
    width: 650px;
    height: 110px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: center;
    border-bottom: 1px solid #d9d2c942;
    color: #7a7a7a;
    font-weight: 500;
    font-size: 28px;
    position: relative;
    margin: 0 auto;
    &.header {
      height: 97px;
      font-size: 30px;
      color: #5b5a5a;
    }
    .index {
      margin-left: 60px;
      width: 80px;
      height: 80px;
      flex: none;
      background-size: 100% 100%;
      background-position: center center;
      line-height: 80px;
      &.index_0 {
        color: transparent;
        background-image: url('../img/kmh/index_0.png');
      }
      &.index_1 {
        color: transparent;
        background-image: url('../img/kmh/index_1.png');
      }
      &.index_2 {
        color: transparent;
        background-image: url('../img/kmh/index_2.png');
      }
    }
    .name {
      line-height: 1;
      flex: 1;
      text-align: center;
      margin-left: 70px;
    }
    .score {
      flex: none;
      width: 150px;
      margin-right: 55px;
      text-align: right;
      line-height: 1;
    }
  }
  .btn-more {
    font-size: 24px;
    color: #456ce6;
    line-height: 42px;
    font-weight: 400;
    height: 84px;
    width: 200px;
    text-align: center;
    line-height: 82px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    &::after {
      content: '';
      width: 13px;
      height: 22px;
      background: url('../img/kmh/icon_right.png') no-repeat;
      background-size: cover;
      background-position: center;
      margin-left: 20px;
    }
  }
}
</style>
