<template>
  <focus-render
    v-if="focusFid"
    :focusFid="focusFid"
    @onLoginSucc="afterLogin"
    :slotMountedCb="slotMountedCb()"
    @onFocusConfigSucc="setFocus"
  >
    <template #gaoduanka>
      <!-- 未开户 -->
      <div class="main" v-if="!hasAccount">
        <div class="tip">新开户绑定指定高端银行卡</div>
        <div class="tip">开户成功后，次日即享铂金会员权益</div>
        <div class="btn" @click="goApp">立即开户</div>
      </div>
      <!-- 已开户 -->
      <div class="main" v-else>
        <div class="tip">
          {{ isBindCard ? '您已成功开户并绑定指定高端银行卡' : '您尚未绑定指定高端银行卡' }}
        </div>
        <div class="tip" v-if="!isBindCard">可前往银行卡页，添加指定银行卡</div>

        <div class="tip" v-if="isBindCard">
          {{ hasEquity ? '当前铂金会员权益已生效' : ' 开户成功后，次日即享铂金会员权益' }}
        </div>
        <div class="btn" @click="goEquety" v-if="!hasEquity && isBindCard">查看会员权益</div>
        <div class="btn" @click="goEquety" v-if="hasEquity">立即体验会员权益</div>
        <div class="btn" @click="goCardList" v-if="!isBindCard && !hasEquity">添加银行卡</div>
      </div>
    </template>
  </focus-render>
</template>

<script lang="ts" setup>
import 'swiper/swiper.min.css'
import { provide, readonly, ref, toRaw, computed, nextTick, watchEffect, watch, onMounted } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import { gaoduankaService } from '../service/service'
const { modalStore, dynimicDataStore } = focusStore.stores
const { activityService, taskService, jumpService, mgmService } = focusServices
const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { pageid, aid, fid } = locationHrefPared.query
const focusFid = fid || pageid
const activityId = ref('')
const hasAccount = ref(false)
const isBindCard = ref(false)
const hasEquity = ref(false)
const isLogined = ref(false)
const jumpLink = ref('')

watchEffect(() => {
  if (hasAccount.value && activityId.value) {
    modalStore.loadingStart('gaoduanka_info')
    gaoduankaService.getUserInfo(activityId.value).then((data) => {
      console.log('🚀 ~ file: gaoduanka.vue:55 ~ gaoduankaService.getUserInfo ~ data:', data)

      // 不符合哦！
      if (!data.checkStatus) {
        console.error('不符合哦！')
        // jumpService.jump(
        //   {
        //     path: jumpLink.value,
        //   },
        //   true
        // )
        // return
      }
      isBindCard.value = data.isBindCard
      hasEquity.value = data.hasEquity
      modalStore.loadingEnd('gaoduanka_info')
    })
  }
})

function afterLogin(data: any) {
  console.log('🚀 ~ file: gaoduanka.vue:56 ~ afterLogin ~ data:', data)
  hasAccount.value = data.hasAccount
  isLogined.value = data.isLogined
}

function clickTip() {
  modalStore.confirmContrl({
    show: true,
    contents: ['指定高端银行卡包括招商银行金葵花卡、钻石卡、私人银行卡。'],
    btnConfirmText: '知道了',
    hideCancel: true,
  })
}

function goApp() {
  jumpService.commonUse.openAccount()
}

function goCardList() {
  jumpService.jump({
    path: '/bindCard/BankCardListScene',
  })
}

function goEquety() {
  jumpService.jump({
    path: '/memberEquity/MemberEquityCenterScene',
  })
}

function setFocus(fc: any) {
  // focusRenderCtr.value = fc
}

function slotMountedCb() {
  return {
    gaoduanka: (data: any) => {
      console.log('🚀 ~ file: kmh.vue:78 ~ slotMountedCb ~ data:', data)

      activityId.value = data[0] || ''
      jumpLink.value = data[1] || ''
    },
  }
}
</script>

<style lang="scss" scoped>
.main {
  margin: 0 auto;
  .tip {
    width: 542px;
    height: 84px;
    background: url('../img/gaoduanka/bg_tip.png') no-repeat;
    background-size: 100% 100%;
    font-size: 28px;
    color: #ae6e2b;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    margin-bottom: 20px;
    padding-bottom: 2px;
  }
  .btn {
    width: 530px;
    height: 138px;
    font-size: 30px;
    font-weight: bold;
    text-align: center;
    background: url('../img/gaoduanka/bg_btn.png') no-repeat;
    background-size: 100% 100%;
    color: #fff;
    line-height: 136px;
    margin: 0 auto;
    margin-top: -5px;
    cursor: pointer;
  }
  .btn-tip {
    height: 28px;
    width: 28px;
    background: url('../img/gaoduanka/icon_tip.png') no-repeat;
    background-size: contain;
    cursor: pointer;
    margin-left: 4px;
  }
}
</style>
