<template>
  <div class="mgm-list-classic">
    <div class="flex header" :class="{ actived: selectedHeaderIndex }" v-if="headerListData.length > 1">
      <div
        class="header-bg"
        :style="{
          background: curStyles.headerBg,
        }"
      ></div>
      <div
        class="item-header"
        v-for="(item, index) in headerListData"
        :key="index"
        :class="{ actived: selectedHeaderIndex === index }"
        @click="selectedHeaderIndex = index"
        :style="[
          {
            color: selectedHeaderIndex === index ? curStyles.headerActivedColor : curStyles.headerColor,
          },
        ]"
      >
        <p>{{ item.title }}</p>
        <img
          :src="curStyles.headerActivedBg || require('../../img/kmh/bg_header_actived.png')"
          alt=""
          class="bg"
          :class="`bg_${index}`"
        />
      </div>
    </div>
    <div
      class="flex list-nav"
      :style="{
        background: curStyles.navBg,
      }"
    >
      <div
        class="item-nav"
        v-for="(item, index) in navList"
        :key="index"
        :class="{ actived: selectedNavIndex === item.key }"
        @click="selectedNavIndex = item.key"
        :style="{
          color: selectedNavIndex === item.key ? curStyles.activedColor : curStyles.titleColor,
        }"
      >
        {{ item.label }}
        <div class="bar" :style="selectedNavIndex === item.key ? `background:${curStyles.activedColor}` : ''"></div>
      </div>
    </div>
    <div class="list-main">
      <div class="flex item-main" v-for="(item, index) in showList" :key="index">
        <img class="avator" :src="item.avator" />
        <div class="info">
          <div class="name">
            <div class="name-wrap">
              <p>{{ item.nickName }}</p>
            </div>
          </div>
          <p class="time">{{ item.time }}</p>
        </div>
        <div class="status">{{ item.isCustomEvent ? item.statusText + curCustomEventTitle : item.statusText }}</div>
      </div>
      <div class="empty" v-if="!showList.length">还没有记录,快去邀请吧~</div>
    </div>
    <div class="btn-showMore" @click="clickShowMore" v-if="isShowCurBtnMore">
      {{ isShowAll ? '收起' : '显示更多' }}
      <img src="../../img/kmh/arrow.png" alt="" class="icon" :class="{ showall: isShowAll }" />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  inject,
  provide,
  toRefs,
  watch,
  ref,
  watchEffect,
  unref,
  isRef,
  isProxy,
  toRaw,
  markRaw,
  computed,
  defineProps,
  reactive,
} from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import { kmhService } from '../../service/service'
const { jumpService, mgmService, focusService } = focusServices
const { modalStore, dynimicDataStore } = focusStore.stores
const props = defineProps({
  activityId: {
    type: String,
    default: '',
  },
})

const configData = ref({
  minSize: '3',
  maxSize: '100',
  uiType: {
    headerActivedBg: '',
    headerActivedColor: '#D13333',
    headerBg: '#fdf9e8',
    headerColor: '#A2630F',
    tabsText: {
      '0000': '未开户',
      '0001': '已开户',
      '0002': '认证',
      '9999': '已达标',
    },
    type: '4',
  },
})

const showMinLenth = computed(() => {
  const { minSize = '3' } = configData.value
  console.log('🚀 ~ file: focus-mgm-list-classic.vue:98 ~ showMinLenth ~ minSize:', minSize)
  return minSize ? Number(minSize) : 3
})

const showMaxLength = computed(() => {
  const { maxSize = '100' } = configData.value
  console.log('🚀 ~ file: focus-mgm-list-classic.vue:103 ~ showMaxLength ~ maxSize:', maxSize)

  return maxSize ? (Number(maxSize) >= 100 ? 100 : Number(maxSize)) : 100
})
const selectedHeaderIndex = ref(0)
const selectedNavIndex = ref('')
const headerTypes = [
  { title: '新户邀请记录', type: '1' },
  { title: '老户邀请记录', type: '2' },
]
const tabTypes = [
  {
    key: '0000',
    label: '未开户',
  },
  {
    key: '0001',
    label: '已开户',
  },
  {
    key: '0002',
    label: '认证',
  },
  {
    key: '9999',
    label: '已达标',
  },
]
const originList = reactive({})
const headerListData = ref<
  {
    title: string
    navs: { key: string; label: string; customEventTitle: string }[]
  }[]
>([])
const pageContrls = reactive({})

const curStyles = computed(() => {
  const { uiType } = configData.value
  const { headerColor, headerBg, headerActivedBg } = uiType
  const headerStyles = {}
  return {
    navBg: '#ffeabe',
    activedColor: '#D13333',
    titleColor: '#A2630F',
    headerColor: headerColor || '#a2630f',
    headerBg: headerBg || '#fdf9e8',
    headerActivedBg,
    headerActivedColor: '#D13333',
  }
})

function initData() {
  console.log('初始化啦！')
  const uiType = configData.value.uiType
  const { type, tabsText } = uiType
  const contrls = {
    offset: 0,
    limit: 100,
    total: 0,
    curLength: 0,
  }
  const contrlsObj = {}
  const _originList = {}

  let filterHeaderTypes = headerTypes
  let headerList: any[] = []

  filterHeaderTypes.forEach((header) => {
    const { title } = header
    const headerType = header.type
    const headerData: {
      title: string
      navs: { key: string; label: string; customEventTitle: string }[]
    } = {
      title,
      navs: [],
    }
    tabTypes.forEach((tabType) => {
      const { key } = tabType
      const newKey = `${headerType}_${key}`

      if (headerType === '2' && (key === '0000' || key === '9999')) {
        return
      }
      let customEventTitle = ''
      let text = tabsText[key]
      if (key === '0002') {
        customEventTitle = text
        text = '已' + text
      }
      if (tabsText[key]) {
        contrlsObj[newKey] = contrls
        _originList[newKey] = []
        headerData.navs.push({
          key: newKey,
          label: text,
          customEventTitle,
        })
      }
    })
    headerList.push(headerData)
    console.log('🚀 ~ file: MgmListCompany.vue:229 ~ filterHeaderTypes.forEach ~ headerList:', headerList)
  })
  Object.assign(pageContrls, contrlsObj)
  Object.assign(originList, _originList)
  headerListData.value = headerList
}
initData()

const navList = computed(() => {
  const navs =
    (headerListData.value[selectedHeaderIndex.value] && headerListData.value[selectedHeaderIndex.value].navs) || []
  return navs
})

const curNavTitle = computed(() => {
  const target = navList.value.find((i) => i.key === selectedNavIndex.value)
  console.log('🚀 ~ file: focus-mgm-list-classic.vue:215 ~ curNavTitle ~ target:', target)
  return target && target.label
})

const curCustomEventTitle = computed(() => {
  const target = navList.value.find((i) => i.key === selectedNavIndex.value)
  console.log('🚀 ~ file: focus-mgm-list-classic.vue:215 ~ curNavTitle ~ target:', target)
  return target && target.customEventTitle
})

const showList = computed(() => {
  if (selectedNavIndex.value) {
    const target = originList[selectedNavIndex.value] || []
    console.log('🚀 ~ file: MgmListCompany.vue:258 ~ showList ~ target:', target)
    const pageContrl = pageContrls[selectedNavIndex.value]
    // console.log('🚀 ~ file: focus-mgm-list-classic.vue:254 ~ showList ~ pageContrl:', pageContrl, pageContrl.curLength)
    // console.log('🚀 ~ file: focus-mgm-list-classic.vue:229 ~ showList ~ target:', target)
    return target.slice(0, pageContrl.curLength)
  }
  return []
})

const isShowCurBtnMore = computed(() => {
  const pageContrl = pageContrls[selectedNavIndex.value]
  console.log('🚀 ~ file: MgmListCompany.vue:269 ~ isShowCurBtnMore ~ pageContrl:', pageContrl)
  const { offset, limit, total, curLength, catchTime } = toRefs(pageContrl)
  const max = 100
  console.log('🚀 ~ file: MgmListCompany.vue:272 ~ isShowCurBtnMore ~ max:', max)

  if (pageContrl) {
    return total.value > showMinLenth.value
  }
  return false
})

const isShowAll = computed(() => {
  const pageContrl = pageContrls[selectedNavIndex.value]
  const { offset, limit, total, curLength, catchTime } = toRefs(pageContrl)
  return curLength.value >= total.value
})

watchEffect(() => {
  if (navList.value && navList.value.length) {
    if (!selectedNavIndex.value) {
      selectedNavIndex.value = navList.value[0].key
    }
  }
})

watch(selectedHeaderIndex, (newHeader, oldHeader) => {
  if (newHeader !== oldHeader) {
    selectedNavIndex.value = navList.value[0].key
  }
})

getData()

function getData() {
  if (!focusCore.hasAccount) {
    console.error('没开户哦！')
    return
  }
  kmhService.getCompanyInviteList(props.activityId).then((res) => {
    console.log('🚀 ~ file: focus-mgm-list-classic.vue:318 ~ .then ~ res:', res)
    const { lists } = res
    originList['1_0000'] = lists['101']
    originList['1_0001'] = lists['102']
    originList['1_0002'] = lists['103']
    originList['1_9999'] = lists['104']
    originList['2_0001'] = lists['105']
    originList['2_0002'] = lists['106']

    const headers = ['1_0000', '1_0001', '1_0002', '1_9999', '2_0001', '2_0002']
    const listsMap = ['101', '102', '103', '104', '105', '106']
    headers.forEach((i, index) => {
      const k = listsMap[index]
      pageContrls[i] = {
        offset: 0,
        limit: 100,
        total: lists[k].length || 0,
        curLength: showMinLenth.value,
      }
    })
    modalStore.loadingEnd('getInviteList')
  })
}

function clickShowMore() {
  // isShowAll.value = !isShowAll.value

  const pageContrl = pageContrls[selectedNavIndex.value]
  const { offset, limit, total, curLength, catchTime } = toRefs(pageContrl)

  if (!isShowAll.value) {
    curLength.value = total.value
  } else {
    curLength.value = showMinLenth.value
  }
}
</script>

<style scoped lang="scss">
.mgm-list-classic {
  width: 670px;
  overflow: hidden;
  border-radius: 15px;
  position: relative;
  margin: 0 auto;
  &::after {
    display: block;
    content: '';
    width: 100%;
    height: 30px;
    background: #fff;
  }
  .flex {
    display: flex;
    align-items: center;
    justify-content: space-around;
    > div {
      flex: auto;
    }
  }
  .header {
    width: 100%;
    height: 92px;
    border-radius: 16px 16px 0 0;
    align-items: flex-end;
    position: relative;
    .header-bg {
      width: 100%;
      height: 82px;
      position: absolute;
      left: 0;
      bottom: 0;
      border-radius: 16px 16px 0 0;
      background: #fdf9e8;
    }
    .item-header {
      width: 50%;
      flex: auto;
      height: 82px;
      position: relative;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2;
      p {
        color: #a2630f;
        font-weight: 600;
        position: relative;
        line-height: 1;
        font-size: 28px;
        text-align: center;
        z-index: 2;
      }
      .bg {
        position: absolute;
        width: 355px;
        height: 92px;
        z-index: 1;
        display: none;
      }
      &.actived {
        p {
          color: #d13333;
        }
        .bg {
          display: block;
          top: -10px;
          &.bg_0 {
            left: 0;
          }
          &.bg_1 {
            right: 0;
            transform: rotateY(180deg);
          }
        }
      }
    }
  }

  .list-nav {
    width: 100%;
    background-color: #ffeabe;
    .item-nav {
      height: 92px;
      text-align: center;
      line-height: 92px;
      width: 100%;
      position: relative;
      font-size: 28px;
      color: #a2630f;
      font-weight: 600;
      .bar {
        opacity: 0;
      }
      &.actived {
        color: #d13333;
        .bar {
          opacity: 1;
          background: #d13333;
          width: 100%;
          height: 7px;
          position: absolute;
          bottom: 0;
          left: 0;
        }
      }
    }
  }
  .list-main {
    background: #fff;
    max-height: 1160px;
    overflow-y: auto;
    overflow-x: hidden;
    width: 100%;
    .empty {
      height: 172px;
      box-sizing: border-box;
      width: 100%;
      padding-top: 50px;
      font-size: 28px;
      color: #808bab;
      text-align: center;
      line-height: 42px;
      font-weight: 400;
      background-color: #ffffff;
    }

    .item-main {
      padding: 20px 25px;
      position: relative;

      &::after {
        position: absolute;
        content: '';
        left: 50%;
        bottom: 0;
        background: #f5f5f5;
        width: 650px;
        height: 1px;
        transform: translateX(-50%);
      }

      .avator {
        flex: none;
        width: 70px;
        height: 70px;
        border-radius: 50%;
        margin-right: 20px;
      }
      .info {
        flex: auto;
        text-align: left;
        line-height: 1.5;
        width: 40%;
        margin-right: 20px;
        .name {
          font-size: 28px;
          color: #808bab;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .name-wrap {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            p {
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
          }
          .tag {
            flex: none;
            height: 38px;
            &.t_01 {
              width: 141px;
            }
            &.t_02 {
              width: 118px;
            }
          }
        }
        .time {
          font-size: 24px;
          color: #b4bacc;
          font-weight: 400;
        }
      }
      .status {
        flex: none;
        width: fit-content;
        text-align: right;
        height: 70px;
        line-height: 70px;
        font-size: 28px;
        color: #808bab;
      }
    }
  }
  .btn-showMore {
    padding-top: 30px;
    font-size: 24px;
    color: #456ce6;
    line-height: 42px;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    .icon {
      width: 22px;
      height: 13px;
      margin-left: 10px;
      &.showall {
        transform: rotate(180deg);
      }
    }
  }
}
</style>
