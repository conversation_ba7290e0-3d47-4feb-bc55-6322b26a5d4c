<template>
  <div class="photoCard-container">
    <div class="photo" :class="{ vertical: isVertical }">
      <img
        class="photo-img"
        :src="photoData.fileUrl || defaultImg"
        @error="(e:any)=>{ e.target.src = defaultImg }"
        alt=""
      />
      <div class="photo-mask">
        <img class="user-avator" :src="photoData.headImgUrl" alt="" />
        <div class="user-name">{{ photoData.nickName }}</div>
        <div class="photo-likeCount">{{ photoData.likedCount }}</div>
        <img class="photo-like" src="../../img/photograph/likeinhome.png" alt="" />
      </div>
      <div v-if="selectedTab === ListType.Like" class="rank-icon">
        <img v-if="photoData.rank < 4" class="rank-top3" :src="rankPhoto" alt="" />
        <div v-else class="rank-other">
          <span>NO.{{ photoData.rank }}</span>
        </div>
      </div>
    </div>
    <div class="title">
      <p class="photo-title">{{ photoData.title }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { toRefs, computed } from 'vue'
import { PhotoData, ListType, directionType } from '../../Types/index'
const loadErr = require('../../img/photograph/loadErr.png')
const loadErrHoriaontal = require('../../img/photograph/loadErrHorizontal.png')

const props = defineProps<{
  photoData: PhotoData
  selectedTab: ListType
}>()

const { photoData, selectedTab } = toRefs(props)

const rankPhoto = computed(() => {
  if (photoData.value.rank < 4 && photoData.value.rank > 0)
    return require(`../../img/photograph/rank_${photoData.value.rank}.png`)
  else return ''
})

const isVertical = computed(() => {
  return photoData.value.photoDirection === directionType.Vertical
})

const defaultImg = computed(() => {
  return photoData.value.photoDirection === directionType.Vertical ? loadErr : loadErrHoriaontal
})
</script>

<style lang="scss" scoped>
.photoCard-container {
  margin: 7px;
  .title {
    width: 100%;
    max-height: 96px;
    padding: 12px 12px 21px 12px;
    background-color: #f8e0a1;
    overflow: hidden;
    display: flex;
    .photo-title {
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      font-size: 24px;
      color: #702215;
      opacity: 0.8;
      text-align: justify;
      line-height: 32px;
      font-weight: 400;
    }
  }
  .photo {
    position: relative;
    //padding-top: 75%;
    background-color: black;
    border-radius: 12px;
    overflow: hidden;
    width: 324px;
    height: 243px;
    display: flex;
    align-items: center;
    justify-content: center;
    &.vertical {
      //padding-top: 133.33%;
      height: 432px;
      .photo-img {
        height: 100%;
        width: auto;
      }
    }
    .photo-mask {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 92px;
      background-image: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
      border-radius: 0 0 12px 12px;
    }
    .photo-img {
      width: 100%;
      height: auto;
      display: block;
      //position: absolute;
      //left: 50%;
      //top: 50%;
      //transform: translate(-50%, -50%);
    }
    .rank-icon {
      position: absolute;
      top: 0;
      left: 14px;
      .rank-other {
        background-image: url('../../img/photograph/bigger4.png');
        width: 48px;
        height: 30px;
        background-size: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          font-size: 12px;
          line-height: 12px;
          color: #fff8f6;
          letter-spacing: 0;
          text-align: center;
          font-weight: 600;
          margin-top: -10px;
        }
      }
      .rank-top3 {
        width: 48px;
        height: 45px;
      }
    }
    .user-avator {
      position: absolute;
      bottom: 12px;
      left: 12px;
      width: 48px;
      height: 48px;
      border: 2px solid #edeeef;
      border-radius: 50%;
    }
    .user-name {
      position: absolute;
      bottom: 12px;
      left: 75px;
      font-size: 28px;
      letter-spacing: 0;
      line-height: 48px;
      font-weight: 600;
      color: #ffffff;
    }
    .photo-like {
      position: absolute;
      bottom: 24px;
      right: 12px;
      width: 28px;
      height: 25px;
    }
    .photo-likeCount {
      position: absolute;
      bottom: 12px;
      right: 48px;
      font-size: 28px;
      line-height: 48px;
      margin-right: 10px;
      color: white;
    }
  }
}
</style>
