<template>
  <div class="wxupload">
    模拟App获取图片的功能
    <input class="getphoto" type="file" ref="fileInput" @change="handleFileSelect" />
  </div>
</template>

<script setup lang="ts">
import photoService from '../../service/photoService'
function handleFileSelect(event: any) {
  const selectedFile = event.target.files[0]
  // 处理用户选择的文件
  console.log(selectedFile)
  // 可以在这里调用上传文件的方法
  photoService.AppPhotoHandler.upload(selectedFile).then((res) => {
    console.log('🚀 ~ file: wxupload.vue:16 ~ photoService.AppPhotoHandler.upload ~ res:', res)
  })
}
</script>

<style scoped lang="scss">
.wxupload {
  width: 100%;
  height: 100px;
  background-color: aqua;
  font-size: 30px;
  font-weight: bold;
  line-height: 1.5;

  .getphoto {
    width: 100px;
    height: 50px;
    background: red;
  }
}
</style>
