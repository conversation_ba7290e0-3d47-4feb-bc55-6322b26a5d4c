import { createRouter, createWebHashHistory } from 'vue-router'
import { focusCore, focusServices } from '@focus/render'
import licaijie from '../pages/licaijie.vue'
import photograph from '../pages/photo_competition/photograph.vue'
import photoDetail from '../pages/photo_competition/photoDetail.vue'
import uploadPhoto from '../pages/photo_competition/uploadPhoto.vue'
import w2kmgmVue from '../pages/w2kmgm.vue'
import kmhVue from '../pages/kmh.vue'
import zcjj from '../pages/zcjj.vue'
import kmh2025 from '../pages/kmh2025.vue'
import gaoduanka from '../pages/gaoduanka.vue'
// import photoDetail from '../components/photograph/PhotoDetail.vue'
import nzj from '../pages/nzj.vue'
import wyd from '../pages/wyd.vue'
const { loginService } = focusServices
const routes = [
  {
    path: '/',
    redirect: '/licaijie',
  },
  {
    path: '/licaijie',
    component: licaijie,
    meta: {
      noNeedLogin: true,
    },
  },
  {
    path: '/kmh',
    component: kmhVue,
    meta: {
      noNeedLogin: true,
    },
  },
  {
    path: '/zcjj',
    component: zcjj,
    meta: {
      noNeedLogin: true,
    },
  },
  {
    path: '/kmh2025',
    component: kmh2025,
    meta: {
      noNeedLogin: true,
    },
  },
  {
    path: '/nzj',
    component: nzj,
    meta: {
      noNeedLogin: true,
      title: '微众银行',
    },
  },
  {
    path: '/gaoduanka',
    component: gaoduanka,
    meta: {
      noNeedLogin: true,
    },
  },
  // {
  //   path: '/w2kmgm',
  //   component: w2kmgmVue,
  //   meta: {
  //     noNeedLogin: true,
  //   },
  // },
  {
    path: '/photograph',
    meta: {
      title: '微众银行',
      noNeedLogin: true,
    },
    children: [
      {
        path: '',
        component: photograph,
        meta: {
          title: '微众银行',
          noNeedLogin: true,
        },
      },
      {
        path: 'photodetail',
        component: photoDetail,
        meta: {
          title: '作品详情',
          noNeedLogin: true,
        },
      },
      {
        path: 'uploadPhoto',
        component: uploadPhoto,
        meta: {
          title: '上传参赛作品',
          noNeedLogin: true,
        },
      },
    ],
  },
  {
    path: '/wyd',
    component: wyd,
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes, // `routes: routes` 的缩写
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  },
})
router.beforeEach((to, from, next) => {
  console.log('🚀 ~ file: index.ts:76 ~ router.beforeEach ~ to:', to)
  focusCore.routerBeforeEach(to, from, next)
})

export default router
