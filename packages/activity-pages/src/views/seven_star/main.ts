BUILD_TEST &&
  import(/* webpackChunkName: "vconsole" */ 'vconsole').then((cls) => {
    const Cls = cls.default
    return new Cls()
  })

import { createApp } from 'vue'
import App from './App.vue'
// 导入组件库-focusRender
// import { focusRender } from '../../packages/index'
import focusRender from '@focus/render'
import '@focus/render/build-lib/focus-render.css'
import { createPinia } from 'pinia'
const app = createApp(App)
const pinia = createPinia()
app.use(pinia).use(focusRender, { $pinia: pinia })

app.mount('#app')
