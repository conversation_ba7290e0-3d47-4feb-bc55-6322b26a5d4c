<template>
  <div class="task-list" v-show="taskList.length">
    <div class="list">
      <div
        class="item"
        :class="item.direction === 'row' ? '' : 'reverse'"
        v-for="(item, index) in taskList"
        :key="item.taskId"
        :style="{ backgroundImage: `url(${item.bgUrl})`, flexDirection: item.direction }"
      >
        <div class="left">
          <img :src="item.starImg" alt="" />
        </div>
        <div class="center">
          <div class="desc">
            <div v-html="item.showTitle"></div>
          </div>

          <div class="task-reward">
            <span v-html="item.someThingStr"></span>
            已得 <img :src="item.taskRewardCoinIcon" /> x{{ item.taskRewardCoinNums }}
          </div>
        </div>
        <div class="right">
          <div
            class="btn"
            :class="item.isDone ? 'disabled' : ''"
            @click="clickTask(item, index)"
            :style="{ opacity: item.btnDisabled ? '0.3' : '1' }"
          >
            <img :src="item.btnTxtImg" alt="" />
          </div>
          <!-- <div class="light-nums">{{ item.taskSummaryCount || 0 }}人已点亮</div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, onMounted } from 'vue'
import { focusStore, focusServices } from '@focus/render'
const { taskStore, modalStore } = focusStore.stores
const { jumpService, mgmService } = focusServices
const props = defineProps<{
  taskList: any[]
}>()
onMounted(() => {
  const dom = document.querySelector('.task-list')
  console.log('🚀 ~ file: seven.vue ~ line 50 ~ onMounted ~ dom', dom)
  dom?.addEventListener('click', (e) => {
    const tip = e.target
    const popid = (tip as HTMLElement)?.getAttribute('data-popid')
    if (popid) {
      modalStore.setPopupViewId(1)
    }
  })
})

const clickTask = (data: any, index: number) => {
  console.log('🚀 ~ file: focus-task-list.vue ~ line 40 ~ clickTask ~ data', data)
  const { placeId, jumpPath, isDone, isShare, mgmAid, reportid, taskPerfectibilityTime, shareData, btnDisabled } = data
  if (btnDisabled) {
    return false
  }
  if (isShare) {
    mgmService.clickShare(reportid, shareData)
  } else {
    jumpService.jump({ path: jumpPath })
  }
}
</script>

<style lang="scss">
.height-light {
  font-size: 32px;
  color: #ec6b7b;
}
.task-list {
  margin: 0 auto;
  box-sizing: border-box;
  line-height: 1.5;

  .list {
    width: 100%;
    padding-bottom: 20px;
    .item {
      width: 100%;
      background-image: linear-gradient(to bottom, #ffa45940 0%, #fff 96%);
      color: #405080;
      margin-bottom: 10px;
      position: relative;
      width: 750px;
      height: 250px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 0 40px;
      padding-left: 0;
      .left {
        width: 280px;
        height: 250px;
        flex: none;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .center {
        width: 290px;
        height: 100%;
        flex: auto;
        display: flex;
        align-items: flex-start;
        justify-content: flex-end;
        flex-direction: column;
        box-sizing: border-box;
        padding: 35px 30px;
        padding-left: 0;

        .desc {
          color: #2d3b75;
          font-size: 28px;
          width: 100%;
          flex: none;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: left;
          flex: auto;
          img {
            width: 26px;
            height: 26px;
          }
          .btn-tip {
            width: 30px;
            height: 30px;
            margin-bottom: 5px;
            margin-left: 10px;
          }
        }
        .task-reward {
          width: 150%;
          font-size: 22px;
          color: #7583bc;
          line-height: 33px;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: flex-start;

          img {
            height: 26px;
            width: 26px;
          }
        }
        .progress-msg {
          width: 100%;
          display: flex;
          align-items: flex-end;
          height: 33px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .progress-bar {
            background: #fff;
            height: 10px;
            border-radius: 10px;
            width: 200px;
            margin-right: 20px;
            span {
              display: block;
              height: 100%;
              background: #89d1ff;
              border-radius: 10px;
            }
          }
          .progress {
            font-size: 22px;
            color: #7583bc;
            flex: none;
          }
        }
      }
      .right {
        box-sizing: border-box;
        padding-bottom: 20px;
        position: relative;
        .light-nums {
          position: absolute;
          right: 0;
          bottom: -20px;
          font-size: 22px;
          color: #7583bc;
          width: 200px;
          text-align: right;
        }
        .btn {
          flex: none;
          width: 120px;
          height: 125px;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-image: url('./img/btn_task_1.png');
          color: #fff;
          font-size: 24px;
          border-radius: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
          > img {
            height: 36px;
          }
          &.disabled {
            // background-image: url('./img/btn_task_2.png');
          }
        }
      }

      &.reverse {
        padding-right: 0;
        padding-left: 40px;
        .center {
          padding-left: 30px;
          padding-right: 0;
          .progress-msg {
            padding-left: 20px;
          }
        }
        .right {
          .light-nums {
            text-align: left;
            right: none;
            left: 0;
          }
        }
      }
    }
  }
}
</style>
