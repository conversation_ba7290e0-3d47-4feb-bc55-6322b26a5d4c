import { focusCore } from '@focus/render'

const cgis = {
  getherStars: {
    name: '合成七龙珠',
    url: '/wm-htrserver/cop/hj/save_gather_seven_stars_record',
  },
}

class SevenService {
  getherStars(configId: number) {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getherStars, {
          configId,
        })
        .then((res: any) => {
          console.log('🚀 ~ file: service.ts ~ line 15 ~ SevenService ~ getherStars ~ res', res)
          resolve(res)
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: service.ts ~ line 19 ~ SevenService ~ .then ~ err', err)
        })
    })
  }
}

export default new SevenService()
