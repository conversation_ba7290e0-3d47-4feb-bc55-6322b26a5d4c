<script setup lang="ts">
import { UrlParser } from '../../utils/url'

import { ref, computed, onMounted } from 'vue'
import sevenService from './service'
import seven from './seven.vue'
import { focusServices, Mask, modalStore } from '@focus/render'
const { mgmService, lotteryService, taskService } = focusServices
import Seven from './seven.vue'
console.log('🚀 ~ file: App.vue ~ line 15 ~ focusServices', focusServices)

const configId = 4
const locationHrefPared = new UrlParser(window.location.href)
const { pageid, aid, fid } = locationHrefPared.query
const focusFid = pageid || fid
const focusAid = aid

const curTaskList = ref<any[]>([])
const curLotteryTimes = ref(0)
const dialogTogettherFlag = ref(false)

const synthesisFlag = ref(0)

const taskDoneLen = ref(0)

let shareData = {}
const userCoinsAmout = ref(0)
const dataIsReady = ref(false)

function setFocus(loginData: any) {
  console.log('🚀 ~ file: App.vue ~ line 31 ~ setFocus ~ loginData', loginData)
  if (loginData.hasAccount) {
    //获取抽奖机会
    lotteryService.getLotteryTimes(configId).then((res: any) => {
      console.log('🚀 ~ 抽奖信息：！', res)
      const { lotteryTimes = 0 } = res || {}
      curLotteryTimes.value = lotteryTimes
    })

    const somethings = [
      '已登录: {times}',
      '已邀请: {times}',
      '已设置: {times}',
      '已转入: {times}元',
      '已新增: {times}元',
      '已购买: {times}元',
      '已购买: {times}元',
    ]

    taskService.getTaskProgressByConfigId(configId).then((res: any) => {
      const {
        sortTaskList = [],
        taskCoins = 0,
        starsFlag, // 1 ：已经合成过
      } = res || {}
      console.log('🚀 ~ taskService 返回的任务列表 sortTaskList', sortTaskList)
      const taskList = sortTaskList
      if (!taskList.length) {
        return false
      }

      curTaskList.value = taskList.map((i: any, index: number) => {
        const starImg = require(`./img/task_star_${index + 1}_no.png`)
        const starDoneImg = require(`./img/task_star_${index + 1}.png`)

        const {
          maxProgress,
          curProgress,
          taskLotteryImgUrl,
          taskImgUrl,
          isDone,
          title,
          taskLotteryDesc,
          taskSummaryCount,
          lotteryTimesChange,
          jumpPath,
          shareExtra,
          isShare,
          btnDisabled,
          taskCoinNum, //当次任务获得代币数量
          taskCoinAmount, //当次任务每X元获得代币
          maxCoinSum, //最大可获得代币数量
          currTransCoin, //当前累计代币数量
          coinUrl, //代币url
          isMoneyTask,
          currTransAmt,
          currTransNum,
        } = i
        if (isDone) {
          taskDoneLen.value++
        }
        let progressPercent: any = null
        if (maxProgress) {
          const num = parseInt(((curProgress / maxProgress) * 100).toString())
          progressPercent = num + '%'
        }
        const isSingle = index % 2 !== 0
        if (isShare) {
          try {
            shareData = JSON.parse(shareExtra) || {}
            mgmService.setRtConnerShare('seven_share', shareData)
          } catch (err) {
            console.log('🚀 ~ file: App.vue ~ line 125 ~ setShareConfig ~ err', err)
          }
        }
        // 最多完成次数
        let progressMaxTimes = parseInt((maxCoinSum / taskCoinNum).toString(), 10).toString()

        let curProgressText = currTransNum
        if (isMoneyTask) {
          console.error('这是金钱任务，进度展示currTransAmt', currTransAmt)
          curProgressText =
            currTransAmt >= 10000 ? parseFloat((currTransAmt / 10000).toString()).toFixed(1) + '万' : currTransAmt
          progressMaxTimes = parseInt(((Number(progressMaxTimes) * taskCoinAmount) / 10000).toString(), 10) + '万'
        }

        const showTitle = `${title}(最多${maxCoinSum})`

        const someThingStr = somethings[index]

        return {
          ...i,
          progressPercent,
          showTitle: heightLight(showTitle, coinUrl, taskLotteryImgUrl),
          direction: index % 2 === 0 ? 'row' : 'row-reverse',
          btnTxtImg: taskImgUrl,
          starImg: isDone ? starDoneImg : starImg,
          bgUrl: isSingle ? require('./img/bg-task-reverse.png') : require(`./img/bg-task.png`),
          taskLotteryDesc,
          taskSummaryCount,
          jumpPath: jumpPath,
          shareData,
          btnDisabled: btnDisabled,
          taskRewardCoinNums: currTransCoin || 0,
          taskRewardCoinIcon: coinUrl,
          taskRewardCoinMax: maxCoinSum || 0,
          curProgressText,
          someThingStr: setSomthing(someThingStr, curProgressText),
          taskLotteryImgUrl,
        }
      })

      userCoinsAmout.value = taskCoins

      const isAllDone = !taskList.some((i) => !i.isDone)
      // const isAllDone = true
      if (starsFlag !== 1 && isAllDone) {
        // 全部完成了至少一次，且未合成；那就弹窗！
        dialogTogettherFlag.value = true
      }
      if (starsFlag === 1) {
        gether()
      }
      dataIsReady.value = true
    })
  }
}

function setSomthing(_str: string, curTimes: number) {
  const str = _str
    .replace('{times}元', `<span style="color:#ec6b7b;">${curTimes}</span><span style="padding-right:10px">元</span>`)
    .replace('{times}', `<span style="color:#ec6b7b;padding-right:10px">${curTimes}</span>`)
  return str
}

function heightLight(string: string, coinUrl: string, tipImg: string) {
  const style = 'color=#ec6b7b;'

  const str = string
    .replace('{coin}', `<img class="coin" src="${coinUrl}" />`)
    .replace(/{/g, '<span class="height-light" style="color:#ec6b7b;">')
    .replace(/}/g, '</span>')

  const btnTip = tipImg ? `<img class="btn-tip" src="${require('./img/tip.png')}" data-popid="${tipImg}"/>` : ''
  return `${str}${btnTip}`
}

function gether() {
  sevenService.getherStars(configId).then((res: any) => {
    console.log('🚀 ~ file: App.vue ~ line 148 ~ sevenService.getherStars ~ res', res)
    //synthesisFlag ——》  0：未合成；1：当次合成；2：已合成过
    synthesisFlag.value = res.synthesisFlag || 0
    if (res.synthesisFlag >= 1) {
      dialogTogettherFlag.value = false
    }
  })
}

function clickBtn(btnData: any) {
  // const { configData } = btnData
  // const { params } = configData
  // const jumpPath = params[0]
  // focusServices.jumpService.jump({ path: jumpPath })
  goLottery()
}

function hideTogettherDilaog() {
  dialogTogettherFlag.value = false
  // 关闭弹窗的时候 合成！
  gether()
}

function clickBtnStar(btnData: any) {
  const { configData } = btnData
  const { params } = configData
  const jumpPath = params[0]
  focusServices.jumpService.jump({ path: jumpPath })
}
const lotterLink = ref('')
function saveData(data: any) {
  const { configData } = data
  const { params } = configData
  const jumpPath = params[0]
  lotterLink.value = jumpPath
}
function goLottery() {
  // 跳转之前 合成！
  gether()
  setTimeout(() => {
    focusServices.jumpService.jump({ path: lotterLink.value })
  }, 100)
}
</script>

<template>
  <focus-render v-if="focusFid" :focusFid="focusFid" :focusAid="focusAid" @onLoginSucc="setFocus">
    <template #btn_getstars="slotProps">
      <!-- 星星按钮 -->
      <div class="btn-lottery-1" @click="clickBtnStar(slotProps)" :style="{ ...slotProps.styles.boxSize }">
        <img src="./img/btn-text-times-1.png" v-if="!userCoinsAmout" />
        <div class="get-star" v-else><img class="icon" src="./img/text_star.png" alt="" />{{ userCoinsAmout }}</div>
      </div>
    </template>
    <!-- 抽奖按钮 -->
    <template #btn_lottery="slotProps">
      <div
        class="btn-lottery-1 btn_2"
        @click="clickBtn(slotProps)"
        :style="{ ...slotProps.styles.boxSize }"
        :class="{ noic: dataIsReady && !curLotteryTimes && synthesisFlag > 1 }"
      >
        {{ saveData(slotProps) }}

        <img src="./img/btn-text-lottery.png" v-if="!curLotteryTimes && !synthesisFlag && !taskDoneLen" />
        <img
          src="./img/btn-text-lottery-2.png"
          v-if="curLotteryTimes ? true : dataIsReady && taskDoneLen >= 7 && synthesisFlag <= 1"
        />
        <img src="./img/btn-text-lottery-3.png" v-if="dataIsReady && !curLotteryTimes && synthesisFlag > 1" />
        <div
          class="get-chance"
          v-if="dataIsReady && !curLotteryTimes && !synthesisFlag && taskDoneLen && taskDoneLen < 7"
        >
          <span>{{ taskDoneLen }}</span>
        </div>
      </div>
    </template>
    <template v-slot:seven>
      <div class="seven-star">
        <Seven :taskList="curTaskList"></Seven>
      </div>
    </template>
  </focus-render>
  <Mask class="dialog-lottery" v-show="dialogTogettherFlag">
    <div class="contain">
      <div class="btn-close" @click="hideTogettherDilaog">
        <img src="./img/btn-close.png" alt="" />
      </div>
      <div class="title">
        <p>恭喜你点亮七种星球</p>
        <p>送你一次抽奖机会</p>
      </div>
      <img src="./img/dialog-cover.png" alt="" class="cover" />
      <div class="btn" @click="goLottery">去抽奖</div>
    </div>
  </Mask>
</template>

<style lang="scss">
@import '../../styles/normalize.scss';
body {
  background-repeat: no-repeat;
  width: 100%;
  overflow-x: hidden;
}
#app {
  width: 100%;
  text-align: center;
  overflow-x: none;
  line-height: 0;
  font-size: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  background: #2b48c4;
}
.dialog-lottery {
  .contain {
    background: #fff;
    border-radius: 8px;
    margin-top: -100px;
    width: 575px;
    height: 630px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    position: relative;
    .title {
      margin-top: 60px;
      margin-bottom: 10px;
      font-size: 34px;
      color: #405080;
      text-align: center;
      line-height: 51px;
      font-weight: 500;
    }
    .cover {
      width: 391px;
      height: 310px;
    }
    .btn {
      background: #456ce6;
      border-radius: 44px;
      width: 300px;
      height: 72px;
      font-size: 28px;
      color: #ffffff;
      text-align: center;
      line-height: 72px;
    }
    .btn-close {
      width: 60px;
      height: 60px;
      position: absolute;
      right: 0;
      top: -110px;
    }
  }
}

.btns-lottery {
  width: 100%;
  height: 300px;
  display: flex;
  > div {
    width: 50%;
    height: 100%;
    background: gray;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    .btn {
      width: 290px;
      height: 74px;
      background-color: red;
      margin-bottom: 40px;
    }
  }
}

.btn-lottery-1 {
  width: 100%;
  height: 100%;
  background-image: url('./img/but-01.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  > img {
    width: 112px;
    // height: 36px;
  }
  &.noic {
    > img {
      width: 90px;
    }
  }
  .get-star,
  .get-chance {
    // background: url('./img/btn-text-times.png');
    background-size: auto 34px;

    background-repeat: no-repeat;
    // width: 217px;
    height: 36px;
    font-size: 32px;
    position: relative;
    background-position: center;
  }
  .get-star {
    display: flex;
    align-items: center;
    justify-content: center;
    .icon {
      width: 162px;
      height: 34px;
    }
    color: #fade8e;
    display: block;
    text-align: center;
    font-weight: bold;
    line-height: 34px;
  }
  .get-chance {
    background-image: url('./img/btn-text-lottery-1.png');
    width: 215px;
    height: 34px;
    span {
      position: absolute;
      top: 45%;
      display: block;
      width: 50px;
      text-align: center;
      left: 113px;
      color: #fade8e;
      font-weight: bold;
    }
  }
}
</style>
