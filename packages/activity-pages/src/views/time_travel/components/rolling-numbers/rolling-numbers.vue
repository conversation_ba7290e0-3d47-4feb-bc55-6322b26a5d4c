<template>
  <div class="num-list" :style="{ fontSize, height: fontSize }" ref="numlist">
    <div
      class="single-num"
      v-for="(num, index) in numsArray"
      :key="index"
      :style="`${num === ',' || num === '.' ? 'transform:translateY(0)' : ''}`"
    >
      <div class="item notnum" v-if="num === ',' || num === '.'">
        {{ num }}
      </div>
      <template v-if="num !== ',' && num !== '.'">
        <div class="item" v-for="(item, index) in numsList" :key="index">{{ item }}</div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, getCurrentInstance } from 'vue'
import { px2vw } from '../../../../utils'
const props = defineProps<{
  number: string
  delay: number
  height: number
}>()
const numsList = [9, 8, 7, 6, 5, 4, 3, 2, 1, 0]
const numsArray = computed(() => {
  return props.number.split('')
})
const fontSize = px2vw(props.height)
const instance = getCurrentInstance()

onMounted(() => {
  const numListDom: any = instance?.refs.numlist
  console.log('🚀 ~ file: rolling-numbers.vue ~ line 36 ~ onMounted ~ dom', numListDom)
  console.log(numsArray)
  if (numListDom) {
    setTimeout(() => {
      const list = numListDom.querySelectorAll('.single-num')
      for (let i = 0; i < list.length; i++) {
        const target = numsArray.value[i]
        console.log('🚀 ~ file: rolling-numbers.vue ~ line 42 ~ onMounted ~ target', target)
        const dom = list[i]
        if (target !== '.' && target !== ',') {
          const targetNum = Number(target)
          const style = `;transform:translateY(${(9 - targetNum) * -100}%)`
          dom.style = dom.style += style
        }
      }
    }, 2000)
  }
})
</script>

<style lang="scss" scoped>
.num-list {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  .single-num {
    height: 100%;
    transform: translateY(-900%);
    transition: all 0.5s;
    @for $i from 1 through 10 {
      &:nth-child($i + 1) {
        transition: all 0.5s 0.1s * $i;
      }
    }
  }
}
</style>
