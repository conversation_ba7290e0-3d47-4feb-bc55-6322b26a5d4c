<template>
  <div class="page-10" :class="`time_${timeIndex}`">
    <template v-if="canShow">
      <div class="section s_1">
        <p>
          你累计登录微众银行App <span class="big-num">{{ loginTimes }}</span> 天
        </p>
        <p>超过{{ lowerPeoplePercent }}的App用户</p>
        <p class="tip">{{ loginTimesStr }}</p>
      </div>
      <div class="section card_1">
        <img src="../img/10/2.png" alt="" class="icon" />

        <div class="info">
          <p style="font-weight: bold">理财产品</p>
          <p>
            浏览<span class="big-num">{{ moneyMenageViewTimes }}</span
            >次
          </p>
        </div>
      </div>
      <div class="section card_2">
        <img src="../img/10/1.png" alt="" class="icon" />
        <div class="info">
          <p style="font-weight: bold">投资产品</p>
          <p>
            浏览<span class="big-num">{{ investViewTimes }}</span
            >次
          </p>
        </div>
      </div>

      <div class="section t_b">
        <p>
          <span class="big">{{ spaceTimeName }}</span
          >是你最常登录的时间
        </p>
        <p>有{{ samePeoplePercent }}的人和你是同类</p>
        <p class="tip">{{ spaceTimeStr }}</p>
      </div>
    </template>

    <img :src="bgImg" alt="" class="cover" />
  </div>
</template>

<script setup lang="ts">
import rollingNumbers from './rolling-numbers/rolling-numbers.vue'
import { ref, inject, computed, watch, nextTick, onMounted } from 'vue'
import { useSwiperSlide } from 'swiper/vue/swiper-vue'
import gsap from 'gsap'
const swiperSlide = useSwiperSlide()
const props = defineProps<{
  page10Data: {
    loginTimes: string
    moneyMenageViewTimes: string
    investViewTimes: string
    moneyMenageTime: string
    samePeoplePercent: string
    lowerPeoplePercent: string
  }
}>()
const canShow = inject('canShow')
const { loginTimes, moneyMenageViewTimes, investViewTimes, moneyMenageTime, samePeoplePercent, lowerPeoplePercent } =
  props.page10Data || {}

const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})

const pushPageListId = inject('pushPageListId', (id: any) => {})
pushPageListId && pushPageListId('10')

let spaceTimeName = ref('早上')
let timeIndex = ref(0)
const bgImg = ref('')
const spaceTimeStr = computed(() => {
  const str = [
    '理财也要做早起的鸟儿',
    '给工作间歇不忘理财的你点赞',
    '定闹钟抢理财的小窍门，已被你掌握',
    '做最独特的"理财夜猫子"',
  ]
  const spaceTimeHour = Number(moneyMenageTime.split(':')[0])
  console.log('🚀 ~ file: page-10.vue ~ line 84 ~ spaceTimeStr ~ spaceTimeHour', spaceTimeHour)
  let index = 0
  if (spaceTimeHour >= 11 && spaceTimeHour < 15) {
    spaceTimeName.value = '中午'
    index = 1
  }
  if (spaceTimeHour >= 15 && spaceTimeHour < 18) {
    spaceTimeName.value = '下午'

    index = 2
  }
  if (spaceTimeHour >= 18 || spaceTimeHour <= 6) {
    spaceTimeName.value = '夜晚'

    index = 3
  }
  timeIndex.value = index
  bgImg.value = require(`../img/10/bg_${index}.png`)

  return str[index]
})

const loginTimesStr = computed(() => {
  if (Number(loginTimes) < 300) {
    return '每次遇见都让财富多加一点'
  }
  if (Number(loginTimes) < 1000) {
    return '因为相信，所以值得投入更多'
  }
  return '从一次惊喜，到每次陪伴'
})

watch(isCurrentPage, (val) => {
  console.log('🚀 ~ file: page-10.vue ~ line 73 ~ watch ~ val', val)
  if (val) {
    console.log('切换到第10页拉，加载动画')
    nextTick(() => {
      const list = document.querySelectorAll('.page-10 .section')
      for (let i = 0; i < list.length; i++) {
        const dom = list[i]
        gsap.fromTo(
          dom,
          {
            opacity: 0,
            duration: 0,
            delay: 0,
            y: 10,
          },
          {
            x: 0,
            y: 0,
            opacity: 1,
            duration: 0.4,
            delay: 0.8 * (i + 1),
            ease: 'ease-out',
            onComplete: () => {},
          }
        )
      }
    })
  }
})
</script>

<style lang="scss" scoped>
.page-10 {
  background: linear-gradient(180deg, #e1ecff 6%, #c8daff 49%, #e9eeff 65%, #9ad9ff 92%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 40px;
  line-height: 1;
  font-size: 28px;
  &.time_1 {
    background: linear-gradient(179deg, #abe5ff 13%, #ade4ff 24%, #fdf9df 65%, #ffe7bc 86%, #ffedb9 100%);
    .t_b {
      color: #cb9872;
    }
  }
  &.time_2 {
    background: linear-gradient(180deg, #e1f7ff 8%, #dffbfd 50%, #d9faff 67%, #9aedff 92%);
    .t_b {
      color: #447feb;
    }
  }
  &.time_3 {
    background: linear-gradient(179deg, #f4f9ff 0%, #e1ecff 22%, #7dafff 48%, #538ef9 61%, #2761d1 92%);
    .t_b {
      color: #fff;
    }
  }
  .cover {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
  }
  .section {
    opacity: 0;
    color: #fff;
    text-align: left;
    width: 100%;
    font-size: 28px;
    color: #246ae8;
    line-height: 2;
    padding-left: 70px;
    position: relative;
    z-index: 222;
    margin-top: 50px;
    &.s_1 {
      padding-top: 30px;
    }
    .tip {
      font-size: 38px;
      font-weight: bold;
      span {
        color: #246ae8;
      }
    }
    .big {
      font-size: 52px;
      font-weight: bold;
    }
  }
  .card_1 {
    margin-top: 165px;
    position: relative;
    &::after,
    &::before {
      content: '';
      position: absolute;
      right: 0;
      height: 60px;
      top: -140px;
      width: 40%;
      // border: 2px solid #fff;
      border-right: none;
      border-bottom: none;
      border-radius: 40px 0 0 0;
      opacity: 0.2;
      background: #fff;
    }
    &::before {
      width: 70%;
      height: 80px;
      top: -80px;
    }
  }
  .card_1,
  .card_2 {
    padding: 0;
    font-size: 28px;
    color: #407be9;
    letter-spacing: 0;
    line-height: 38px;
    color: #407be9;
    align-self: flex-end;
    width: 546px;
    height: 236px;

    display: flex;
    align-items: center;
    justify-content: flex-start;
    text-align: left;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.56), rgba(255, 255, 255, 0.1));
    border-radius: 40px 0 0 40px;
    line-height: 1.5;
    p:first-child {
      font-size: 38px;
    }
    span {
      font-size: 52px;
    }
    .icon {
      width: 144px;
      height: 144px;
      margin-right: 40px;
      margin-left: 40px;
    }
  }
  .card_2 {
    width: 678px;
    height: 236px;
    color: #fff;
    margin-top: -40px;
    background: linear-gradient(to right, #35a9ff, #b6d4fb);
    span {
      color: #fdffb1;
      font-size: 52px;
    }
    margin-bottom: 100px;
  }
}
</style>
