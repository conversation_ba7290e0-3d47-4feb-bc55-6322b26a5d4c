<template>
  <div class="page-8-1">
    <template v-if="canShow">
      <div class="section">
        <p style="font-weight: bold">2021年，微众上线了积分系统</p>
        <p style="font-weight: bold">累计发放积分<span>120</span>亿</p>
      </div>
      <div class="section">
        <p>积分活动不断，海量福利随享</p>
        <p>兑换路径：微众银行App-权益-积分商城</p>
      </div>
      <div class="section">
        <div class="icon icon_1">
          <img src="../img/8/1.png" alt="" />
        </div>
        <div class="icon icon_2">
          <img src="../img/8/2.png" alt="" />
        </div>
        <div class="icon icon_3">
          <img src="../img/8/3.png" alt="" />
        </div>
        <div class="icon icon_4">
          <img src="../img/8/4.png" alt="" />
        </div>
        <div class="icon icon_5">
          <img src="../img/8/5.png" alt="" />
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { sectionAnimate } from '../utils/sectionAnimate'
import { ref, inject, computed, watch, nextTick, onMounted } from 'vue'
import { useSwiperSlide } from 'swiper/vue/swiper-vue'
const swiperSlide = useSwiperSlide()

const canShow = inject('canShow')
watch(canShow, (val) => {
  console.log('🚀 ~ file: page-2.vue ~ line 45 ~ watch ~ val', val)
})

const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})
const pushPageListId = inject('pushPageListId', (id: any) => {})
pushPageListId && pushPageListId('8-1')
watch(isCurrentPage, (val) => {
  if (val) {
    nextTick(() => {
      sectionAnimate('8-1')
    })
  }
})
</script>

<style lang="scss" scoped>
.page-8-1 {
  background: linear-gradient(359deg, #f5fdff, #cce2f8, #76b6ff 100%);
  color: #fff;
  font-size: 28px;
  text-align: left;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  .section {
    margin-top: 60px;
    padding-left: 60px;
    opacity: 0;
    width: 100%;
    &:first-child {
      font-size: 35px;
      span {
        color: #fdffb1;
        font-size: 52px;
        font-weight: bold;
      }
    }
    .icon {
      width: 195px;
      height: 195px;
      position: absolute;
      padding: 0;
      margin: 0;
      animation: flow 1s ease-in 0s infinite alternate;

      img {
        width: 100%;
        height: 100%;
      }
      &.icon_1 {
        left: 60px;
        top: 50px;
      }
      &.icon_2 {
        right: 60px;
        top: 70px;
        width: 210px;
        height: 210px;
        animation: flow 1s ease-in 0.2s infinite alternate;
      }
      &.icon_3 {
        left: 220px;
        width: 300px;
        height: 300px;
        top: 270px;
        animation: flow 1s ease-in 0.3s infinite alternate;
      }
      &.icon_4 {
        right: 60px;
        width: 210px;
        height: 210px;
        top: 540px;
        animation: flow 1s ease-in 0.4s infinite alternate;
      }
      &.icon_5 {
        left: 60px;
        width: 270px;
        height: 270px;
        top: 580px;
        animation: flow 1s ease 0.5s infinite alternate;
      }
    }
  }
}

@keyframes flow {
  from {
    margin-top: -10px;
  }
  to {
    margin-top: 20px;
  }
}
</style>
