<template>
  <div class="page-9-1 nodotaion">
    <template v-if="canShow">
      <div class="section">
        <p>理财之路除了眼光和机遇,还需要有计划的管理</p>
        <p>截止{{ dataLastDate ? dataLastDate : '2022.11' }}，共有</p>
        <p>
          <span class="big-num color: #637cce;">{{ setPlanUserNums ? setPlanUserNums : '98567' }}</span>
          名用户在微众设置计划
        </p>
      </div>
      <div class="section s_2">
        <p>有为自己梦想小家的奋斗者；</p>
        <p>有为父母带去一份孝心的儿女；</p>
        <p>有每月规律理财，工资增值的打工人；</p>
        <p>更有为乡村孩子构建美丽梦想的公益行人；</p>
      </div>

      <div class="section s_3">
        <p class="tip"><span>你想成为哪一个呢？</span></p>
        <p class="tip"><span>微众“计划”，期待与你相遇</span></p>
      </div>

      <div class="section cover">
        <img src="../img/9/bg2.png" alt="" />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import rollingNumbers from './rolling-numbers/rolling-numbers.vue'
import { ref, inject, computed, watch, nextTick, onMounted } from 'vue'
import { useSwiperSlide } from 'swiper/vue/swiper-vue'

import { sectionAnimate } from '../utils/sectionAnimate'

const swiperSlide = useSwiperSlide()
const props = defineProps<{
  page9Data: {
    planTimes: string
    donationTimes: string
    maxPlanTimes: string
    maxPlanName: string
    dataLastDate: string
    setPlanUserNums: string
  }
}>()
const canShow = inject('canShow')
const { planTimes, donationTimes, maxPlanName, maxPlanTimes, dataLastDate, setPlanUserNums } = props.page9Data || {}

const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})

const pushPageListId = inject('pushPageListId', (id: any) => {})
pushPageListId && pushPageListId('9-1')
const showNext = inject('showNext', ref(true))

watch(isCurrentPage, (val) => {
  console.log('🚀 ~ file: page-9-1.vue ~ line 73 ~ watch ~ val', val)
  if (val) {
    console.log('切换到第9-1页拉，加载动画')
    showNext.value = true
    nextTick(() => {
      sectionAnimate('9-1')
    })
  }
})
</script>

<style lang="scss" scoped>
.page-9-1 {
  background: linear-gradient(180deg, #fffcff 0%, #f1d9ff 100%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 40px;
  line-height: 1;
  font-size: 28px;

  .section {
    opacity: 0;
    color: #fff;
    text-align: left;
    width: 100%;
    font-size: 28px;
    color: #637cce;
    line-height: 2;
    padding-left: 70px;
    z-index: 2;
    margin-top: 60px;

    .big {
      font-size: 52px;
    }
    &.s_2 {
      p {
        &:nth-child(2) {
          opacity: 0.7;
        }
        &:nth-child(3) {
          opacity: 0.6;
        }
        &:nth-child(4) {
          opacity: 0.5;
        }
      }
    }
    &.s_3 {
      margin-top: 100px;
    }
    .tip {
      font-size: 38px;
      font-weight: bold;
      span {
        color: #637cce;

        font-size: 35px;
      }
    }

    &.cover {
      width: 100%;
      padding: 0;
      margin-top: 50px;
      align-self: flex-end;
      height: 100%;
      flex: auto;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex-direction: column;
      z-index: 1;
      img {
        width: 100%;
      }
    }
  }

  &.nodotaion {
    background: linear-gradient(180deg, #fffcff 0%, #84d6ff 100%);
    .section {
      color: #687ca9;
      .tip {
        span {
          color: #5485d5;
        }
      }
    }
  }
}
</style>
