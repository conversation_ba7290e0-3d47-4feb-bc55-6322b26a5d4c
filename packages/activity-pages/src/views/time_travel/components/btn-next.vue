<template>
  <div class="btn-page-next"></div>
</template>

<script setup lang="ts"></script>

<style lang="scss" scoped>
.btn-page-next {
  width: 120px;
  height: 60px;
  background: url('../img/arrow.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 38px 14px;
  position: absolute;
  left: 50%;
  bottom: 100px;
  transform: translateX(-50%);
  z-index: 20;
  animation: iconflow 5s linear infinite;
  &::before {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    background: url('../img/arrow.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 38px 14px;

    transform: translateY(0);
    position: relative;
    animation: btnnext 1s linear infinite;
  }
}
@keyframes btnnext {
  from {
    top: 0;
    opacity: 1;
  }
  to {
    opacity: 0.4;

    top: -20px;
  }
}
</style>
