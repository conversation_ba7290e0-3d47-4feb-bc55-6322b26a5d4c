<template>
  <div class="page-4">
    <div class="bg"></div>
    <template v-if="canShow">
      <div class="section">
        <!-- <img class="rocket" src="../img/4/rocket.png" alt="" v-if="Number(earningsCount) > 0" /> -->

        <div class="page4-title title" v-if="Number(earningsCount) > 0">
          <!-- <p>财富</p>
          <p>{{ earningTagStr }}</p> -->
          <img v-if="earningTagStr === '四平八稳'" src="../img/4/title_2.png" alt="" class="img_title" />
          <img v-else-if="earningTagStr === '飞速上涨'" src="../img/4/title_3.png" alt="" class="img_title" />
          <img v-else src="../img/4/title_4.png" alt="" class="img_title" />
        </div>
        <div class="title page4-title" v-else>
          <img src="../img/4/title_1.png" alt="" class="img_title" style="width: 100%" />
        </div>
        <div class="contain">
          <p>在微众银行App，已累计交易理财 {{ financingCount }} 笔</p>

          <p>
            至今已累计收益<span class="big-num"> {{ earningsCountFormate }} </span>元
          </p>

          <template v-if="Number(earningsCount) > 0">
            <p class="s_38 fb">
              实现累计 <span class="big-num">{{ earningsDays }}</span> 天正收益
            </p>
            <p>在微众的收益{{ earningTagStr }}</p>
          </template>
          <template v-if="Number(earningsCount) < 0">
            <p>投资的路上充满了充满了变数和未知，</p>
            <p class="s_5">价值投资需要更有耐心</p>
          </template>
        </div>
        <!-- <div class="table act" v-if="Number(earningsCount) > 0">
          <div class="name">每月收益</div>
          <img class="tips" src="../img/4/Tips.png" />
          <div class="ball"></div>
          <div class="line-good">
            <svg
              class="table-line"
              viewBox="0 0 564 442"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
            >
              <title>线</title>
              <defs>
                <linearGradient
                  x1="2.68320344%"
                  y1="71.4880435%"
                  x2="98.262918%"
                  y2="21.1793665%"
                  id="linearGradient-1"
                >
                  <stop stop-color="#C7EBFF" offset="0%"></stop>
                  <stop stop-color="#3096FF" offset="49.8012229%"></stop>
                  <stop stop-color="#52C1EB" offset="100%"></stop>
                </linearGradient>
              </defs>
              <g id="line-good" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <g id="编组-17" transform="translate(2.969188, 5.000000)">
                  <path
                    class="path"
                    d="M2.93706232,432.165902 C9.71449445,314.57202 43.7276449,246.255224 104.976514,227.215511 C196.849817,198.655942 250.738562,265.582932 340.501875,265.799222 C400.344083,265.943415 472.142888,177.343674 555.898288,8.52651283e-14"
                    id="路径-9"
                    stroke="url(#linearGradient-1)"
                    stroke-width="8.781696"
                    stroke-linecap="round"
                  ></path>
                </g>
              </g>
            </svg>

            <svg
              class="bg-line"
              viewBox="0 0 564 442"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
            >
              <title>面积</title>
              <defs>
                <linearGradient x1="61.6654984%" y1="55.3482269%" x2="61.6654984%" y2="100%" id="linearGradient-2">
                  <stop stop-color="#4C9CE4" offset="0%"></stop>
                  <stop stop-color="#07BFF5" stop-opacity="0" offset="100%"></stop>
                </linearGradient>
              </defs>
              <g id="标注定稿" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <g id="编组-17" transform="translate(2.969188, 5.000000)">
                  <path
                    class="path"
                    d="M-5.68434189e-14,435.876449 C8.73547368,315.80887 43.7276449,246.255224 104.976514,227.215511 C196.849817,198.655942 250.738562,265.582932 340.501875,265.799222 C400.344083,265.943415 472.142888,177.343674 555.898288,5.68434189e-14 L555.898288,435.876449 L-5.68434189e-14,435.876449 Z"
                    id="路径-9"
                    fill="url(#linearGradient-2)"
                    opacity="0.207449777"
                  ></path>
                </g>
              </g>
            </svg>
          </div>
          <div class="times">
            <span>{{ firstDate }}</span>
            <span>{{ lastDate }}</span>
          </div>
        </div> -->
        <div class="table-img act" v-if="Number(earningsCount) > 0">
          <img src="../img/4/img_income.png" alt="" />
        </div>
        <div class="table-img act" v-else>
          <img src="../img/4/img_loss.png" alt="" />
        </div>
      </div>
    </template>
    <img class="cover" src="../img/4/bg-before.png" />
    <p class="tip-text">*图表仅供参考，非准确收益数据</p>
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, computed, watch, nextTick } from 'vue'
import { useSwiperSlide } from 'swiper/vue/swiper-vue'
import gsap from 'gsap'
const swiperSlide = useSwiperSlide()
const props = defineProps<{
  page4Data: {
    financingCount: string
    earningsCount: string
    earningsCountFormate: string
    earningsDays: string
    firstDate: string
    lastDate: string
    earningTagStr: string
    hasLoss: boolean
  }
}>()
const canShow = inject('canShow')
console.log('🚀 ~ file: page-2.vue ~ line 43 ~ canShow', canShow)
const {
  firstDate,
  lastDate,
  financingCount,
  earningsCount,
  earningsDays,
  earningTagStr,
  earningsCountFormate,
  hasLoss,
} = props.page4Data || {}

watch(canShow, (val) => {
  console.log('🚀 ~ file: page-4.vue ~ line 45 ~ watch ~ val', val)
})
const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})

const pushPageListId = inject('pushPageListId', (id: any) => {})
pushPageListId && pushPageListId('4')

watch(isCurrentPage, (val) => {
  console.log('🚀 ~ file: page-3.vue ~ line 84 ~ watch ~ val', val)
  if (val) {
    console.log('切换到第4页拉，加载动画')
    nextTick(() => {
      const anDuration = 0.4
      const tl = gsap.timeline()
      tl.fromTo(
        '.page-4 .section',
        {
          opacity: 0,
          duration: 0,
          delay: 0.4,
          y: 10,
        },
        {
          x: 0,
          y: 0,
          opacity: 1,
          duration: anDuration,
          delay: anDuration,
          ease: 'ease-out',

          onComplete: () => {},
        }
      )
        // .fromTo(
        //   '.rocket',
        //   {
        //     y: 400,
        //     x: -300,
        //     opacity: 0,
        //     duration: 0,
        //     delay: 0.4,
        //     scale: 0.5,
        //     rotate: -5,
        //   },
        //   {
        //     x: 0,
        //     y: 0,
        //     opacity: 1,
        //     duration: Number(earningsCount) > 0 ? anDuration : 0,
        //     delay: 0.4,
        //     scale: 1,
        //     rotate: -5,
        //     ease: 'ease-out',
        //     onComplete: () => {},
        //   }
        // )
        .fromTo(
          '.page-4 .contain',
          {
            opacity: 0,
            duration: 0,
            delay: 0.4,
            y: 10,
          },
          {
            x: 0,
            y: 0,
            opacity: 1,
            duration: anDuration,
            delay: 0.4,
            ease: 'ease-out',

            onComplete: () => {},
          }
        )
        .fromTo(
          '.act',
          {
            opacity: 0,
            duration: 0,
            delay: 0.4,
            y: 10,
          },
          {
            x: 0,
            y: 0,
            opacity: 1,
            duration: anDuration,
            delay: 0.4,
            ease: 'ease-out',
            onComplete: () => {},
          }
        )
        .fromTo(
          '.table-line .path',
          {
            duration: 0,
            delay: 0.4,
            strokeDashoffset: Number(earningsCount) < 0 ? -900 : 900,
          },
          {
            strokeDashoffset: Number(earningsCount) < 0 ? 0 : 0,
            opacity: 1,
            duration: 0.5,
            delay: 0.4,
            onComplete: () => {
              gsap.fromTo(
                '.page-4 .bg-line',
                {
                  duration: 0,
                  delay: 0.4,
                  opacity: 0,
                },
                {
                  opacity: 1,
                  duration: 0.3,
                  delay: 0.4,
                }
              )
            },
          }
        )
        .fromTo(
          '.ball',
          {
            scale: 0,
            duration: 0,
            delay: 0.4,
          },
          {
            scale: 1,
            opacity: 1,
            duration: 0.3,
            delay: 0.4,
            onComplete: () => {},
          }
        )
        .fromTo(
          '.tips',
          {
            scale: 0,
            duration: 0,
            delay: 0.4,
            transformOrigin: 'right top',
          },
          {
            scale: 1,
            opacity: 1,
            duration: 0.3,
            delay: 0.4,
            onComplete: () => {},
          }
        )
    })
  }
})
</script>

<style lang="scss" scoped>
.page-4 {
  background-image: linear-gradient(179deg, #c7e0f8 0%, #d7e8fb 22%, #a7d2f7 69%, #8bc4f2 100%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 40px;
  line-height: 1;
  position: relative;
  .bg {
    position: absolute;
    top: 0;
    left: -140px;
    z-index: 1;
    background: rgba(255, 255, 255, 0.5);
    height: 500px;
    width: 500px;
    border-radius: 50%;
    transform: scale(2);
    opacity: 0.2;
    &::before {
      content: '';
      width: 400px;
      height: 400px;
      background: rgba(255, 255, 255, 0.5);
      position: absolute;
      right: -300px;
      top: -150px;
      border-radius: 50%;
    }
  }

  .section {
    margin-top: 50px;
    height: 100%;
    width: 690px;
    border: 2px solid #fff;
    border-radius: 20px 20px 0 0;

    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.1));
    padding: 60px 0;
    text-align: left;
    line-height: 2;
    position: relative;
    opacity: 0;
    z-index: 2;
    .title {
      .img_title {
        width: 361px;
        // height: 203px;
      }
    }
    .rocket {
      position: absolute;
      right: -40px;
      top: -60px;
      width: 343px;
      height: 419px;
    }
    .contain {
      padding-top: 60px;
      padding-left: 60px;
      padding-right: 40px;
      font-size: 28px;
      color: #6a7cb7;
      line-height: 1;

      span {
        font-size: 52px;
        font-weight: bold;
        color: #4186ee;
      }
    }
    .table-img {
      width: 625px;
      height: 542px;
      margin: 0 auto;
      margin-top: 60px;

      img {
        width: 100%;
        height: 100%;
      }
    }
    .table {
      border-radius: 23px;
      background: #fff;
      margin: 0 auto;
      margin-top: 60px;
      width: 625px;
      height: 542px;
      background: linear-gradient(127deg, #ffffff 0%, rgba(233, 245, 255, 0.86) 100%);
      border: 3px solid rgba(37, 124, 175, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      &::after,
      &::before {
        content: '';
        width: calc(100% - 60px);
        height: 50%;
        // background: red;
        border: 1px solid #e2edf6;
        border-right: none;
        position: absolute;
        top: 80px;
        left: 30px;
        border-top: none;
        z-index: 0;
      }
      &::before {
        transform: translateY(50%);
        border-top: 1px solid #e2edf6;
      }

      .name {
        position: absolute;
        left: 55px;
        top: 35px;
        font-size: 25.2px;
        color: #525b78;
        line-height: 29.4px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        &::before {
          content: '';
          width: 24.14px;
          height: 24.15px;
          background: #59b3ff;
          border-radius: 50%;
          margin-right: 10px;
        }
      }
      .times {
        position: absolute;
        bottom: 15px;
        left: 25px;
        right: 25px;
        font-size: 24px;
        color: #98bedf;
        letter-spacing: -0.05px;
        line-height: 29.4px;
        font-weight: 900;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .line-good {
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 222;
      }
      .ball {
        width: 34px;
        height: 34px;
        border: 8px solid #fff;
        background-color: #36a3ff;
        box-shadow: 0px 8px 15px 0px rgba(0, 93, 245, 0.43);
        border-radius: 50%;
        position: absolute;
        right: 14px;
        top: 22px;
        z-index: 2222;
      }
      .tips {
        width: 240px;
        height: 96px;
        position: absolute;
        right: 14px;
        top: 22px;
        z-index: 21;
      }
    }
  }
  .cover {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
  }
}
.table-line,
.bg-line {
  width: 564px;
  height: 442px;
  display: block;
  position: absolute;
  .path {
    stroke-dasharray: 900;
    stroke-dashoffset: 0;
  }
}
.bg-line {
  opacity: 0;
}
.s_5 {
  font-size: 40px;
  color: #3d55a0;
  font-weight: bold;
}
.tip-text {
  font-size: 22px;
  color: #898989;
  line-height: 22px;
  position: absolute;
  bottom: 75px;
  left: 55px;
  opacity: 0.5;
}
</style>
