<template>
  <div class="page-7">
    <template v-if="canShow">
      <div class="section">
        <img class="title" src="../img/7/title.png" alt="" />
      </div>
      <div class="section">
        <p>你在微众消费支出{{ consumeTimes }}笔</p>
        <p class="tip">
          支出 <span style="color: #6a76ff">{{ consumeMoney }}</span
          >元
        </p>
      </div>
      <div class="section">
        <p>
          其中发红包 <span class="tip big_num fs_52" style="color: #6a76ff"> {{ hongbaoTimes }} </span> 笔
        </p>
        <p>
          消费支付<span class="tip big_num fs_52" style="color: #6a76ff"> {{ payTimes }} </span> 笔
        </p>
        <p class="tip">每一笔支出，都彰显着潇洒</p>
      </div>
      <div class="section cover">
        <img src="../img/7/bg.png" />
      </div>
      <slot></slot>
    </template>
  </div>
</template>

<script setup lang="ts">
import rollingNumbers from './rolling-numbers/rolling-numbers.vue'
import { ref, inject, computed, watch, nextTick, onMounted } from 'vue'
import { useSwiperSlide } from 'swiper/vue/swiper-vue'
import gsap from 'gsap'
const swiperSlide = useSwiperSlide()
const props = defineProps<{
  page7Data: {
    consumeTimes: string
    consumeMoney: string
    hongbaoTimes: string
    payTimes: string
  }
}>()
const canShow = inject('canShow')
const { consumeTimes, consumeMoney, hongbaoTimes, payTimes } = props.page7Data || {}
const pushPageListId = inject('pushPageListId', (id: any) => {})
pushPageListId && pushPageListId('7')
const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})
watch(isCurrentPage, (val) => {
  console.log('🚀 ~ file: page-7.vue ~ line 73 ~ watch ~ val', val)
  if (val) {
    console.log('切换到第7页拉，加载动画')
    nextTick(() => {
      const list = document.querySelectorAll('.page-7 .section')
      for (let i = 0; i < list.length; i++) {
        const dom = list[i]
        gsap.fromTo(
          dom,
          {
            opacity: 0,
            duration: 0,
            delay: 0,
            y: 10,
          },
          {
            x: 0,
            y: 0,
            opacity: 1,
            duration: 0.4,
            delay: 0.8 * (i + 1),
            ease: 'ease-out',
            onComplete: () => {},
          }
        )
      }
    })
  }
})
</script>

<style lang="scss" scoped>
.page-7 {
  // background: linear-gradient(180deg, #c5aeff 0%, #a0b8ff 36%, #acd5ff 66%, #ffffff 100%);
  background: linear-gradient(180deg, #f7f8ff 0%, #c3e1fb 47%, #b7e2fd 100%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 40px;
  line-height: 1;
  font-size: 28px;
  .section {
    opacity: 0;
    color: #fff;
    text-align: left;
    padding-left: 70px;
    width: 100%;
    font-size: 28px;
    color: #6c87c3;
    line-height: 2;
    margin-top: 50px;
    span {
      font-size: 52px;
    }
    .tip {
      font-size: 38px;
      font-weight: bold;
    }
    &:nth-child(1) {
      .title {
        height: 58px;
        width: 308px;
      }
      // font-size: 66px;
      // color: #7588e9;
      // font-weight: bold;
    }
    &:nth-child(2) {
      margin-top: 70px;
    }
    &:nth-child(3) {
      margin-top: 70px;
    }
  }
  .cover {
    width: 100%;
    padding: 0;
    height: 100%;
    flex: auto;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-direction: column;
    img {
      width: 100%;
    }
  }
}
.fs_52 {
  font-size: 52px !important;
}
</style>
