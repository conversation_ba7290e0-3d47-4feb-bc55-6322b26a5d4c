<template>
  <div class="page-13" :class="`level_${userTagType}`">
    <img src="../img/13/bg-top.png" alt="" class="bg-top" />
    <img src="../img/13/bg-bottom.png" alt="" class="bg-bottom" />
    <div class="contain" v-show="!showLoading">
      <img class="bg" :src="bgImg" alt="" />

      <div class="info">{{ userName || '我' }} <b>的财富+形象是</b></div>

      <div class="desc">
        <div class="my">
          未来寄语
          <div class="word">
            {{ myword }}
            <div class="btn-back" @click="backPrev">
              <img :src="btnBackImg" alt="" />
            </div>
          </div>
        </div>

        <div class="tags">
          <div class="item" v-for="(item, index) in tagListTop" :key="index">
            <div class="cont">#{{ item }}</div>
          </div>
        </div>
        <div class="tags">
          <div class="item" v-for="(item, index) in tagListBottom" :key="index">
            <div class="cont">#{{ item }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="btn-share" @click="showSharePannel" v-if="!showLoading">立即分享</div>
    <div class="btn-save" @click="showSavePannel" v-if="!showLoading">保存到相册</div>
    <div class="dialog" v-show="showDialog">
      <div class="wrap">
        <div class="contain">
          <img class="bg" :src="bgImg" alt="" />

          <div class="info">{{ userName }} <b>的财富+形象是</b></div>

          <div class="desc">
            <div class="my">
              未来寄语
              <div class="word">
                {{ myword }}
              </div>
            </div>

            <div class="tags">
              <div class="item" v-for="(item, index) in tagListTop" :key="index">
                <div class="cont">
                  <span> #{{ item }} </span>
                </div>
              </div>
            </div>
            <div class="tags">
              <div class="item" v-for="(item, index) in tagListBottom" :key="index">
                <div class="cont">
                  <span> #{{ item }} </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <img :src="bttomImg" alt="" class="cover" />
      </div>
      <div class="share-pannel">
        <div class="btns" v-if="!isSaveImage">
          <div class="btn-wx" @click="shareWx">
            <img src="../img/13/wx.png" alt="" class="icon" />
            微信
          </div>
          <div class="btn-timeline" @click="shareWxtimeline">
            <img src="../img/13/wx_timeline.png" alt="" class="icon" />
            朋友圈
          </div>
        </div>
        <div class="btns" v-else>
          <div class="btn-wx" @click="saveImage">
            <img src="../img/13/save_img.png" alt="" class="icon" />
            保存图片
          </div>
        </div>
        <div class="cancel" @click="hideDialog">取消</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import rollingNumbers from './rolling-numbers/rolling-numbers.vue'
import loadingPlant from './loading-plant.vue'
import { ref, Ref, inject, computed, watch, nextTick, onMounted, toRefs } from 'vue'
import { focusStore, focusCore } from '@focus/render'
import html2canvas from 'html2canvas'
import { useSwiperSlide, useSwiper } from 'swiper/vue/swiper-vue'
const swiperSlide = useSwiperSlide()
const swiper = useSwiper()
const props = defineProps<{
  page13Data: {
    tagList: string[]
    userAvator: string
    userName: string
    userTagStrs: string[]
  }
}>()
const { tagList, userName, userTagStrs } = props.page13Data || {}
console.log('🚀 ~ file: page-13.vue ~ line 91 ~ userTagStrs', userTagStrs)
console.log('🚀 ~ file: page-13.vue ~ line 54 ~ tagList', tagList)
const myword = inject('myword')
const showDialog = ref(false)
const showLoading = ref(true)
const userTagType = ref(0)
const userTagStr = ref('')

const bgImg = computed(() => {
  const img = userTagType.value ? require(`../img/13/${userTagType.value}.png`) : ''
  return img
  // if (BUILD_TEST) {
  //   return img.replace(/dbd\.test\.webankwealthcdn\.net\/wm\-resm/, 'personal.test.webank.com/s')
  // }
  // return img.replace(/dbd\.webankwealthcdn\.net\/wm\-resm/, 'personal.test.webank.com/s')
})

const bttomImg = computed(() => {
  const img = userTagType.value ? require(`../img/13/${userTagType.value}-1.png?123`) : ''
  return img
  // if (BUILD_TEST) {
  //   return img.replace(/dbd\.test\.webankwealthcdn\.net\/wm\-resm/, 'personal.test.webank.com/s')
  // }
  // return img.replace(/dbd\.webankwealthcdn\.net\/wm\-resm/, 'personal.webank.com/s')
})

const btnBackImg = computed(() => {
  const img = userTagType.value ? require(`../img/13/${userTagType.value}-2.png`) : ''
  return img
})

const isSaveImage = ref(false)
onMounted(() => {})
const dataUrl = ref('')
const tagListTop = computed(() => {
  if (tagList.length === 3) {
    return [tagList[0]]
  }
  if (tagList.length === 6) {
    return [tagList[0], tagList[1], tagList[2]]
  }

  return [tagList[0], tagList[1]]
})
const tagListBottom = computed(() => {
  if (tagList.length === 3) {
    return [tagList[1], tagList[2]]
  }
  if (tagList.length === 6) {
    return [tagList[3], tagList[4], tagList[5]]
  }

  return tagList.slice(2)
})
const canShow = inject<Ref<boolean>>('canShow') || ref(false)

const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})

watch(isCurrentPage, (val) => {
  if (val) {
    console.log('🚀 ~ file: page-13.vue ~ line 142 ~ watch ~ val', val)
    resetUserType()
  }
})

function resetUserType() {
  console.log('🚀 ~ file: page-13.vue ~ line 152 ~ resetUserType ~ userTagStrs', userTagStrs)

  const userTagStrList = [
    '理财收益王者',
    '微众奋斗使者',
    '梦想起航先锋',
    '超级理财专家',
    '基迪匹贡献者',
    '筑梦爱心大使',
  ]
  let id = Math.floor(Math.random() * userTagStrs.length)
  console.log('🚀 ~ file: page-13.vue ~ line 155 ~ resetUserType ~ userTagStrs.length', userTagStrs.length)
  console.log('🚀 ~ file: page-13.vue ~ line 155 ~ resetUserType ~ id', id)
  console.log('id === 0 && userTagStrs.length > 1', id === 0 && userTagStrs.length > 1)
  if (id === 0 && userTagStrs.length > 1) {
    console.error('id 不能为0 ！')
    id = 1
  }
  const str = userTagStrs[id]
  userTagType.value = userTagStrList.findIndex((i) => i === str) + 1
  userTagStr.value = userTagStrs[id]
  showLoading.value = false

  focusCore.clickLog('new_info', { name: userTagStr.value, tags: tagList, myword: myword?.value })
}

function shareWx() {
  focusCore.clickLog('share_wx')

  const hjCoreIab = window.hjCoreIab
  const shareType = hjCoreIab && hjCoreIab.CONS.ShareType.WX_SESSION

  if (dataUrl.value) {
    hjCoreIab.shareImage({
      shareType,
      imgData: dataUrl.value,
    })
  } else {
    loadImg((dataUrl: string) => {
      hjCoreIab.shareImage({
        shareType,
        imgData: dataUrl,
      })
    })
  }
}

function shareWxtimeline() {
  focusCore.clickLog('share_wxt')

  const hjCoreIab = window.hjCoreIab
  const shareType = hjCoreIab && hjCoreIab.CONS.ShareType.WX_TIME_LINE

  if (dataUrl.value) {
    hjCoreIab.shareImage({
      shareType,
      imgData: dataUrl.value,
    })
  } else {
    loadImg((dataUrl: string) => {
      hjCoreIab.shareImage({
        shareType,
        imgData: dataUrl,
      })
    })
  }
}

function saveImage() {
  focusCore.clickLog('save_img')

  const hjCoreIab = window.hjCoreIab
  focusStore.stores.modalStore.loadingStart('save_img')

  if (dataUrl.value) {
    hjCoreIab &&
      hjCoreIab.saveImage(
        {
          imgData: dataUrl.value,
          isToastDisabled: true,
        },
        () => {
          focusStore.stores.modalStore.loadingEnd('save_img')

          focusStore.stores.modalStore.toastShow('图片保存成功！')
          focusCore.clickLog('save_img_success')
        },
        (error: any) => {
          focusStore.stores.modalStore.loadingEnd('save_img')

          console.log(error)
          focusStore.stores.modalStore.toastShow('图片保存失败！')
          focusCore.clickLog('save_img_fail')
        }
      )
  } else {
    loadImg((dataUrl: string) => {
      hjCoreIab &&
        hjCoreIab.saveImage(
          {
            imgData: dataUrl,
            isToastDisabled: true,
          },
          () => {
            focusStore.stores.modalStore.loadingEnd('save_img')

            focusStore.stores.modalStore.toastShow('图片保存成功！')
            focusCore.clickLog('save_img_success')
          },
          (error: any) => {
            console.log(error)
            focusStore.stores.modalStore.loadingEnd('save_img')

            focusStore.stores.modalStore.toastShow('图片保存失败！')
            focusCore.clickLog('save_img_fail')
          }
        )
    })
  }
}

function loadImg(cb?: Function) {
  focusStore.stores.modalStore.loadingStart('make_pic')
  setTimeout(() => {
    const dom = document.querySelector('.page-13 .dialog .wrap')
    dom &&
      html2canvas(dom, {
        useCORS: true,
        scale: 3,
        allowTaint: true,
      }).then((canvas) => {
        dataUrl.value = canvas.toDataURL('img/jpg')
        // document.querySelector('.page-13 ')?.appendChild(canvas)
        focusStore.stores.modalStore.loadingEnd('make_pic')

        cb && cb(dataUrl.value)
      })
  }, 100)
}

function setSpan(str: string) {
  const arr = str.split('')
  return arr.map((i) => `<span>${i}</span>`).join('')
}
function hideDialog() {
  showDialog.value = false
  isSaveImage.value = false
}
function backPrev() {
  dataUrl.value = ''
  swiper.value.slidePrev()
  showLoading.value = false
}

function showSharePannel() {
  showDialog.value = true
  loadImg()
}
function showSavePannel() {
  showDialog.value = true
  isSaveImage.value = true
}
</script>

<style lang="scss" scoped>
//理财收益王者
.page-13 {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 40px;
  line-height: 1;
  background-image: linear-gradient(1deg, #fff0d7 0%, #fff3e1 31%, #fff2de 45%, #deb681 82%, #deb984 100%);
  font-size: 28px;
  color: #cdaa7c;
  position: relative;
  z-index: 2222;
  .bg-top,
  .bg-bottom {
    position: absolute;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 0;
  }
  .bg-top {
    top: 0;
  }
  .bg-bottom {
    bottom: 0;
  }
  .contain {
    width: 100%;
    height: 1152px;
    position: relative;
    z-index: 1;
    .bg {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      z-index: 0;
    }
    .info {
      position: absolute;
      left: 80px;
      top: 90px;
      line-height: 1;
      font-size: 28px;
      z-index: 1;
    }
    .desc {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1;
      padding: 0 60px;
      padding-bottom: 87px;
      font-weight: bold;
      .my {
        width: 100%;

        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding-left: 9px;
        .word {
          flex: none;
          font-weight: normal;
          display: inline-block;
          padding-left: 20px;
          margin-left: 20px;
          border-left: 1px solid #cdaa7c62;
          text-align: left;
          position: relative;
          .btn-back {
            width: 30px;
            height: 25px;
            margin-left: 5px;
            line-height: 0;
            position: absolute;
            right: -35px;
            top: 50%;
            transform: translateY(-50%);
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }

      p {
        text-align: left;
        width: 100%;
        font-weight: bold;

        span {
          font-weight: normal;
          display: inline-block;
          padding-left: 20px;
          margin-left: 20px;
          border-left: 1px solid rgba(255, 255, 255, 0.1);
        }
      }
      .tags {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: nowrap;

        width: 100%;
        .item {
          flex: auto;
          text-align: center;
          height: 68px;
          line-height: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          margin-top: 40px;
          width: 100%;
          span {
            margin-top: -10px;
            vertical-align: center;
          }
          .cont {
            background: rgba(255, 255, 255, 0.45);
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            border-radius: 17px;
            width: calc(100% - 10px);
            height: 100%;
          }
        }
      }
    }
  }
  .btn-save {
    z-index: 2;

    font-size: 32px;
    font-weight: 500;
    color: #d7a764;
    line-height: 28px;
    padding: 30px;
    margin-top: 20px;
    opacity: 0.8;
  }
  .btn-share {
    z-index: 2;
    background-image: linear-gradient(130deg, #f4d6aa 5%, #ddb781 35%, #d7a764 100%);

    box-shadow: inset 0px 3px 0px 0px rgba(255, 255, 255, 0.83);
    border-radius: 59px;
    width: 620px;
    height: 118px;
    line-height: 118px;
    font-size: 40px;
    color: #fff;
    font-weight: bold;
    margin-top: 15px;
  }

  .dialog {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: rgba($color: #000000, $alpha: 0.3);
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    padding-top: 100px;
    z-index: 222;
    .wrap {
      width: 750px;
      height: 1376px;

      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-direction: column;
      position: absolute;
      left: 50%;
      top: -100px;
      background-image: linear-gradient(1deg, #fff0d7 0%, #fff3e1 31%, #fff2de 45%, #deb681 82%, #deb984 100%);
      transform-origin: center center;
      transform: scale(0.7);
      margin-left: -50%;
      // border-radius: 40px;
      overflow: hidden;
      .tags {
      }
    }
  }
  //微众奋斗使者
  &.level_2 {
    background-image: linear-gradient(0deg, #dfe7ff 45%, #86b3ff 79%, #6682ff 91%);
    color: #6994eb;
    .wrap {
      background-image: linear-gradient(0deg, #dfe7ff 45%, #86b3ff 79%, #6682ff 91%);
      color: #6994eb;
    }

    .btn-share {
      background-image: linear-gradient(130deg, #7691ff 5%, #4b7aff 28%, #3856fe 100%);
    }
    .btn-save {
      color: #435afe;
    }
    .desc {
      .my {
        .word {
          border-color: #6994eb63;
        }
      }
    }
  }
  //梦想起航先锋
  &.level_3 {
    background-image: linear-gradient(0deg, #dfe7ff 45%, #9b9aff 80%, #8366ff 91%);
    color: #9085f2;
    .wrap {
      background-image: linear-gradient(0deg, #dfe7ff 45%, #9b9aff 80%, #8366ff 91%);
    }
    .btn-share {
      background-image: linear-gradient(130deg, #8384ff 5%, #8380ff 37%, #7846ff 100%);
    }
    .btn-save {
      color: #435afe;
    }
    .desc {
      .my {
        .word {
          border-color: #9085f273;
        }
      }
    }
  }
  //超级理财专家
  &.level_4 {
    background-image: linear-gradient(1deg, #feedda 45%, #ebb38d 77%, #e1a57b 87%);
    color: #eaa263;
    .wrap {
      background-image: linear-gradient(1deg, #feedda 45%, #ebb38d 77%, #e1a57b 87%);
    }
    .btn-share {
      background-image: linear-gradient(130deg, #dca87e 5%, #db9259 36%, #da894c 100%);
    }
    .btn-save {
      color: #d7a764;
    }
    .desc {
      .my {
        .word {
          border-color: #eaa2636d;
        }
      }
    }
  }

  // 基迪匹贡献者
  &.level_5 {
    background-image: linear-gradient(1deg, #fff8e1 45%, #f5ba79 77%, #f4ae4e 87%);
    color: #eaa263;
    .wrap {
      background-image: linear-gradient(1deg, #fff8e1 45%, #f5ba79 77%, #f4ae4e 87%);
    }
    .btn-share {
      background-image: linear-gradient(130deg, #ffb13f 5%, #f8a225 36%, #fe8921 100%);
    }
    .btn-save {
      color: #d7a764;
    }
    .desc {
      .my {
        .word {
          border-color: #eaa2635e;
        }
      }
    }
  }
  //筑梦爱心大使
  &.level_6 {
    background-image: linear-gradient(1deg, #ffe4e4 45%, #ffa3a3 78%, #ff8181 88%);
    color: #ff787a;
    .wrap {
      background-image: linear-gradient(1deg, #ffe4e4 45%, #ffa3a3 78%, #ff8181 88%);
    }
    .btn-share {
      background-image: linear-gradient(130deg, #ff9c9c 5%, #ff8585 32%, #eb6363 100%);
    }
    .btn-save {
      color: #bd1c1c;
    }
    .desc {
      .my {
        .word {
          border-color: #ff787a66;
        }
      }
    }
  }
  .share-pannel {
    width: 100%;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2222;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-direction: column;
    padding-bottom: 34px;
    background: #fff;

    .cancel {
      height: 100px;
      width: 100%;
      border-top: 10px solid #f7f7f7;
      font-size: 32px;
      color: #808bab;
      line-height: 100px;
      background: #fff;
      &:active {
        opacity: 0.6;
      }
    }
    .btns {
      width: 100%;
      height: 266px;
      background: #fff;
      font-size: 28px;
      color: #405080;
      line-height: 34px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      .btn-wx,
      .btn-timeline,
      .btn-img {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: column;
        .icon {
          width: 92px;
          height: 92px;
          margin-bottom: 20px;
        }
      }
    }
  }
}
</style>
