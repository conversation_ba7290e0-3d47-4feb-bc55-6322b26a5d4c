<template>
  <div class="page-5">
    <div class="bg"></div>
    <template v-if="canShow">
      <div class="section">
        <div class="page4-title title" v-if="lossMore">
          <img src="../img/5/title_6.png" alt="" class="img_title" />
        </div>
        <div class="page4-title title" v-else>
          <!-- <p>{{ topProdName }}</p>
          <p>是你最大的收益来源</p> -->
          <img :src="topProdName" alt="" class="img_title" />
        </div>

        <div class="contain" v-if="lossMore">
          <p>投资市场存在一定波动</p>
          <p>科学的资产配置稳健增值更抗波动</p>
          <p class="tip">多一点耐心和信心留给自己</p>
        </div>

        <div class="contain" v-if="hasIncome">
          <p class="tip">
            累计帮你赚<span>{{ topEarnings }}</span
            >元
          </p>
          <p>投资市场存在一定波动</p>
          <p>科学的资产配置稳健增值更抗波动</p>
        </div>

        <div class="contain" v-if="noLoss || hasLoss">
          <p class="tip">收益稳得一骑绝尘</p>
          <p class="tip">
            累计帮你赚<span class="big-num">{{ topEarnings }}</span
            >元
          </p>
          <p style="line-height: 2">{{ incomeStr }}</p>
        </div>

        <div class="table">
          <div class="list" :class="`${lossMore ? 'loss-more' : ''}  ${!noLoss ? 'hasloss' : ''}`">
            <div class="bg-line"></div>
            <div class="item" v-for="(item, index) in listData">
              <div class="bar" :class="`bar_${index} ${Number(item.money) === 0 ? 'small' : ''}`"></div>
            </div>
          </div>
          <div class="list loss" v-if="!noLoss" :class="{ hasincome: hasIncome }">
            <div class="bg-line" style="opacity: 0"></div>

            <div class="item" v-for="(item, index) in listData">
              <div class="bar" :class="`bar_loss_${index}`"></div>
            </div>
          </div>

          <div class="info">
            <div class="item" v-for="(item, index) in listData">
              <!-- <p>{{ item.name }}</p> -->
              <img :src="getImg(item.name)" alt="" />
              <p>{{ setMoney(item.money) }}</p>
            </div>
          </div>
        </div>
        <!-- <div class="table-tip">*以上收益来源不包含结构化、活期金、定期金和保险产品</div> -->
      </div>
    </template>

    <img class="cover" src="../img/5/bg-before.png" />
    <p class="tip-text">*以上收益来源不包含结构化、活期金、定期金和保险产品</p>
    <slot style="color: #b0aac8"></slot>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, computed, watch, nextTick } from 'vue'
import { useSwiperSlide } from 'swiper/vue/swiper-vue'
import gsap from 'gsap'
const swiperSlide = useSwiperSlide()
const props = defineProps<{
  page5Data: {
    listData: { percent: string; name: string; money: string }[]
    topProdName: string
    topEarnings: string
    hasIncome: boolean
    hasLoss: boolean
    noLoss: boolean
    incomeStr: string
    lossMore: boolean
  }
}>()
const canShow = inject('canShow')
const {
  listData = [],
  topProdName,
  topEarnings,
  noLoss,
  hasLoss,
  hasIncome,
  incomeStr,
  lossMore,
} = props.page5Data || {}

watch(canShow, (val) => {
  console.log('🚀 ~ file: page-5.vue ~ line 45 ~ watch ~ val', val)
})
const pushPageListId = inject('pushPageListId', (id: any) => {})
pushPageListId && pushPageListId('5')
const nameImgs = [
  require('../img/5/name_1.png'), // 存款
  require('../img/5/name_2.png'), // 高端理财
  require('../img/5/name_3.png'), // 活期+
  require('../img/5/name_4.png'), // 基金
  require('../img/5/name_5.png'), // 稳健理财
]

const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})

const hasNegative = computed(() => {
  return listData.some((i) => Number(i.money) < 0)
})

function setMoney(money: string) {
  const num = Number(money) > 10 || Number(money) < -10 ? parseInt(money) : Number(money)

  if (num >= 10000 || num <= -10000) {
    const s = Number(parseInt(((num / 10000) * 10).toString())) / 10 + '万'
    return num > 0 ? '+' + s : s
  }
  return num > 0 ? '+' + num : num
}
function getImg(name: string) {
  if (name === '基金') {
    return nameImgs[3]
  }
  if (name === '稳健理财') {
    return nameImgs[4]
  }
  if (name === '活期+') {
    return nameImgs[2]
  }
  if (name === '高端理财') {
    return nameImgs[1]
  }
  if (name === '存款') {
    return nameImgs[0]
  }
}
const lockSwiperNext = inject<Ref<boolean>>('lockSwiperNext', ref(false))

watch(isCurrentPage, (val) => {
  console.log('🚀 ~ file: page-5.vue ~ line 84 ~ watch ~ val', val)
  resetData()
  if (val) {
    console.log('切换到第5页拉，加载动画')
    lockSwiperNext.value = false

    nextTick(() => {
      const tl = gsap.timeline()
      tl.fromTo(
        '.page-5 .section',
        {
          opacity: 0,
          duration: 0,
          delay: 0.4,
          y: 10,
        },
        {
          x: 0,
          y: 0,
          opacity: 1,
          duration: 0.4,
          delay: 0.4,
          ease: 'ease-out',

          onComplete: () => {},
        }
      )
        .fromTo(
          '.page-5 .section .contain',
          {
            opacity: 0,
            duration: 0,
            delay: 0.4,
            y: 10,
          },
          {
            x: 0,
            y: 0,
            opacity: 1,
            duration: 0.4,
            delay: 0.4,
            ease: 'ease-out',

            onComplete: () => {},
          }
        )
        .fromTo(
          '.page-5 .table',
          {
            opacity: 0,
            duration: 0,
            delay: 0.4,
            y: 10,
          },
          {
            x: 0,
            y: 0,
            opacity: 1,
            duration: 0.4,
            delay: 0.4,
            ease: 'ease-out',
            onComplete: () => {
              nextTick(() => {
                setTableHeight()
              })
            },
          }
        )
        .fromTo(
          '.page-5 .table-tip',
          {
            opacity: 0,
            duration: 0,
            delay: 0.4,
            y: 10,
          },
          {
            x: 0,
            y: 0,
            opacity: 1,
            duration: 0.4,
            delay: 0.4,
            ease: 'ease-out',
            onComplete: () => {},
          }
        )
    })
  }
})

function resetData() {
  listData.forEach((item, index) => {
    const height = item.percent
    const className = `.bar_${index}`
    const classNameLoss = `.bar_loss_${index}`
    console.log('🚀 ~ file: page-5.vue ~ line 147 ~ listData.forEach ~ className', className)
    gsap.to(className, {
      opacity: 0,
      height: 0,
    })
    gsap.to(classNameLoss, {
      opacity: 0,
      height: 0,
    })
  })
}

function setTableHeight() {
  listData.forEach((item, index) => {
    const height = item.percent
    const className = `.bar_${index}`
    const classNameLoss = `.bar_loss_${index}`
    console.log('🚀 ~ file: page-5.vue ~ line 147 ~ listData.forEach ~ className', className)
    gsap.fromTo(
      className,
      {
        opacity: 0,
        duration: 0,
        height: 0,
      },
      {
        opacity: Number(item.money) >= 0 ? 1 : 0,
        duration: 0.4,
        height,
        delay: 0.4 * (index + 1),
      }
    )
    gsap.fromTo(
      classNameLoss,
      {
        opacity: 0,
        duration: 0,
        height: 0,
      },
      {
        opacity: Number(item.money) < 0 ? 1 : 0,
        duration: 0.4,
        height: Number(item.money) < 0 ? 70 + index * 6 + '%' : 0,
        delay: 0.4 * (index + 1),
      }
    )
  })
}
</script>

<style lang="scss" scoped>
.page-5 {
  background-image: linear-gradient(179deg, #ebccf3 0%, #eee0fe 21%, #babafb 88%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 40px;
  line-height: 1;
  position: relative;
  .bg {
    position: absolute;
    top: 0;
    left: -140px;
    z-index: 1;
    background: rgba(255, 255, 255, 0.5);
    height: 500px;
    width: 500px;
    border-radius: 50%;
    transform: scale(2);
    opacity: 0.5;
    &::before {
      content: '';
      width: 400px;
      height: 400px;
      background: rgba(255, 255, 255, 0.5);
      position: absolute;
      right: -300px;
      top: -150px;
      border-radius: 50%;
    }
  }

  .section {
    margin-top: 50px;
    height: 100%;
    width: 690px;
    border: 2px solid #fff;
    border-radius: 20px 20px 0 0;

    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.1));
    padding: 60px 0;
    text-align: left;
    line-height: 2;
    position: relative;
    opacity: 0;
    z-index: 2;
    .title {
      color: #3d55a0;
      padding-right: 0;
      background: linear-gradient(119deg, #aa7dd2 0%, #765acd 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      letter-spacing: -5px;
      &::before {
        background: linear-gradient(119deg, #aa7dd2 0%, #765acd 100%);
      }
    }

    .contain {
      padding-top: 50px;
      padding-left: 60px;
      padding-right: 40px;
      color: #5b5297;
      font-size: 28px;
      line-height: 2;
      .tip {
        font-size: 38px;
        font-weight: bold;
        span {
          color: #8b51d6;
        }
      }
    }
    .table {
      border-radius: 20px;
      background: #fff;
      margin: 0 auto;
      margin-top: 60px;
      width: 625px;
      height: 562px;
      background: linear-gradient(127deg, #ffffff 0%, rgba(233, 245, 255, 0.86) 100%);
      border: 6px solid rgba(37, 124, 175, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      position: relative;
      overflow: hidden;
      border: 4px solid #fff;
      .info {
        width: 100%;
        text-align: center;
        background: #fff;
        height: 112px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: #695ca6;
        letter-spacing: -0.06px;
        text-align: center;
        line-height: 28px;
        font-weight: 500;
        padding: 0 20px;
        flex: none;
        z-index: 222;
        .item {
          width: 20%;
          flex: none;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          line-height: 1.5;
          p {
            font-size: 28px;
            line-height: 1.5;
            margin-top: 0;
          }
          p:last-child {
            opacity: 0.5;
          }
        }
      }
      .list {
        background: #f1effd;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 30px 20px;
        height: 450px;
        box-sizing: border-box;
        width: 100%;
        margin-left: -20px;
        margin-right: -20px;
        padding-bottom: 0;
        position: relative;
        flex: 1 1 auto;

        .bg-line {
          height: 220px;
          width: 100%;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          border: 1px solid rgba(151, 151, 151, 1);
          border-left: none;
          border-right: none;
          opacity: 0.1;
          &::after {
            content: '';
            height: 1px;
            width: 100%;
            background: rgba(151, 151, 151, 1);
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        .item {
          height: 100%;
          font-size: 24px;
          flex: auto;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          flex-direction: column;
          width: 20%;
          z-index: 2;
          .bar {
            background-image: linear-gradient(
              -3deg,
              #6da6f9 0%,
              #6e9af0 25%,
              #5d94ff 47%,
              #6fb3fa 72%,
              #6bc0ff 90%,
              #a8d7fc 100%
            );
            width: calc(100% - 20px);
            margin: 0 auto;
            border-radius: 10px 10px 0 0;
            position: relative;
            opacity: 0;
            &::before {
              content: '';
              position: absolute;
              top: 10px;
              left: 10px;
              background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
              border-radius: 10px;
              width: calc(100% - 20px);
              height: 50%;
            }
            &::after {
              font-size: 40px;
              font-weight: bold;
              color: #fff;
              position: absolute;
              left: 50%;
              top: 20px;
              transform: translateX(-50%);
              z-index: 2;
              content: '1';
              font-family: Roboto-Bold;
            }
            &.small {
              &::before {
                display: none;
              }
              &::after {
                display: none;
              }
            }
          }

          &:nth-child(3) {
            .bar {
              background-image: linear-gradient(0deg, #a7e1ff 0%, #7fcfe4 20%, #64c9e3 46%, #82d2ff 100%);
              &::after {
                content: '2';
              }
            }
          }
          &:nth-child(4) {
            .bar {
              background-image: linear-gradient(0deg, #e4a7fe 0%, #db84f7 11%, #cd6edb 44%, #f291d6 100%);
              &::after {
                content: '3';
              }
            }
          }
          &:nth-child(5) {
            .bar {
              background-image: linear-gradient(0deg, #c1a6ff 0%, #b18eff 47%, #b7abfd 100%);
              &::after {
                content: '4';
              }
            }
          }
          &:nth-child(6) {
            .bar {
              background-image: linear-gradient(0deg, #c1a6ff 0%, #b18eff 47%, #b7abfd 100%);
              &::after {
                content: '5';
              }
            }
          }
        }
        &.hasloss {
          height: 270px;
        }
        &.loss {
          transform: rotateX(180deg);
          height: 180px;
          border-bottom: 1px solid #fff;
          flex: none;
          &.hasincome {
            height: 220px;
          }
          .item {
            .bar {
              &::after {
                transform: translateX(-50%) rotateX(180deg);
              }
            }
          }
        }
        &.loss-more {
          height: 220px;
        }
      }
    }
    .table-tip {
      font-size: 22px;
      color: #b0aac8;
      line-height: 22px;
      text-align: left;
      margin-left: 40px;
      margin-top: 30px;
    }
  }
  .cover {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
  }
}
.tip-text {
  font-size: 22px;
  color: #898989;
  line-height: 22px;
  position: absolute;
  bottom: 75px;
  left: 55px;
  opacity: 0.5;
}
</style>
