<template>
  <div class="page-4-2">
    <div class="bg"></div>

    <template v-if="canShow">
      <div class="section">
        <div class="title">
          <img src="../img/4/title_5.png" alt="" class="img_title" />
        </div>
        <div class="contain">
          <p>连续4年入选中国银行100强</p>
          <p>合作银行理财子公司达16家</p>
          <p>帮助微众银行App客户进行财富管理</p>
          <p>用户买入最多Top3产品：</p>
          <div class="prod-list">
            <div class="item" v-for="(item, index) in prodList">
              <img class="icon" :src="require(`../img/4/winner_${index + 1}.png`)" />
              <p>{{ item }}</p>
            </div>
          </div>
          <p>更多精选的稳健理财产品</p>
          <p>请通过微众银行App-理财页查看</p>
        </div>
      </div>
    </template>

    <img class="cover" src="../img/4/bg-before.png" />
  </div>
</template>

<script setup lang="ts">
import { ref, inject, computed, watch, nextTick } from 'vue'
import { useSwiperSlide } from 'swiper/vue/swiper-vue'
import gsap from 'gsap'
const swiperSlide = useSwiperSlide()
const props = defineProps<{
  page4Data: {
    prodList: string[]
  }
}>()
const canShow = inject('canShow')
console.log('🚀 ~ file: page-2.vue ~ line 43 ~ canShow', canShow)
// const { prodList } = props.page4Data || {}

const prodList = ['活期+', '青银理财璀璨人生月定开', '苏银理财恒源季开放系列']

watch(canShow, (val) => {
  console.log('🚀 ~ file: page-4-2.vue ~ line 45 ~ watch ~ val', val)
})
const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})
const pushPageListId = inject('pushPageListId', (id: any) => {})
pushPageListId && pushPageListId('4-2')

watch(isCurrentPage, (val) => {
  console.log('🚀 ~ file: page-3.vue ~ line 84 ~ watch ~ val', val)
  if (val) {
    console.log('切换到第4页拉，加载动画')
    nextTick(() => {
      const list = document.querySelectorAll('.page-4-2 .section')
      console.log('🚀 ~ file: page-3.vue ~ line 90 ~ nextTick ~ list', list)

      const tl = gsap.timeline()
      tl.fromTo(
        '.page-4-2 .section',
        {
          opacity: 0,
          duration: 0,
          delay: 0,
          y: 10,
        },
        {
          x: 0,
          y: 0,
          opacity: 1,
          duration: 0.4,
          delay: 1,
          ease: 'ease-out',

          onComplete: () => {},
        }
      ).fromTo(
        '.page-4-2 .contain',
        {
          opacity: 0,
          duration: 0,
          delay: 0,
          y: 10,
        },
        {
          x: 0,
          y: 0,
          opacity: 1,
          duration: 0.4,
          delay: 0,
          ease: 'ease-out',

          onComplete: () => {},
        }
      )
    })
  }
})
</script>

<style lang="scss" scoped>
.page-4-2 {
  background: linear-gradient(180deg, #c7e0f8 0%, #d7e8fb 22%, #a7d2f7 69%, #8bc4f2 100%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 40px;
  line-height: 1;
  .bg {
    position: absolute;
    top: 0;
    left: -140px;
    z-index: 1;
    background: rgba(255, 255, 255, 0.5);
    height: 500px;
    width: 500px;
    border-radius: 50%;
    transform: scale(2);
    opacity: 0.2;
    &::before {
      content: '';
      width: 400px;
      height: 400px;
      background: rgba(255, 255, 255, 0.5);
      position: absolute;
      right: -300px;
      top: -150px;
      border-radius: 50%;
    }
  }

  .section {
    margin-top: 40px;
    height: 100%;
    width: 690px;
    border: 2px solid #fff;
    border-radius: 20px 20px 0 0;
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.1));
    padding: 64px 60px;
    text-align: left;
    position: relative;
    opacity: 0;
    .title {
      font-size: 50px;
      font-weight: bold;
      color: #3d55a0;
      line-height: 1.5;
      letter-spacing: 3px;
      background: linear-gradient(119deg, #6a69ff 0%, #00adff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      // padding-bottom: 40px;
      // border-bottom: 4px solid rgb(197, 211, 232);
      position: relative;
    }
    .contain {
      padding-top: 60px;

      font-size: 28px;
      color: #6a7cb7;
      line-height: 1;
      p {
        &.tip {
          font-size: 32px;
          font-weight: bold;
        }
      }
      span {
        font-size: 52px;
        font-weight: bold;
        color: #4186ee;
      }
      .prod-list {
        font-weight: bold;
        color: #545097;
        margin-bottom: 60px;
        margin-left: -30px;
        margin-right: -30px;
        margin-top: 30px;
        .item {
          border-radius: 20px;
          background: rgba(255, 255, 255, 0.2);
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding: 0 30px;
          margin-bottom: 20px;
          p {
            padding: 0;
            font-size: 35px;
            margin-top: 0;
          }
          .icon {
            width: 47px;
            height: 102px;
            margin-right: 30px;
          }
        }
      }
    }
    .table {
      border-radius: 40px;
      background: #fff;
      margin: 0 auto;
      margin-top: 30px;
      width: 625px;
      height: 542px;
      background: linear-gradient(127deg, #ffffff 0%, rgba(233, 245, 255, 0.86) 100%);
      border: 3px solid rgba(37, 124, 175, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      .times {
        position: absolute;
        bottom: 15px;
        left: 25px;
        right: 25px;
        font-size: 24px;
        color: #98bedf;
        letter-spacing: -0.05px;
        line-height: 29.4px;
        font-weight: 900;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .line-good {
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .ball {
        width: 34px;
        height: 34px;
        border: 8px solid #fff;
        background-color: #36a3ff;
        box-shadow: 0px 8px 15px 0px rgba(0, 93, 245, 0.43);
        border-radius: 50%;
        position: absolute;
        right: 14px;
        top: 22px;
        z-index: 22;
      }
      .tips {
        width: 240px;
        height: 96px;
        position: absolute;
        right: 14px;
        top: 22px;
        z-index: 21;
      }
    }
  }
  .cover {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
  }
}
.table-line,
.bg-line {
  width: 564px;
  height: 442px;
  display: block;
  position: absolute;
  .path {
    stroke-dasharray: 900;
    stroke-dashoffset: 0;
  }
}
.bg-line {
  opacity: 0;
}

@keyframes red-ball {
  from {
    offset-distance: 0%;
    // left: 30px;
    // top: 70px;
  }
  to {
    offset-distance: 100%;
    // left: 70px;
    // top: 55px;
  }
}

@keyframes dash {
  from {
    stroke-dashoffset: 900;
  }
  to {
    stroke-dashoffset: 0;
  }
}
</style>
