<template>
  <div class="page-12" :class="`level_${userLevelNum}`">
    <div class="user-avator">
      <div class="avator">
        <img class="cover" :src="userAvator" alt="" />
        <img :src="equityIcon" alt="" class="icon" />
      </div>
      <div class="title"><img src="../img/12/title.png" alt="" /></div>
      <div class="desc">
        <p v-if="userName">
          亲爱的<span>{{ userName }}</span
          >，过去{{ accountDiffYear }}年，微众感谢有你
        </p>
        <p v-else>过去{{ accountDiffYear }} 年，微众感谢有你</p>
        <p>未来你想对自己说些什么呢</p>
      </div>
    </div>
    <tags></tags>
    <div class="btn-jump" :class="{ disabled: !myword }" @click="jump">生成我的财富+形象</div>
  </div>
</template>

<script setup lang="ts">
import rollingNumbers from './rolling-numbers/rolling-numbers.vue'
import tags from './comps-12/tags.vue'
import { ref, inject, computed, watch, nextTick, onMounted, Ref } from 'vue'
import { dayjs } from '../../../utils'
import { useSwiperSlide, useSwiper } from 'swiper/vue/swiper-vue'
import { focusServices, focusStore, focusCore } from '@focus/render'
const { kvService } = focusServices
const swiperSlide = useSwiperSlide()
const swiper = useSwiper()
const props = defineProps<{
  page12Data: {
    userAvator: string
    userName: string
    userLevelNum: string
    accountCreateTime: string
  }
}>()
const canShow = inject('canShow')

const { accountCreateTime, userLevelNum, userName, userAvator } = props.page12Data || {}

const accountDiffYear = computed(() => {
  const d = dayjs(dayjs()).diff(accountCreateTime, 'year')
  return Number(d) < 1 ? 1 : d
})
// const pushPageListId = inject('pushPageListId', (id: any) => {})
// pushPageListId && pushPageListId('12')
const equityIcon = computed(() => {
  const imgs = [
    require('../img/12/1.png'),
    require('../img/12/2.png'),
    require('../img/12/3.png'),
    require('../img/12/4.png'),
  ]
  return imgs[Number(userLevelNum) - 1]
})

const myword = inject<Ref<string>>('myword') || ref('')
const lockSwiper = inject<Ref<boolean>>('lockSwiper', ref(false))
const showNext = inject('showNext', ref(true))

const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})
watch(isCurrentPage, (val) => {
  if (val) {
    lockSwiper.value = true
    showNext.value = false
    // nextTick(() => {
    //   setTimeout(() => {
    //     swiper.value.disable()
    //     showNext.value = false
    //   }, 500)
    // })
  }
})

function setAgree() {
  const kvKey = 'time_travel'

  kvService.setUserKv(kvKey, { agree: 1 }).then((res) => {
    console.log('🚀 ~ file: page-1.vue ~ line 58 ~ kvService.setUerKv ~ res', res)
  })
}

function jump() {
  // swiper.value.allowTouchMove = true
  if (!myword.value) {
    return false
  }
  focusCore.clickLog('new_photo')
  setAgree()
  console.log('🚀 ~ file: page-12.vue ~ line 74 ~ jump ~ swiper', swiper)
  setTimeout(() => {
    swiper.value.slideNext()
  }, 100)
}
</script>

<style lang="scss" scoped>
.page-12 {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 40px;
  line-height: 1;
  // padding-top: 200px;
  background: linear-gradient(180deg, #324ffe 0%, #89b5fe 15%, #cde8ff 27%, #f8fbff 58%, #eaf9ff 100%);
  font-size: 28px;
  .user-avator {
    background: url('../img/12/bg.png') no-repeat;
    background-size: 100%;
    width: 100%;
    // height: 600px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-direction: column;
    margin-bottom: 70px;
    margin-top: 20px;
    .avator {
      width: 158px;
      height: 158px;
      border: 7px solid #fff;
      border-radius: 50%;
      line-height: 0;
      position: relative;
      margin-top: 6px;
      .cover {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }

      .icon {
        width: 88px;
        height: 108px;
        position: absolute;
        bottom: -6px;
        right: -40px;
      }
    }
    .title {
      width: 435px;
      height: 62px;
      margin: 0 auto;
      margin-top: 44px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .desc {
      font-size: 32px;
      color: #6994eb;
      line-height: 1.5;
      margin-top: 35px;
      span {
        font-weight: bold;
      }
    }
  }
  .btn-jump {
    width: 620px;
    height: 118px;
    line-height: 118px;
    text-align: center;
    background: linear-gradient(125deg, #fdcd88 0%, #fb6a6a 100%);
    box-shadow: inset 0px 3px 0px 0px rgba(255, 255, 255, 0.62);
    border-radius: 59px;
    color: #fff;
    font-size: 40px;
    margin-top: 60px;
    font-weight: bold;
    &:active {
      opacity: 0.6;
    }
    &.disabled {
      opacity: 0.3;
      &:active {
        opacity: 0.3;
      }
    }
  }
}
</style>
