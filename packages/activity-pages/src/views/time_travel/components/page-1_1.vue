<template>
  <div class="page-1_1" v-if="showPage">
    <img src="../img/1/title.png" alt="" class="title" />
    <div class="center">
      <div class="plant-mini" @click="jumpPage('11')">
        <div class="info">
          <p class="time bignum">{{ equityTime }}</p>
          <p class="desc">成为{{ equityName }}会员 <img src="../img/1/1-2.png" alt="" class="arrow" /></p>
          <img src="../img/1/1-1.png" alt="" class="bg" />
        </div>
        <img src="../img/1/plant_3.png" alt="" class="cover" />
      </div>
      <div class="plant-mini" @click="jumpPage('4')">
        <div class="info">
          <p class="time bignum">{{ firstBuyTime }}</p>
          <p class="desc">第一次买产品 <img src="../img/1/2-2.png" class="arrow" /></p>
          <img src="../img/1/2-1.png" alt="" class="bg" />
        </div>
        <img src="../img/1/plant_2.png" alt="" class="cover" />
      </div>
      <div class="plant-mini" @click="jumpPage('2')">
        <div class="info">
          <p class="time bignum">{{ accountCreateTime }}</p>
          <p class="desc">开户成功 <img src="../img/1/3-2.png" class="arrow" /></p>
          <img src="../img/1/3-1.png" alt="" class="bg" />
        </div>
        <img src="../img/1/plant_1.png" alt="" class="cover" />
      </div>
    </div>
    <div class="bottom">
      <div class="btn" @click="jumpPage('2')">
        <p class="text">重温时光之旅</p>
      </div>
      <div class="tip protocal" :class="{ selected: selectedProtocal, 'click-no-selected': clickOnNotSelected }">
        <div class="radio" @click="clickAgree"></div>
        同意授权
        <div class="pdf" @click="clickPdf">《微众时光机服务及授权协议》</div>
      </div>
      <p class="tip">数据截止至 {{ lastDataTime }}</p>
      <img src="../img/1/door2.png" class="door" alt="" />
      <img src="../img/1/plant.png" alt="" class="plant" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch, ref, inject, toRefs, nextTick } from 'vue'
import { focusServices, focusStore, focusCore } from '@focus/render'
import gsap from 'gsap'
const { jumpService } = focusServices

const props = defineProps<{
  page1Data: {
    accountCreateTime: string
    equityTime: string
    firstBuyTime: string
    equityName: string
  }
}>()
const lastDataTime = inject('lastDataTime')
const pageList = inject('pageList', ref<any[]>([]))
const { accountCreateTime, equityTime, firstBuyTime, equityName } = props.page1Data || {}
const showPage = ref(true)
watch(props, (val) => {
  console.log('🚀 ~ file: page-1_1.vue ~ line 56 ~ watch ~ val', val)
})
const setCanShow = inject('setCanShow', () => {})
const clickOnNotSelected = ref(false)

const selectedProtocal = ref(true)
const mockSlidIndex = inject('mockSlidIndex', ref(0))
const seconTimeCtl = inject('seconTimeCtl', ref(false))

function jumpPage(id: string) {
  seconTimeCtl.value = true

  console.log('🚀 ~ file: page-1_1.vue ~ line 71 ~ jumpPage ~ id', id)
  const index = pageList.value && pageList.value.indexOf(id)
  console.log('🚀 ~ file: page-1_1.vue ~ line 73 ~ jumpPage ~ index', index)
  nextTick(() => {
    if (index > -1) {
      mockSlidIndex.value = index
      seconTimeCtl.value = false
      clickStart()
    }
  })
}

function clickStart() {
  if (!selectedProtocal.value) {
    clickOnNotSelected.value = true
    return false
  }
  gsap.to('.page-1_1', {
    opacity: 0,
    duration: 0.4,
    delay: 0,
    onComplete: () => {
      setTimeout(() => {
        showPage.value = false
        setCanShow && setCanShow()
      }, 100)
    },
  })
}

function clickAgree() {
  selectedProtocal.value = !selectedProtocal.value
  clickOnNotSelected.value = false
}

function clickPdf() {
  // https://dbd.test.webankwealthcdn.net/wm-resm/hj/utils/pdfjs2/web/viewer.html?file=https%3A%2F%2Fdbd.test.webankwealthcdn.net%2Fwm-resm%2Fhjupload%2Fcommon%2Fshiguangji.pdf
  let viewer = 'https://dbd.webankwealthcdn.net/wm-resm/hj/utils/pdfjs2/web/viewer.html?file='
  let pdf =
    'https://dbd.webankwealthcdn.net/wm-resm/hjupload/common/%E6%B7%B1%E5%9C%B3%E5%89%8D%E6%B5%B7%E5%BE%AE%E4%BC%97%E9%93%B6%E8%A1%8CAPP%E9%9A%90%E7%A7%81%E6%94%BF%E7%AD%96%E2%80%94%E5%BE%AE%E4%BC%97%E6%97%B6%E5%85%89%E6%9C%BA%E8%A1%A5%E5%85%85%E6%8E%88%E6%9D%83%E8%AF%B4%E6%98%8E%E4%B9%A6V1.0.pdf'

  if (BUILD_TEST) {
    viewer = 'https://dbd.test.webankwealthcdn.net/wm-resm/hj/utils/pdfjs2/web/viewer.html?file='
    pdf =
      'https://dbd.test.webankwealthcdn.net/wm-resm/hjupload/common/%E6%B7%B1%E5%9C%B3%E5%89%8D%E6%B5%B7%E5%BE%AE%E4%BC%97%E9%93%B6%E8%A1%8CAPP%E9%9A%90%E7%A7%81%E6%94%BF%E7%AD%96%E2%80%94%E5%BE%AE%E4%BC%97%E6%97%B6%E5%85%89%E6%9C%BA%E8%A1%A5%E5%85%85%E6%8E%88%E6%9D%83%E8%AF%B4%E6%98%8E%E4%B9%A6V1.0.pdf'
  }
  const link = viewer + encodeURIComponent(pdf)
  // location.href = link
  jumpService.jumpNewWebview(link)
}
</script>

<style lang="scss" scoped>
.page-1_1 {
  width: 100%;
  height: 100%;
  background: url('../img/1/BG.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 2222;
  .title {
    width: 468px;
    height: 289px;
    margin: 0 auto;
    margin-top: 20px;
  }
  .center {
    position: relative;
    width: 100%;
    height: 500px;
    .plant-mini {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-direction: column;
      position: absolute;
      animation: flow 1s ease 0s infinite alternate;
      font-size: 30px;
      line-height: 1;
      z-index: 222;

      .info {
        height: 126px;
        width: 224px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: column;
        position: relative;
        .time {
          margin-top: 2px;
        }
        p {
          z-index: 2;
        }
        p:first-child {
          color: #fff;
          margin-bottom: 15px;
          line-height: 1.5;
        }
        .desc {
          color: #30a3e7;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;

          .arrow {
            width: 12px;
            height: 20px;
            margin-left: 10px;
          }
        }
        .bg {
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          top: 0;
          z-index: 1;
        }
      }
      .cover {
        width: 236px;
        height: 236px;
      }
      &:nth-child(1) {
        left: 100px;
        top: -30px;
      }
      &:nth-child(2) {
        position: absolute;
        right: 0;
        top: 50px;
        animation: flow 1.5s ease 0s infinite alternate;

        .info {
          margin-left: 40px;
          .desc {
            color: #6d68ff;
          }
        }
        .cover {
          width: 435.84px;
          height: 258px;
        }
      }
      &:nth-child(3) {
        left: 60px;
        top: 350px;
        animation: flow 1.2s ease 0s infinite alternate;
        .info {
          .desc {
            color: #5688ff;
          }
        }
        .cover {
          width: 261px;
          height: 261px;
        }
      }
    }
  }

  .bottom {
    flex: auto;
    position: relative;
    z-index: 2;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-direction: column;
    padding-bottom: 100px;
    .door {
      width: 544px;
      height: 692px;
      position: absolute;
      bottom: 100px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1;
    }
    .plant {
      width: 100%;
      position: absolute;
      bottom: -100px;
      left: 0;
      right: 0;
      z-index: 2;
    }
    .tip {
      font-size: 22px;
      text-align: center;
      color: #6b6db1;
      line-height: 2;
      z-index: 2;
    }
    .protocal {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      width: fit-content;
      margin: 0 auto;
      z-index: 3;

      &::before {
        content: '';
        font-size: 30px;
        color: #fff;
        background: url('../img/1/tip.png') no-repeat center;
        background-size: 100%;
        border-radius: 10px;
        height: 78px;
        width: 120px;
        text-align: center;
        line-height: 60px;
        position: absolute;
        left: -48px;
        top: -78px;
        display: none;
      }

      .radio {
        width: 28px;
        height: 28px;
        border: 5px solid #4846da;
        border-radius: 50%;
        margin-right: 10px;
        position: relative;
        transform: rotate(-45deg);
      }
      .pdf {
        font-size: 22px;
        font-weight: bold;
        color: #6665de;
      }
      &.click-no-selected {
        &::before {
          display: block;
        }
      }
      &.selected {
        &::before {
          display: none;
        }
        .radio {
          background: #4846da;
          &::before {
            content: '';
            width: 15px;
            height: 4px;
            background: #fff;
            border: 4px;
            position: absolute;
            left: 4px;
            top: 9px;
          }
          &::after {
            content: '';
            width: 3px;
            height: 8px;
            background: #fff;
            border: 4px;
            position: absolute;
            left: 4px;
            top: 3px;
          }
        }
      }
    }
    .btn {
      width: 620px;
      height: 118px;
      background: linear-gradient(131deg, #e8fcff 0%, #f7edff 100%);
      border-radius: 59px;
      border: 2px solid #ffffff;
      line-height: 118px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 35px;
      position: relative;
      overflow: hidden;
      z-index: 3;
      p {
        line-height: 1.5;
      }
      :active {
        opacity: 0.7;
      }
      &::before {
        content: '';
        width: 60px;
        height: 170px;
        background: rgba($color: #fff, $alpha: 0.4);
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%) rotate(20deg);
        z-index: 2;
        animation: flink 2s ease-in 0s infinite reverse;
      }
      .text {
        font-size: 40px;
        font-weight: 600;
        color: #3d55a0;
        line-height: 1.5;
        letter-spacing: 3px;
        background: linear-gradient(119deg, #6a69ff 0%, #00adff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}

@keyframes blink {
  from {
    box-shadow: 0 0px 80px 40px rgba($color: #fff, $alpha: 1);
  }
  to {
    box-shadow: 0 0px 80px 60px rgba($color: #fff, $alpha: 1);
  }
}

@keyframes flow {
  from {
    // margin-top: -100px;
    transform: translateY(-0px);
  }
  to {
    transform: translateY(10px);
    // margin-top: -80px;
  }
}

@keyframes flink {
  from {
    left: 0;
  }
  from {
    left: 570px;
  }
}
</style>
