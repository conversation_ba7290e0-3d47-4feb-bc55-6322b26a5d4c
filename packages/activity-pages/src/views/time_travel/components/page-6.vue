<template>
  <div class="page-6" :class="{ status_2: status2 }">
    <div class="bg"></div>
    <template v-if="canShow">
      <div class="section">
        <div class="title">
          <img v-show="!status2" src="../img/6/title_1.png" alt="" class="img_title" />
          <img v-show="status2" src="../img/6/title_2.png" alt="" class="img_title" />

          <!-- <div class="last-time">
            <p>截止累计时间</p>
            <p>{{ lastCountTime }}</p>
          </div> -->
        </div>
        <div class="contain">
          <div class="item">
            <div class="tag">最爱购买产品</div>
            <p class="name">{{ bestProd.name }}</p>
            <div class="desc">
              买入<span style="font-weight: bold">{{ bestProd.buyTimes }}</span
              >次
              <div class="line"></div>
              累计收益 <span style="font-weight: bold">{{ bestProd.earnings }}元</span>
            </div>
            <div class="info">
              <p>
                <span>{{ bestProd.buyerCount }}</span
                >用户也选择Ta!
              </p>
              <p style="font-weight: bold">千里马产品需要您这样的伯乐</p>
            </div>
          </div>
          <div class="item">
            <div class="tag">历史收益最高产品</div>
            <p class="name">{{ topEarningProd.name }}</p>
            <div class="desc">
              买入<span>{{ topEarningProd.buyTimes }}</span
              >次
              <div class="line"></div>
              累计收益 <span>{{ topEarningProd.earnings }}元</span>
            </div>

            <div class="info">
              <p>
                <span>{{ topEarningProd.buyerCount }}</span
                >用户也选择Ta!
              </p>
              <p style="font-weight: bold">专业的投资者有最独特的眼光</p>
            </div>
          </div>
          <!-- <div class="item empty"></div>
          <div class="item empty"></div> -->
        </div>
      </div>
    </template>
    <slot></slot>
    <img class="cover" v-show="status2" src="../img/6/bg.png" />
    <img class="cover" v-show="!status2" src="../img/5/bg-before.png" />
    <canvas id="zan" @click="clickZan"></canvas>
    <canvas id="hand"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, computed, watch, nextTick, onMounted, Ref } from 'vue'
import { useSwiperSlide, useSwiper } from 'swiper/vue/swiper-vue'
import { focusServices, focusStore, focusCore } from '@focus/render'

import { Parser, Player } from 'svga'
import gsap from 'gsap'
const swiperSlide = useSwiperSlide()
const swiperInstance = useSwiper()
const props = defineProps<{
  page6Data: {
    listData: { percent: string; name: string; money: string }[]
    topEarningProd: {
      name: string
      earnings: string
      buyerCount: string
      buyTimes: string
    }
    bestProd: {
      name: string
      earnings: string
      buyTimes: string
      buyerCount: string
    }
    firstPointTime: string
    firstProdName: string
  }
}>()
const canShow = inject('canShow')
const zanSvgaFile = require('../img/6/zan.svga')
const handSvgaFile = require('../img/6/hand.svga')
const lastCountTime = inject('lastCountTime')
const {
  topEarningProd = {
    earnings: '',
    name: '',
    buyTimes: '',
    buyerCount: '',
  },
  bestProd = {
    name: '',
    earnings: '',
    buyTimes: '',
    buyerCount: '',
  },
} = props.page6Data || {}

const status2 = ref(true)
let handPlayer: any = null
let zanPlayer: any = null
const dianzanSvga: any = ref(null)
const handSvga: any = ref(null)

const parser = new Parser()
const pushPageListId = inject('pushPageListId', (id: any) => {})
pushPageListId && pushPageListId('6')
async function getData() {
  dianzanSvga.value = await parser.load(zanSvgaFile)
  handSvga.value = await parser.load(handSvgaFile)
}
getData()
onMounted(async () => {
  handPlayer = new Player({ container: document.getElementById('hand'), loop: 1, isDisableImageBitmapShim: true })
  zanPlayer = new Player(document.getElementById('zan'))
})

watch(dianzanSvga, async (val) => {
  console.log('🚀 ~ file: dianzanSvga', val)
  await zanPlayer.mount(val)
  zanPlayer.start()
})
watch(handSvga, async (val) => {
  console.log('🚀 ~ file: handSvga', val)
  await handPlayer.mount(val)
})

watch(canShow, (val) => {
  console.log('🚀 ~ file: page-6.vue ~ line 45 ~ watch ~ val', val)
})
const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})
const lockSwiper = inject<Ref<boolean>>('lockSwiper', ref(false))
const lockSwiperNext = inject<Ref<boolean>>('lockSwiperNext', ref(false))

const showNext = inject('showNext', ref(false))

watch(isCurrentPage, (val) => {
  console.log('🚀 ~ file: page-6.vue ~ line 84 ~ watch ~ val', val)
  if (val) {
    console.log('切换到第6页拉，加载动画')
    lockSwiper.value = false
    lockSwiperNext.value = true
    showNext.value = false

    resetStatus()
    nextTick(() => {
      const tl = gsap.timeline()
      tl.fromTo(
        '.page-6 .section',
        {
          opacity: 0,
          duration: 0,
          delay: 0,
          y: 10,
        },
        {
          x: 0,
          y: 0,
          opacity: 1,
          duration: 0.4,
          delay: 0.4,
          ease: 'ease-out',

          onComplete: () => {
            const list = document.querySelectorAll('.page-6 .contain .item')
            for (let i = 0; i < list.length; i++) {
              const dom = list[i]

              gsap.fromTo(
                dom,
                {
                  opacity: 0,
                  duration: 0,
                  delay: 0,
                  y: 10,
                },
                {
                  x: 0,
                  y: 0,
                  opacity: 1,
                  duration: 0.4,
                  delay: 0.4 * i,
                }
              )
            }
          },
        }
      ).fromTo(
        '#zan',
        {
          opacity: 0,
          duration: 0,
          delay: 0,
          y: 10,
        },
        {
          x: 0,
          y: 0,
          opacity: 1,
          duration: 0.4,
          delay: 0.8,
          ease: 'ease-out',
        }
      )
    })
  }
})

function clickZan() {
  focusCore.clickLog('dianzan')
  handPlayer.onStop = () => {
    handPlayer.destroy()

    document.querySelector('#hand')
  }

  zanPlayer.pause()
  handPlayer.start()

  gsap.to('#zan', {
    opacity: 0,
    duration: 1,
    onComplete: () => {
      // zanPlayer.destroy()
      setStatus2()
    },
  })
}
function setStatus2() {
  setTimeout(() => {
    lockSwiper.value = false
    lockSwiperNext.value = false
    swiperInstance.value.allowTouchMove = true
    showNext.value = true
  }, 1000)
  status2.value = true
}
function resetStatus() {
  status2.value = false
  zanPlayer.start()
  gsap.fromTo(
    '.page-6 .contain',
    {
      duration: 0,
      opacity: 0,
    },
    {
      duration: 0.4,
      opacity: 1,
    }
  )
  const list = document.querySelectorAll('.page-6 .contain .item')
  for (let i = 0; i < list.length; i++) {
    const dom = list[i]

    gsap.to(
      dom,

      {
        opacity: 0,
        duration: 0,
      }
    )
  }
}
</script>

<style lang="scss" scoped>
.page-6 {
  background-image: linear-gradient(180deg, #fbecf9 0%, #fbecf9 36%, #edb8e0 100%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 40px;
  line-height: 1;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: red;
    transition: all 0.5s ease-out;
    opacity: 0;
    background-image: linear-gradient(179deg, #7782fd 0%, #edb9e4 40%, #f7eefc 93%);
  }

  .bg {
    position: absolute;
    top: 0;
    left: -140px;
    z-index: 1;
    background: rgba(255, 255, 255, 0.5);
    height: 500px;
    width: 500px;
    border-radius: 50%;
    transform: scale(2);
    opacity: 0.2;
    &::before {
      content: '';
      width: 400px;
      height: 400px;
      background: rgba(255, 255, 255, 0.5);
      position: absolute;
      right: -300px;
      top: -150px;
      border-radius: 50%;
    }
  }

  .cover {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 0;
  }

  #zan {
    position: absolute;
    bottom: 200px;
    z-index: 222;
    width: 750px;
    height: 600px;
    left: 50%;
    transform: translateX(-50%);
  }
  #hand {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    z-index: 3;
  }
  .section {
    margin-top: 50px;
    height: 90%;
    width: 690px;
    // border: 2px solid #fff;
    border-radius: 40px;
    // background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.1));
    text-align: left;
    line-height: 2;
    position: relative;
    opacity: 0;
    padding: 0 40px;

    z-index: 2;
    .title {
      font-size: 70px;
      font-weight: bold;
      color: #b24fbe;
      line-height: 90px;
      letter-spacing: 3px;
      padding-bottom: 30px;
      width: 100%;
      position: relative;
      p {
        line-height: 80px;
        margin: 0;
      }

      .img_title {
        width: 330px;
        height: 144px;
      }
      .last-time {
        position: absolute;
        bottom: 30px;
        right: 0;
        font-size: 28px;
        color: #ab6ac1;
        font-weight: normal;
        text-align: right;
        p {
          margin-top: 0;
          line-height: 38px;
        }
      }
    }

    .contain {
      .item {
        opacity: 0;
        border-radius: 20px;
        position: relative;
        margin-top: 30px;
        opacity: 0;
        border: 2px solid #fff;
        background-image: linear-gradient(
          90deg,
          rgba(255, 255, 255, 0.91) 0%,
          rgba(255, 255, 255, 0.68) 70%,
          rgba(255, 255, 255, 0.23) 100%
        );
        overflow: hidden;
        height: 220px;
        box-sizing: border-box;
        transition: all 1s ease-out;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: column;
        .tag {
          border-radius: 20px 0 30px 0;
          background: linear-gradient(to right, #d8a9f6, #6e87f3);
          position: absolute;
          left: 0px;
          top: 0px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 30px;
          padding: 0 30px;
          height: 54px;
          font-weight: bold;
        }
        .name {
          font-size: 38px;
          font-weight: 600;
          color: #5d4f9c;
          line-height: 38px;
          padding-left: 30px;
          padding-right: 30px;
          margin-top: 80px;
          width: 100%;
          box-sizing: border-box;
          text-align: left;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
        .info {
          flex: auto;
          box-sizing: border-box;
          width: 100%;
          height: 0;
          font-size: 30px;
          display: flex;
          align-items: flex-start;
          justify-content: center;
          flex-direction: column;
          background: #fff;
          color: #5d4f9c;
          opacity: 0;
          padding: 0;
          transition: all 1s ease-out;
          span {
            font-weight: bold;
            color: #e85386;
          }
        }
        &:nth-child(2) {
          .tag {
            background: linear-gradient(to right, #f7cb92, #f08b79);
          }
        }
        .desc {
          font-size: 30px;
          padding-bottom: 20px;
          color: #5d4f9c;
          font-size: 28px;
          padding-left: 30px;
          margin-top: 30px;
          width: 100%;
          text-align: left;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          line-height: 1;
          .line {
            height: 22px;
            width: 1px;
            background: #5d4f9c;
            margin-left: 10px;
            margin-right: 10px;
          }
          span {
            color: #e85386;
            font-weight: bold;
          }
        }
        &.empty {
          height: 200px;
          opacity: 0;
        }
      }
    }
  }
  &.status_2 {
    &::before {
      opacity: 1;
    }
    .section {
      .title {
        -webkit-text-fill-color: #fff;
        color: #fff;
        background: none;
        .last-time {
          -webkit-text-fill-color: #fff;
          color: #fff;
          background: none;
        }
      }
      .contain {
        .item {
          height: 360px;
          .info {
            height: 155px;
            padding: 20px;
            opacity: 1;
            display: flex;
            line-height: 1;
            align-items: center;
            justify-content: space-around;
            text-align: left;
            p {
              margin: 0;
              text-align: left;
              width: 100%;
            }
          }
          &.empty {
            height: 0;
            opacity: 0;
          }
        }
      }
    }
  }
}
</style>
