<template>
  <div class="page-9" :class="{ nodotaion: !(Number(donationTimes) > 0) }">
    <template v-if="canShow">
      <div class="section">
        <p>理财之路除了眼光和机遇</p>
        <p>还需要有计划的管理</p>
      </div>
      <div class="section s_2">
        <p>不知不觉你已经</p>
        <p class="tip">
          执行计划 <span>{{ planTimes }}</span> 次
        </p>
        <p :class="{ tip: !(Number(donationTimes) > 0) }">坚持{{ maxPlanTimes }}次{{ maxPlanName }}计划</p>
        <p>{{ planStr }}</p>
      </div>

      <div class="section" v-if="Number(donationTimes) > 0">
        <p>予人玫瑰 手有余香</p>

        <p class="tip">
          累计捐赠“益点心意”
          <span>
            {{ donationTimes }}
          </span>
          次
        </p>
        <p>谢谢有爱的你</p>
      </div>
      <div class="section cover">
        <img src="../img/9/bg.png" alt="" v-if="Number(donationTimes) > 0" />
        <img src="../img/9/bg2.png" alt="" v-else />
      </div>
      <slot style="width: 100%"></slot>
    </template>
  </div>
</template>

<script setup lang="ts">
import rollingNumbers from './rolling-numbers/rolling-numbers.vue'
import { ref, inject, computed, watch, nextTick, onMounted } from 'vue'
import { useSwiperSlide } from 'swiper/vue/swiper-vue'
import { Parser, Player } from 'svga'
const cdnPrefix = CDN_PREFIX_FOCUS_RENDER
import gsap from 'gsap'
const swiperSlide = useSwiperSlide()
const props = defineProps<{
  page9Data: {
    planTimes: string
    donationTimes: string
    maxPlanTimes: string
    maxPlanName: string
  }
}>()
const canShow = inject('canShow')
const { planTimes, donationTimes, maxPlanName, maxPlanTimes } = props.page9Data || {}

const planStr = computed(() => {
  if (maxPlanName === '存工资') {
    return '让自己的小金库源源不断的增值'
  }
  if (maxPlanName === '还房贷/车贷') {
    return '为自己的生活添砖加瓦'
  }
  if (maxPlanName === '交房租') {
    return '让自己的生活有条不紊'
  }
  if (maxPlanName === '孝敬父母') {
    return '为父母的生活多一份经济保障'
  }
  if (maxPlanName === '其他') {
    return '漫漫人生路，有规律的资产管理让生活更轻松'
  }
})
const pushPageListId = inject('pushPageListId', (id: any) => {})
pushPageListId && pushPageListId('9')

const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})
watch(isCurrentPage, (val) => {
  console.log('🚀 ~ file: page-9.vue ~ line 73 ~ watch ~ val', val)
  if (val) {
    console.log('切换到第9页拉，加载动画')
    nextTick(() => {
      const list = document.querySelectorAll('.page-9 .section')
      for (let i = 0; i < list.length; i++) {
        const dom = list[i]
        gsap.fromTo(
          dom,
          {
            opacity: 0,
            duration: 0,
            delay: 0,
            y: 10,
          },
          {
            x: 0,
            y: 0,
            opacity: 1,
            duration: 0.4,
            delay: 0.8 * (i + 1),
            ease: 'ease-out',
            onComplete: () => {},
          }
        )
      }
    })
  }
})
</script>

<style lang="scss" scoped>
.page-9 {
  background: linear-gradient(180deg, #fffcff 0%, #f1d9ff 100%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 40px;
  line-height: 1;
  font-size: 28px;

  .section {
    opacity: 0;
    color: #fff;
    text-align: left;
    width: 100%;
    font-size: 28px;
    color: #a578b2;
    line-height: 2;
    padding-left: 70px;
    z-index: 2;
    margin-top: 50px;
    .tip {
      font-size: 38px;
      font-weight: bold;
      span {
        color: #bd77d0;
        font-size: 52px;
      }
    }
    &:nth-child(2) {
      margin-top: 38px;
    }
    // &:nth-child(3) {
    //   margin-top: 30px;
    // }
    &:nth-child(3),
    &:nth-child(5) {
      text-align: right;
      padding-right: 70px;
      margin-top: 30px;
      line-height: 100px;
    }
    &:nth-child(4) {
      margin-top: 50px;
    }
    &:nth-child(4) {
      padding: 0;
    }

    &.cover {
      width: 100%;
      padding: 0;
      margin-top: 50px;
      height: 100%;
      flex: auto;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex-direction: column;
      img {
        width: 100%;
      }
    }
  }

  &.nodotaion {
    background: linear-gradient(180deg, #fffcff 0%, #84d6ff 100%);
    .section {
      color: #687ca9;
      .tip {
        span {
          color: #5485d5;
        }
      }
    }
  }
}
</style>
