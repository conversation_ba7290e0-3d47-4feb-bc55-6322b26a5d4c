<template>
  <div class="page-2">
    <template v-if="canShow">
      <div class="section">
        <p class="name">Hi~ {{ userName }}</p>
        <p class="bignum s_1">{{ _accountCreateTime }}</p>
      </div>
      <div class="section">
        <!-- 已推数 -->
        <template v-if="userIndexId">
          <p v-if="hasTrans">
            你成为微众银行App第
            <span class="num fs_52">{{ userIndexId }}</span> 位用户
          </p>
          <div v-else>
            <p>你成为微众银行App第</p>
            <p>
              <span class="noTrans big-num fs_52">{{ userIndexId }}</span> 位用户
            </p>
          </div>
          <p>如今，已有3.4亿的微众银行用户与你同行</p>
        </template>
        <p v-if="!hasTrans && !userIndexId && !hasAccount">欢迎来到微众时光机</p>
        <p v-if="!hasTrans && !userIndexId && hasAccount">欢迎你成为App用户中的一员</p>
        <p v-if="!hasTrans && !userIndexId && hasAccount">
          如今，已有<span class="fs_52 big-num">3.4</span>亿的微众银行用户与你同行
        </p>
        <p v-if="!hasTrans && !userIndexId && !hasAccount">
          如今，已有<span class="fs_52 big-num">3.4</span>亿个人客户选择微众银行
        </p>

        <template v-if="hasTrans && wezhongTagStr">
          <p>{{ wezhongTagStr }}</p>
          <p>
            你是微众的 <span style="color: #fdffb1">{{ wezhongTag }}</span>
          </p>
        </template>
      </div>
      <div class="section">
        <template v-if="hasTrans">
          <p>
            在微众的第 <span class="num fs_52">{{ accountDiffDays }}</span> 天
          </p>
          <p class="big">留下了许多<span>特殊的“第一次”</span></p>
        </template>
        <template v-if="!hasTrans && accountDiffDays"
          ><p>与微众银行App相遇的第 {{ accountDiffDays }} 天,我们的交集虽不多</p>
          <p>但每一次都值得珍惜</p>
        </template>
        <template v-if="!hasTrans && hasAccount">
          <p>一起看看同行的微众银行客户都发生了什么吧~</p>
          <p v-if="!userIndexId">跟随时光机，即刻出发！</p>
        </template>
        <template v-if="!hasAccount">
          <p>一起去看看这群梦想同路人都发生了什么吧~</p>
          <p v-if="!userIndexId">跟随时光机，即刻出发！</p>
        </template>
      </div>
      <div class="section cake">
        <img src="../img/2/cake.png" alt="" class="cake" />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import 'animate.css'

import { inject, computed, watch, nextTick, toRefs } from 'vue'
import { dayjs } from '../../../utils'
import { useSwiperSlide } from 'swiper/vue/swiper-vue'
import { sectionAnimate } from '../utils/sectionAnimate'
import { focusCore } from '@focus/render'
const swiperSlide = useSwiperSlide()
const props = defineProps<{
  page2Data: {
    userName: string
    accountCreateTime: string
    userIndexId: string
    wezhongTag: string
    wezhongTagStr: string
    hasTrans: boolean
  }
}>()

const canShow = inject('canShow')
const pushPageListId = inject('pushPageListId', (id: any) => {})
pushPageListId && pushPageListId('2')
watch(canShow, (val) => {
  console.log('🚀 ~ file: page-2.vue ~ line 45 ~ watch ~ val', val)
})
const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})

const hasAccount = inject('hasAccount')

watch(isCurrentPage, (val) => {
  if (val) {
    console.log('切换到第二页拉，加载动画')
    nextTick(() => {
      sectionAnimate('2')
    })
  }
})

const { userName, accountCreateTime, userIndexId, wezhongTag, wezhongTagStr, hasTrans } = toRefs(props.page2Data) || {}

const accountDiffDays = computed(() => {
  return accountCreateTime?.value && dayjs(dayjs()).diff(accountCreateTime.value, 'day')
})
const _accountCreateTime = computed(() => {
  return accountCreateTime?.value && dayjs(accountCreateTime?.value).format('YYYY.MM.DD')
})
</script>

<style lang="scss" scoped>
.page-2 {
  background: linear-gradient(181deg, #5d9bff 0%, #abceff 66%, #c0e4ff 100%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 28px;
  line-height: 1;
  .section {
    color: #fff;
    text-align: left;
    width: 100%;
    padding-left: 70px;
    z-index: 22;
    .name {
      font-size: 48px;
    }
    .s_1 {
      margin-top: 22px;
      font-size: 62px;
    }
    span {
      font-size: 38px;
      font-weight: bold;
      display: inline-block;
      transform: translateY(4px);
      &.noTrans {
        color: #fdffb1;
        font-size: 52px;
      }
    }
    &.cake {
      padding-left: 0;
      flex: auto;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex-direction: column;
      margin-top: 0;
      z-index: 1;
    }
    &:nth-child(1) {
      font-size: 62px;
      font-weight: bold;
    }
    &:nth-child(3) {
      p.big {
        font-size: 38px;
        font-weight: bold;
        span {
          color: #fdffb1;
          transform: translateY(0);
        }
      }
    }
  }
  .cake {
  }
}
.fs_52 {
  font-size: 52px !important;
}
</style>
