<template>
  <div class="page-8">
    <template v-if="canShow">
      <div class="section">
        <p>
          累计获积分 <span class="big-num">{{ getPointFormate }}</span>
        </p>
        <p>
          累计兑换积分 <span class="big-num">{{ useUpPointFormate }} </span>
        </p>
        <p v-if="Number(pointSaveMoney) > 0" class="tip">
          为你节省 <span class="big-num">{{ _pointSaveMoney }}</span
          >元
        </p>
      </div>
      <div class="section">
        <template v-if="Number(pointSaveMoney) >= 20 && Number(pointSaveMoney) < 100">
          <p>仔细算算才知道</p>
          <p>微众已经请我喝了几杯咖啡</p>
        </template>
        <template v-if="Number(pointSaveMoney) >= 100 && Number(pointSaveMoney) < 1000">
          <p>仔细算算才知道</p>
          <p>微众已经请我吃了几顿大餐</p>
        </template>
        <template v-if="Number(pointSaveMoney) >= 1000">
          <p>仔细算算才知道</p>
          <p>微众已经送了我几次周边游</p>
        </template>
        <template v-if="Number(pointSaveMoney) < 20">
          <p>积分活动不断，海量福利随享</p>
          <p class="p_10">悄悄告诉你，小积分大福利快去兑换积分吧</p>
        </template>
      </div>
      <div class="section cover">
        <img src="../img/8/bg.png" alt="" />
      </div>
    </template>
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import rollingNumbers from './rolling-numbers/rolling-numbers.vue'
import { ref, inject, computed, watch, nextTick, onMounted } from 'vue'
import { useSwiperSlide } from 'swiper/vue/swiper-vue'
import { formatAmount, dayjs } from '../../../utils/index'

import gsap from 'gsap'
const swiperSlide = useSwiperSlide()
const props = defineProps<{
  page8Data: {
    getPoint: number
    useUpPoint: number
    pointSaveMoney: string
    _pointSaveMoney: string
    getPointFormate: string
    useUpPointFormate: string
  }
}>()
const canShow = inject('canShow')

const { getPoint, useUpPoint, pointSaveMoney, _pointSaveMoney, useUpPointFormate, getPointFormate } = props.page8Data
const pushPageListId = inject('pushPageListId', (id: any) => {})
pushPageListId && pushPageListId('8')
const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})
watch(isCurrentPage, (val) => {
  console.log('🚀 ~ file: page-8.vue ~ line 73 ~ watch ~ val', val)
  if (val) {
    console.log('切换到第8页拉，加载动画')
    nextTick(() => {
      const list = document.querySelectorAll('.page-8 .section')
      for (let i = 0; i < list.length; i++) {
        const dom = list[i]
        gsap.fromTo(
          dom,
          {
            opacity: 0,
            duration: 0,
            delay: 0,
            y: 10,
          },
          {
            x: 0,
            y: 0,
            opacity: 1,
            duration: 0.4,
            delay: 0.8 * (i + 1),
            ease: 'ease-out',
            onComplete: () => {},
          }
        )
      }
    })
  }
})
</script>

<style lang="scss" scoped>
.page-8 {
  background-image: linear-gradient(180deg, #faf7ff 0%, #e5d2fb 49%, #bab3f9 100%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 40px;
  line-height: 1;
  font-size: 28px;

  .section {
    opacity: 0;
    color: #fff;
    text-align: left;
    width: 100%;
    font-size: 28px;
    color: #a578b2;
    line-height: 1.5;
    padding-left: 70px;
    margin-top: 50px;
    span {
      font-size: 52px;
      font-weight: bold;
      color: #bd77d0;
    }

    &:nth-child(4) {
      margin-top: 70px;
    }
    p.tip {
      font-size: 38px;
      font-weight: bold;
    }
  }

  .cover {
    width: 100%;
    padding: 0;
    line-height: 0;
    height: 100%;
    flex: auto;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-direction: column;
    img {
      width: 100%;
    }
  }
}
.p_10 {
  padding-top: 10px;
}
</style>
