<template>
  <div class="tags">
    <div class="row" v-for="(lineItem, index) in msgs" :key="index">
      <div class="slide">
        <div
          class="item"
          v-for="(item, _index) in lineItem.slice(0, 2)"
          :key="item.id"
          :class="`${item.text === selectedText ? 'slected' : ''} before_${index}_${_index}`"
          @click="selectStr(item.text, `before_${index}_${_index}`)"
        >
          <img :src="item.icon" alt="" class="icon" />

          <p>{{ item.text }}</p>
          <div class="btn"></div>
        </div>
      </div>
      <div class="slide">
        <div
          class="item"
          v-for="(item, _index) in lineItem.slice(2, 4)"
          :key="item.id"
          :class="`${item.text === selectedText ? 'slected' : ''} after_${index}_${_index + 2}`"
          @click="selectStr(item.text, `after_${index}_${_index + 2}`)"
        >
          <img :src="item.icon" alt="" class="icon" />
          <p>{{ item.text }}</p>
          <div class="btn"></div>
        </div>
      </div>
    </div>
    <div class="btn-refresh" @click="refresh">
      <img class="icon" src="../../img/12/refresh.png" alt="" />
      <p>换一批</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import gsap from 'gsap'
import { ref, onMounted, defineEmits, inject, Ref } from 'vue'
import { focusServices, focusStore, focusCore } from '@focus/render'

const emit = defineEmits(['onSelcet'])
const icons = [
  require('../../img/12/icon_1.png'),
  require('../../img/12/icon_2.png'),
  require('../../img/12/icon_3.png'),
  require('../../img/12/icon_4.png'),
  require('../../img/12/icon_5.png'),
  require('../../img/12/icon_6.png'),
  require('../../img/12/icon_7.png'),
  require('../../img/12/icon_8.png'),
]
const selectedText = ref('')
let slideCtrl: any = []
const myword = inject<Ref<string>>('myword') || ref('')
function selectStr(str: string, className?: string) {
  console.log('🚀 ~ file: tags.vue ~ line 58 ~ selectStr ~ className', className)
  // const dom = document.querySelector(`.${className}`)
  // console.log('🚀 ~ file: tags.vue ~ line 60 ~ selectStr ~ dom', dom)
  if (selectedText.value === str) {
    selectedText.value = ''
    ctlAllSlides(true)
  } else {
    focusCore.clickLog('settag', { tag: str })
    selectedText.value = str
    ctlAllSlides(false, className)
  }
  myword.value = selectedText.value
}

function ctlAllSlides(start: boolean, className?: string) {
  // const parentDom = dom && dom.parent
  // console.log('🚀 ~ file: tags.vue ~ line 72 ~ ctlAllSlides ~ parentDom', parentDom)
  let percent = '0'

  // const dom: Element | any = document.querySelector(`.${className}`)
  // const parentDom = dom && dom.parentElement
  // console.log('🚀 ~ file: tags.vue ~ line 86 ~ slideCtrl.forEach ~ parentDom', parentDom)
  // const isBefore = Number(className?.indexOf('before'))>-1
  // if (dom && parentDom) {
  //   const domRect = dom.getBoundingClientRect()
  //   const parentDomRect = parentDom.getBoundingClientRect()
  //   const leftWidth = domRect.left
  //   const windowWidth = window.screen.width
  //   if(leftWidth<0 || windowWidth<leftWidth+domRect.width){
  //     //已经在屏幕外了
  //  const parentWidth = parentDomRect.width
  //   percent = (leftWidth / parentWidth).toFixed(2)
  //   }

  // }

  slideCtrl.forEach((i) => {
    // console.log('🚀 ~ file: tags.vue ~ line 73 ~ slideCtrl.forEach ~ i', i)
    if (start) {
      i && i.resume()
    } else {
      if (Number(percent)) {
        i.progress(percent)
      }
      i && i.pause()
    }

    // i.progress(0.5)
  })
}

function getStrs() {
  const msgs_1 = [
    '健康平安赚大钱',
    '早日排上大额存单+～',
    '在微众银行APP实现用利息生活！',
    '多多存钱，财富自由',
    '收益率直线飞升，冲破10个点',
    '存款多多，健康多多',
  ]
  const msgs_2 = [
    '1年内年收益赶超巴菲特',
    '成为微众资产的半壁江山',
    '活期+plus日日好收益',
    '理财崛起，基金起飞',
    '高端理财实现一个亿的小目标',
  ]

  const msgs_3 = [
    '何以解忧，唯有暴富',
    '顺势而为，厚德载富',
    '马无夜草不肥，人无活期+不富',
    '活期+在手，爆富无忧',
    '投资就是要做时间的朋友',
  ]
  const allStrs = [msgs_1, msgs_2, msgs_3]
  console.log('🚀 ~ file: tags.vue ~ line 43 ~ getStrs ~ allStrs', allStrs)
  const allResult: { id: number; text: string; icon: string }[][] = []
  const iconResult: string[][] = []
  const sortIcons = icons.sort(() => {
    if (Math.random() < 0.5) {
      return -1
    }
    return 1
  })
  allStrs.forEach((strs, index) => {
    const result: string[] = []
    iconResult.push([sortIcons[index * 2], sortIcons[index * 2 + 1]])
    strs.reduce((pre, cur, currentIndex, strs) => {
      console.log(
        '🚀 ~ file: tags.vue ~ line 42 ~ strs.reduce ~ pre,cur,currentIndex,strs',
        pre,
        cur,
        currentIndex,
        strs
      )
      const id = parseInt((Math.random() * strs.length).toString())
      console.log('🚀 ~ file: tags.vue ~ line 42 ~ strs.reduce ~ id', id)
      if (result.length < 2) {
        const target = strs[id]
        strs.splice(id, 1)
        result.push(target)
      }
      return cur
    })
    allResult.push(
      result.concat(result).map((i, _index) => {
        return {
          id: parseInt((Math.random() * 1000).toString()),
          text: i,
          icon: _index % 2 === 0 ? iconResult[index][0] : iconResult[index][1],
        }
      })
    )
  })

  return allResult
}

const msgs = ref<Array<{ id: number; text: string; icon: string }[]>>(getStrs())
console.log('🚀 ~ file: tags.vue ~ line 79 ~ msgs', msgs)

function refresh() {
  focusCore.clickLog('hyp')
  selectStr(selectedText.value)
  msgs.value = getStrs()
}

onMounted(() => {
  const slides = document.querySelectorAll('.tags .slide')
  for (let i = 0; i < slides.length; i++) {
    const dom = slides[i]
    slideCtrl[i] = gsap.to(dom, {
      x: '-100%',
      duration: 8,
      repeat: -1,
      ease: 'linear',
    })
  }
})
</script>

<style lang="scss" scoped>
.tags {
  width: 100%;
  overflow-x: hidden;
  position: relative;
  .row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: auto;
    margin-bottom: 70px;
    &:nth-child(2) {
      padding-left: 70px;
    }
    .slide {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: auto;
      flex: none;
    }
    .item {
      flex: none;
      color: #2f8df6;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 100px;
      padding-left: 50px;
      padding-right: 30px;
      font-size: 28px;
      font-weight: bold;
      line-height: 1;
      margin-right: 20px;
      border-radius: 30px 30px 30px 0;
      position: relative;
      &::before {
        width: 100%;
        height: 100%;
        content: '';
        background: #c3e1fc;
        border-radius: 30px 30px 30px 0;
        z-index: 2;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
      }
      &::after {
        width: 80px;
        height: 50px;
        content: '';
        background: #c3e1fc;

        position: absolute;
        left: 0;
        bottom: 0;
        transform: rotate(-45deg) scaleX(1.4) skewX(-35deg);
        border-radius: 0 0 0 30px;
        transform-origin: center;
        z-index: 1;
      }
      .icon {
        width: 54px;
        height: 54px;
        position: relative;
        z-index: 3;
        margin-right: 10px;
      }
      p {
        z-index: 3;
      }
      .btn {
        width: 35px;
        height: 35px;
        background: rgba(255, 255, 255, 0.59);
        border: 4px solid #ffffff;
        border-radius: 50%;
        margin-left: 50px;
        z-index: 3;
      }
      &.slected {
        color: #fff;
        &::before {
          background: linear-gradient(121deg, #00c4eb 0%, #4ea7ff 100%);
        }

        &::after {
          background: #00c4eb;
        }
        .btn {
          position: relative;
          transform: rotate(-45deg);
          background: #fff;

          &::before {
            content: '';
            width: 20px;
            height: 4px;
            background: #49a9fd;
            border: 4px;
            position: absolute;
            left: 4px;
            top: 15px;
          }
          &::after {
            content: '';
            width: 4px;
            height: 8px;
            background: #49a9fd;
            border: 4px;
            position: absolute;
            left: 4px;
            top: 7px;
          }
        }
      }
    }
  }
  .btn-refresh {
    font-size: 32px;
    font-weight: 600;
    color: #1283ff;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1.5;
    cursor: pointer;
    .icon {
      width: 40px;
      height: 32px;
      margin-right: 10px;
      transition: all 0.5s ease;
      transform-origin: center;
    }
    &:active {
      color: #1283ff;
      opacity: 1;
      .icon {
        transform: rotate(720deg);
      }
    }
  }
}
</style>
