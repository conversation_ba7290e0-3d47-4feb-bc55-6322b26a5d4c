<template>
  <div class="page-end">
    <div class="bg"></div>

    <template v-if="canShow">
      <div class="section">
        <div class="info">
          <div class="avator">
            <img :src="userAvator" alt="" />
          </div>
          <p>亲爱的 {{ userNickName }}</p>
        </div>
        <!-- 未开户 -->
        <template v-if="!hasAccount">
          <p>
            截止2022.11.01，有800万用户通过“活期+”收获在微众的第一桶金。搭上财富+航班，和梦想同路人们一起在微众银行App开启理财之路吧！
          </p>
          <img src="../img/end/cover2.png" alt="" class="cover2" />
        </template>

        <!-- 已开户 -->
        <template v-if="hasAccount">
          <p>
            过去已成回忆，错过的收益虽然不在，但机会仍在，未来收益可期。搭上财富+航班，和3.4亿人一起在微众银行App开启理财之路吧！
          </p>
          <img src="../img/end/cover.png" alt="" class="cover" />
        </template>
        <div class="btn" @click="clickJump">前往活期+看看</div>
        <div class="btn-share" @click="clickShare">分享微众时光机</div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, computed, watch, nextTick, onMounted } from 'vue'
import { useSwiperSlide } from 'swiper/vue/swiper-vue'
import { focusServices, focusCore } from '@focus/render'
const { mgmService, userService, jumpService } = focusServices
import { sectionAnimate } from '../utils/sectionAnimate'
import { UrlParser } from '../../utils/url'

const swiperSlide = useSwiperSlide()
const props = defineProps<{
  endData: {
    userAvator: string
    userNickName: string
  }
}>()
const canShow = inject('canShow')
const { userAvator, userNickName } = props.endData || {}
const showNext = inject('showNext', ref(true))
const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})

const hasAccount = inject('hasAccount')

watch(isCurrentPage, (val) => {
  if (val) {
    console.log('切换到第end页拉，加载动画')
    showNext.value = false
    nextTick(() => {
      setTimeout(() => {
        showNext.value = false
      }, 200)
      sectionAnimate('end')
    })
  }
})
function clickJump() {
  focusCore.clickLog('go_hqj')

  jumpService.jump({
    path: '/monetaryFund/MonetaryFundPlusDetailScene',
  })
}
function clickShare() {
  focusCore.clickLog('share_default')

  const link = location.origin + location.pathname + '?useTransparentNavBar=1'

  mgmService.clickShare(`family-share`, {
    shareTitle: '微众时空之旅邀约，即刻启程开启时光之旅',
    shareDesc: '邀你一起来参加',
    shareImage: require('../img/end/share.png'),
    shareUrl: link,
  })
}
</script>

<style lang="scss" scoped>
.page-end {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 40px;
  line-height: 1;
  background: linear-gradient(180deg, #468bff 0%, #8bcaff 27%, #c9e8ff 55%, #f2faff 76%, #eaf9ff 100%);
  height: 100%;
  .bg {
    position: absolute;
    top: 0;
    left: -140px;
    z-index: 1;
    background: rgba(255, 255, 255, 0.5);
    height: 500px;
    width: 500px;
    border-radius: 50%;
    transform: scale(2);
    opacity: 0.2;
    &::before {
      content: '';
      width: 400px;
      height: 400px;
      background: rgba(255, 255, 255, 0.5);
      position: absolute;
      right: -300px;
      top: -150px;
      border-radius: 50%;
    }
  }
  .section {
    color: #fff;
    font-size: 30px;
    text-align: left;
    width: 100%;
    box-sizing: border-box;
    position: relative;
    z-index: 222;
    p {
      line-height: 1.5;
      margin-top: 20px;
      padding-left: 60px;
      padding-right: 60px;
    }
    .cover {
      width: 750px;
    }
    .info {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-size: 42px;
      font-weight: bold;
      width: 100%;
      padding-left: 60px;
      p {
        margin: 0;
        padding: 0;
      }
      .avator {
        width: 73px;
        height: 72px;
        line-height: 0;
        border: 4px solid #fff;
        margin-right: 20px;
        border-radius: 50%;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .btn {
    margin: 0 auto;
    margin-top: 28px;
    width: 620px;
    height: 118px;
    background: linear-gradient(125deg, #fdcd88 0%, #fb6a6a 100%);
    box-shadow: inset 0px 3px 0px 0px rgba(255, 255, 255, 0.62);
    border-radius: 59px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
    font-weight: bold;
    color: #fff;
  }
  .btn-share {
    text-align: center;
    font-size: 32px;
    font-weight: bold;
    color: #8ba3d4;
    line-height: 28px;
    padding: 30px;
    margin: 0 auto;
    margin-top: 20px;
  }
}
</style>
