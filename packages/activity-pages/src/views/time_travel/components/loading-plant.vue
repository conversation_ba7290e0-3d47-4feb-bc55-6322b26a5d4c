<template>
  <div class="loading_plant">
    <div class="door">
      <img src="../img/1/loading_plant.png" alt="" class="plant" />
    </div>
    <p>正在进入时光机之旅...</p>
  </div>
</template>

<script setup lang="ts"></script>

<style lang="scss" scoped>
.loading_plant {
  position: fixed;
  z-index: 999999;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  background: linear-gradient(to bottom, #bcdcfe, #a3cffe);
  .door {
    width: 272px;
    height: 346px;
    z-index: 0;
    position: relative;
    background: url('../img/1/door2.png') no-repeat;
    background-size: 100% 100%;
    margin-top: -200px;
    .plant {
      width: 260px;
      height: 130px;
      animation: plantflow 1s ease-in 0s infinite alternate;
    }
  }
  p {
    font-size: 32px;
    color: #5f6ea6;
    letter-spacing: 0.3px;
    font-weight: 400;
    margin-top: 30px;
  }
}

@keyframes plantflow {
  from {
    margin-top: 60px;
  }
  to {
    margin-top: 50px;
  }
}
</style>
