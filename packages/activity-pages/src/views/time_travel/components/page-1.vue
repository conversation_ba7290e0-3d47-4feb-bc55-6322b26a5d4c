<template>
  <div class="page-1" v-if="showPage">
    <img src="../img/1/title.png" alt="" class="title" />
    <div class="center">
      <div class="door"></div>
      <img src="../img/1/ship.png" alt="" class="ship" />
    </div>
    <div class="bottom">
      <div class="btn" @click.stop="clickStart">
        <p class="text">即刻启程</p>
      </div>
      <template v-if="!isInApp">
        <div class="btn_go" @click="goApp">打开微众银行App <img src="../img/1/icon_arrow.png" alt="" /></div>
      </template>
      <template v-if="isInApp">
        <div class="tip protocal" :class="{ selected: selectedProtocal, 'click-no-selected': clickOnNotSelected }">
          <div class="radio" @click="clickAgree"></div>
          同意授权
          <div class="pdf" @click="clickPdf">《微众时光机服务及授权协议》</div>
        </div>
        <p class="tip">数据截止至 {{ lastDataTime }}</p>
      </template>

      <img src="../img/1/plant.png" alt="" class="plant" />
    </div>
  </div>
</template>

<script setup lang="ts">
import gsap from 'gsap'
import { onMounted, ref, inject, watch, toRefs, computed } from 'vue'
import { focusServices, focusStore, focusCore } from '@focus/render'
const { kvService, jumpService } = focusServices
const { userStore } = focusStore.stores
const setCanShow = inject('setCanShow', () => {})
const px2vw = function (px: number) {
  const viewPort = 750
  const vw = ((Number(px) / viewPort) * 100).toFixed(3)
  return `${vw}vw`
}
const kvKey = 'time_travel'
let doorTween: any = ''
const showPage = ref(true)
const selectedProtocal = ref(false)
const clickOnNotSelected = ref(false)
const isTraveled = inject('isTraveled', ref(false))
const lastDataTime = inject('lastDataTime')
const { hasAccount } = toRefs(userStore)
const kvIsReady = inject('kvIsReady')

const isInApp = computed(() => {
  return focusCore.env.isInApp
})

function clickPdf() {
  // https://dbd.test.webankwealthcdn.net/wm-resm/hj/utils/pdfjs2/web/viewer.html?file=https%3A%2F%2Fdbd.test.webankwealthcdn.net%2Fwm-resm%2Fhjupload%2Fcommon%2Fshiguangji.pdf
  let viewer = 'https://dbd.webankwealthcdn.net/wm-resm/hj/utils/pdfjs2/web/viewer.html?file='
  let pdf =
    'https://dbd.webankwealthcdn.net/wm-resm/hjupload/common/%E6%B7%B1%E5%9C%B3%E5%89%8D%E6%B5%B7%E5%BE%AE%E4%BC%97%E9%93%B6%E8%A1%8CAPP%E9%9A%90%E7%A7%81%E6%94%BF%E7%AD%96%E2%80%94%E5%BE%AE%E4%BC%97%E6%97%B6%E5%85%89%E6%9C%BA%E8%A1%A5%E5%85%85%E6%8E%88%E6%9D%83%E8%AF%B4%E6%98%8E%E4%B9%A6V1.0.pdf'

  if (BUILD_TEST) {
    viewer = 'https://dbd.test.webankwealthcdn.net/wm-resm/hj/utils/pdfjs2/web/viewer.html?file='
    pdf =
      'https://dbd.test.webankwealthcdn.net/wm-resm/hjupload/common/%E6%B7%B1%E5%9C%B3%E5%89%8D%E6%B5%B7%E5%BE%AE%E4%BC%97%E9%93%B6%E8%A1%8CAPP%E9%9A%90%E7%A7%81%E6%94%BF%E7%AD%96%E2%80%94%E5%BE%AE%E4%BC%97%E6%97%B6%E5%85%89%E6%9C%BA%E8%A1%A5%E5%85%85%E6%8E%88%E6%9D%83%E8%AF%B4%E6%98%8E%E4%B9%A6V1.0.pdf'
  }
  const link = viewer + encodeURIComponent(pdf)
  // location.href = link
  jumpService.jumpNewWebview(link)
}

function checkAgree() {
  kvService.getUerKv([kvKey]).then((res) => {
    console.log('🚀 ~ file: page-1.vue ~ line 53 ~ kvService.getUerKv ~ res', res)
    const { time_travel = {} } = res || {}
    if (time_travel?.agree) {
      selectedProtocal.value = true
      isTraveled.value = true
    }
    kvIsReady && kvIsReady()
  })
}

watch(hasAccount, (val) => {
  console.log('🚀 ~ userStore.hasAccount', val)
  if (focusCore.env.isInApp) {
    checkAgree()
  }
})

function clickAgree() {
  selectedProtocal.value = !selectedProtocal.value
  clickOnNotSelected.value = false
}

function goApp() {
  jumpService.commonUse.appHome()
}

function clickStart() {
  if (focusCore.env.isInApp && !selectedProtocal.value) {
    clickOnNotSelected.value = true
    return false
  }

  focusCore.clickLog('ljqc')

  gsap.to('.door', {
    scale: 2,
    duration: 0.3,
    ease: 'ease-out',
  })
  gsap.to('.ship', {
    scale: 0.5,
    opacity: '0.5',
    duration: 0.3,
    ease: 'ease-out',
  })
  gsap.to('.page-1', {
    scale: 1.2,
    opacity: '0.1',
    duration: 0.1,
    delay: 0.3,
    onComplete: () => {
      setTimeout(() => {
        showPage.value = false
        setCanShow && setCanShow()
      }, 400)
    },
  })
}
</script>

<style lang="scss" scoped>
.page-1 {
  width: 100%;
  height: 100%;
  background: url('../img/1/BG.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 2222;
  .title {
    width: 468px;
    height: 289px;
    margin: 0 auto;
    margin-top: 50px;
  }
  .center {
    position: relative;
    width: 100%;
    z-index: 2222;
    flex: auto;
    padding-top: 20px;
    .door {
      width: 280px;
      height: 400px;
      background: #fff;
      border-radius: 20px 20px 0 0;
      box-shadow: 0 0px 80px 50px rgba($color: #fff, $alpha: 1);
      animation: blink 1s ease 0s infinite alternate;
      z-index: 3;
      margin: 0 auto;
    }
    .ship {
      position: absolute;
      z-index: 4;
      left: 50%;
      top: 300px;
      transform: translateX(-50%);
      width: 684px;
      height: 391px;
      animation: flow 1s ease-in 0s infinite alternate;
    }
  }

  .bottom {
    flex: auto;
    position: relative;
    z-index: 2;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-direction: column;
    padding-bottom: 110px;
    .plant {
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1;
    }
    .tip {
      font-size: 22px;
      text-align: center;
      color: #6b6db1;
      line-height: 2;
      z-index: 2;
    }
    .protocal {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      width: fit-content;
      margin: 0 auto;
      z-index: 2;

      &::before {
        content: '';
        font-size: 30px;
        color: #fff;
        background: url('../img/1/tip.png') no-repeat center;
        background-size: 100%;
        border-radius: 10px;
        height: 78px;
        width: 120px;
        text-align: center;
        line-height: 60px;
        position: absolute;
        left: -48px;
        top: -78px;
        display: none;
      }

      .radio {
        width: 28px;
        height: 28px;
        border: 5px solid #4846da;
        border-radius: 50%;
        margin-right: 10px;
        position: relative;
        transform: rotate(-45deg);
      }
      .pdf {
        font-size: 22px;
        font-weight: bold;
        color: #6665de;
      }
      &.click-no-selected {
        &::before {
          display: block;
        }
      }
      &.selected {
        &::before {
          display: none;
        }
        .radio {
          background: #4846da;
          &::before {
            content: '';
            width: 15px;
            height: 4px;
            background: #fff;
            border: 4px;
            position: absolute;
            left: 4px;
            top: 9px;
          }
          &::after {
            content: '';
            width: 3px;
            height: 8px;
            background: #fff;
            border: 4px;
            position: absolute;
            left: 4px;
            top: 3px;
          }
        }
      }
    }
    .btn {
      width: 572px;
      height: 148px;
      background: linear-gradient(131deg, #e8fcff 0%, #f7edff 100%);
      border-radius: 74px;
      border: 2px solid #ffffff;
      line-height: 148px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 35px;
      position: relative;
      overflow: hidden;
      z-index: 2;
      cursor: pointer;
      p {
        line-height: 1.5;
      }
      :active {
        opacity: 0.7;
      }
      &::before {
        content: '';
        width: 60px;
        height: 170px;
        background: rgba($color: #fff, $alpha: 0.4);
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%) rotate(20deg);
        z-index: 2;
        animation: flink 2s ease-in 0s infinite reverse;
      }
      .text {
        font-size: 50px;
        font-weight: 600;
        color: #3d55a0;
        line-height: 1.5;
        letter-spacing: 3px;
        background: linear-gradient(119deg, #6a69ff 0%, #00adff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}
.btn_go {
  font-size: 32px;
  color: #5150a1;
  letter-spacing: 1.62px;
  text-align: center;
  line-height: 38px;
  font-weight: 600;
  height: 40px;
  width: 100%;
  z-index: 22;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 16px;
    height: 26px;
    margin-left: 10px;
  }
}

@keyframes blink {
  from {
    box-shadow: 0 0px 80px 40px rgba($color: #fff, $alpha: 1);
  }
  to {
    box-shadow: 0 0px 80px 60px rgba($color: #fff, $alpha: 1);
  }
}

@keyframes flow {
  from {
    margin-top: -120px;
  }
  to {
    margin-top: -100px;
  }
}

@keyframes flink {
  from {
    left: 0;
  }
  from {
    left: 570px;
  }
}
</style>
