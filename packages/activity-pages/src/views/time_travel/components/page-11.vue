<template>
  <div class="page-11" :class="`level_${userLevelNum}`">
    <div class="bg"></div>
    <template v-if="canShow">
      <div class="section">
        <img :src="cardImg" alt="" />
      </div>
      <div class="section">
        <p class="tip bignum">{{ equityTime }}</p>
        <p>你成为了{{ equityName }}会员</p>
        <p>伴随财富+开启新征程</p>
      </div>
      <div class="section">
        <img :src="equityImg" alt="" />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, computed, watch, nextTick, onMounted } from 'vue'
import { useSwiperSlide } from 'swiper/vue/swiper-vue'
import gsap from 'gsap'
const swiperSlide = useSwiperSlide()
const props = defineProps<{
  page11Data: {
    userLevelNum: string
    equityTime: string
  }
}>()
const canShow = inject('canShow')
const showNext = inject('showNext', ref(true))

const { userLevelNum, equityTime } = props.page11Data || {}
const pushPageListId = inject('pushPageListId', (id: any) => {})
pushPageListId && pushPageListId('11')
const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})

const equityImg = computed(() => {
  return require(`../img/11/${userLevelNum}-1.png`)
})

const cardImg = computed(() => {
  return require(`../img/11/${userLevelNum}.png`)
})

const equityName = computed(() => {
  const equitys = ['白银', '黄金', '铂金', '钻石']
  return equitys[Number(userLevelNum) - 1]
})

watch(isCurrentPage, (val) => {
  if (val) {
    showNext.value = true
    nextTick(() => {
      const list = document.querySelectorAll('.page-11 .section')
      for (let i = 0; i < list.length; i++) {
        const dom = list[i]
        gsap.fromTo(
          dom,
          {
            opacity: 0,
            duration: 0,
            delay: 0,
            y: 10,
          },
          {
            x: 0,
            y: 0,
            opacity: 1,
            duration: 0.4,
            delay: 0.8 * (i + 1),
            ease: 'ease-out',
            onComplete: () => {},
          }
        )
      }
    })
  }
})
</script>

<style lang="scss" scoped>
.page-11 {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 40px;
  line-height: 1;
  font-size: 28px;
  .bg {
    width: 570px;
    height: 570px;
    position: absolute;
    right: -200px;
    top: 80px;
    border-radius: 50%;
    &::after {
      content: '';
      position: absolute;
      left: -30px;
      bottom: 20px;
      width: 175px;
      height: 175px;
      border-radius: 50%;
      background: linear-gradient(301deg, #cbd9f6 0%, #b6cbf4 100%);
    }
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      width: 570px;
      height: 570px;
      background-image: linear-gradient(160deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.41) 100%);
      border-radius: 50%;
      z-index: 1;
    }
  }
  .section {
    opacity: 0;
    text-align: left;
    width: 100%;
    font-size: 28px;
    line-height: 2;
    width: 600px;
    z-index: 2;
    margin-top: 50px;
    .tip {
      font-size: 52px;
      font-weight: bold;
    }
    &:nth-child(2) {
      width: 683px;
      height: 560px;
      align-self: flex-end;
    }
    &:nth-child(4) {
      margin-top: 80px;
    }
  }
  &.level_1 {
    background: linear-gradient(180deg, #94b1ee 0%, #f1f6ff 57%, #fafaff 100%);
    color: #526fa8;
  }
  &.level_2 {
    background-image: linear-gradient(141deg, #f0c497 0%, #faf1e6 54%, #fcf6f6 100%);
    color: #a27c5b;
    .bg {
      &::after {
        background: linear-gradient(323deg, #f6e3d0 0%, #f0c497 100%);
      }
    }
  }
  &.level_3 {
    background-image: linear-gradient(141deg, #678cff 0%, #e5ecff 48%, #faf4fb 100%);
    color: #246ae8;
    .bg {
      &::after {
        background: linear-gradient(301deg, #c2d1ff 0%, #aabefe 100%);
      }
    }
  }
  &.level_4 {
    background: linear-gradient(180deg, #2c365c 0%, #515580 59%, #66729f 100%);
    color: #c5cbe2;
    .bg {
      &::after {
        background: linear-gradient(301deg, #c2d1ff 0%, #aabefe 100%);
      }
    }
  }
}
</style>
