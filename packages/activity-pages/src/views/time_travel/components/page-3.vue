<template>
  <div class="page-3">
    <template v-if="canShow">
      <div class="section s_1">
        <div class="info">
          <p>第一次向<span> 微众银行App </span>转入</p>
          <p>
            <span class="money">{{ firstTransMoney }}</span> <b style="color: #056be5">元</b>
          </p>
        </div>
        <div class="time bignum">{{ firstTransTime }}</div>
      </div>
      <div class="section s_2">
        <div class="info">
          <p>
            第一次 <span>买入{{ firstProdType }}</span>
          </p>
          <p>
            {{ firstProdName }}
          </p>
        </div>
        <div class="time bignum">{{ firstProdTime }}</div>
      </div>
      <div class="section s_3">
        <div class="info hastrans" v-if="firstBuyTime">
          <img src="../img/3/icon_3.png" alt="" />
          <div>
            <p>第一次 <span>微众卡消费</span></p>
            <p>正式开启买买买之路</p>
          </div>
        </div>
        <div class="info" v-else :style="{ display: !firstBuyTime ? 'block' : 'flex' }">
          <p>第一次获得 <span>投资收益</span></p>
          <p>
            <span class="money">{{ firstEarningMoney }}</span
            >元
          </p>
        </div>
        <div class="time bignum">{{ firstBuyTime ? firstBuyTime : firstEarningTime }}</div>
      </div>
      <div class="section s_4">
        <div class="info" v-if="firstPointTime">
          <div>
            <p>第一次 <span>获得积分</span></p>
            <p>省钱密码已被你掌握</p>
          </div>
          <img src="../img/3/icon_4.png" alt="" />
        </div>
        <div class="info" v-else>
          <div>
            <p>第一次成为 <span>微众会员</span></p>
            <p>
              <!-- {{ firstViewProdName }} -->
              开启尊贵会员权益之旅
            </p>
          </div>
        </div>
        <div class="time bignum">{{ firstPointTime ? firstPointTime : firstViewProdTime }}</div>
      </div>
    </template>
    <img class="cover" src="../img/3/page-bg.png" />
  </div>
</template>

<script setup lang="ts">
import { ref, Ref, inject, computed, watch, nextTick } from 'vue'
import { useSwiperSlide } from 'swiper/vue/swiper-vue'
import { sectionAnimate } from '../utils/sectionAnimate'

import gsap from 'gsap'
const swiperSlide = useSwiperSlide()
const props = defineProps<{
  page3Data: {
    firstTransTime: string
    firstProdTime: string
    firstBuyTime: string
    firstPointTime: string
    firstProdName: string
    firstTransMoney: string
    firstProdType: string
    firstEarningTime: string
    firstViewProdTime: string
  }
}>()
const canShow = inject<Ref<Boolean>>('canShow', ref(false))
const {
  firstTransTime,
  firstProdTime,
  firstBuyTime,
  firstPointTime,
  firstProdName,
  firstTransMoney,
  firstProdType,
  firstEarningTime,
  firstViewProdTime,
  firstEarningMoney,
  firstViewProdName,
} = props.page3Data || {}

const isCurrentPage = computed(() => {
  return canShow.value && swiperSlide && swiperSlide.value.isActive
})

const pushPageListId = inject('pushPageListId', (id: any) => {})
pushPageListId && pushPageListId('3')

watch(isCurrentPage, (val) => {
  console.log('🚀 ~ file: page-3.vue ~ line 84 ~ watch ~ val', val)
  if (val) {
    console.log('切换到第三页拉，加载动画')
    nextTick(() => {
      sectionAnimate(3)
      // const list = document.querySelectorAll('.page-3 .section')

      // for (let i = 0; i < list.length; i++) {
      //   const dom = list[i]

      //   gsap.fromTo(
      //     dom,
      //     {
      //       y: 10,
      //       opacity: 0,
      //       duration: 0,
      //       delay: 0,
      //     },
      //     {
      //       y: 0,
      //       opacity: 1,
      //       duration: 0.4,
      //       delay: 0.4 * (i + 1),
      //       onComplete: () => {},
      //     }
      //   )
      // }
    })
  }
})
</script>

<style lang="scss" scoped>
.page-3 {
  background: linear-gradient(180deg, #71cdff, #fbfdff);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  font-size: 40px;
  line-height: 1;
  .section {
    text-align: left;
    margin-top: 80px;
    position: relative;
    opacity: 0;
    margin-left: -40px;
    z-index: 222;
    &:nth-child(1),
    &:nth-child(3) {
      margin-left: 80px;
    }
    .info {
      width: 607px;
      height: 234px;
      color: #5e74ba;
      font-size: 28px;
      position: relative;
      z-index: 2;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 45px 55px;
    }
    .time {
      position: absolute;
      font-size: 32px;
      color: #fff;
      top: -45px;
      width: 500px;
      height: 120px;
      text-align: left;
      z-index: 1;
      padding-left: 40px;
      padding-top: 10px;
      &::after {
        width: 100%;
        height: 150px;
        content: '';
        position: absolute;
        background: linear-gradient(to right, #4a87ff, #58bdff);
        border-radius: 40px;
        z-index: -1;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        transform: rotate(2deg);
      }
    }
    .money {
      font-size: 48px;
      color: #056be5;
      line-height: 48px;
      font-weight: bold;
    }
    p:first-child {
      color: #3d55a0;
      font-size: 28px;
      font-weight: bold;
      span {
        font-size: 40px;
      }
    }
    p:nth-child(2) {
      margin-top: 30px;
    }
    &.s_1 {
      margin-top: 100px;
      .info {
        background-image: url('../img/3/bg_right.png');
      }
      .time {
        right: 20px;
      }
    }
    &.s_2 {
      .info {
        background-image: url('../img/3/bg_left.png');
        padding-top: 55px;
      }
      .time {
        left: 10px;
        width: 550px;
      }
    }
    &.s_3 {
      .info {
        background-image: url('../img/3/bg_right.png');
        display: flex;
        align-items: center;
        justify-content: flex-start;
        // transform: translateY(10px);
        &.hastrans {
          padding-top: 25px;
        }
        img {
          width: 148px;
          height: 103px;
          margin-right: 25px;
        }
      }
      .time {
        right: 20px;
      }
    }
    &.s_4 {
      .info {
        background-image: url('../img/3/bg_left.png');
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 35px;

        img {
          width: 160px;
          height: 109px;
        }
      }
      .time {
        left: 10px;
        width: 550px;
      }
    }
  }
  .cover {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
  }
}
</style>
