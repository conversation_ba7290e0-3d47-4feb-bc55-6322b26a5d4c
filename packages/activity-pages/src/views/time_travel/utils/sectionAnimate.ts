import gsap from 'gsap'

export function sectionAnimate(pageClassIndex: string) {
  const className = `.page-${pageClassIndex}`
  const list = document.querySelectorAll(`${className} .section`)
  for (let i = 0; i < list.length; i++) {
    const dom = list[i]
    gsap.fromTo(
      dom,
      {
        opacity: 0,
        duration: 0,
        delay: 0,
        y: 10,
      },
      {
        x: 0,
        y: 0,
        opacity: 1,
        duration: 0.4,
        delay: 0.8 * (i + 1),
        ease: 'ease-out',
        onComplete: () => {},
      }
    )
  }
}
