<script setup lang="ts">
import loadingPlant from './components/loading-plant.vue'

import page1 from './components/page-1.vue'
import page1_1 from './components/page-1_1.vue'
import page2 from './components/page-2.vue'
import page2_1 from './components/page-2_1.vue'
import page3 from './components/page-3.vue'
import page4 from './components/page-4.vue'
import page4_2 from './components/page-4_2.vue'
import page5 from './components/page-5.vue'
import page5_2 from './components/page-5_2.vue'
import page6 from './components/page-6.vue'
import page7 from './components/page-7.vue'
import page8 from './components/page-8.vue'
import page9 from './components/page-9.vue'
import page10 from './components/page-10.vue'
import page11 from './components/page-11.vue'
import page12 from './components/page-12.vue'
import page13 from './components/page-13.vue'
import page8_1 from './components/page-8_1.vue'
import page9_1 from './components/page-9_1.vue'
import endNodata from './components/endNodata.vue'
import { focusServices, focusCore, focusStore } from '@focus/render'
const { kvService, dynimicDataService, userService } = focusServices
import { ref, provide, computed, onMounted, onBeforeMount, watch, toRaw, nextTick, toRefs } from 'vue'
import gsap from 'gsap'
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue'
import { EffectFade, Controller } from 'swiper'
import BtnNext from './components/btn-next.vue'
import { formatAmount, dayjs } from '../../utils/index'
import 'swiper/swiper.min.css'
import 'swiper/modules/effect-fade/effect-fade.min.css'
const { modalStore, userStore } = focusStore.stores
import { UrlParser } from '../../utils/url'

const urlParsed = new UrlParser(window.location.href)
const { useTransparentNavBar, mock, index, inwx } = urlParsed.query

const activeIndex = ref(0)
const lockSwiper = ref(false)
const lockSwiperNext = ref(false)
const canShow = ref(false)
const notab = ref(false)
const isMock = mock === '1'
const canShowTimeTravel = ref(false)
const showLoading = ref(true)
const loadingList = ref<any>({ kv: true, originData: true })

const seconTimeCtl = ref(false)
provide('seconTimeCtl', seconTimeCtl)
watch(loadingList, (val) => {
  console.log('🚀 ~ file: App.vue ~ line 571 ~ watch ~ val', val)
  if (!val.kv && !val.originData) {
    loadingHide()
  }
})

const hasAccount = ref(false)
provide('hasAccount', hasAccount)
provide('canShow', canShow)
provide('lockSwiper', lockSwiper)
provide('lockSwiperNext', lockSwiperNext)

const pageList = ref<any[]>([])
provide('pushPageListId', (id: string) => {
  pageList.value.push(id)
})
provide('pageList', pageList)

if (!focusCore.env.isInApp && !isMock && !inwx) {
  console.error('只能在APP打开！')
  loadingHide()

  // jumpService.jump({
  //   path: location.href,
  //   mothod: 'url',
  // })
}
focusCore.env.chekAppH5Ver({ ios: 2310, android: 5010 }).then((status) => {
  console.log('🚀 ~ 是否符合指定版本？', status)

  notab.value = isMock ? true : status && useTransparentNavBar == '1'
  console.log('是否开沉浸式！', notab.value)
})
const setCanShow = function () {
  console.log('setCanShow')
  canShow.value = true
}
provide('setCanShow', setCanShow)

const controlledSwiper: any = ref(null)
const sliderLength = computed(() => {
  const data = Array.from(new Set(pageList.value))

  return data.length
})
const setControlledSwiper = (swiper: any) => {
  // console.log('🚀 ~ file: App.vue ~ line 76 ~ setControlledSwiper ~ swiper', swiper)
  // console.log(swiper.slides)
  // console.log(swiper.slides)
  controlledSwiper.value = swiper
  // sliderLength.value = swiper.slides.length - 3
}

const onSlideChange = function (data) {
  const swiperData = toRaw(data)
  activeIndex.value = swiperData.activeIndex
  console.log(controlledSwiper.value)
  if (controlledSwiper.value) {
    controlledSwiper.value.allowTouchMove = false
    console.log('lockSwiperNext.value....', lockSwiperNext.value)
    controlledSwiper.value.allowSlideNext = true

    nextTick(() => {
      setTimeout(() => {
        if (!lockSwiper.value) {
          // 每一页都要2.5秒以后才能翻页
          controlledSwiper.value.allowTouchMove = true
        }
        if (lockSwiperNext.value) {
          controlledSwiper.value.allowSlideNext = false
        }
      }, 2500)
    })
  }
}
watch(lockSwiperNext, (val) => {
  if (!val && controlledSwiper.value) {
    controlledSwiper.value.allowSlideNext = true
  }
  // if (val && controlledSwiper.value) {
  //   controlledSwiper.value.allowSlideNext = false
  // }
})

const originData: any = ref(null)
const lastDataTime = ref('')
provide('lastDataTime', lastDataTime)
const myword = ref('')
provide('myword', myword)
const showNext = ref(true)
provide('showNext', showNext)

const pageDatas = ref({
  page1Data: {},
  page2Data: {},
  page3Data: {},
  page4Data: {},
  page5Data: {},
  page6Data: {},
  page7Data: {},
  page8Data: {},
  page9Data: {},
  page10Data: {},
  page11Data: {},
  page12Data: {},
  page13Data: {},
  endData: {},
})
const isReady = ref(false)
const isTraveled = ref(false)
provide('isTraveled', isTraveled)

const lastCountTime = ref('')
provide('lastCountTime', lastCountTime)
watch(originData, (val) => {
  if (val) {
    console.log('🚀 ~ file: App.vue ~ line 138 ~ watch ~ val', val)
    // loadingHide()
    if (!val.isReady) {
      console.error('这个用户没数据，走兜底')
      loadingHide()
      // modalStore.errorMaskContrl(`no_origin_data`, ['非常抱歉，您不在本次活动的白名单内'])
      lastDataTime.value = dayjs('2022-11-01').format('YYYY年MM月DD日')

      const { userName, userAvator } = val
      pageDatas.value = {
        page2Data: {
          userName,
        },
        endData: {
          userAvator,
          userNickName: userName,
        },
      }
      isReady.value = true

      return false
    }
    lastDataTime.value = val.dataLastDate && dayjs(val.dataLastDate).format('YYYY年MM月DD日')
    lastCountTime.value = val.dataLastDate && dayjs(val.dataLastDate).format('YYYY.MM.DD')
    pageDatas.value = setPagesData(val)
    console.log('🚀 ~ file: App.vue ~ line 157 ~ watch ~ pageDatas.value', pageDatas.value)
    isReady.value = true
  }
})

function setPagesData(data) {
  const {
    userName = '',
    userAvator = '',
    accountCreateTime = '',
    userIndexId = '',
    firstTransTime = '',
    firstProdTime = '',
    firstBuyTime = '',
    firstPointTime = '',
    firstEarningTime = '',
    firstViewProdTime = '',
    firstProdName = '',
    firstTransMoney = '',
    firstProdType = '',
    financingCount = '',
    earningsCount = '',
    earningsDays = '',
    prodList = '',
    earningProds = '',
    topEarningProd = '',
    bestProd = '',
    consumeTimes = '', // 微众卡支出xx笔
    consumeMoney = '', // 微众卡支出多少钱
    hongbaoTimes = '', // 发红包的笔数
    payTimes = '', // 消费支付的笔数

    getPoint = '', // 累计获得积分
    useUpPoint = '', // 已使用的积分
    pointSaveMoney = '', // 节省的钱

    planTimes = '', // 执行计划次数
    donationTimes = '',
    maxPlanTimes = '',
    maxPlanName = '',

    lowerPeoplePercent = '',
    loginTimes = '',
    moneyMenageViewTimes = '',
    investViewTimes = '',
    moneyMenageTime = '',
    samePeoplePercent = '',
    userLevelNum = '1', // 用户会员等级  1 2 3 4
    equityTime = '', // 用户获得会员的时间

    dataLastDate = '',
    setPlanUserNums = '',
    firstEarningMoney,
    firstViewProdName,
  } = data
  let wezhongTagStr = ''
  const tagList = []
  const userTagStrs = ['微众奋斗使者']

  const equityName = ['白银', '黄金', '铂金', '钻石'][Number(userLevelNum) - 1]

  const wezhongTag = (() => {
    if (userIndexId < 10000) {
      wezhongTagStr = '你慧眼独具，在互联网理财的路上一马当先'
      const str = '梦想启明星'
      tagList.push(str)
      return str
    } else if (userIndexId > 1000000) {
      wezhongTagStr = '你与梦同行，在互联网理财的路上稳步前行'

      const str = '梦想同路人'
      tagList.push(str)
      return str
    }
    wezhongTagStr = '你与时偕行，在互联网理财的路上扬帆起航'

    const str = '理想奠基者'
    tagList.push(str)

    return '理想奠基者'
  })()

  let hasLoss = false
  let hasIncome = false
  let earningsLen = 0

  const page5Data = (() => {
    let incomeStr = '适当考虑多种投资类型，让收益更上一层楼。'
    const listData = [
      { percent: '100%' },
      { percent: '70%' },
      { percent: '60%' },
      { percent: '50%' },
      { percent: '40%' },
    ]
    const result =
      (earningProds &&
        earningProds.sort((a, b) => {
          if (Number(a.money) < Number(b.money)) {
            return 1
          } else if (Number(a.money) > Number(b.money)) {
            return -1
          }
          return 0
        })) ||
      []
    result.forEach((i) => {
      if (Number(i.money) !== 0) {
        earningsLen += 1
      }
      if (Number(i.money) > 0) {
        hasIncome = true
      }
      if (Number(i.money) < 0) {
        hasLoss = true
      }
    })
    if (earningsLen >= 1 && earningsLen < 3) {
      // tagList.push('专一理财')
    } else if (earningsLen >= 3 && earningsLen < 5) {
      incomeStr = '投资类型较为多元，良好的配置习惯有助于提升收益，继续保持！'
      // tagList.push('多元理财')
    } else if (earningsLen === 5) {
      // tagList.push('专业理财')
      incomeStr = '专业的你选择多元化分布资产'
    }
    if (earningsLen >= 3) {
      userTagStrs.push('超级理财专家')
    }

    return {
      listData: listData.map((i, id) => {
        const data = result[id] || {}
        if (Number(data.money) === 0) {
          i.percent = '5%'
        }
        return {
          ...i,
          ...result[id],
        }
      }),
      topProdName: (() => {
        const name = result[0]?.name
        const names = ['存款', '高端理财', '活期+', '基金', '稳健理财']
        const index = names.indexOf(name)
        return require(`./img/5/title_${index + 1}.png`)
      })(),
      topEarnings: formatAmount(result[0]?.money, true),
      noLoss: !hasLoss,
      hasLoss: hasLoss && Number(earningsCount) > 0, // 整体盈利，存在亏损
      lossMore: !hasIncome && Number(earningsCount) < 0, // 没有盈利
      hasIncome: hasIncome && Number(earningsCount) < 0, // 整体亏损，存在盈利
      incomeStr,
      show5_2: Number(earningsCount) < 0,
    }
  })()

  const num_earningsCount = Number(earningsCount)
  if (num_earningsCount > 0) {
    if (num_earningsCount < 10000) {
      tagList.push('小有成就')
    } else if (num_earningsCount > 50000) {
      tagList.push('理财赢家')
    } else if (num_earningsCount < 50000) {
      tagList.push('收益之星')
    }
  }

  if (earningsLen >= 1 && earningsLen < 3) {
    tagList.push('专一理财')
  } else if (earningsLen >= 3 && earningsLen < 5) {
    tagList.push('多元理财')
  } else if (earningsLen === 5) {
    tagList.push('专业理财')
  }

  // 红包tag
  if (Number(hongbaoTimes) >= 100) {
    tagList.push('社交达人')
  } else {
    // 消费产生的标签
    if (Number(payTimes) >= 100) {
      tagList.push('消费达人')
    }
  }

  // 积分省钱 生成的标签
  if (Number(pointSaveMoney)) {
    if (Number(pointSaveMoney) < 100) {
      tagList.push('省钱萌新')
    } else if (Number(pointSaveMoney) >= 1000) {
      tagList.push('省钱专家')
    } else {
      tagList.push('省钱能手')
    }
  } else {
    tagList.push('积分爱好者')
  }

  // 有益点心意
  if (Number(donationTimes)) {
    tagList.push('公益达人')
  }

  canShowTimeTravel.value = !!firstTransTime && !!firstProdTime
  console.log('🚀 ~ file: App.vue ~ line 310 ~ setPagesData ~ canShowTimeTravel', canShowTimeTravel.value)
  loadingList.value = {
    ...loadingList.value,
    originData: false,
  }
  return {
    page1Data: {
      accountCreateTime: (accountCreateTime && dayjs(accountCreateTime).format('YYYY.MM.DD')) || '',
      equityTime: dayjs(equityTime).format('YYYY.MM.DD'),
      userLevelNum,
      equityName,
      firstBuyTime: dayjs(firstProdTime).format('YYYY.MM.DD'),
    },
    page2Data: {
      userName,
      accountCreateTime,
      userIndexId,
      wezhongTag,
      wezhongTagStr,
      hasTrans: !!firstTransTime && !!firstProdTime,
    },
    page3Data: {
      firstTransTime: firstTransTime?.replace(/\-/g, '.'),
      firstProdTime: firstProdTime?.replace(/\-/g, '.'),
      firstBuyTime: firstBuyTime?.replace(/\-/g, '.'),
      firstPointTime: firstPointTime?.replace(/\-/g, '.'),
      firstEarningTime: firstEarningTime?.replace(/\-/g, '.'),
      firstViewProdTime: firstViewProdTime?.replace(/\-/g, '.'),
      firstProdName,
      firstProdType,
      firstTransMoney: formatAmount(firstTransMoney, true),
      firstEarningMoney,
      firstViewProdName,
    },
    page4Data: {
      firstDate: dayjs(accountCreateTime).format('YYYY.MM'),
      lastDate: dayjs(dataLastDate).format('YYYY.MM'),
      financingCount,
      earningsCount,
      earningsCountFormate: formatAmount(earningsCount, true),
      earningsDays,
      hasLoss: hasLoss && Number(earningsCount) > 0, // 整体盈利，存在亏损
      earningTagStr: (() => {
        const num = Number(earningsCount)
        if (num < 10000) {
          return '四平八稳'
        } else if (num > 50000) {
          return '飞速上涨'
        }
        return '稳步上涨'
      })(),
      prodList,
    },
    page5Data,
    page6Data: {
      showPage6: Number(earningsCount) > 0,
      topEarningProd: {
        ...bestProd,
        earnings: formatAmount(bestProd.earnings, true),
        // buyerCount: (() => {
        //   if (!topEarningProd.buyerCount) {
        //     return ''
        //   }
        //   if (topEarningProd.buyerCount < 20000) {
        //     return '1万+'
        //   }
        //   return parseInt((topEarningProd.buyerCount / 10000).toString(), 10) + '万+'
        //   // if (topEarningProd.buyerCount < 100000) {
        //   //   const num = topEarningProd.buyerCount[0]
        //   //   return num + '万+'
        //   // }
        //   // if (topEarningProd.buyerCount < 2000000) {
        //   //   return '10万+'
        //   // }
        //   // return '20万+'
        // })(),
      },
      bestProd: {
        ...topEarningProd,
        earnings: formatAmount(topEarningProd.earnings, true),
        // buyerCount: (() => {
        //   if (!bestProd.buyerCount) {
        //     return ''
        //   }
        //   if (bestProd.buyerCount < 20000) {
        //     return '1万+'
        //   }
        //   if (bestProd.buyerCount < 100000) {
        //     const num = bestProd.buyerCount[0]
        //     return num + '万+'
        //   }
        //   return parseInt((bestProd.buyerCount / 10000).toString(), 10) + '万+'
        // })(),
      },
    },
    page7Data: {
      consumeTimes, // 微众卡支出xx笔
      consumeMoney: formatAmount(consumeMoney, true), // 微众卡支出多少钱
      hongbaoTimes, // 发红包的笔数
      payTimes, // 消费支付的笔数
    },
    page8Data: {
      getPoint: getPoint, // 累计获得积分
      getPointFormate: formatAmount(getPoint || 0), // 累计获得积分
      useUpPointFormate: formatAmount(useUpPoint || 0), // 已使用的积分
      useUpPoint: useUpPoint, // 已使用的积分
      pointSaveMoney, // 节省的钱
      _pointSaveMoney: formatAmount(pointSaveMoney, true),
    },
    page9Data: {
      planTimes, // 执行计划次数
      donationTimes,
      maxPlanTimes,
      maxPlanName,
      dataLastDate: dayjs(dataLastDate).format('YYYY.MM'),
      setPlanUserNums,
    },
    page10Data: {
      loginTimes,
      lowerPeoplePercent,
      moneyMenageViewTimes,
      investViewTimes,
      moneyMenageTime,
      samePeoplePercent,
    },
    page11Data: {
      userLevelNum,
      equityTime: dayjs(equityTime).format('YYYY.MM.DD'),
    },
    page12Data: {
      userLevelNum,
      userAvator,
      accountCreateTime,
      userName,
    },
    page13Data: (() => {
      if (Number(userIndexId) < 10000) {
        userTagStrs.push('梦想起航先锋')
      }

      if (Number(earningsCount) > 10000) {
        userTagStrs.push('理财收益王者')
      }

      if (Number(donationTimes) >= 10) {
        userTagStrs.push('筑梦爱心大使')
      }
      if (Number(payTimes) >= 100) {
        userTagStrs.push('基迪匹贡献者')
      }

      return {
        tagList,
        userAvator,
        userName,
        userTagStrs,
      }
    })(),
    endData: {
      userAvator,
      userNickName: userName,
    },
  }
}

// const {
//   page2Data,
//   page3Data,
//   page4Data,
//   page5Data,
//   page6Data,
//   page7Data,
//   page8Data,
//   page9Data,
//   page10Data,
//   page11Data,
//   page12Data,
//   page13Data,
// } = toRefs(pageDatas)
const mockSlidIndex = ref(0)
provide('mockSlidIndex', mockSlidIndex)
if (isMock) {
  mockSlidIndex.value = index || 0

  setMock()
  // loadingHide()

  kvIsReady()
  // setCanShow()
}

function loginFocus(data) {
  console.log('🚀 ~ file: App.vue ~ line 392 ~ loginFocus ~ data', data)
  // data.hasAccount = false
  if (focusCore.env.isInWx) {
    // 在微信里面，已开户需要去app
    if (data.hasAccount) {
      // const link = location.origin + location.pathname + '?useTransparentNavBar=1'
      // modalStore.confirmContrl({
      //   show: true,
      //   contents: ['前往微众银行App进入“微众时光机”，解锁我的“财富+”形象'],
      //   hideCancel: true,
      //   btnConfirmText: '前往微众银行App',
      //   btnConfirmJumpConfig: {
      //     path: link,
      //     method: 'url',
      //   },
      // })
      // return false
    } else {
      // 未开户进入
      userService.getWxUserInfo().then((userinfo: any) => {
        const { nickName, avator } = userinfo || {}
        console.log('🚀 ~ userinfo', userinfo)
        modalStore.loadingEnd('timetravel_in')
        const userName = nickName
        const userAvator = avator || require('./img/12/avator_default.png')
        const isReady = false
        originData.value = { userName, userAvator, isReady }
      })
    }
  }
  if (data.hasAccount) {
    hasAccount.value = true
    // modalStore.loadingStart('timetravel_in')
    dynimicDataService.queryMarketingData(9999, 89).then((res: any) => {
      userService.getWxUserInfo().then((userinfo: any) => {
        const { nickName, avator } = userinfo || {}
        console.log('🚀 ~ userinfo', userinfo)
        console.log('🚀 ~ file: App.vue ~ line 394 ~ dynimicDataService.queryMarketingData ~ res', res)
        modalStore.loadingEnd('timetravel_in')
        const userName = nickName
        const userAvator = avator || require('./img/12/avator_default.png')
        const isReady = !!res.dataLastDate
        originData.value = { ...res, userName, userAvator, isReady }
      })
    })
  }
}
function loadingHide() {
  gsap.to('.loading_plant', {
    opacity: '0',
    duration: 0.4,
    onComplete: () => {
      setTimeout(() => {
        showLoading.value = false
      }, 100)
    },
  })
}

function kvIsReady() {
  loadingList.value = {
    ...loadingList.value,
    kv: false,
  }
}

provide('kvIsReady', kvIsReady)

function setMock() {
  // originData.value = {
  //   dataLastDate: '2022-09-01', // 数据结算日期

  //   prodList: ['产品1', '产品2', '产品3'], // 微众买入最多的3个理财产品
  //   accountCreateTime: '2019-01-01',
  //   userIndexId: 100000, // 开户次序
  //   // 第三页
  //   firstTransTime: '2022-01-01', // 第一次转账时间
  //   firstProdTime: '2022-01-01', // 第一次购买产品时间（理财、基金）
  //   firstBuyTime: '', // 第一次微众卡消费时间
  //   firstPointTime: '', // 第一次 获得积分 的时间
  //   firstEarningTime: '2022-02-01', // 第一次 获得投资收益 的时间
  //   firstViewProdTime: '2022-01-01', // 第一次 浏览理财产品 的时间
  //   firstProdName: '恒源日申月持', //
  //   firstProdType: '基金', // 第一次买入的产品类型
  //   firstTransMoney: '123123.00', // 第一次转账的金额
  //   firstEarningMoney: '123123.00', // 第一次获得投资收益的金额
  //   firstViewProdName: '恒源日申月持', // 第一次 浏览理财产品 的产品名

  //   // 第四页
  //   financingCount: '123', // 累计交易笔数
  //   earningsCount: '88888.32', // 累计收益 可为负数
  //   earningsDays: '22', // 累计正收益的天数

  //   // 第五页
  //   earningProds: [
  //     // 五类产品收益
  //     { name: '稳健理财', money: '1000' },
  //     { name: '高端理财', money: '1000' },
  //     { name: '存款', money: '100000' },
  //     { name: '活期+', money: '13330' },
  //     { name: '基金', money: '10000' },
  //   ],
  //   // 第六页
  //   topEarningProd: {
  //     // 最爱购买产品
  //     earnings: '123123', // 产品收益
  //     name: '1231321', // 产品名字
  //     buyTimes: '333333', // 买入次数
  //     buyerCount: '111', // 一共多少用户购买
  //   },
  //   bestProd: {
  //     // 收益最高产品
  //     name: '11111', // 产品名字
  //     earnings: '22222', // 产品收益
  //     buyTimes: '333333', // 买入次数
  //     buyerCount: '123123', // 一共多少用户购买
  //   },
  //   // 第七页
  //   consumeTimes: '123', // 微众卡支出xx笔
  //   consumeMoney: '1111', // 微众卡支出多少钱
  //   hongbaoTimes: '123', // 发红包的笔数
  //   payTimes: '321', // 消费支付的笔数
  //   // 第八页
  //   getPoint: '', // 累计获得积分
  //   useUpPoint: '123123', // 已使用的积分
  //   pointSaveMoney: '0.98', // 节省的钱
  //   // 第九页
  //   planTimes: '32132', // 执行计划次数
  //   donationTimes: '12313', // 捐赠一点心意的次数
  //   maxPlanTimes: '123123', // 执行最多次的计划
  //   maxPlanName: '', // 执行最多次的计划类型   存工资  还房贷/车贷  交房租  孝敬父母 其他
  //   setPlanUserNums: '12312', // 一共有多少用户设置了计划
  //   // 第10 页
  //   loginTimes: '11111', // 累计登录天数
  //   lowerPeoplePercent: '20%', //   小于该天数的用户占比= 比该天数少的用户数/总用户数
  //   moneyMenageViewTimes: '11111', // 浏览理财产品的次数
  //   investViewTimes: '11111', // 浏览投资产品的次数
  //   moneyMenageTime: '18:01', // 最常登录的时间段（点）
  //   samePeoplePercent: '20%', // 有多少人跟你一样  百分比
  //   // 第11页
  //   userLevelNum: '2', // 用户会员等级  1 2 3 4
  //   equityTime: '2022.09.01', // 用户获得会员的时间
  //   // 第12页
  // }
  // originData.value = {
  //   noData: true,
  // }
  // return

  originData.value = {
    isReady: true,
    dataLastDate: '2020-05-10',
    prodList: ['苏银添利宝', '长城固收', '苏银2号'],
    accountCreateTime: '2022-05-09',
    userIndexId: 100002,
    firstTransTime: '2020-01-01',
    firstProdTime: '2020-01-01',
    firstBuyTime: '2020-01-01',
    firstPointTime: '',
    firstEarningTime: '2020-01-04',
    firstViewProdTime: '2022-01-05',
    firstProdName: '华能信托1号',
    firstProdType: '基金',
    firstTransMoney: '123123.15',
    firstEarningMoney: '100.99',
    firstViewProdName: '恒源日申月持',
    financingCount: '123',
    earningsCount: '5685.65',
    earningsDays: '22',
    earningProds: [
      { name: '稳健理财', money: '-1000.22' },
      { name: '高端理财', money: '-312312.123' },
      { name: '存款', money: '20222.22' },
      { name: '活期+', money: '203231.1' },
      { name: '基金', money: '2021321.15' },
    ],
    topEarningProd: { earnings: '123123', name: '中信', buyTimes: '13', buyerCount: '********' },
    bestProd: { name: '平安', earnings: '22222', buyTimes: '8', buyerCount: '********' },
    consumeTimes: '123',
    consumeMoney: '1111',
    hongbaoTimes: '123',
    payTimes: '321',
    getPoint: '100000',
    useUpPoint: '10000',
    pointSaveMoney: '1000.00',
    planTimes: '15',
    donationTimes: '1230',
    maxPlanTimes: '89',
    maxPlanName: '还房贷/车贷',
    setPlanUserNums: '12312',
    loginTimes: '300',
    lowerPeoplePercent: '20%',
    moneyMenageViewTimes: '11111',
    investViewTimes: '11111',
    moneyMenageTime: '07:01',
    samePeoplePercent: '20%',
    userLevelNum: '2',
    equityTime: '2022-10-12',
    userAvator:
      'https://thirdwx.qlogo.cn/mmopen/vi_32/pbN6Mhj8YdPhY5wkGPw8ZbYoYUM1dpPhaqXhoVFL4XQSkYHGHuYw2hCcSnetqfWj6uCn8U5ysibbFkIhWP33UdQ/132',
    userName: '@#👦🏻哈哈哈',
  }
}
</script>

<template>
  <loading-plant v-show="showLoading"></loading-plant>
  <page1 :class="{ notab }" :page1Data="pageDatas.page1Data" v-if="(isReady && !isTraveled) || !isReady"></page1>
  <page1_1 :class="{ notab }" :page1Data="pageDatas.page1Data" v-if="isReady && isTraveled"></page1_1>
  <btn-next v-if="showNext"></btn-next>
  <div class="pagenation" v-if="showNext">
    <span style="font-weight: bold">{{ activeIndex + 1 }}</span> /{{ sliderLength }}
  </div>
  <!-- 没有转入或者购买理财 -->
  <swiper
    class="pages"
    :speed="400"
    :slides-per-view="1"
    :space-between="0"
    :resistanceRatio="0"
    @swiper="setControlledSwiper"
    @slideChange="onSlideChange"
    direction="vertical"
    :modules="[EffectFade, Controller]"
    effect="fade"
    :followFinger="false"
    :cross-fade="true"
    :preventInteractionOnTransition="true"
    :initialSlide="mockSlidIndex"
    v-if="!canShowTimeTravel && isReady"
  >
    <swiper-slide>
      <page-2 :page2Data="pageDatas.page2Data" :class="{ notab }"></page-2>
    </swiper-slide>
    <swiper-slide>
      <page4_2 :page4Data="pageDatas.page4Data" :class="{ notab }"></page4_2>
    </swiper-slide>

    <swiper-slide>
      <page8_1 :class="{ notab }"></page8_1>
    </swiper-slide>
    <swiper-slide>
      <page9_1 :class="{ notab }" :page9Data="pageDatas.page9Data"></page9_1>
    </swiper-slide>
    <swiper-slide>
      <endNodata :endData="pageDatas.endData" :class="{ notab }"></endNodata>
    </swiper-slide>
  </swiper>
  <!-- 有数据 -->
  <swiper
    class="pages"
    :speed="400"
    :slides-per-view="1"
    :space-between="0"
    :resistanceRatio="0"
    @swiper="setControlledSwiper"
    @slideChange="onSlideChange"
    direction="vertical"
    :modules="[EffectFade, Controller]"
    effect="fade"
    :followFinger="false"
    :cross-fade="true"
    :preventInteractionOnTransition="true"
    :initialSlide="mockSlidIndex"
    v-if="canShowTimeTravel && isReady && !seconTimeCtl"
  >
    <swiper-slide>
      <page-2 :page2Data="pageDatas.page2Data" :class="{ notab }"></page-2>
    </swiper-slide>
    <swiper-slide v-if="pageDatas.page2Data.hasTrans">
      <page-3 :page3Data="pageDatas.page3Data" :class="{ notab }"></page-3>
    </swiper-slide>
    <swiper-slide>
      <page-4 :page4Data="pageDatas.page4Data" :class="{ notab }">
        <div class="last-date">*以上数据截止至{{ lastCountTime }}</div></page-4
      >
    </swiper-slide>
    <swiper-slide>
      <page-5 :class="{ notab }" :page5Data="pageDatas.page5Data"
        ><div class="last-date">*以上数据截止至{{ lastCountTime }}</div></page-5
      >
    </swiper-slide>
    <swiper-slide v-if="pageDatas.page5Data.show5_2">
      <page-5_2 :class="{ notab }" :page5Data="pageDatas.page5Data">
        <div class="last-date">*以上数据截止至{{ lastCountTime }}</div>
      </page-5_2>
    </swiper-slide>
    <swiper-slide v-if="pageDatas.page6Data.showPage6">
      <page-6 :page6Data="pageDatas.page6Data" :class="{ notab }">
        <div class="last-date">*以上数据截止至{{ lastCountTime }}</div>
      </page-6>
    </swiper-slide>
    <swiper-slide v-if="pageDatas.page7Data.consumeTimes">
      <page-7 :page7Data="pageDatas.page7Data" :class="{ notab }">
        <div class="last-date">*以上数据截止至{{ lastCountTime }}</div>
      </page-7>
    </swiper-slide>
    <swiper-slide>
      <page-8 :page8Data="pageDatas.page8Data" :class="{ notab }" v-if="Number(pageDatas.page8Data.getPoint)">
        <div class="last-date">*以上数据截止至{{ lastCountTime }}</div>
      </page-8>
      <page8_1 :page8Data="pageDatas.page8Data" :class="{ notab }" v-else>
        <div class="last-date">*以上数据截止至{{ lastCountTime }}</div>
      </page8_1>
    </swiper-slide>
    <swiper-slide>
      <page-9 :page9Data="pageDatas.page9Data" :class="{ notab }" v-if="pageDatas.page9Data.planTimes">
        <div class="last-date">*以上数据截止至{{ lastCountTime }}</div>
      </page-9>
      <page9_1 :class="{ notab }" :page9Data="pageDatas.page9Data" v-else>
        <div class="last-date">*以上数据截止至{{ lastCountTime }}</div>
      </page9_1>
    </swiper-slide>
    <swiper-slide>
      <page-10 :page10Data="pageDatas.page10Data" :class="{ notab }">
        <div class="last-date">*以上数据截止至{{ lastCountTime }}</div>
      </page-10>
    </swiper-slide>
    <swiper-slide>
      <page-11 :page11Data="pageDatas.page11Data" :class="{ notab }">
        <div class="last-date">*以上数据截止至{{ lastCountTime }}</div>
      </page-11>
    </swiper-slide>
    <swiper-slide>
      <page-12 :page12Data="pageDatas.page12Data" :class="{ notab }"></page-12>
    </swiper-slide>
    <swiper-slide>
      <page-13 :page13Data="pageDatas.page13Data" :class="{ notab }"></page-13>
    </swiper-slide>
  </swiper>
  <focus-render @onLoginSucc="loginFocus"></focus-render>
</template>

<style lang="scss">
@import '../../styles/normalize.scss';
@font-face {
  font-family: Roboto-Bold;
  src: url('../../assets/Roboto-Bold.ttf');
}
body,
html {
  background-repeat: no-repeat;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
}
#app {
  width: 100%;
  height: 100%;
  text-align: center;
  overflow-x: none;
  line-height: 0;
  font-size: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  // background: #2b48c4;
  position: relative;
  animation: iconflow 5s linear infinite;
  overflow: hidden;
  vertical-algin: bottom;
}

.bignum {
  font-family: Roboto-Bold;
  margin-bottom: -4px;
}

.fs_52 {
  font-size: 52px;
  line-height: 1;
  display: inline-block;
  transform: translateY(4px);
}
.pages {
  width: 100%;
  height: 100%;
}
.notab {
  padding-top: 130px;
}

.section {
  line-height: 1;
  margin-top: 60px;

  p {
    margin-top: 38px;
    line-height: 1;
    &:first-child {
      margin-top: 0;
    }
    &.s_38 {
      font-size: 38px;
    }
    &.fb {
      font-weight: bold;
    }
  }
}
.num {
  font-family: KumbhSans-Bold;
}
.big-num {
  font-weight: bold;
  font-size: 52px;
  display: inline-block;
  transform: translateY(4px);
}
.last-date {
  font-size: 22px;
  letter-spacing: 0.3px;
  line-height: 22px;
  font-weight: 400;
  margin-top: 25px;
  width: 100%;
  text-align: left;
  position: absolute;
  bottom: 40px;
  left: 55px;
  z-index: 222;
  color: #898989;
  opacity: 0.5;
}
.pagenation {
  width: 120px;
  height: 60px;
  position: absolute;
  right: 60px;
  bottom: 100px;
  z-index: 200;
  color: #fff;
  line-height: 1.5;
  font-size: 32px;
  opacity: 0.8;
  color: #898989;
}
.page4-title {
  font-size: 70px;
  font-weight: bold;
  color: #3d55a0;
  line-height: 0;
  letter-spacing: 3px;
  background: linear-gradient(-119deg, #6a69ff 0%, #00adff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  padding-right: 60px;
  margin-left: 60px;

  //   left: 0;
  //   bottom: 0;
  //   transform: scaleY(0.5);
  // }
  .img_title {
    width: 579px;
    height: 181px;
  }
}
</style>
