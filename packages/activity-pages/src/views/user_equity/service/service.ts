import { focusStore, focusServices, focusCore, focusUtils } from '@focus/render'
console.log('🚀 ~ file: service.ts ~ line 2 ~ focusUtils', focusUtils)
const { dayjs, formatAmount } = focusUtils
const { mgmService, kvService } = focusServices

const cgis = {
  userLevel: {
    url: '/wm-hjhtr/wealth_equity/current_query',
    name: '用户财富值',
  },
  //
}

class userEquityService {
  getUserLevelData(): Promise<{
    rewardWealth: number
    standardWealth: number
    updateTime: string
    wealth: number
    isError?: boolean
  }> {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.userLevel)
        .then((res: any) => {
          console.log('🚀 ~ userEquityService ~ focusCore.request ~ res:', res)
          const { dsDate = '', equityWealth = '' } = res

          return resolve({
            wealth: (equityWealth && formatAmount(equityWealth.wealth)) || 0,
            standardWealth: (equityWealth && formatAmount(equityWealth.standardWealth)) || 0,
            rewardWealth: (equityWealth && formatAmount(equityWealth.rewardWealth)) || 0,
            updateTime: dsDate ? dayjs(dsDate).format('YYYY年MM月DD日') : '--',
          })
        })
        .catch((err: any) => {
          console.log('🚀 ~ userEquityService ~ focusCore.request ~ err:', err)
          return resolve({
            isError: true,
            wealth: 0,
            standardWealth: 0,
            rewardWealth: 0,
            updateTime: '--',
          })
        })
    })
  }
}
export default new userEquityService()
