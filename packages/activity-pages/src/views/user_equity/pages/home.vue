<template>
  <focus-render v-if="focusFid" :focusFid="focusFid" @onLoginSucc="afterLogin">
    <template #user_level="slotProps">
      <!-- <div class="bg-cover"></div> -->
      <div class="user_level">
        <div class="btn-more" @click="jumpMore">更多历史财富值</div>
        <p class="score">{{ levelData.wealthScore }}</p>
        <div class="score-text">
          <p>
            含<span class="score_detail">{{ levelData.standardScore }}</span
            >标准财富值
          </p>
          <p>
            +<span class="score_detail">{{ levelData.rewardScore }}</span
            >奖励财富值
          </p>
          <div class="time">数据统计日期: {{ levelData.updateTime }}</div>
        </div>
      </div>
    </template>
  </focus-render>
</template>

<script setup lang="ts">
import { provide, readonly, ref, toRaw, computed, nextTick, watch, watchEffect } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import userEquitySetvice from '../service/service'

const { modalStore } = focusStore.stores
const { activityService, jumpService } = focusServices
const activeAid = ref(0)
provide('activeAid', activeAid)
const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { fid } = locationHrefPared.query
const focusFid = fid

const hasAccount = ref(false)

watchEffect(async () => {
  // console.log('activeAid.value', activeAid.value)
  if (hasAccount.value) {
    // console.log('activeAid.value in true', activeAid.value)
  }
})

function afterLogin(data: any) {
  if (focusCore.isLogined) {
    hasAccount.value = focusCore.hasAccount
    getEquityData()
  }
}
const levelData = ref<{
  wealthScore: number
  standardScore: number
  rewardScore: number
  updateTime: string
}>({
  wealthScore: 0,
  standardScore: 0,
  rewardScore: 0,
  updateTime: '--',
})

async function getEquityData() {
  userEquitySetvice.getUserLevelData().then((res) => {
    console.log('🚀 ~ userEquitySetvice.getUserLevelData ~ res:', res)
    const { rewardWealth, standardWealth, updateTime, wealth, isError } = res
    if (isError) {
      modalStore.errorMaskContrl('getUserLevelData')
    }
    levelData.value = {
      wealthScore: wealth || 0,
      standardScore: standardWealth || 0,
      rewardScore: rewardWealth || 0,
      updateTime: updateTime || '--',
    }
  })
}

function jumpMore() {
  jumpService.jump({
    path: '/memberEquity/EquityWealthHistoryScene',
  })
}
</script>

<style lang="scss" scoped>
.user_level {
  width: 750px;
  height: 505px;
  margin: 0 auto;
  position: relative;
  &::before {
    width: 100%;
    height: 295px;
    display: block;
    content: '';
    background: url('../img/01.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    z-index: 0;
  }
  &::after {
    width: 100%;
    height: 70px;
    display: block;
    content: '';
    background: url('../img/03.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    z-index: 0;
  }
  .score {
    position: absolute;
    left: 50%;
    top: 158px;
    font-size: 60px;
    font-weight: bold;
    line-height: 60px;
    transform: translateX(-50%);
    color: #cd835f;
    z-index: 1;
  }
  .btn-more {
    background: #456ce6;
    border-radius: 26px;
    border-radius: 26px;
    width: 180px;
    height: 50px;
    line-height: 50px;
    font-size: 20px;
    color: #ffffff;
    text-align: center;
    position: absolute;
    left: 470px;
    top: 30px;
    cursor: pointer;
    z-index: 1;

    :active {
      opacity: 0.6;
    }
  }
  .time {
    font-size: 24px;
    color: #445c95;
    text-align: center;
    line-height: 36px;
    opacity: 0.7;
    position: relative;
    z-index: 1;
    margin: 0 auto;
    padding-top: 10px;
    padding-bottom: 10px;
    background-size: 100% 100%;
    height: 36px;
    width: 100%;
    flex: none;
  }
  .score-text {
    width: 100%;
    box-sizing: border-box;
    padding: 0 60px;
    padding-bottom: 20px;
    min-height: 80px;
    background: url('../img/02.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    font-size: 28px;
    color: #445c95;
    text-align: center;
    line-height: 45px;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    .score_detail {
      font-size: 34px;
      color: #cd835f;
      font-weight: bold;
    }
  }
}
</style>
