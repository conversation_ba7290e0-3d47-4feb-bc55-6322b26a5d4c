<template>
  <div
    class="preview-tool"
    v-if="showPreviewTool"
    :style="{
      top: topLevel * 10 + '%',
    }"
  >
    <p class="title">你正在使用预览版本V{{ pageVer }}，仅供内部使用，不得外泄！</p>
    <p class="title">模拟访问只用于预览，不代表实际效果！</p>
    <div class="btn-up" v-if="!showOptions" @click="moveUp">向上移动</div>
    <div class="btn-down" v-if="!showOptions" @click="moveDown">向下移动</div>
    <div class="mock">
      <span class="title">模拟时间：</span>
      {{ mockTimeFormate }}
    </div>
    <div class="mock">
      <span class="title">模拟访问人群：</span>
      {{ mockGray.text }}
      <div class="selectors" v-if="showOptions">
        <div
          class="item"
          :class="{ selected: selectedGrayId === Number(item.id) }"
          v-for="(item, index) in grayListOptions"
          @click="selectedGrayId = Number(item.id)"
        >
          {{ item.text }}
        </div>
      </div>
    </div>
    <div class="mock">
      <span class="title">模拟渲染条件：</span> {{ mockCondition.length ? '' : '无' }}
      <p v-for="(i, index) in mockCondition" :key="index">{{ i }}</p>
    </div>
    <div class="conditions" v-if="showOptions" :style="{}">
      <div class="item" v-for="(item, index) in conditionOptions" :key="index">
        <div class="name">
          {{ item.name }}
        </div>
        <div class="options" v-for="(opt, index) in item.list" :key="index">
          <div
            class="btn"
            :class="{ selected: conditionSelected[item.keyName] === opt.value }"
            @click="
              () => {
                conditionSelected[item.keyName] = index + 1
                conditionSelectedName[item.keyName] = `${item.name}-${opt.label}`
              }
            "
          >
            {{ opt.label }}
          </div>
        </div>
      </div>
    </div>

    <div class="bottom">
      <div class="btn-refresh" @click="refresh">重新加载让模拟生效</div>
      <div class="btn-open" @click="showOptions = !showOptions">{{ showOptions ? '隐藏选项' : '修改模拟选项' }}</div>
      <div class="btn-refresh" @click="openInApp" v-if="!focusCore.env.isInApp">在App中查看</div>
      <div class="btn-refresh" @click="reloadForAutoClaim" v-if="!focusCore.env.isInApp">测试卡券自动弹窗</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { dayjs } from '@/utils'
import { computed, ref, watch, watchEffect } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import { Base64 } from 'js-base64'
import { storeToRefs } from 'pinia'
const { jumpService, grayService } = focusServices
const { conditionStore, modalStore } = focusStore.stores
const { conditions } = storeToRefs(conditionStore)
console.log('🐬 ~ conditions:', conditions)
const { UrlParser } = focusUtils

const props = defineProps<{
  focusRenderCtr: any
}>()
const urlQuery = new UrlParser().query
const showOptions = ref(false)
const topLevel = ref(0)
const selectedGrayId = ref(0)
const conditionSelected = ref<any>({})
const conditionSelectedName = ref<any>({})
const userCanSee = ref(false)
const showPreviewTool = computed(() => {
  console.log('🚀 ~ showPreviewTool ~ focusCore.isPreview:', focusCore.isPreview)
  return userCanSee.value && focusCore.isPreview && (urlQuery.focus_review === '1' || urlQuery.focus_preview === '1')
})
// 对selectedGrayId进行监听
watch(selectedGrayId, (val) => {
  console.log('🚀 ~ watch ~ val:', val)
})

const mockGray = computed(() => {
  console.log('....focusCore.mockdata', focusCore.mockData)
  const { mockGrayId = {} } = focusCore.mockData
  console.log('🚀 ~ mockGrayText ~ mockGrayId:', mockGrayId)
  if (mockGrayId) {
    let data: any = mockGrayId
    let text = '无'
    if (data.id) {
      if (data.id === '0') {
        text = '未登录'
      } else if (data.id === '1') {
        text = '已登录，未满足通用灰度'
      } else {
        text = `满足通用灰度[${data.id}]`
      }
    }
    return {
      id: data.id || '',
      text: text,
    }
  }
  return {
    id: '',
    text: '无',
  }
})

const pageVer = computed(() => {
  console.log('🚀 ~ pageVer ~ props.focusRenderCtr:', props.focusRenderCtr)
  return props.focusRenderCtr && props.focusRenderCtr.pageVer
})

const grayListOptions = computed(() => {
  return (
    props.focusRenderCtr &&
    props.focusRenderCtr.grayList.map((i) => {
      console.log(
        '🚀 ~ props.focusRenderCtr.grayList.map ~ props.focusRenderCtr.grayList:',
        props.focusRenderCtr.grayList,
      )
      const data = i
      let text = '无'
      if (data.id) {
        if (data.id === '0') {
          text = '未登录'
        } else if (data.id === '1') {
          text = '已登录，未满足通用灰度'
        } else {
          text = `满足通用灰度[${data.id}]`
        }
      }
      return {
        id: data.id || '',
        text: text,
      }
    })
  )
})

const conditionOptions = computed(() => {
  console.log('🚀 ~ conditionOptions ~ conditionStore:', conditions)

  const drawCondition = [
    {
      conditionType: 'dc01',
      alias: '关注公众号',
      list: [
        {
          value: 1,
          label: '未关注公众号',
        },
        {
          value: 2,
          label: '已关注公众号',
        },
      ],
    },
    {
      conditionType: 'dc02',
      alias: '开户状态',
      list: [
        {
          value: 1,
          label: '用户未开户',
        },
        {
          value: 2,
          label: '用户已开户',
        },
      ],
    },
    {
      conditionType: 'dc03',
      alias: '是否满足指定灰度ID',
      list: [
        {
          value: 1,
          label: '未满足',
        },
        {
          value: 2,
          label: '已满足',
        },
      ],
    },
    {
      conditionType: 'dc04',
      alias: '是否是新手状态（仅首次资产达标活动使用）',
      list: [
        {
          value: 1,
          label: '不是新手状态',
        },
        {
          value: 2,
          label: '新手状态',
        },
      ],
    },
    {
      conditionType: 'dc05',
      alias: '是否满足数据源状态判断',
      dataSourceApiKey: '_api_10',
      list: [
        {
          value: 1,
          label: '不满足数据源状态',
        },
        {
          value: 2,
          label: '满足数据源状态',
        },
      ],
    },
    {
      conditionType: 'dc06',
      alias: '是否是使用iOS设备',
      list: [
        {
          value: 1,
          label: '用户使用非iOS设备（Android/鸿蒙）',
        },
        {
          value: 2,
          label: '用户使用iOS',
        },
      ],
    },
    {
      conditionType: 'dc07',
      alias: '是否在App环境打开',
      list: [
        {
          value: 1,
          label: '在微信打开（包括小程序）',
        },
        {
          value: 2,
          label: '在App内打开页面',
        },
      ],
    },
    {
      conditionType: 'dc08',
      alias: '大MGM邀请人数大于0',
      list: [
        {
          value: 1,
          label: '大MGM未邀请人',
        },
        {
          value: 2,
          label: '大MGM邀请了至少1人',
        },
      ],
    },
    {
      conditionType: 'dc09',
      alias: '产品可售状态Code=',
      dataSourceApiKey: '_api_09',
      list: [
        {
          value: 1,
          label: '不可售',
        },
        {
          value: 2,
          label: '正常可售',
        },
      ],
    },
  ]
  const codeTypeArr: string[] = []

  if (props.focusRenderCtr && Array.isArray(props.focusRenderCtr.allModulesData)) {
    props.focusRenderCtr.allModulesData.forEach((i: any) => {
      if (i.moduleSubType === 'conditionBox' && i.data && i.data.conditionType === 'dc09' && i.data.extendStr) {
        codeTypeArr.push(i.data.extendStr)
      }
    })
  } else {
    console.error('allModulesData is not an array or focusRenderCtr is null:', props.focusRenderCtr)
  }

  console.log('🐬 ~ conditionOptions ~ codeTypeArr:', codeTypeArr)
  console.log('🐬 ~ conditionOptions ~ conditions.value:', conditions.value)
  const filteredConditions = conditions.value
    ? Object.keys(conditions.value).filter((key) => {
        const target = drawCondition.find((i) => key.indexOf(i.conditionType) > -1)
        if (target && target.conditionType === 'dc09') {
          return codeTypeArr.includes(key.split('__')[2])
        }
        if (target && target.conditionType === 'dc03') {
          return key.split('__')[2] !== '1'
        }
        return !!target
      })
    : []
  console.log('🐬 ~ conditionOptions ~ filteredConditions:', filteredConditions)
  return filteredConditions.map((key) => {
    console.log('🚀 ~ Object.keys ~ key:', key)
    const target = drawCondition.find((i) => key.indexOf(i.conditionType) > -1)
    if (target) {
      let name = target.alias
      let idName = ''
      if (target.conditionType === 'dc09') {
        const code = key.split('=')[1]
        console.log('🚀 ~ Object.keys ~ code:', code)
        name = `${name}${code}`
      }
      if (target.conditionType === 'dc03') {
        idName = key.split('__')[2]
        name = `${name}(${idName})`
      }
      return {
        name,
        list: target.list,
        keyName: key,
        id: idName,
      }
    }
    return {
      name: '',
      list: [],
      keyName: key,
      id: '',
    }
  })
})

// 对conditionOptions进行监听
watch(conditionOptions, (val) => {
  console.log('🐬 ~ watch ~ conditionOptions:', conditionOptions)
})

const mockTimeFormate = computed(() => {
  // const { mockTime = '' } = urlQuery
  const mockTime = focusCore.mockData.isPreview && focusCore.mockData.mockTime
  console.log('🚀 ~ mockTimeFormate ~ mockTime:', mockTime)
  if (mockTime) {
    return dayjs(Number(mockTime)).format('YYYY年MM月DD日 hh:mm:ss')
  }
  return '无'
})

const mockCondition = computed(() => {
  // const { mockCondition = '' } = urlQuery
  // console.log('🚀 ~ mockCondition ~ mockCondition:', mockCondition)
  const { mockCondition } = (focusCore.mockData.isPreview && focusCore.mockData) || {}
  console.log('🚀 ~ mockCondition ~ mockCondition:', mockCondition)
  if (mockCondition) {
    return Object.keys(mockCondition).map((key: any) => {
      const d = mockCondition[key]
      return d.title
    })
  }
  return []
})

watchEffect(() => {
  console.log('>>>>conditionSelected.value', conditionSelected.value)
  // conditionSelected.value = conditions.value
  if (props.focusRenderCtr && focusCore.isLogined && focusCore.isPreview) {
    checkGray()
  }
})

function checkGray() {
  // 在这个名单内才可以看预览版本
  let grayId = [{ id: 1726816549 }]
  if (BUILD_TEST) {
    grayId = [
      {
        id: 1726194915,
      },
    ]
  }
  console.error('>>>> 现在要检查用户是否有预览版本控制权限。。。。')
  grayService.checkBussinessGray(grayId, '2').then((list: any) => {
    if (BUILD_TEST) {
      userCanSee.value = true
      return
    }
    console.log('🚀 ~ grayService.checkBussinessGray ~ list:', list)
    if (list.length) {
      userCanSee.value = true
    } else {
      modalStore.errorMaskContrl('no_preview', ['你不是可访问的用户哦！'])
    }
  })
}

function moveDown() {
  topLevel.value = topLevel.value < 5 ? topLevel.value + 1 : topLevel.value
}
function moveUp() {
  topLevel.value = topLevel.value > 0 ? topLevel.value - 1 : topLevel.value
}

function openInApp() {
  jumpService.jump({ path: location.href, method: 'url' })
}

function refresh() {
  modalStore.loadingStart('refresh')
  console.log('....', selectedGrayId.value)
  console.log('....', conditionSelected.value)
  const mockCondition: any = {}
  Object.keys(conditionSelected.value).forEach((i) => {
    mockCondition[i] = {
      val: conditionSelected.value[i],
      title: conditionSelectedName.value[i],
    }
  })
  const mockData = {
    mockGrayId: {
      id: selectedGrayId.value,
    },
    mockCondition,
  }
  console.log('🐬 ~ refresh ~ mockData:', mockData)

  const parsed = new UrlParser(location.href)
  const { mockTime, focus_preview, fid, focus_review } = parsed.query
  const newPath = new UrlParser(parsed.path)
  const query: any = {
    mockTime,
    fid,
  }
  if (focus_review) {
    query.focus_review = focus_review
  }
  if (focus_preview) {
    query.focus_preview = focus_preview
  }
  const path = newPath.appendQuery({
    ...query,
    mockData: Base64.encode(JSON.stringify(mockData)),
  }).fullPath
  console.log('🚀 ~ refresh ~ path:', path)

  location.replace(path)
}

function reloadForAutoClaim() {
  const parsed = new UrlParser(location.href)
  const path = parsed.appendQuery({
    autoClaim: 1,
  }).fullPath
  console.log('🚀 ~ reloadForAutoClaim ~ path:', path)
  location.replace(path)
}
</script>

<style scoped lang="scss">
.conditions {
  width: 100%;
  background: #fff;
  .item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 5px 20px;
    .btn {
      border: 1px solid #eee;
      padding: 5px 10px;
      border-radius: 10px;
      &.selected {
        color: #fff;
        background: #e5966a;
      }
    }
  }
}
.bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  > div {
    margin-right: 10px;
  }
}
.btn-open {
  width: fit-content;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40px;
  color: #fff;
  background: #e5966a;
  padding: 5px 10px;
  margin-top: 10px;
}
.btn-refresh {
  width: 300px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40px;
  color: #fff;
  background: #e5966a;
  margin-top: 10px;
}

.selectors {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  .item {
    cursor: pointer;
    margin-bottom: 10px;
    margin-left: 10px;
    width: fit-content;
    padding: 5px 10px;
    background: #eee;
    border-radius: 10px;
    &.selected {
      background: #e5966a;
      color: #fff;
    }
  }
}

.preview-tool {
  overflow-y: auto;
  width: 100%;
  max-height: 60%;
  min-height: 100px;
  background: rgba(0, 0, 0, 0.3);
  color: red;
  font-size: 24px;
  text-align: center;
  display: flex;
  align-items: center;
  flex-direction: column;
  text-align: center;
  position: fixed;
  z-index: 111111;
  padding: 20px 0;
  line-height: 1.5;
  .version {
  }

  .btn-up,
  .btn-down {
    color: pink;
    border: 1px solid #000;
    width: 120px;
    height: 50px;
    position: absolute;
    right: 0;
    line-height: 50px;
  }
  .btn-up {
    top: 0;
  }
  .btn-down {
    top: 60px;
  }
  .title {
  }
  .mock {
    height: auto;
    width: 100%;
  }
}
</style>
