export type PhotoData = {
  photoId: string
  reviewStatus: string //0:审核中,1:审核通过,2:审核未通过
  dayDate: string //上传时间
  fileUrl: string
  title: string
  likedCount: number
  rank: number
  headImgUrl: string
  nickName: string
  likeFlag?: boolean
  createTime?: string
  photoDirection: directionType
}

export type UploadPhotoData = {
  fileUrl: string
  title: string
  headImgUrl: string
  nickName: string
}

export enum ListType {
  Time = 'time',
  Like = 'like',
}

export enum directionType {
  Vertical = 'vertical',
  Horizontal = 'horizontal',
}
