<script setup lang="ts"></script>

<template>
  <router-view v-slot="{ Component, route }">
    <component :is="Component" :key="route.meta.usePathKey ? route.path : undefined" />
  </router-view>
</template>

<script lang="ts" setup></script>

<style lang="scss">
@import '../../styles/normalize.scss';
body {
  background-repeat: no-repeat;
  width: 100%;
  overflow-x: hidden;
}
.difalt-share {
  position: absolute;
  left: 0;
  top: -128px;
  visibility: hidden;
}
#app {
  width: 100%;
  text-align: center;
  overflow-x: hidden;
  line-height: 0;
  font-size: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
}
img {
  // 默认不可以长按触发扫一扫和下载
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -o-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}
</style>
