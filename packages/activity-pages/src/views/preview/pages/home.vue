<template>
  <focus-render v-if="focusFid" :focusFid="focusFid" @onLoginSucc="afterLogin" @onFocusConfigSucc="setFocus">
  </focus-render>
  <PreviewTool :focusRenderCtr="focusRenderCtr"> </PreviewTool>
</template>

<script setup lang="ts">
import { provide, readonly, ref, Ref, toRaw, computed, nextTick, watchEffect, watch, onMounted } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import { formatAmount } from '@/utils'
import PreviewTool from '../components/preview-tool'
const { modalStore, dynimicDataStore } = focusStore.stores
const { activityService, taskService, jumpService, mgmService, dynimicDataService } = focusServices

const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { fid } = locationHrefPared.query
const focusFid = fid
const focusRenderCtr: any = ref(null)
function afterLogin(data: any) {
  if (focusCore.isLogined) {
  }
}
function setFocus(fc: any) {
  focusRenderCtr.value = fc
  checkSpecailLink()
}

function checkSpecailLink() {
  let path = ''
  let host = ''
  // 页面和自定义模块的关系
  // 左边是自定义模块的名称，类似 例如<template #bigmgm_ranklist></template> 、<template #money_task_list></template>
  // 右边是使用模块的页面  /focus2/big_mgm/index.html 、 /foucs2/money_first_2/index.html

  const path2CustomModules: any = {
    bigmgm_ranklist: 'big_mgm',
    money_task_list: 'money_first_2',
    user_level: 'user_equity',
    family_walking_pannel: 'family',
    salary: 'salary_week',
    reward: 'company',
    company_tasks: 'company',
  }

  focusRenderCtr.value.allModulesData.forEach((i: any) => {
    if (i.moduleType === 'PlaceHolder') {
      const { data } = i
      Object.keys(path2CustomModules).find((_i: any) => {
        if (data.text === _i) {
          path = path2CustomModules[_i]
        }
      })
    }
  })
  console.log(path)
  const newPath = location.href.replace('/preview/', `/${path}/`)

  console.log('🚀 ~ checkSpecailLink ~ newPath:', newPath)
  if (path) {
    location.replace(newPath)
  }
}

onMounted(() => {})
</script>

<style lang="scss" scoped></style>
