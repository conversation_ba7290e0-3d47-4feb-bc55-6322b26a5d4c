import home from '../pages/home.vue'

import { createRouter, createWebHashHistory } from 'vue-router'
import { focusCore, focusServices } from '@focus/render'
import nzj from '../pages/nzj.vue'
const { loginService } = focusServices
const routes = [
  {
    path: '/',
    redirect: '/home',
  },
  {
    path: '/home',
    component: home,
    meta: {
      noNeedLogin: true,
    },
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes, // `routes: routes` 的缩写
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  },
})
router.beforeEach((to, from, next) => {
  console.log('🚀 ~ file: index.ts:76 ~ router.beforeEach ~ to:', to)
  focusCore.routerBeforeEach(to, from, next)
})

export default router
