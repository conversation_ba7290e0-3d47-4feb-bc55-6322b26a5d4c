<template>
  <previewTool :focus-render-ctr="focusRenderCtl" v-if="focusCore.isPreview && focusRenderCtl"></previewTool>

  <!-- 未开户要去开户 -->
  <div class="btn-challenge noaccount" v-show="showBtn && !hasAccount" @click="goApp"></div>
  <!-- 已开户未参与 -->
  <div class="btn-challenge noadding" v-show="showBtn && hasAccount && !isAdding" @click="startChallenge"></div>
  <!-- 已开户已参与 -->
  <div class="btn-challenge isadding" v-show="showBtn && hasAccount && isAdding" @click="jumpTrans"></div>

  <focus-render
    v-if="focusFid"
    :focusFid="focusFid"
    @onLoginSucc="afterLogin"
    @onFocusConfigSucc="setFocus"
    :slotMountedCb="slotMountedCb()"
  >
    <template #money_task_list>
      <!-- <div class="bg-cover"></div> -->
      <money-task-list
        :is-adding="isAdding"
        :task-config-list="taskConfigList"
        :canShowDailyProgress="canShowDailyProgress"
        :isNewUser="_isNewUser"
      ></money-task-list>
      <div class="time" v-if="dsDate">标准财富值数据统计时间: {{ dsDate }}</div>
    </template>
  </focus-render>

  <dialog-mask :show="false" @touchmove="false">
    <template #dialog_contain> <img class="dialog_data" src="../img/data_not_ready.png" alt="" /> </template>
  </dialog-mask>
</template>

<script setup lang="ts">
import { provide, readonly, ref, toRaw, computed, nextTick, watch, watchEffect } from 'vue'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'
import MoneyTaskList from '../components/MoneyTaskList.vue'
import moneyFirstService from '../service/service'
import previewTool from '@/views/preview/components/preview-tool.vue'

const { modalStore } = focusStore.stores
const { activityService, jumpService } = focusServices
const activeAid = ref('')
const curActiveAid = ref('')
provide('activeAid', activeAid)
const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { pageid, aid, fid } = locationHrefPared.query
const focusFid = fid || pageid
const showBtn = ref(false)
const isAdding = ref(false)
const isT0Adding = ref(false)
const focusRenderCtl: any = ref(null)
const dataNotReadyInMonthFirstDay = ref(false)
const taskConfigList = ref<any>([])
const lastDayAumNumber = ref(0)
const _isNewUser = ref(false)
const task3LastAddingDate = ref('')
const task4LastAddingDate = ref('')
const dsDate = ref('')
const hasAccount = ref(false)

provide('isT0Adding', isT0Adding)
const canShowDailyProgress = computed(() => {
  if (!isAdding.value) {
    return true
  } else {
    return !!lastDayAumNumber.value || (!isT0Adding.value && !lastDayAumNumber.value)
  }
})
// provide('canShowMonth', showMonthAum)

const showDataNotReady = computed(() => {
  return dataNotReadyInMonthFirstDay.value && showBtn.value
})

const userMaxPrice = ref({
  maxPriceAmount: 0,
})

const userMaxPriceAmount = computed(() => {
  return userMaxPrice.value.maxPriceAmount || '--'
})

watchEffect(async () => {
  console.log('activeAid.value', activeAid.value)
  if (activeAid.value) {
    if (focusCore.isLogined) {
      console.log('activeAid.value in true', activeAid.value)

      modalStore.loadingStart('checkAdding')
      activityService
        .checkWrInfoIsToday(activeAid.value)
        .then((res: { aid: number; isT0orT1: boolean; nowTime: any; addingTime: any }) => {
          console.log('🚀 ~ file: home.vue:56 ~ activityService.checkWrInfoIsToday ~ res:', res)
          modalStore.loadingEnd('checkAdding')
          isT0Adding.value = res.isT0orT1
          showBtn.value = true

          if (!res.aid) {
            //未参与活动
            isAdding.value = false
          } else {
            isAdding.value = true
          }
          getTaskList()
        })
    }

    focusCore.updateDynamicData('_custom_max_reward', userMaxPriceAmount.value, true)
  }
})

function afterLogin(data: any) {
  if (focusCore.isLogined) {
    hasAccount.value = focusCore.hasAccount
  }
}

function goApp() {
  modalStore.loadingStart('setChallenge_goApp')

  activityService.setWrInfo(activeAid.value).then((aid: number) => {
    if (aid) {
      // TODO: toast
      isAdding.value = true
      isT0Adding.value = true
    }
    setTimeout(() => {
      jumpService.commonUse.openAccount()

      modalStore.loadingEnd('setChallenge_goApp')
    }, 200)
  })
}

function jumpTrans() {
  jumpService.commonUse.transDialog()
}

function slotMountedCb() {
  console.log('>>>>>>>>>>>>>>>..money_task_list')
  return {
    money_task_list: (data: string[]) => {
      console.log('🚀 ~ slotMountedCb ~ data:', data)
      activeAid.value = data[0]
    },
  }
}

async function getTaskList() {
  if (curActiveAid.value) {
    return
  }
  if (activeAid.value) {
    curActiveAid.value = activeAid.value
    modalStore.loadingStart('getTaskProgress')
    moneyFirstService
      .getMoneyFirstTaskProgress(activeAid.value)
      .then((res: any) => {
        console.log('🚀 ~ .then ~ res:', res)
        const {
          configData,
          first_reg_day,
          todayIsMonthFristDay,
          dataReadyInMonthFirstDay,
          userIsAdding,
          newUserActivityUrl,
          isOverTopLevel,
          lastDayAumNumber,
          isNewUser,
          maxPriceAmount,

          dsDateFormat,
        } = res
        // 新手当月不能参加
        if (newUserActivityUrl) {
          jumpService.jump({ path: newUserActivityUrl }, true)
          return
        }
        _isNewUser.value = isNewUser
        if (isOverTopLevel) {
          modalStore.errorMaskContrl('over_top_level', ['您不在受邀名单，请前往微众银行App首页领福利参与其他热门活动'])
          return
        }
        if (configData.length) {
          taskConfigList.value = configData
        }
        if (!isNewUser && todayIsMonthFristDay && !dataReadyInMonthFirstDay) {
          dataNotReadyInMonthFirstDay.value = true
        }
        if (userIsAdding) {
          dsDate.value = dsDateFormat
        }
        userMaxPrice.value = {
          maxPriceAmount: maxPriceAmount,
        }
      })
      .catch(() => {
        modalStore.errorMaskContrl('getTaskProgress')
      })
      .finally(() => {
        modalStore.loadingEnd('getTaskProgress')
      })
  }
}

function setFocus(_focusRenderCtr: any) {
  console.log('🚀 ~ file: home.vue ~ line 25 ~ setFocus ~ data', _focusRenderCtr)
  focusRenderCtl.value = _focusRenderCtr
}

function startChallenge() {
  //TODO: loading
  modalStore.loadingStart('setChallenge')
  if (focusCore.hasAccount) {
    activityService.setWrInfo(activeAid.value).then((aid: number) => {
      if (aid) {
        // TODO: toast
        isAdding.value = true
        isT0Adding.value = true
      }
      modalStore.loadingEnd('setChallenge')
    })
  } else {
    jumpService.commonUse.openAccount()
  }
}
</script>

<style lang="scss" scoped>
.btn-challenge {
  width: 100%;
  height: 160px;
  position: fixed;
  z-index: 333;
  bottom: 0;
  left: 0;
  right: 0;
  background: url('../img/btn_noaccount.gif') no-repeat;
  background-size: 100% 100%;
  &:active {
    opacity: 0.7;
  }
  &.noadding {
    background-image: url('../img/btn_not_adding.gif');
  }
  &.isadding {
    background-image: url('../img/btn_has_addding.gif');
  }
}
.dialog_data {
  width: 438px;
  height: 464px;
}
.bg-cover {
  width: 750px;
  height: 218px;
  background-color: #ffe7bd;
  opacity: 0.6;
  margin: 0 auto;
  border-radius: 20px 20px 0 0;
  margin-bottom: -220px;
}
.time {
  font-size: 28px;
  color: #725d47;
  text-align: center;
  width: 100%;
  line-height: 1.5;
  background: #fff7ec;
}
</style>
