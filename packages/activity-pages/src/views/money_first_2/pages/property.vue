<template>
  <div class="property">
    <div class="month-list">
      <div class="item" v-for="(month, index) in monthList" :key="index">
        <div class="header">
          <p>{{ month.date }}</p>
          <p>
            月日均 <span class="money">{{ month.amountMonth }}</span
            >元
          </p>
        </div>
        <div class="content">
          <div class="date-item" v-for="(item, index) in month.dayList" :key="index">
            <p>{{ item.date }}</p>
            <p>
              <span class="money">{{ item.amount }}</span
              >元
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import moneyFirstService from '../service/service'

const monthList = ref<Array<any>>([])

getData()
function getData() {
  moneyFirstService.getPropertyHistoryList().then((res: any) => {
    console.log('🚀 ~ file: property.vue ~ line 196 ~ moneyFirstService.getPropertyHistoryList ~ res', res)
    monthList.value = res
  })

  // monthList.value = originData.monthList || []
  // const obj = {}
  // originData.monthList.forEach((i) => {
  //   const { date } = i
  //   const data = originData.dayList.filter((_i) => {
  //     console.log('🚀 ~ file: property.vue ~ line 158 ~ data ~ _i', _i)
  //     const reg = new RegExp(date)
  //     console.log('🚀 ~ file: property.vue ~ line 160 ~ data ~ reg', reg)
  //     return reg.test(_i.date)
  //   })
  //   obj[date] = data
  //   console.log('🚀 ~ file: property.vue ~ line 164 ~ originData.monthList.forEach ~ data', data)
  // })
  // dayList.value = obj
  // console.log('🚀 ~ file: property.vue ~ line 160 ~ getData ~ dayList', dayList)
  // selectMonth(monthList.value[0])
}
</script>

<style lang="scss" scoped>
.property {
  width: 100%;
  height: 100%;
  overflow: auto;
  .month-list {
    width: 100%;

    .money {
      color: #f05446;
      font-weight: normal;
    }
    .item {
      width: 100%;
      .header {
        height: 100px;
        width: 100%;

        background: #f8f8f8;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 30px;
        box-sizing: border-box;
        font-size: 28px;
        line-height: 1;
        color: #717884;
        font-weight: bold;
      }
      .content {
        width: 100%;
        background: #fff;

        .date-item {
          width: 690px;
          margin: 0 auto;
          border-bottom: 1px solid #f0f0f0;
          height: 80px;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 26px;
          line-height: 1;
          color: #867b6f;
        }
      }
    }
  }
}
</style>
