<template>
  <div class="reward-list">
    <div class="item" v-for="(item, index) in listData" :key="index" @click="clickReward(item)">
      <img class="icon" :src="item.awardUrl" />
      <div class="info">
        <div class="name">{{ item.awardName }}</div>
        <div class="desc">{{ item.awardDesc }}</div>
        <div class="time">{{ item.awardTime }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import moneyFirstService from '../service/service'
import { focusUtils, focusCore, focusStore, focusServices, DialogMask } from '@focus/render'

const { activityService, jumpService } = focusServices

const listData = ref<any>([])
const route = useRoute()
getData()
function getData() {
  console.log(route.query.rewardAids)
  const aids = [route.query.rewardAids]
  moneyFirstService.getAwardList({ aids }).then((res: any) => {
    const { rewardList = [] } = res
    listData.value = rewardList
  })
}

function clickReward(data: any) {
  console.log('🚀 ~ file: rewardList.vue ~ line 35 ~ clickReward ~ data', data)
  const { dataType } = data

  if (dataType === '1') {
    jumpService.commonUse.userPoint()
  } else if (dataType === '2') {
    jumpService.commonUse.couponList()
  }
}
</script>

<style lang="scss" scoped>
.reward-list {
  line-height: 1.5;
  width: 100%;
  min-height: 100vh;
  padding-top: 10px;
  background: #f9f9f9;
  .item {
    width: 690px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin: 0 auto;
    background: #fff;
    margin-top: 20px;

    .icon {
      width: 160px;
      height: 160px;
    }
    .info {
      flex: auto;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      flex-direction: column;
      .name {
        font-weight: bold;
        color: #425282;
        font-size: 28px;
      }
      .desc {
        font-size: 26px;
        color: #425282;
        margin-top: 10px;
        margin-bottom: 10px;
      }
      .time {
        font-size: 22px;
        color: #ccd0dc;
      }
    }
  }
}
</style>
