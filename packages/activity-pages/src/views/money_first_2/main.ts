BUILD_TEST &&
  import(/* webpackChunkName: "vconsole" */ 'vconsole').then((cls) => {
    const Cls = cls.default
    return new Cls()
  })
console.log('1111111')
import { createApp } from 'vue'
import App from './App.vue'
import focusRenderPlugin, { focusCore } from '@focus/render'
import '@focus/render/build-lib/focus-render.css'
import { createPinia } from 'pinia'
import router from './routes/index'
// if (BUILD_TEST) {
//   // 使用测试环境
//   focusCore.useTestMode()
// }
const app = createApp(App)
const pinia = createPinia()
app.use(pinia).use(focusRenderPlugin, { $pinia: pinia }).use(router)

app.mount('#app')
