import { focusStore, focusServices, focusCore, focusUtils } from '@focus/render'
console.log('🚀 ~ file: service.ts ~ line 2 ~ focusUtils', focusUtils)
const { dayjs, formatAmount } = focusUtils
const { mgmService, kvService } = focusServices

const cgis = {
  moneyFirstTaskProgress: {
    url: '/wm-htrserver/cop/hj/asset_reach/queryAssetProgressDetail',
    name: '标准财富值达标进度-已开户',
  },
  awardList: {
    url: '/wm-htrserver/cop/hj/query_user_award',
    name: '标准财富值达标奖励',
  },
  moneyFirstTaskProgressNoAccount: {
    url: '/wm-htrserver/cop/hj/asset_reach/queryAssetConfigWithoutOpen',
    name: '标准财富值达标进度-未开户',
  },
  checkRewardStatus: {
    name: '查看奖励发放情况',
    url: '/wm-htrserver/cop/hj/queryWealthActivityAward',
  },
  //
}

class MoneyFirstService {
  getProgressData(aid: string) {
    console.log('....focusCore,', focusCore.hasAccount)
    if (focusCore.hasAccount) {
      return focusCore.request(cgis.moneyFirstTaskProgress, { activityId: aid })
    }
    return new Promise((resolve) => {
      focusCore.request(cgis.moneyFirstTaskProgressNoAccount, { activityId: aid, productId: 'HJ' }).then((res: any) => {
        console.log('🚀 ~ file: service.ts:91 ~ MoneyFirstService ~ focusCore.request ~ res:', res)
        return resolve({
          ...res,
          assetProgressDetail: {
            remainingDays: res.remainDays || 0,
          },
        })
      })
    })
  }

  getMoneyFirstTaskProgress(aid: string) {
    return new Promise((resolve, reject) => {
      this.getProgressData(aid)
        .then(async (res: any) => {
          console.log('🚀 ~ file: service.ts:103 ~ MoneyFirstService ~ .then ~ res:', res)
          const {
            assetReachActivityConfig = {
              newUserActivityUrl: '',
              taskType: '',
            },
            assetProgressDetail = {
              // 这里的数据是由大数据推送的
              overchargeId: 0, //加码任务配置ID
              assetExtra: '{}',
              lastDayAum: '',
              curMonthAum: '',
              startlevelamt: null,
              userCurrentStatus: '2', // 用户当前状态  '1':新人；'2':非新人
              remainingDays: 0, //新手状态剩余天数
              overchargeLevelAmt: 0,
              userTomorrowStatus: null, //用户次日状态	1:新人用户，2:非新人用户，后台没值时给null
            },
            first_reg_day,
            rewardDays, // 新手状态剩余天数(后台计算)
            rewardStatus = '2', //用户当前状态 '1':新人；'2':非新人
            assetTagInfo = {
              currentMonthAvgAum: 0,
              fDate: 0,
            },
            assetOverChargeItem = {
              stallConfigPlus: '[]', //加码配置	json字符串
            },
            dsDate = '',
          } = res
          console.log('🚀 ~ file: service.ts ~ line 83 ~ MoneyFirstService ~ .then ~ res', res)
          console.log('🚀 ~ file: service.ts:131 ~ MoneyFirstService ~ .then ~ dsDate:', dsDate)
          const { stallConfigPlus = '[]' } = assetOverChargeItem || {}
          const { newUserActivityUrl = '', stallConfig = '[]', taskType = '1' } = assetReachActivityConfig || {}
          // console.log('🚀 ~ 任务类型 taskType:', taskType)
          const { assetExtra = '{}', startlevelamt = null, overchargeLevelAmt = 0 } = assetProgressDetail
          // 这里是默认4个任务
          // taskType 4 以后，都是按管理端处理了，这里
          let canUseTaskType = ['1', '2', '3', '4']
          let topAumLevel = 15000000

          if (taskType === '1') {
            canUseTaskType = ['1', '2']
            topAumLevel = 300000
          }
          if (taskType === '2') {
            canUseTaskType = ['1', '2']
            topAumLevel = 1000000
          }
          if (taskType === '3') {
            canUseTaskType = ['1', '2', '3']
            topAumLevel = 300000
          }

          // 0326版本更换字段 userCurrentStatus 为 rewardStatus
          let isNewUser = rewardStatus === '1'

          const priceScaling = 100 // 奖励缩放倍率，积分缩放100，京东卡缩放1

          // 未开户用户就是新人用户
          if (!focusCore.hasAccount) {
            isNewUser = true
          }
          // 0326版本，更换 remainingDays 为 rewardDays，用后台实时计算的新手到期
          const newUserCountTime = isNewUser ? Number(rewardDays) : 0

          const { conditionStore } = focusStore.stores
          conditionStore.setCondition('dc04', isNewUser ? 2 : 1)

          const curStartLevelAmt = startlevelamt === 0 ? 0 : !startlevelamt ? 0 : startlevelamt
          console.log('🚀 ~ 用户当前起始档位', curStartLevelAmt)

          console.log('🚀 ~ 用户是不是新人', isNewUser)

          console.log('🚀 ~ 新人状态倒计时', newUserCountTime)
          console.log(
            '🚀 ~ file: service.ts ~ line 84 ~ MoneyFirstService ~ .then ~ assetProgressDetail',
            assetProgressDetail
          )

          let taskPlusConfig: {
            stallAmount: number
            allPriceAmount: number
            data: {
              taskType: string
              amt: number
            }[]
          }[] = []

          try {
            taskPlusConfig = JSON.parse(stallConfigPlus) || []
          } catch (err) {
            console.log(err)
          }
          console.log('🚀 ~ file: service.ts:139 ~ MoneyFirstService ~ .then ~ taskPlusConfig:', taskPlusConfig)

          // 确定新手任务加码范围
          const taskPlusConfigObj: any = {}
          taskPlusConfig = taskPlusConfig
            .filter((i) => {
              if (isNewUser) {
                return true
              }
              return i.stallAmount <= overchargeLevelAmt
            })
            .map((i) => {
              // const list = i.data
              const list = i.data.filter((i) => canUseTaskType.indexOf(i.taskType) > -1)

              const allPriceAmount = list.reduce((sum, i) => {
                return sum + i.amt
              }, 0)
              return {
                ...i,
                data: list,
                allPriceAmount,
              }
            })
          taskPlusConfig.forEach((i) => {
            const { data, stallAmount, allPriceAmount } = i
            data.forEach((_i, index) => {
              if (_i.amt && Number(_i.amt)) {
                const countKey = `taskType_${_i.taskType}_${stallAmount}_amt`
                taskPlusConfigObj[countKey] = Number(_i.amt)
              }
            })
          })
          console.log(
            '🚀 ~ file: service.ts:216 ~ MoneyFirstService ~ data.forEach ~ taskPlusConfigObj:',
            taskPlusConfigObj
          )

          const originStallConfig: Array<{
            stallAmount: number
            allPriceAmount: number

            levelStatus: string
            dataList: Array<{
              taskType: string
              id: number
              taskTitle: string
              taskDone?: boolean
              data: {
                rewardCount: number
                rewardDesc: string
                rewardName: string
                rewardPic: string
                rewardScen: string
                rewardType: string
              }
            }>
          }> = JSON.parse(stallConfig || '[]') || []

          console.log('原始的配置档位信息。。，', originStallConfig)
          // 选出可展示的档位配置，档位
          const configList = originStallConfig
            .filter((item, index) => {
              return item.stallAmount > curStartLevelAmt && item.stallAmount <= topAumLevel
            })
            .map((i) => {
              // const dataList = i.dataList
              const dataList = i.dataList.filter((i) => canUseTaskType.indexOf(i.taskType) > -1)

              const allPriceAmount = dataList.reduce((sum, item) => sum + item.data.rewardCount, 0)
              return {
                ...i,
                dataList,
                allPriceAmount,
              }
            })
          console.log('🚀 ~ 可展示的配置', configList)

          let taskStatusList: Array<{
            completeProgress: string
            completeFlag: string
            taskID: string
            rewardScene: string
          }> = []

          try {
            const temp = JSON.parse(assetExtra || '{}')
            taskStatusList = (temp.processInfo && temp.processInfo.items) || []
          } catch (err) {
            console.log('🚀 ~ MoneyFirstService ~ .then ~ err:', err)
          }

          console.log('🚀 ~ MoneyFirstService ~ .then ~ taskStatusList:', taskStatusList)
          const taskStatusListObj: any = {}
          const sceneIds: string[] = []
          taskStatusList.forEach((i) => {
            const { completeProgress, taskID, completeFlag, rewardScene } = i
            sceneIds.push(rewardScene)
            const amount = taskID.split('_')[0]
            const taskType = taskID.split('_')[1]
            const key = `taskType_${taskType}_${amount}`
            let percent: string | number = ''
            if (completeProgress.indexOf('/') > -1) {
              percent =
                ((Number(completeProgress.split('/')[0]) / Number(completeProgress.split('/')[1])) * 100).toFixed(2) +
                '%'
            } else {
              percent = completeProgress + '%'
            }
            const text = completeProgress.indexOf('/') > -1 ? completeProgress : completeProgress + '%'
            taskStatusListObj[key] = {
              taskID,
              percent,
              text,
              status: completeFlag,
            }
          })

          console.log('🚀 ~ MoneyFirstService ~ .then ~ taskStatusListObj:', taskStatusListObj)

          let doneRawardTaskIds: any = []
          if (sceneIds.length) {
            try {
              doneRawardTaskIds = await this.getAwardStatus(aid, sceneIds)

              console.log('🚀 ~ MoneyFirstService ~ .then ~ doneRawardTaskIds:', doneRawardTaskIds)
            } catch (err) {
              console.log('🚀 ~ MoneyFirstService ~ .then ~ err:', err)
            }
          }

          const userIsAdding = taskStatusList.length
          const lastDayAum = assetProgressDetail.lastDayAum || 0
          const curMonthAum = assetProgressDetail.curMonthAum || assetTagInfo.currentMonthAvgAum || 0

          const lastDayAumNumber = Number(assetProgressDetail.lastDayAum || 0)
          const curMonthAumNumber = Number(assetProgressDetail.curMonthAum || 0)
          let maxPriceAmount = 0 // 有4个任务时，所有档位加起来的奖励

          const configData = configList.map((i, index) => {
            const { stallAmount, dataList, allPriceAmount = 0 } = i

            const noWan = Number(stallAmount) < 10000
            const amountFormate = noWan ? stallAmount : Number(stallAmount) / 10000
            const unit = !noWan ? '万' : ''
            //当前档位的加码总奖励
            const targetNewUser: any = taskPlusConfig.find((_i) => _i.stallAmount === stallAmount) || {}

            // 当前单个档位的总奖励，
            let curStallAllPriceAmount = ((allPriceAmount || 0) + (targetNewUser.allPriceAmount || 0)) / priceScaling

            // 累计到当前档位的总奖励，
            let curStallCountingAllPriceAmount = 0

            for (let i = 0; i < index + 1; i++) {
              // 需要到当前档位为止
              const target = configList[i] || {}
              // console.log('🚀 ~ file: service.ts:295 ~ MoneyFirstService ~ configData ~ target:', target)

              const targetNewUser: any = taskPlusConfig.find((_i) => _i.stallAmount === target.stallAmount) || {}

              curStallCountingAllPriceAmount +=
                Number(target.allPriceAmount || 0) + Number(targetNewUser.allPriceAmount || 0)

              if (index === configList.length - 1) {
                maxPriceAmount = curStallCountingAllPriceAmount
              }
            }
            curStallCountingAllPriceAmount = curStallCountingAllPriceAmount / priceScaling
            maxPriceAmount = Math.floor(maxPriceAmount / priceScaling)
            const taskList = dataList.map((i) => {
              const { taskTitle, id, data } = i
              const taskStatusKeyName = `taskType_${id}_${stallAmount}` // 档位完成状态
              const newUserRewardCountKey = `taskType_${id}_${stallAmount}_amt` // 加码档位名称
              const taskData = taskStatusListObj[taskStatusKeyName] || {}
              const taskId = taskData.taskID || ''
              const taskStatus = doneRawardTaskIds.indexOf(taskId) > -1 ? '2' : taskData.status || '0'
              // console.log('🚀 ~ MoneyFirstService ~ taskList ~ taskStatus:', taskStatus)
              let isPlusTask = false // 这个任务是否加码
              const progressPercent = taskData.percent || '0%'
              let progressText = taskData.text || (id === 3 ? '0/3' : id === 4 ? '0/6' : '0%')
              const propertyText =
                taskStatus === '0' ? (id === 1 ? `昨日标准财富值 ${lastDayAum}` : `当月标准财富值 ${curMonthAum}`) : ''
              // 任务2隐藏进度
              if (id === 2) {
                progressText = ''
              }

              let newUserRewardName = ''
              let newUserRewardCount = 0
              let rewardMore = 0
              if (taskPlusConfigObj && taskPlusConfigObj[newUserRewardCountKey]) {
                const total = taskPlusConfigObj[newUserRewardCountKey] + data.rewardCount
                newUserRewardName = total + `${data.rewardType === '0' ? '积分' : '元京东卡'}`
                newUserRewardCount = taskPlusConfigObj[newUserRewardCountKey]
                isPlusTask = true
              }

              return {
                taskId: id,
                taskTitle: `${taskTitle}`,
                amountFormate,
                unit,
                rewardName: data.rewardName,
                rewardIcon: data.rewardPic,
                rewardScene: data.rewardScen,
                rewardDesc: data.rewardDesc,
                rewardMore,
                rewardTypeName: data.rewardType === '0' ? '积分' : '京东卡',
                taskDone: taskStatus !== '0',
                taskStatus,
                propertyText,
                progressPercent,
                progressText,

                // 新户数据
                isPlusTask,
                isNewUser,
                newUserRewardName,
                newUserRewardCount,
                newUserCountTime,
              }
            })
            const taskAllDone = !taskList.some((i) => !i.taskDone)

            return {
              amount: Number(stallAmount),
              noWan,
              amountFormate,
              unit,
              taskList,
              taskAllDone,
              userIsAdding,
              monthAumFromTag: formatAmount(assetTagInfo.currentMonthAvgAum || 0, true),
              lastDayAumNumber,

              curStallCountingAllPriceAmount,
              curStallAllPriceAmount,
            }
          })

          focusCore.getNowTime().then((now: any) => {
            const todayIsMonthFristDay = now.format('D').toString() === '1'
            const userIsRegInThisMonth =
              !first_reg_day ||
              (first_reg_day &&
                dayjs(first_reg_day).isSame(now, 'month') &&
                first_reg_day &&
                dayjs(first_reg_day).isSame(now, 'year'))

            console.log(
              '🚀 ~ file: service.ts ~ line 165 ~ MoneyFirstService ~ focusCore.getNowTime ~ first_reg_day',
              first_reg_day
            )
            console.log(
              '🚀 ~ file: service.ts ~ line 165 ~ MoneyFirstService ~ focusCore.getNowTime ~ userIsRegInThisMonth',
              userIsRegInThisMonth
            )
            // const dataReadyInMonthFirstDay =  dsDate && dayjs(dsDate).add(1, 'd').format('D') === '1' // 数据日期 在 1号的前一天

            console.log('🚀 ~ 原始的 startlevelamt', startlevelamt)
            const dataReadyInMonthFirstDay = startlevelamt === null || startlevelamt === '' ? false : true // 只要 startlevelamt 不为 null 就说明数据已更新
            console.log(
              '🚀 ~ file: service.ts ~ line 285 ~ MoneyFirstService ~ focusCore.getNowTime ~ dataReadyInMonthFirstDay',
              dataReadyInMonthFirstDay
            )

            resolve({
              configData,
              todayIsMonthFristDay,
              dataReadyInMonthFirstDay,
              userIsAdding,
              isOverTopLevel: !configList.length,
              isNewUser,
              maxPriceAmount,
              dsDateFormat: dsDate ? dayjs(dsDate).format('YYYY-MM-DD') : '暂无数据',
            })
          })
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: service.ts ~ line 172 ~ MoneyFirstService ~ focusCore.request ~ err', err)
          reject()
        })
    })
  }

  getAwardList(params: { aids?: any[]; batchNos?: any[] }) {
    const _aid = params.aids && params.aids.join(',')
    const _batchNos = params.batchNos && params.batchNos.join(',')
    return new Promise((resolve) => {
      focusCore.request(cgis.awardList, { activityId: _aid, batchNo: _batchNos }).then((res: any) => {
        console.log('🚀 ~ file: service.ts ~ line 39 ~ MoneyFirstService ~ focusCore.request ~ res', res)
        // activityId: 8728
        // awardDesc: "积分ss"
        // awardName: "积分"
        // awardTime: 1675249608000
        // awardUrl: "http://www.baidu.com"
        // batchNo: "10001820221208000100000002"
        // bizNo: "2212080KA02596RollTkCw9Ho9bixAcL"
        // cardNo: "6236330070680815808"
        // createTime: 1674376887000
        // dataStatus: "1"
        // dataType: "1"
        // ecifNo: "0999960255224459"
        // id: 2
        // issueSuccNumber: 19
        // lastDataStatusDesc: "奖励发放成功"
        // lastMsgStatusDesc: "发送短信消息ecif或手机号为空|微信账户信息open为空|或者没有配置"
        // msgStatus: "2"
        // orderId: "2212080KA02419Ee5loXEDNoePxZ62YK"
        // productId: "HJ"
        // showStatus: "0"
        // transScenarioId: 100552
        // updateTime: 1675336584000
        const { hjUserAwardList = [] } = res
        const batchNos: string[] = []
        const rewardList = hjUserAwardList.map(
          (i: {
            awardNameGroup: string
            awardDesc: string
            awardTime: number
            awardUrl: string
            batchNo: string
            dataType: string
          }) => {
            const { awardDesc, awardNameGroup, awardTime, awardUrl, batchNo, dataType } = i
            batchNos.push(batchNo)
            return {
              awardDesc,
              awardName: awardNameGroup,
              awardTime: dayjs(awardTime).format('YYYY年MM月DD日 HH:mm发放'),
              awardUrl,
              dataType,
            }
          }
        )
        resolve({ rewardList, batchNos })
      })
    })
  }

  getAwardStatus(aid: string, transScenarioIdList: string[]) {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.checkRewardStatus, {
          activityId: aid,
          transScenarioIdList: Array.from(new Set(transScenarioIdList)),
        })
        .then((res: any) => {
          console.log('🚀 ~ MoneyFirstService ~ .then ~ res:', res)
          const { subBatchNoList = [] } = res
          return resolve(subBatchNoList)
        })
        .catch((err: any) => {
          console.log('🚀 ~ MoneyFirstService ~ returnnewPromise ~ err:', err)
          return resolve([])
        })
    })
  }
}
export default new MoneyFirstService()
