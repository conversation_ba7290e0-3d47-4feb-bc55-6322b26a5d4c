import { focusStore, focusServices, focusCore, focusUtils } from '@focus/render'
console.log('🚀 ~ file: service.ts ~ line 2 ~ focusUtils', focusUtils)
const { entitiestoUtf16, dayjs } = focusUtils
const { mgmService, kvService, dynimicDataService } = focusServices
const cgis = {
  actInfo: {
    url: '/wm-htrserver/cop/hj/auth_enterprise/queryPayWeekConfig',
    name: '获取活动信息',
  },
}

class Service {
  rewardList: { time: string; point: number; newAum: number; companyName: string }[] = []
  _lastMonthAllPoint: number | null = null
  get rewardListData() {
    return this.rewardList
  }
  get lastMonthAllPoint() {
    return this._lastMonthAllPoint
  }
  queryActInfo(): Promise<{
    compId: number
    userPoint?: number
    diffMaxAumInSalaryWeek?: number
    diffPerAmount?: number
    lastDayDate?: string
    lastDayNewAum?: number
    lastDayRewardPoint?: number
    lastMonthAum?: number
    maxRewardPointInSalaryWeek?: number
    rewardEndDateUserPoint?: number
    rewardList?: { time: string; point: number; newAum: number; companyName: string }[]

    companyIsChange?: boolean
    dayPercent?: string
    lastDayNewAumIsOverPerAmount?: boolean
    lastDayRewardPointIsOverWeekMax?: boolean
    lastDayRewardPointIsOverPerMax?: boolean

    isExpiredSalaryWeek?: boolean
    isPointOverTarget?: boolean
    isNotFirstDate?: boolean
    config?: {
      salaryEndTime: string
      perAmount: string
      perPoint: string
      dailyPointMax: string
      salaryStartTime: string
      rewardEndTime: string
      nextSalaryStartTime: string
      nextSalaryEndTime: string
      compLogo: string
    }
    salaryWeekFullTime?: string
  }> {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.actInfo)
        .then((res: any) => {
          console.log('🚀 ~ file: service.ts:19 ~ Service ~ focusCore.request ~ res:', res)
          const { ret_code, salaryConfig = {}, id = 0, icon = '', activityId = 0 } = res

          if (!/0000$/.test(res.ret_code) || !activityId) {
            return resolve({
              compId: 0,
            })
          }
          const {
            dailyPointMax,
            lastMonthAvarageEndTime,
            lastMonthAvarageStartTime,
            nextSalaryEndTime,
            nextSalaryStartTime,
            perAmount,
            perPoint,
            rewardEndTime,
            rewardStartTime,
            salaryEndTime,
            salaryStartTime,
          } = salaryConfig

          dynimicDataService
            .queryActivityDataPushRecord(activityId, 107)
            .then((dynimicData: any) => {
              console.log('🚀 ~ file: service.ts:85 ~ Service ~ .then ~ dynimicData:', dynimicData)
              // const d = {
              //   companyId: 2,
              //   lastMonthAum: 0,
              //   lastDayDate: '2023-09-23',
              //   lastDayNewAum: 50000.05,
              //   diffPerAmount: 0,
              //   lastDayRewardPoint: 100,
              //   userPoint: 1500,
              //   maxRewardPointInSalaryWeek: 200,
              //   diffMaxAumInSalaryWeek: 49999.95,
              //   rewardEndDateUserPoint: 1500,
              //   rewardList: [
              //     { time: '2023年09月13日', newAum: 150000, point: 200 },
              //     { time: '2023年09月14日', newAum: 50000.05, point: 100 },
              //     { time: '2023年09月15日', newAum: 50000.05, point: 100 },
              //     { time: '2023年09月16日', newAum: 50000.05, point: 100 },
              //     { time: '2023年09月17日', newAum: 50000.05, point: 100 },
              //     { time: '2023年09月18日', newAum: 50000.05, point: 100 },
              //     { time: '2023年09月19日', newAum: 50000.05, point: 100 },
              //     { time: '2023年09月20日', newAum: 50000.05, point: 100 },
              //     { time: '2023年09月21日', newAum: 150000, point: 200 },
              //     { time: '2023年09月22日', newAum: 50000.05, point: 100 },
              //     { time: '2023年09月23日', newAum: 50000.05, point: 100 },
              //     { time: '2023年09月24日', newAum: 50000.05, point: 100 },
              //     { time: '2023年09月25日', newAum: 50000.05, point: 100 },
              //   ],
              //   dsDate: '2023-09-23',
              // }
              const {
                companyId = 0,
                diffMaxAumInSalaryWeek = 0,
                diffPerAmount = 0,
                lastDayDate = '',
                lastDayNewAum = 0,
                lastDayRewardPoint = 0,
                lastMonthAum = '--',
                maxRewardPointInSalaryWeek = 0,
                rewardEndDateUserPoint = 0,
                rewardList = [],
                userPoint = '--',
                dsDate = '--',
                lastMonthAumUseCruDate = '--',
              } = dynimicData
              const trueToday = dayjs()
              const today = dsDate && dsDate !== '--' ? dayjs(dsDate).add(1, 'day') : dayjs()
              const allDays = dayjs(rewardEndTime).diff(dayjs(rewardStartTime), 'day')
              // 有了推数数据后，经过的天数
              const passDays = today.diff(dayjs(rewardStartTime), 'day')
              console.log('🚀 ~ file: service.ts:61 ~ Service ~ .then ~ passDays:', passDays)
              console.log('🚀 ~ file: service.ts:60 ~ Service ~ .then ~ allDays:', allDays)
              const dayPercent = (passDays >= allDays ? 100 : (passDays / allDays) * 100) + '%'

              // 今天是发薪周首天
              const isFirstDate = !!(rewardStartTime && trueToday.diff(dayjs(rewardStartTime), 'day') === 0)

              const isNotFirstDate = !isFirstDate
              console.log('🚀 ~ file: service.ts:64 ~ Service ~ .then ~ dayPercent:', dayPercent)
              // 发薪周是否已结束
              const isExpiredSalaryWeek = today.isAfter(dayjs(salaryEndTime))
              // 是否达到了单日奖励上限（1.发薪周内，单日奖励积分大于登录配置上限；2.发薪周结束，单日奖励积分大于等于【发薪周内奖励峰值】）
              let isPointOverTarget = false
              if (!isExpiredSalaryWeek) {
                // 在发薪周内
                isPointOverTarget = dailyPointMax ? lastDayRewardPoint >= dailyPointMax : false
              } else {
                // 不在发薪周内
                isPointOverTarget = maxRewardPointInSalaryWeek
                  ? lastDayRewardPoint >= maxRewardPointInSalaryWeek
                  : false
              }

              const salaryWeekFullTime = `${dayjs(salaryStartTime).format('YYYY年MM月DD日')}-${dayjs(
                salaryEndTime
              ).format('MM月DD日')}`
              const _lastDayDate = dayjs(`${lastDayDate} 00:00:00`).format('M月DD日')

              console.log('>>>>>>>>>>>>>>>>>>')
              console.log(dayjs(`${lastDayDate} 00:00:00`).add(1, 'day').valueOf() === dayjs(salaryStartTime).valueOf())
              console.log(dayjs(`${lastDayDate} 00:00:00`).add(1, 'day').valueOf())
              console.log(dayjs(salaryStartTime).valueOf())
              // 今天已经推数拉！
              const todayIsPushData = dsDate && dayjs(dsDate).add(1, 'day').diff(trueToday, 'day') === 0
              const _lastMonthAllPoint = userPoint

              console.log('🚀 ~ file: service.ts:154 ~ Service ~ .then ~ _lastMonthAllPoint:', _lastMonthAllPoint)
              console.log(lastDayRewardPoint >= dailyPointMax)
              console.log(lastDayRewardPoint)
              console.log(dailyPointMax)
              this._lastMonthAllPoint = isFirstDate ? _lastMonthAllPoint : null

              const result = {
                compId: id,
                userPoint,
                diffMaxAumInSalaryWeek,
                diffPerAmount,
                lastDayDate: _lastDayDate,
                lastDayNewAum,
                lastDayRewardPoint,
                lastMonthAum,
                showLastMonthAum: isFirstDate ? (todayIsPushData ? lastMonthAumUseCruDate : '--') : lastMonthAum,
                maxRewardPointInSalaryWeek,
                rewardEndDateUserPoint,
                rewardList,
                companyIsChange: !!companyId && companyId !== id,
                dayPercent,
                lastDayNewAumIsOverPerAmount: lastDayNewAum >= perAmount,
                lastDayRewardPointIsOverWeekMax:
                  !!lastDayRewardPoint && lastDayRewardPoint >= maxRewardPointInSalaryWeek,
                lastDayRewardPointIsOverPerMax: lastDayRewardPoint >= dailyPointMax,
                isExpiredSalaryWeek,
                isPointOverTarget,
                isNotFirstDate,
                config: {
                  perAmount,
                  perPoint,
                  dailyPointMax,
                  salaryStartTime: dayjs(salaryStartTime).format('M月DD日'),
                  salaryEndTime: dayjs(salaryEndTime).format('M月DD日'),
                  rewardEndTime: dayjs(rewardEndTime).format('M月DD日'),
                  nextSalaryStartTime: dayjs(nextSalaryStartTime).format('YYYY年MM月DD日'),
                  nextSalaryEndTime: dayjs(nextSalaryEndTime).format('YYYY年MM月DD日'),
                  compLogo: icon,
                },
                salaryWeekFullTime,
                lastMonthAllPoint: _lastMonthAllPoint,
              }
              this.rewardList = rewardList

              return resolve(result)
            })
            .catch(() => {
              return resolve({
                compId: 0,
              })
            })
        })
        .catch((err: any) => {
          return resolve({
            compId: 0,
          })
        })
    })
  }
}

export default new Service()
