<template>
  <div class="reward">
    <div class="contain">
      <img src="../img/title_reward.png" alt="" class="header-img" />
      <div class="list">
        <div class="item header">
          <div class="time">时间</div>
          <div class="company-name">企业简称</div>
          <div class="money">新增资金</div>
          <div class="point">单日积分</div>
        </div>
        <div class="item" v-for="(item, index) in listData" :key="index">
          <div class="time">{{ item.time }}</div>
          <div class="company-name">
            <div>{{ item.companyName }}</div>
          </div>
          <div class="money">{{ formatAmount(item.newAum, true) }}</div>
          <div class="point">{{ item.point }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { focusCore, focusStore, focusServices, Mask, focusUtils } from '@focus/render'
import { onMounted, provide, ref, computed, watchEffect, reactive } from 'vue'
import { useRouter } from 'vue-router'
import service from '../service/service'
const { formatAmount } = focusUtils
const listData = service.rewardListData
const lastMonthAllPoint = service.lastMonthAllPoint
</script>

<style lang="scss" scoped>
.reward {
  width: 100%;
  height: 100%;
  min-height: 100vh;
  background: #ffdd8d;
  padding-top: 30px;
  .contain {
    background: #ffffff;
    border-radius: 20px;
    padding-top: 26px;
    padding-bottom: 40px;
    width: 700px;
    margin: 0 auto;

    .last-month {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-size: 28px;
      color: #9b4722;
      line-height: 28px;
      font-weight: 500;
      padding-left: 24px;
      margin-bottom: 10px;
      span {
        color: #ff2121;
      }
      .icon {
        width: 32px;
        height: 32px;
        margin-right: 12px;
      }
    }
    .header-img {
      width: 654px;
      height: 66px;
      margin: 0 auto;
    }
    .list {
      width: 654px;
      background: #ffffff;
      border: 2px solid #ffdebc;
      border-radius: 16px;
      margin: 0 auto;
      margin-top: 10px;
      overflow: hidden;
      .item {
        flex: auto;
        font-size: 22px;
        color: #75706e;
        text-align: center;
        line-height: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;

        > div {
          width: 100%;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #ffdebc;
          border-top: none;
        }

        .point {
          color: #f2a322;
          font-weight: 600;
          border-right: none;
          border-left: none;
        }
        .time {
          border-left: none;
          border-right: none;
          width: 184px;
          flex: none;
        }
        .company-name {
          overflow: hidden;
          border-right: none;

          div {
            width: 100%;
            height: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 48px;
          }
        }
        &.header {
          background: linear-gradient(to right, #f6cf79, #f29f39);
          height: 68px;
          color: #fff;
          font-weight: bold;
          > div {
            height: 100%;
          }
          .point {
            color: #fff;
          }
        }
        &:last-child,
        &:first-child {
          > div {
            border-bottom: none;
          }
        }
      }
    }
  }
}
</style>
