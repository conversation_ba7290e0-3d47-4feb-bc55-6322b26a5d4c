<template>
  <div class="salary-week">
    <!-- 企业正在替换 -->
    <div class="company-change" v-if="salaryData.companyIsChange">
      <img src="../img/bg_change.png" />
    </div>

    <div class="comp-header" v-if="isRegister && !salaryData.companyIsChange">
      <p>认证企业</p>
      <img class="logo" :src="salaryConfig.compLogo" />
    </div>

    <Transition>
      <!-- 未认证 -->
      <focus-render
        v-if="!isRegister && !salaryData.companyIsChange"
        :focusFid="fidNotRegister"
        @onLoginSucc="afterLoginSucc"
        @onFocusConfigSucc="setFocus"
      >
      </focus-render>

      <!-- 已认证 -->
      <focus-render
        v-if="isRegister && !salaryData.companyIsChange"
        :focusFid="fidRegister"
        :slotMountedCb="slotMountedCb()"
        @onFocusConfigSucc="setFocus"
      >
        <template #salary>
          <div class="salary-save-money" v-if="salaryData.isNotFirstDate && salaryData.userPoint !== '--'">
            <!-- 发薪周内未达到过配置的单位金额 且 发薪周还没结束 -->
            <template v-if="!salaryData.userPoint && !salaryData.isExpiredSalaryWeek">
              <p class="title">继续入金，实现薪水增值</p>
              <p class="desc">
                新增资金<span class="red">{{ formatAmount(salaryData.lastDayNewAum) }}</span
                >元，请在<span class="red">{{ salaryConfig.salaryEndTime }}</span
                >前再入金<span class="red">{{ formatAmount(salaryData.diffPerAmount) }}</span
                >元及以上，赢每日积分奖励
              </p>
            </template>
            <!-- 发薪周内未达到过配置的单位金额 且 已经今天过了发薪周 -->

            <template v-if="!salaryData.userPoint && salaryData.isExpiredSalaryWeek">
              <p class="title">本月发薪周活动已结束</p>
              <p class="desc" style="text-align: center">本月活动已结束，请下个月再来吧！</p>
              <img src="../img/icon_timeout.png" alt="" class="icon-timeout" />
              <p class="next">
                下个月发薪周:{{ salaryConfig.nextSalaryStartTime }}-{{ salaryConfig.nextSalaryEndTime }}
              </p>
            </template>

            <!-- 发薪周内达到过配置的单位金额 -->
            <div class="desc-wrap" v-if="salaryData.userPoint">
              <p class="title haspoint">
                累计已获得<span class="red">{{ salaryData.userPoint }}</span
                >预估积分
              </p>

              <!-- 描述文案——发薪周内获得过奖励积分 且 昨日奖励积分大于等于发薪周峰值 但小于 设置的峰值-->
              <p class="desc haspoint" v-if="salaryData.lastDayRewardPoint && salaryData.userPoint">
                保持至{{ salaryConfig.rewardEndTime }}，总共可获得<span class="red">{{
                  salaryData.rewardEndDateUserPoint
                }}</span
                >积分
              </p>
              <!-- end -->

              <p
                class="desc haspoint"
                v-if="
                  !salaryData.isExpiredSalaryWeek &&
                  salaryData.lastDayRewardPoint &&
                  salaryData.lastDayRewardPointIsOverWeekMax &&
                  !salaryData.lastDayRewardPointIsOverPerMax
                "
              >
                继续入金赢更多积分奖励
              </p>
              <!-- end -->

              <!-- 描述文案——发薪周内获得过奖励积分 且 昨日奖励积分大于0小于发薪周内积分奖励峰值 -->
              <p
                class="desc haspoint"
                v-if="salaryData.lastDayRewardPoint && !salaryData.lastDayRewardPointIsOverWeekMax"
              >
                再入金<span class="red">{{ formatAmount(salaryData.diffMaxAumInSalaryWeek) }}</span
                >元每日可提升至{{ salaryData.maxRewardPointInSalaryWeek }}积分
              </p>
              <!-- end -->

              <!--  描述文案——发薪周内获得过奖励积分 且 昨日奖励积分为0）-->
              <p class="desc haspoint" v-if="!salaryData.lastDayRewardPoint">
                暂无每日奖励，再入金<span class="red">{{ formatAmount(salaryData.diffPerAmount) }}</span
                >元每日可得{{ salaryConfig.perPoint }}积分
              </p>
              <!-- end -->

              <img src="../img/icon_reward.png" alt="" class="icon" />
            </div>
            <!-- 时间进度条 -->
            <div class="progress" v-if="salaryData.userPoint">
              <div class="bar-wrap">
                <div class="bar" :style="{ width: salaryData.dayPercent }"></div>
              </div>
              <p class="time">
                {{ salaryConfig.rewardEndTime }}
              </p>
            </div>
            <!-- 入金按钮 只要没达到奖励上限，都展示 -->
            <div
              class="btn-trans"
              v-if="
                (!salaryData.isExpiredSalaryWeek && !salaryData.isPointOverTarget) ||
                (salaryData.isExpiredSalaryWeek &&
                  salaryData.maxRewardPointInSalaryWeek &&
                  !salaryData.isPointOverTarget)
              "
              @click="jumpTrans"
            >
              继续入金
            </div>

            <!-- 昨日奖励情况 -->
            <div class="daily-reward" v-if="!(salaryData.isExpiredSalaryWeek && !salaryData.userPoint)">
              {{ salaryData.lastDayDate }}比上期日均资产新增{{ formatAmount(salaryData.lastDayNewAum) }}元，{{
                salaryData.lastDayRewardPoint ? `获${salaryData.lastDayRewardPoint}积分` : '未获积分'
              }}
              <div class="btn-reward-desc" @click="jumpReward">详情 ></div>
            </div>
          </div>

          <div class="salary-rules">
            <p>发薪周期间，浏览活动页面完成理财知识学习的企业认证用户，每天日终资产较上期日均资产：</p>
            <div class="last-month-aum">
              <img src="../img/icon_money.png" alt="" class="icon" />
              <p>
                上期日均资产(元):
                <span>{{ formatAmount(salaryData.showLastMonthAum) }}</span>
              </p>
            </div>
            <div class="week-content">
              <div class="left">
                <img src="../img/icon_inweek.png" alt="" class="icon" />
                <p class="desc">
                  每新增<span>{{ formatAmount(salaryConfig.perAmount) }}</span
                  >元
                </p>
              </div>
              <div class="right">
                <img src="../img/icon_outweek.png" alt="" class="icon" />
                <p class="desc">
                  每天奖励<span>{{ salaryConfig.perPoint }}</span
                  >积分
                </p>
              </div>
              <div class="tip">
                <img src="../img/icon_tip.png" alt="" class="icon" />
                小贴士: 每天新增资金最高奖励{{ salaryConfig.dailyPointMax }}积分
              </div>
            </div>
            <!-- 发薪周活动信息 -->
            <div class="act-info">
              <div class="time-list">
                <span>{{ salaryConfig.salaryStartTime }}</span>
                <span>{{ salaryConfig.salaryEndTime }}</span>
                <span>{{ salaryConfig.rewardEndTime }}</span>
              </div>
            </div>
            <p class="act-desc">*每天积分奖励不超过发薪周单日积分最大值，奖励将在奖励期时间结束后5个工作日内发放</p>
            <!-- <img class="example" src="../img/example.png" /> -->
          </div>
        </template>
      </focus-render>
    </Transition>
  </div>
  <previewTool :focus-render-ctr="focusRenderCtl" v-if="focusCore.isPreview && focusRenderCtl"></previewTool>
</template>

<script setup lang="ts">
import { focusCore, focusStore, focusServices, Mask, focusUtils } from '@focus/render'
import { onMounted, provide, ref, computed, watchEffect, reactive, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import service from '../service/service'
import previewTool from '@/views/preview/components/preview-tool.vue'

const router = useRouter()
const { jumpService } = focusServices
const { modalStore, dynimicDataStore } = focusStore.stores
const { formatAmount } = focusUtils
const isRegister = ref(false)
const btnPreDataModuleId = ref(0)
const dataIsReady = ref(false)
const _pushDataIsFirstDate = ref(false)
const focusRenderCtl = ref(null)

let fidNotRegister = 2451
let fidRegister = 2450
if (BUILD_TEST) {
  fidNotRegister = 2185
  fidRegister = 2186
}

const salaryData = ref<{
  compId?: number
  diffMaxAumInSalaryWeek?: string
  diffPerAmount?: string
  lastDayDate?: string // 数据计算日期：产出数据是用哪一天的来计算的
  lastDayNewAum?: string
  lastDayRewardPoint?: string
  lastMonthAum?: number // 上月日均资产
  maxRewardPointInSalaryWeek?: number
  rewardEndDateUserPoint?: number

  companyIsChange?: string

  lastDayNewAumIsOverPerAmount?: string
  lastDayRewardPointIsOverWeekMax?: string
  lastDayRewardPointIsOverPerMax?: string
  isExpiredSalaryWeek?: string
  isPointOverTarget?: string
  dayPercent?: string
  userPoint?: string
  isNotFirstDate?: string
  showLastMonthAum?: string
}>({
  // lastMonthAum: 10000,
  // lastDayDate: '9月5日',
  // lastDayNewAum: 1000, // 计算日期的 新增的日终资产
  // diffPerAmount: 4000, // 计算日期的 新增的日终资产 与 配置的目标值（5000） 的差值
  // lastDayRewardPoint: 0, // 计算日期 当天奖励积分
  // userPoint: 20, // 截止计算日期 累计的预估奖励
  // maxRewardPointInSalaryWeek: 150, // 计算日期的  发薪周内的 单日奖励积分峰值
  // diffMaxAumInSalaryWeek: 6000, // 计算日期的 新增的日终资产 与 发薪周内新增的日终资产峰值 的差值
  // rewardEndDateUserPoint: 1223, // 按当天新增的日终计算，保持到奖励期结束，预估获得的积分 （当天获得积分 lastDayRewardPoint  *剩余天数）
  // rewardList: [
  //   // 奖励记录
  //   {
  //     time: '9月5日', // 时间
  //     newAum: '3,000', // 新增资金
  //     point: '20', // 单日奖励积分
  //   },
  //   {
  //     time: '9月6日',
  //     newAum: '3,000',
  //     point: '20',
  //   },
  // ],
  // isPointOverTarget: false, // 是否达到了单日奖励上限（1.发薪周内，单日奖励积分大于登录配置上限；2.发薪周结束，单日奖励积分大于等于【发薪周内奖励峰值】）
  // isExpiredSalaryWeek: false, // 奖励期是否已结束
  // companyIsChange: true,
  // lastDayNewAumIsOverPerAmount: false,
  // lastDayRewardPointIsOverWeekMax: false,
  // lastDayRewardPointIsOverPerMax: false,
  // dayPercent: '35%',
  // showPannel: true,
})

const salaryConfig = ref<{
  salaryEndTime?: string
  perAmount?: string
  perPoint?: number
  dailyPointMax?: number
  salaryStartTime?: string
  rewardEndTime?: string
  nextSalaryStartTime?: string
  nextSalaryEndTime?: string
  compLogo?: string
}>({
  // salaryEndTime: '9月11日',
  // perAmount: '5000',
  // perPoint: 10,
  // dailyPointMax: 200,
  // salaryStartTime: '9月5日',
  // rewardEndTime: '9月27日',
  // nextSalaryStartTime: '9月27日',
  // nextSalaryEndTime: '10月4日',
  // compLogo: require('../img/logo.png'),
})
let rewardListData: { time: string; point: number; newAum: number }[] = []

if (BUILD_TEST) {
  fidNotRegister = 2185
  fidRegister = 2186
}

function afterLoginSucc(loginData: any) {
  if (focusCore.hasAccount) {
    modalStore.loadingStart('queryActInfo')
    service.queryActInfo().then((data) => {
      console.log('🚀 ~ file: home.vue:210 ~ service.queryActInfo ~ data:', data)
      const {
        compId,
        userPoint,
        diffMaxAumInSalaryWeek,
        diffPerAmount,
        lastDayDate,
        lastDayNewAum,
        lastDayRewardPoint,
        lastMonthAum,
        maxRewardPointInSalaryWeek,
        rewardEndDateUserPoint,
        rewardList = [],

        companyIsChange,

        lastDayNewAumIsOverPerAmount,
        lastDayRewardPointIsOverWeekMax,
        lastDayRewardPointIsOverPerMax,
        isExpiredSalaryWeek,
        isPointOverTarget,
        dayPercent,
        config,
        salaryWeekFullTime,
        isNotFirstDate,
        lastMonthAllPoint,
        showLastMonthAum,
      } = data
      console.log('🚀 ~ file: home.vue:311 ~ service.queryActInfo ~ lastMonthAllPoint:', lastMonthAllPoint)
      if (compId) {
        isRegister.value = true
        salaryData.value = {
          compId,
          diffMaxAumInSalaryWeek,
          diffPerAmount,
          lastDayDate,
          lastDayNewAum,
          lastDayRewardPoint,
          lastMonthAum,
          maxRewardPointInSalaryWeek,
          rewardEndDateUserPoint,
          showLastMonthAum,
          companyIsChange,

          lastDayNewAumIsOverPerAmount,
          lastDayRewardPointIsOverWeekMax,
          lastDayRewardPointIsOverPerMax,
          isExpiredSalaryWeek,
          isPointOverTarget,
          dayPercent,
          userPoint,
          isNotFirstDate,
        }
        console.log('🚀 ~ file: home.vue:274 ~ service.queryActInfo ~ salaryData.value:', salaryData.value)

        salaryConfig.value = config
        console.log('🚀 ~ file: home.vue:296 ~ service.queryActInfo ~  salaryConfig.value:', salaryConfig.value)
        rewardListData = rewardList
      }
      focusCore.updateDynamicData('_custom_salaryWeekTime', salaryWeekFullTime, true)
      modalStore.loadingEnd('queryActInfo')

      dataIsReady.value = true
    })
  }
  focusCore.onRedirectToSpecialPath('/reward', () => {
    jumpReward()
  })
}

watchEffect(() => {
  if (btnPreDataModuleId.value && dataIsReady.value) {
    if (salaryData.value.userPoint === '--') {
      focusCore.focusRenderCtr.changeModuleConfig(btnPreDataModuleId.value, { hide: true }).render()
    } else {
      if (salaryData.value.isNotFirstDate) {
        focusCore.focusRenderCtr.changeModuleConfig(btnPreDataModuleId.value, { hide: true }).render()
      }
    }
  }
})

function jumpTrans() {
  jumpService.commonUse.transDialog()
}

function jumpReward() {
  router.push({
    path: '/reward',
  })
}

function slotMountedCb() {
  return {
    salary: (data: any, parentModuleId: number) => {
      console.log('🚀 ~ file: licaijie.vue:125 ~ slotMountedCb ~ parentModuleId:', parentModuleId)
      console.log('啦啦啦 ranklist 调用！')
      btnPreDataModuleId.value = data[0] || 0
    },
  }
}

function setFocus(fctl: any) {
  setTimeout(() => {
    console.log('🚀 ~ setFocus ~ fctl:', fctl)
    console.log('🚀 ~ setFocus ~ focusCore.isPreview:', focusCore.isPreview)
    focusRenderCtl.value = fctl
  }, 1000)
}
</script>

<style lang="scss" scoped>
.salary-week {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  min-height: 100%;
  position: relative;

  .company-change {
    height: 100vh;
    width: 100%;
    background: #ffdd8e;
    img {
      width: 100%;
    }
  }
  .comp-header {
    box-sizing: border-box;
    width: 100%;
    height: 92px;
    background: #fff;
    padding: 0 20px;
    font-size: 28px;
    color: #405080;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .logo {
      width: 130px;
      height: 80px;
    }
  }

  .salary-save-money {
    background: #ffffff;
    background-image: linear-gradient(180deg, #fff3e7 0%, #ffffff 50%);
    border-radius: 20px;
    width: 700px;
    height: auto;
    padding-top: 40px;
    margin: 0 auto;
    box-sizing: border-box;
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;
    &::after {
      content: '';
      width: 450px;
      height: 90px;
      background: url('../img/bg_webank.png') no-repeat;
      background-size: 100% 100%;
      position: absolute;
      top: 0;
      right: 0;
      z-index: 0;
    }
    .desc-wrap {
      width: 100%;
      height: 150px;
    }
    p.title {
      font-size: 40px;
      color: #45413e;
      line-height: 40px;
      font-weight: 600;
      z-index: 1;
      margin-bottom: 20px;
      &.haspoint {
        text-align: left;
        padding-left: 24px;
        .red {
          color: #ff2121;
          font-weight: bold;
        }
      }
    }
    p.desc {
      z-index: 1;
      font-size: 28px;
      color: #45413e;
      letter-spacing: 0;
      line-height: 42px;
      font-weight: 400;
      text-align: left;
      padding: 0 24px;
      .red {
        color: #ff2121;
        font-weight: bold;
      }
      &.haspoint {
        padding-right: 180px;
      }
    }
    .icon {
      position: absolute;
      right: 24px;
      top: 40px;
      height: 152px;
      width: 152px;
      z-index: 1;
    }
    .icon-timeout {
      width: 240px;
      height: 240px;
    }
    .next {
      margin: 0 auto;
      width: fit-content;
      height: 58px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-image: linear-gradient(-47deg, rgba(255, 232, 132, 0.3) 0%, rgba(255, 246, 209, 0.35) 99%);
      border-radius: 16px;
      font-size: 28px;
      color: #ff2121;
      text-align: center;
      font-weight: 500;
      margin-bottom: 40px;
      padding: 0 20px;
    }
    .progress {
      width: 654px;
      margin: 0 auto;
      margin-top: 40px;
      margin-bottom: 60px;
      .bar-wrap {
        width: 100%;
        height: 26px;
        background: #ececec;
        border-radius: 16px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        &::after {
          content: '';
          width: 652px;
          height: 24px;
          border-radius: 16px;
          background: #f0f0f0;
        }
        .bar {
          width: 0;
          height: 100%;
          border-radius: 16px;
          overflow: hidden;
          background: url('../img/bg_bar.png') no-repeat;
          background-size: 654px 24px;
          background-position: center center;
          border: 1px solid #fff;
          position: absolute;
          left: 0;
          top: 0;
        }
      }
      .time {
        width: 102px;
        height: 36px;
        background: url('../img/bg_time.png') no-repeat;
        background-size: contain;
        box-sizing: border-box;
        font-size: 22px;
        color: #fd8e00;
        letter-spacing: 0;
        text-align: center;
        line-height: 1;
        font-weight: 500;
        margin-left: 555px;
        margin-top: 5px;
        box-sizing: border-box;
        padding-top: 10px;
      }
    }

    .btn-trans {
      background: rgba(255, 28, 30, 0.13);
      background-image: linear-gradient(87deg, #ff1c1e 15%, rgba(255, 92, 66, 0.86) 100%);
      border-radius: 50px;
      color: #fff;
      font-size: 36px;
      color: #ffffff;
      text-align: center;
      font-weight: 600;
      width: 480px;
      height: 88px;
      line-height: 86px;
      margin: 60px auto;
    }
    .daily-reward {
      background: rgba(255, 132, 56, 0.056);
      border-top: 2px solid #ffede2;
      border-bottom: none;
      box-sizing: border-box;
      width: 100%;
      min-height: 52px;
      line-height: 36px;
      font-family: PingFangSC-Regular;
      font-size: 24px;
      color: #9b462288;
      font-weight: 400;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 8px 24px;
      text-align: left;

      .btn-reward-desc {
        flex: none;
        width: auto;
        height: 52px;
        line-height: 50px;
        font-size: 24px;
        color: #3566ff;
        text-align: right;
        margin-left: 20px;
      }
    }
  }

  .salary-rules {
    background: #ffffff;
    border-radius: 20px;
    padding: 40px 24px;
    width: 700px;
    margin: 0 auto;
    p {
      font-size: 28px;
      color: #45413e;
      text-align: justify;
      line-height: 42px;
      font-weight: 400;
    }
    .last-month-aum {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-top: 30px;
      .icon {
        width: 36px;
        height: 36px;
        margin-right: 10px;
      }
      p {
        color: #9b4722;
        font-weight: bold;
        padding-top: 2px;
        span {
          font-weight: bold;
          color: #ff2121;
        }
      }
    }

    .week-content {
      background-image: linear-gradient(-47deg, #ffefce 0%, #fffaf0 99%);
      border: 1px solid #ffedcb;
      box-shadow: inset 0 2px 0 0 #fffcf6;
      width: 654px;
      margin-top: 20px;
      border-radius: 20px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: wrap;
      padding-top: 20px;
      .left,
      .right {
        width: 50%;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: column;
        position: relative;
        .icon {
          width: 134px;
          height: 134px;
        }
        p.desc {
          font-size: 28px;
          color: #9b4722;
          letter-spacing: 0;
          text-align: center;
          line-height: 42px;
          margin-top: 10px;
          font-weight: bold;
          span {
            color: #ff2121;
            font-weight: bold;
          }
        }
      }
      .left {
        &::after {
          content: '';
          width: 1px;
          height: 188px;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%) scaleX(0.5);
          background: #ffd990;
        }
      }
      .tip {
        background: #ffffff;
        background-image: linear-gradient(-47deg, rgba(255, 237, 199, 0.2) 0%, rgba(255, 250, 240, 0.2) 99%);
        border: 1px solid #ffedcb;
        border-left: none;
        border-right: none;
        border-bottom: none;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 70px;
        margin-top: 20px;
        .icon {
          width: 28px;
          height: 28px;
          margin-right: 5px;
        }
        font-size: 24px;
        color: #9b4722;
        text-align: center;
      }
    }
    .act-info {
      width: 654px;
      height: 257px;
      background: #ffffff;
      background-image: url('../img/salary-rule.png');
      background-size: 100%;
      position: relative;
      margin-top: 60px;
      .time-list {
        position: absolute;
        bottom: 20px;
        left: 0;
        right: 0;
        font-size: 20px;
        color: #9b4722;
        text-align: center;
        line-height: 20px;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
        opacity: 0.5;
        span {
          &:nth-child(2) {
            margin-left: -140px;
          }
        }
      }
    }
    .act-desc {
      opacity: 0.5;
      font-size: 24px;
      color: #9b4722;
      line-height: 36px;
      font-weight: 400;
      margin-top: 12px;
    }
    .example {
      margin-top: 40px;
      width: 654px;
      height: 165px;
    }
  }
}
</style>
