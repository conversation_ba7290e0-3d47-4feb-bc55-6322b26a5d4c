import home from '../pages/home.vue'
import reward from '../pages/reward.vue'
// import photoDetail from '../components/photograph/PhotoDetail.vue'
import { createRouter, createWebHashHistory } from 'vue-router'
import { focusCore, focusServices } from '@focus/render'
const { loginService } = focusServices
const routes = [
  {
    path: '/',
    redirect: '/home',
  },
  {
    path: '/home',
    component: home,
    meta: {
      noNeedLogin: true,
    },
  },
  {
    path: '/reward',
    component: reward,
    meta: {
      noNeedLogin: true,
    },
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes, // `routes: routes` 的缩写
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  },
})
router.beforeEach((to, from, next) => {
  focusCore.routerBeforeEach(to, from, () => {
    next()
  })
})

export default router
