<template>
  <focus-render
    :focusFid="fid"
    @onLoginSucc="afterLoginSucc"
    @onFocusConfigSucc="setFocus"
    :slotMountedCb="slotMountedCb()"
  >
    <template #reward>
      <div class="reward-box">
        <div class="top">
          <div class="left">
            <div class="title" @click="jumpPoint">本期奖励积分</div>
            <p class="point">{{ rewardData.point }}</p>
          </div>
          <div class="right">
            <div class="title" @click="jumpLink">本期邀请人数</div>
            <p class="point">{{ rewardData.inviteNums }}</p>
          </div>
        </div>
        <div class="bottom" @click="jumpLink" v-if="rewardData.noCompanyRegisterNums">
          已开户的朋友还有 <span>{{ rewardData.noCompanyRegisterNums }}人</span>待认证，去提醒他们吧
        </div>
      </div>
    </template>
  </focus-render>
  <previewTool :focus-render-ctr="focusRenderCtl" v-if="focusCore.isPreview && focusRenderCtl"></previewTool>
</template>

<script setup lang="ts">
import { focusCore, focusStore, focusServices, Mask, focusUtils } from '@focus/render'
import { onMounted, provide, ref, computed, watchEffect, reactive, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import service from '../service/service'
import bubble from '../components/bubble.vue'
import previewTool from '@/views/preview/components/preview-tool.vue'
const { UrlParser } = focusUtils
const router = useRouter()
const { jumpService, userService, mgmService } = focusServices
const { modalStore, dynimicDataStore } = focusStore.stores
const { query } = new UrlParser()
const { fid } = query
const focusRenderCtl = ref(null)
const mgmLink = ref('')
const hasAccount = ref(false)
const rewardData = ref<{
  point: string | number
  inviteNums: string | number
  noCompanyRegisterNums: number | string
}>({
  point: '--',
  inviteNums: '--',
  noCompanyRegisterNums: 0,
})
const activityId = ref('')

watchEffect(() => {
  if (hasAccount.value && activityId.value) {
    modalStore.loadingStart('getCompanyData')
    service
      .getCompanyReward(activityId.value)
      .then((res) => {
        console.log('🚀 ~ service.getCompanyReward ~ res:', res)
        const { pointTotal, inviteTotal, noAuthTotal } = res
        rewardData.value = {
          point: pointTotal,
          inviteNums: inviteTotal,
          noCompanyRegisterNums: noAuthTotal === '--' ? 0 : noAuthTotal,
        }
      })
      .finally(() => {
        modalStore.loadingEnd('getCompanyData')
      })
  }
})

function setFocus(fctl: any) {
  focusRenderCtl.value = fctl
}

function afterLoginSucc(data: any) {
  if (data.hasAccount) {
    hasAccount.value = data.hasAccount
  }
}

function slotMountedCb() {
  return {
    reward: (data: any, parentModuleId: number) => {
      activityId.value = data[0]
      mgmLink.value = data[1]
    },
  }
}

function jumpLink() {
  jumpService.jump({
    path: mgmLink.value,
  })
}

function jumpPoint() {
  jumpService.jump({
    path: '/welfare/ActivityRecordScene',
  })
}
</script>

<style lang="scss" scoped>
.reward-box {
  width: 700px;
  height: auto;
  background: #fff;
  border-radius: 18px;
  margin: 0 auto;
  .top {
    width: 100%;
    height: 154px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 24px;
      color: #4c3e43;
      line-height: 36px;
      height: 36px;
      font-weight: 500;
      display: flex;
      &::after {
        display: inline-block;
        content: '';
        width: 36px;
        height: 36px;
        margin-left: 8px;
        background: url('../img/arrow_1.png') no-repeat;
        background-size: 100% 100%;
      }
    }
    .point {
      font-size: 42px;
      color: #f64744;
      letter-spacing: 0;
      line-height: 42px;
      font-weight: 700;
      margin-top: 18px;
    }

    .left {
      height: 100%;
      width: auto;
      padding-left: 85px;
      padding-top: 30px;
      text-align: left;
    }
    .right {
      height: 100%;
      width: auto;
      padding-top: 30px;
      padding-right: 85px;
      text-align: left;
    }
  }
  .bottom {
    width: 100%;
    height: 68px;
    line-height: 68px;
    background-image: linear-gradient(1deg, #ffbc63 0%, #ffd9ad 100%);
    box-shadow: inset 0 -3px 10px 0 #ffcba3;
    border-radius: 0 0 16px 16px;
    font-weight: bold;
    font-size: 24px;
    color: #8c4a00;
    letter-spacing: 0;
    font-weight: 600;
    box-sizing: border-box;
    padding: 0 30px;
    text-align: left;
    display: flex;
    align-items: center;
    position: relative;
    span {
      color: #f64744;
    }
    &::after {
      display: inline-block;
      content: '';
      width: 36px;
      height: 36px;
      background: url('../img/arrow_2.png') no-repeat;
      background-size: 100% 100%;
      position: absolute;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
</style>
