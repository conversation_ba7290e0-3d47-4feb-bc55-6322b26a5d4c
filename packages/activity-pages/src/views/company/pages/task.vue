<template>
  <focus-render
    :focusFid="fid"
    @onLoginSucc="afterLoginSucc"
    @onFocusConfigSucc="setFocus"
    :slotMountedCb="slotMountedCb()"
  >
    <template #company_tasks>
      <div
        class="task-list"
        v-if="
          taskData1.type && ((firstNewColumnURL && userStatus === '0') || (firstOldColumnURL && userStatus === '1'))
        "
      >
        <div class="task" :class="`status_${taskData1.status}`">
          <!-- 上传第一个任务的背景图片 -->
          <div
            class="info"
            :style="`background-image: url(${userStatus === '1' ? firstOldColumnURL : firstNewColumnURL})`"
          >
            <!-- 上传按钮图片 -->
            <div
              v-if="
                (taskData1.status === '0' && firstButtonURL) ||
                (taskData1.status === '1' && secondButtonURL) ||
                (taskData1.status === '2' && thirdButtonURL)
              "
              class="btn"
              :style="`background-image: url(${
                taskData1.status === '0' ? firstButtonURL : taskData1.status === '1' ? secondButtonURL : thirdButtonURL
              })`"
              @click="goAuth"
            ></div>
            <!-- 如果没有匹配的图片则兜底展示 -->
            <div v-else class="btn" @click="goAuth"></div>
          </div>
        </div>
        <div
          class="task"
          v-if="
            taskData2.type && ((secondNewColumnURL && userStatus === '0') || (secondOldColumnURL && userStatus === '1'))
          "
          :class="`status_${taskData2.status}`"
        >
          <!-- 上传第二个任务的背景图片 -->
          <div
            class="info"
            :style="`background-image: url(${userStatus === '1' ? secondOldColumnURL : secondNewColumnURL})`"
          >
            <!-- 上传按钮图片 -->
            <div
              v-if="
                (taskData2.status === '0' && firstButtonURL) ||
                (taskData2.status === '1' && secondButtonURL) ||
                (taskData2.status === '2' && thirdButtonURL)
              "
              class="btn"
              :style="`background-image: url(${
                taskData2.status === '0' ? firstButtonURL : taskData2.status === '1' ? secondButtonURL : thirdButtonURL
              })`"
              @click="goTrans"
            ></div>
            <!-- 如果没有匹配的图片则兜底展示 -->
            <div v-else class="btn" @click="goTrans"></div>
          </div>
        </div>
      </div>
    </template>
  </focus-render>
  <previewTool :focus-render-ctr="focusRenderCtl" v-if="focusCore.isPreview && focusRenderCtl"></previewTool>
</template>

<script setup lang="ts">
import { focusCore, focusStore, focusServices, Mask, focusUtils } from '@focus/render'
import { onMounted, provide, ref, computed, watchEffect, reactive, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import service from '../service/service'
import bubble from '../components/bubble.vue'
import previewTool from '@/views/preview/components/preview-tool.vue'
const { UrlParser } = focusUtils
const router = useRouter()
const { jumpService, userService, mgmService } = focusServices
const { modalStore, dynimicDataStore } = focusStore.stores
const { query } = new UrlParser()
const { fid } = query
const focusRenderCtl = ref(null)
const activityId = ref('')
const hasAccount = ref(false)
const taskData1 = ref({
  type: 0,
  status: '0',
})

const taskData2 = ref({
  type: 0,
  saveDays: 8,
  price: 1000,
  targetTrans: 1000,
  status: '0',
})

const firstNewColumnURL = ref('')
const firstOldColumnURL = ref('')
const secondNewColumnURL = ref('')
const secondOldColumnURL = ref('')
const firstButtonURL = ref('')
const secondButtonURL = ref('')
const thirdButtonURL = ref('')
const userStatus = ref('-1') // -1为未拿到数据时，0为新户，1为老户

watchEffect(() => {
  console.log('🐬 ~ watchEffect ~ activityId.value:', activityId.value)
  console.log('🐬 ~ watchEffect ~ hasAccount.value:', hasAccount.value)
  if (activityId.value && hasAccount.value) {
    modalStore.loadingStart('getCompanyTask')
    service
      .getCompanyTask(activityId.value)
      .then((res) => {
        console.log('🚀 ~ service.getCompanyTask ~ res:', res)
        userStatus.value = res.userStatus
        res.taskList.forEach((i) => {
          if (i.taskType === '1') {
            taskData1.value = {
              type: 1,
              status: i.taskStatus,
            }
          } else if (i.taskType === '2') {
            taskData2.value = {
              type: 2,
              saveDays: i.depositKeepDay,
              price: i.point,
              targetTrans: i.depositNumber,
              status: i.taskStatus,
            }
          }
        })
      })
      .finally(() => {
        modalStore.loadingEnd('getCompanyTask')
      })
  }
})

function setFocus(fctl: any) {
  focusRenderCtl.value = fctl
}

function afterLoginSucc(data: any) {
  if (data.hasAccount) {
    hasAccount.value = data.hasAccount
  }
}

function slotMountedCb() {
  return {
    company_tasks: (data: any, parentModuleId: number) => {
      activityId.value = data[0]
      firstNewColumnURL.value = data[1]
      firstOldColumnURL.value = data[2]
      secondNewColumnURL.value = data[3]
      secondOldColumnURL.value = data[4]
      firstButtonURL.value = data[5]
      secondButtonURL.value = data[6]
      thirdButtonURL.value = data[7]
      console.log('🐬 ~ slotMountedCb ~ data:', data)
    },
  }
}

function goAuth() {
  // 待发奖
  if (taskData1.value.status === '1') {
    return
  }
  // 查看奖励
  if (taskData1.value.status === '2') {
    jumpService.jump({
      path: '/welfare/ActivityRecordScene',
    })
    return
  }
  // 去完成
  jumpService.jump({
    path: '/enterpriseAuthentication/ChooseEnterpriseScene',
  })
}

function goTrans() {
  if (taskData1.value.status === '0') {
    modalStore.toastShow('请先完成企业认证')
    return
  }
  // 查看奖励
  if (taskData2.value.status === '2') {
    jumpService.jump({
      path: '/welfare/ActivityRecordScene',
    })
    return
  }
  // 去完成
  jumpService.jump({
    path: '/webankCard/WebankCardScene',
  })
}
</script>

<style lang="scss" scoped>
.task-list {
  width: 700px;
  height: auto;
  margin: 0 auto;
  padding-bottom: 40px;
  background-color: transparent;
  border-radius: 18px;
  .task {
    width: 100%;
    padding-top: 40px;
    text-align: left;
    box-sizing: border-box;
    font-size: 26px;
    color: #4c3e43;
    letter-spacing: 0;
    font-weight: 400;
    position: relative;
    &::after {
      content: '';
      width: 100px;
      height: 100px;
      background: url('../img/task_done.png') no-repeat;
      background-size: 100% 100%;
      position: absolute;
      top: 20px;
      right: 160px;
      display: none;
      z-index: 1;
    }
    .info {
      width: 660px;
      height: 130px;
      margin: 0 auto;
      background: no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-left: 20px;
      padding-right: 10px;
      line-height: 1.5;
      border-radius: 10px;
      .btn {
        width: 200px;
        height: 90px;
        background: url('../img/btn_1.png') no-repeat center;
        background-size: 200px 90px;
        flex: none;
        cursor: pointer;
        margin-left: 48px;
        z-index: 3;
      }
    }
    &.status_1 {
      &::after {
        display: block;
      }
      .btn {
        background-image: url('../img/btn_2.png');
      }
    }
    &.status_2 {
      .btn {
        background-image: url('../img/btn_3.png');
      }
    }
  }
}
</style>
