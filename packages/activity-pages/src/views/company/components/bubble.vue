<template>
  <div class="bubble" :class="ani1" v-html="bubble1" v-show="bubble1"></div>
  <!-- <div class="bubble" :class="ani2" v-html="bubble2" v-show="bubble2"></div> -->
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'

const props = defineProps({
  bubbleData: {
    type: Array,
    default: [],
  },
})

const htmlDataList = computed(() => {
  return (
    props.bubbleData &&
    props.bubbleData.map((i: any) => {
      const { high_light, text } = i
      let htmlStr = text
      high_light.forEach((_i: string | null) => {
        htmlStr = htmlStr.replace(`${_i}`, `<p>${_i}</p>`)
      })
      return `<div class="desc">${htmlStr}</div>`
    })
  )
})

const randomNum = ref([0, 1])

const bubble1 = ref('')
const bubble2 = ref('')

let timer1 = null
let timer2 = null

const ani1 = ref('')
const ani2 = ref('')
const ani1Status = ref(false)
const ani2Status = ref(false)
const curIndex1 = ref(-2)
const curIndex2 = ref(0)

setTimeout(() => {
  ani1Status.value = true
  setTimeout(() => {
    ani2Status.value = true
  }, 500)
}, 3000)

watch(ani1Status, (val) => {
  const length = props.bubbleData && props.bubbleData.length
  console.log('🚀 ~ watch ~ props.bubbleData:', props.bubbleData)

  if (val) {
    // let newIndex = curIndex1.value + 2 <= length - 1 ? curIndex1.value + 2 : 0
    // if (newIndex === curIndex2.value) {
    //   newIndex += 1
    // }

    const newIndex = Math.floor(Math.random() * (length + 1))
    curIndex1.value = newIndex
    bubble1.value = htmlDataList.value[newIndex] || ''
    ani1.value = 'in'
    setTimeout(() => {
      ani1Status.value = false
    }, 3000)
  }

  if (!val) {
    ani1.value = 'out'
    setTimeout(() => {
      ani1Status.value = true
    }, 300)
  }
})

watch(ani2Status, (val) => {
  const length = props.bubbleData && props.bubbleData.length

  if (val) {
    const newIndex = curIndex1.value + 1 <= length - 1 ? curIndex1.value + 1 : 0
    curIndex2.value = newIndex
    bubble2.value = htmlDataList.value[newIndex] || ''
    ani2.value = 'in'
    setTimeout(() => {
      ani2Status.value = false
    }, 3000)
  }

  if (!val) {
    ani2.value = 'out'
    setTimeout(() => {
      ani2Status.value = true
    }, 300)
  }
})
</script>

<style lang="scss" scoped>
.bubble {
  width: 253px;
  min-height: 161px;
  font-size: 22px;
  color: #0068d3;
  line-height: 33px;
  font-weight: 500;
  position: absolute;
  left: 450px;
  top: 250px;
  text-align: left;
  background: url('../img/box_bubble.png') no-repeat;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding-left: 38px;
  padding-right: 20px;
  padding-top: 20px;
  padding-bottom: 20px;
  box-sizing: border-box;
  overflow: hidden;
  transform-origin: left;
  &:nth-child(2) {
    left: 420px;
    bottom: 20px;
  }
  ::v-deep(.desc) {
    font-size: 22px;
    color: #0068d3;
    letter-spacing: 0;
    line-height: 1.5;
    font-weight: 500;
    p {
      flex: none;
      color: #ea2222;
      max-width: 160px;
      letter-spacing: 0;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.5;
      font-size: 22px;
      margin-top: -2px;
      vertical-align: middle;
    }
  }

  &.in {
    animation: flyin 0.5s ease forwards;
  }
  &.out {
    animation: flyout 0.2s ease forwards;
  }
}

@keyframes flyin {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}

@keyframes flyout {
  0% {
    transform: scaleX(1);
  }
  100% {
    transform: scaleX(0);
  }
}
</style>
