import { focusStore, focusServices, focusCore, focusUtils } from '@focus/render'
const { entitiestoUtf16, dayjs } = focusUtils
const { mgmService, kvService, dynimicDataService } = focusServices
const cgis = {
  companyReward: {
    name: '白领客群活动奖励',
    url: '/op-fmfront/mgm/hj/fm/query-invite-award',
  },
  companyTask: {
    url: '/op-fmfront/mgm/hj/fm/query-white-collar-task',
    name: '白领客群活动任务',
  },
}

class Service {
  constructor() {}

  getCompanyReward(activityId: string): Promise<{
    pointTotal: number | string //已获得积分
    inviteTotal: number | string //邀请总人数
    noAuthTotal: number | string //待认证人数
  }> {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.companyReward, {
          activityId,
        })
        .then(
          (res: {
            pointTotal: number //已获得积分
            inviteTotal: number //邀请总人数
            noAuthTotal: number //待认证人数
          }) => {
            const { pointTotal = '--', inviteTotal = '--', noAuthTotal = 0 } = res
            return resolve({ pointTotal, inviteTotal, noAuthTotal })
          }
        )
        .catch((err: any) => {
          return resolve({ pointTotal: '--', inviteTotal: '--', noAuthTotal: '--' })
        })
    })
  }

  getCompanyTask(activityId: string): Promise<{
    taskList: {
      taskType: string //任务类型：1-认证 2-入金
      taskStatus: string //任务状态：0-去完成 1-待发奖 2-已发奖
      depositNumber: number //入金金额
      depositKeepDay: number // 入金保持天数
      point: number // 奖励的积分
    }[]
    userStatus: string
  }> {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.companyTask, {
          activityId,
        })
        .then(
          (res: {
            userStatus: string //用户状态：0 - 新户 1-老户
            taskList: {
              taskType: string //任务类型：1-认证 2-入金
              taskStatus: string //任务状态：0-去完成 1-待发奖 2-已发奖
              depositNumber: number //入金金额
              depositKeepDay: number // 入金保持天数
              point: number // 奖励的积分
            }[]
          }) => {
            const { taskList = [], userStatus = '0' } = res
            resolve({ taskList, userStatus })
          }
        )
        .catch((err: any) => {
          console.log('🚀 ~ Service ~ returnnewPromise ~ err:', err)

          resolve({ taskList: [], userStatus: '0' })
        })
    })
  }
}

export default new Service()
