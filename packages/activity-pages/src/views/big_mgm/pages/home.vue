<template>
  <div class="big-mgm" :class="{ notzero: mgmData.isInvitedStatus }">
    <Transition name="fade">
      <!-- 未邀请状态 -->
      <div class="no-invited" v-if="!mgmData.isInvitedStatus && dataIsReady">
        <!-- <div class="bubble">用户xxx已经邀请321人，累计获得xxx积分!</div>
  <div class="bubble">用户xxx已经邀请321人，累计获得xxx积分!</div> -->
        <bubble :bubbleData="rankData.bubbleData" v-if="rankData.bubbleData.length"></bubble>
        <!-- <div class="btn-rule" @click="clickRule">规则</div> -->
      </div>
    </Transition>

    <focus-render
      :focusFid="fid"
      @onLoginSucc="afterLoginSucc"
      @onFocusConfigSucc="setFocus"
      :slotMountedCb="slotMountedCb()"
      :renderWhenParentIsReadyStatus="dataIsReady ? 2 : 1"
    >
      <template #bigmgm_home>
        <div class="isinvited" v-if="mgmData.isInvitedStatus && dataIsReady">
          <div class="user">
            <div class="avator" :class="dataIsReady ? '' : 'data-not-ready'">
              <img :src="userInfo.avator" v-show="userInfo.avator" />
            </div>
            <div class="info">
              <p class="name">{{ userInfo.nickName }}</p>
              {{ mgmData.equityLevel }}
              <div
                class="equity"
                :class="`lv${mgmData.equityLevel}`"
                v-if="mgmData.equityLevel === '3' || mgmData.equityLevel === '4'"
                @click="jumpEquity"
              >
                +{{ mgmData.equityBonus }}
              </div>
            </div>
          </div>
          <div class="my-point">
            <div class="main">
              <div class="item">
                <div class="btn-jump" @click="jumpPointList">累计积分奖励<span>(分)</span></div>
                <div class="point">
                  {{ dataIsReady ? mgmData.curPointsForamt : '--' }}
                  <div class="last" v-if="mgmData.lastDayNewPoints">昨日+{{ mgmData.lastDayNewPoints }}</div>
                </div>
              </div>
              <div class="item">
                <div class="btn-jump" @click="jumpMgmList">我邀到的朋友<span>(人)</span></div>
                <div class="point">
                  {{ dataIsReady ? mgmData.curInvited : '--' }}
                  <div class="last" v-if="mgmData.lastDayNewInvited">昨日+{{ mgmData.lastDayNewInvited }}</div>
                </div>
              </div>
            </div>
            <div class="btn-tip" v-show="notActiedNums" @click="jumpMgmList">
              已开户的朋友还有{{ notActiedNums }}人待激活，去提醒他们吧 >
            </div>
          </div>
        </div>
      </template>

      <template #bigmgm_ranklist>
        <div class="rank-list" v-show="showRankList.length">
          <img src="../img/header.png" alt="" class="header" />
          <div class="btn-hide" v-show="mgmData.isInvitedStatus" @click.stop="showBtnHide = !showBtnHide">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="btn" @click.prevent="hideName" v-show="showBtnHide">
              {{ hideNameStatus ? '关闭' : '开启' }}匿名上榜
            </div>
          </div>
          <div class="main" v-if="rankData.dailyRank > 0">
            <div class="main-item">
              <p class="top">{{ rankData.dailyRank }}</p>
              <p class="bottom">我的单日排名</p>
            </div>
            <div class="main-item">
              <p class="top">{{ rankData.monthRank }}</p>
              <p class="bottom">我的月度排名</p>
            </div>
          </div>
          <div class="nav">
            <div class="nav-item" @click="selectedRankIndex = 0" :class="{ actived: selectedRankIndex === 0 }">
              今日排行榜
            </div>
            <div class="nav-item" @click="selectedRankIndex = 1" :class="{ actived: selectedRankIndex === 1 }">
              本月排行榜
            </div>
          </div>
          <div class="list">
            <div class="item" v-for="(item, index) in showRankList" :key="index">
              <div class="index" :class="`index_${index}`">{{ index + 1 }}</div>
              <p class="name">{{ item.userName }}</p>
              <p class="point">
                <span>+{{ item.point }}</span
                >积分
              </p>
            </div>
          </div>

          <div class="updatetime">数据统计至：{{ rankData.rankListUpdateTime }}</div>
        </div>
      </template>
    </focus-render>
  </div>
  <previewTool :focus-render-ctr="focusRenderCtl" v-if="focusCore.isPreview && focusRenderCtl"></previewTool>
</template>

<script setup lang="ts">
import { focusCore, focusStore, focusServices, Mask, focusUtils } from '@focus/render'
import { onMounted, provide, ref, computed, watchEffect, reactive, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import service from '../service/service'
import bubble from '../components/bubble.vue'
import previewTool from '@/views/preview/components/preview-tool.vue'
const { UrlParser } = focusUtils
const router = useRouter()
const { jumpService, userService, mgmService } = focusServices
const { modalStore, dynimicDataStore } = focusStore.stores
const { query } = new UrlParser()
const { fid } = query
const mgmData = ref<{
  curPoints?: number // 当前总积分
  lastDayNewPoints?: number // 昨日新增积分
  curInvited?: number | string // 当前邀请人数
  lastDayNewInvited?: number // 昨日新增邀请人数

  curPointsForamt?: string
  isInvitedStatus: boolean
  equityLevel?: string
  equityBonus?: string
}>({
  curPoints: 0,
  curPointsForamt: '0',
  lastDayNewPoints: 0,
  curInvited: 0,
  lastDayNewInvited: 0,
  isInvitedStatus: false,
})

const notActiedNums = ref(0)
const userInfo = ref({
  avator: '',
  nickName: '',
})
const rankData = ref<{
  dailyRank: number
  monthRank: number
  dailyRankList: any[]
  monthlyRankList: any[]
  rankListUpdateTime: string
  bubbleData: any[]
}>({
  dailyRank: -1,
  monthRank: -1,
  dailyRankList: [],
  monthlyRankList: [],
  rankListUpdateTime: '--',
  bubbleData: [],
})
const dataIsReady = ref(false)
const selectedRankIndex = ref(0)
const userSid = ref('')
const focusRenderCtl: any = ref(null)
const ruleLink = ref('')
const useNewMgmList = ref(false)
const rankListUpdateTime = ref('--')
const hideNameStatus = ref(false)
const showBtnHide = ref(false)

const showRankList = computed(() => {
  return selectedRankIndex.value ? rankData.value.monthlyRankList : rankData.value.dailyRankList
})

function setFocus(fctl: any) {
  focusRenderCtl.value = fctl
}

function afterLoginSucc(data: any) {
  if (data.hasAccount) {
    getData()
  }

  focusCore.beforeBtnClick((datas: any, next: any) => {
    console.log('🚀 ~ file: home.vue:128 ~ focusCore.beforeBtnClick ~ next:', next)
    console.log('🚀 ~ file: home.vue:128 ~ focusCore.beforeBtnClick ~ datas:', datas)
    const { clickEvent } = datas
    console.log('🚀 ~ file: home.vue:160 ~ focusCore.beforeBtnClick ~ clickEvent:', clickEvent)
    const _query = clickEvent.query || {}
    const config = Object.assign(clickEvent, {
      query: {
        ..._query,
        autoShare: 1,
        bigMGMSid: userSid.value,
      },
    })
    console.log('🚀 ~ file: home.vue:167 ~ focusCore.beforeBtnClick ~ config:', config)
    // 跳转前尝试清空isAutoShareInApp
    try {
      localStorage.removeItem('isAutoSharedInApp')
    } catch (err) {
      console.log('🚀 ~ file: home.vue:134 ~ focusCore.beforeBtnClick ~ err:', err)
    }
    setTimeout(() => {
      next({
        clickEvent: config,
      })
    }, 50)
  })
}

function getData() {
  modalStore.loadingStart('getmgmdata')
  userService
    .getWxUserInfo()
    .then(({ avator, defaultAvator, nickName }: { avator: string; defaultAvator: string; nickName: string }) => {
      userInfo.value = {
        avator: avator || defaultAvator,
        nickName,
      }
    })
  service
    .queryHomeData()
    .then((data) => {
      console.log('🚀 ~ file: home.vue:109 ~ service.queryMgmData ~ data:', data)
      modalStore.loadingEnd('getmgmdata')

      const {
        curPoints = 0,
        lastDayNewPoints = 0,
        curInvited = 5,
        lastDayNewInvited = 3,
        bubbleData = [],
        sid = '',
        curPointsForamt = '0',
        isError,
        isInvitedStatus,
        noActivateInviteSum,
        equityBonus,
        equityLevel,
      } = data
      if (isError) {
        modalStore.errorMaskContrl('bigMgm_home_Error')
        return
      }
      userSid.value = sid
      notActiedNums.value = noActivateInviteSum || 0

      mgmData.value = {
        curPoints,
        lastDayNewPoints,
        curInvited,
        lastDayNewInvited,
        curPointsForamt,
        isInvitedStatus,
        equityBonus,
        equityLevel,
      }
      setTimeout(() => {
        dataIsReady.value = true
      }, 50)
    })
    .catch(() => {
      modalStore.errorMaskContrl('bigmgm_home', ['抱歉，链接权限已失效'])
    })

  getRankData()
}

function getRankData() {
  service.checkHideName().then((data) => {
    console.log('🚀 ~ service.checkHideName ~ data:', data)
    hideNameStatus.value = data.status
  })
  service.queryRankList().then((data) => {
    console.log('🚀 ~ file: home.vue:128 ~ service.queryRankList ~ data:', data)

    rankData.value = data
  })
}

function jumpPointList() {
  router.push({
    path: '/pointlist',
  })
  // let link = `https://personal.webank.com/s/hj/op/mgm_common/index.html?aid=1939&sid=${userSid.value}&oauthed=y#!/statistics/reward/M0/1`
  // if (BUILD_TEST) {
  //   link = `https://personal.test.webank.com/s/hj/op/mgm_common/index.html?aid=1939&sid=${userSid.value}&oauthed=y#!/statistics/reward/M0/1`
  // }
  // jumpService.jump({
  //   path: link,
  // })
}

function jumpMgmList() {
  router.push({
    path: '/mgmlist',
    query: {
      fid,
    },
  })
}

function hideName() {
  let confirmData = !hideNameStatus.value
    ? {
        title: '开启匿名上榜',
        contents: ['若开启匿名上榜，将以系统默认头像代替你的微信头像。并以“用户+手机号后四位”代替你的微信昵称'],
      }
    : {
        title: '关闭匿名上榜',
        contents: ['若关闭匿名上榜，将显示你真实的微信头像和微信昵称。'],
      }

  modalStore.confirmContrl({
    show: true,
    title: confirmData.title,
    btnCancelText: '取消',
    btnConfirmText: '确认',
    contents: confirmData.contents,
    confirmCb: () => {
      modalStore.loadingStart('hideName')
      service.changeHideNameStatus(!hideNameStatus.value).then((res: any) => {
        console.log('🚀 ~ service.changeHideNameStatus ~ res:', res)
        // hideNameStatus.value = res.status
        showBtnHide.value = false
        setTimeout(() => {
          getRankData()
          modalStore.loadingEnd('hideName')
        }, 300)
      })
    },
    cancelCb: () => {
      showBtnHide.value = false
    },
  })
}

function jumpEquity() {
  jumpService.jump({
    path: '/memberEquity/MemberEquityCenterScene',
  })
}

function clickShare() {
  const shareConfigData = focusRenderCtl.value && focusRenderCtl.value.getShareConfigByConfigId(1)
  console.log('🚀 ~ file: home.vue:225 ~ clickShare ~ shareConfigData:', shareConfigData)
  mgmService.clickShare('bigigm', shareConfigData)
}

function slotMountedCb() {
  return {
    bigmgm_home: (data: any, parentModuleId: number) => {
      console.log('🚀 ~ slotMountedCb ~ parentModuleId:', parentModuleId)
      console.log('🚀 ~ slotMountedCb ~ data:', data)

      console.log('啦啦啦 ranklist 调用！')
      const link1 = data[0]
      const link2 = data[1]
      let activeLink = ''
      let inviteLink = ''
      if (link1) {
        activeLink = new UrlParser(link1).appendQuery({
          autoShare: '1',
        }).fullPath
      }
      if (link2) {
        inviteLink = new UrlParser(link2).appendQuery({
          autoShare: '1',
        }).fullPath
      }
      service.setHomeData({
        activeLink,
        inviteLink,
      })
    },
  }
}
</script>

<style>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

<style lang="scss" scoped>
.big-mgm {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  z-index: 2;
  background: linear-gradient(to bottom, #d0f8ff, #67baff);
  padding-top: 278px;
  &::before {
    content: '';
    background: url('../img/bg-linear.png') no-repeat;
    width: 750px;
    height: 610px;
    position: absolute;
    left: 0;
    top: -128px;
    background-size: 100% 100%;
  }
  &.notzero {
    padding-top: 0;
  }

  .no-invited {
    width: 100%;
    height: 615px;
    background: url('../img/bg.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }
  .isinvited {
    width: 100%;
    margin-bottom: 20px;

    .user {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 30px 30px;
      padding-bottom: 20px;
      box-sizing: border-box;
      .avator {
        display: block;
        width: 85px;
        height: 85px;
        border: 3px solid #c8e7ff;
        box-shadow: 0 2px 20px 0 rgba(68, 123, 255, 0.1);
        flex: none;
        margin-right: 20px;
        border-radius: 100%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 0;
        img {
          width: 100%;
          height: 100%;
          border-radius: 100%;
        }
        &.data-not-ready {
          background-color: #eeeeee;
        }
      }
      .info {
        .name {
          flex: auto;
          text-align: left;
          font-size: 26px;
          color: #364a95;
          letter-spacing: 0;
          line-height: 26px;
          font-weight: 600;
        }
        .equity {
          width: 219px;
          height: 48px;
          font-size: 24px;
          margin-top: 8px;
          &.lv3 {
            background: url('../img/icon_lv_3.png') no-repeat center;
            background-size: 100% 100%;
            color: #6c6e79;
          }
          &.lv4 {
            background: url('../img/icon_lv_4.png') no-repeat center;
            background-size: 100% 100%;
            color: #d6d7e8;
          }
          display: flex;
          align-items: center;
          justify-content: flex-end;
          padding-right: 13px;
          box-sizing: border-box;
        }
      }
      .btn-rule {
        width: 100px;
        height: 60px;
        border: 2px solid #0c88ff;
        box-shadow: 0 2px 10px 0 rgba(54, 157, 255, 0.52);
        border-radius: 30px;
        font-size: 26px;
        color: #0c88ff;
        letter-spacing: 0;
        line-height: 58px;
        font-weight: 600;
        text-align: center;
      }
    }
    .my-point {
      width: 690px;
      border-radius: 10px;
      overflow: hidden;
      margin: 0 auto;
      box-sizing: border-box;

      .main {
        width: 100%;
        height: 155px;
        box-sizing: border-box;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 20px 0 #c0deff;
        position: relative;
        z-index: 2;
        .item {
          text-align: left;
          flex: auto;
          padding-left: 30px;
          .btn-jump {
            font-size: 30px;
            color: #364a95;
            letter-spacing: 0;
            line-height: 35px;
            font-weight: 400;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            cursor: pointer;
            &::after {
              content: '';
              display: block;
              width: 36px;
              height: 36px;
              background: url('../img/arrow_right.png') no-repeat;
              background-size: 100% 100%;
            }
            span {
              font-size: 24px;
              color: #364a95;
            }
          }
          .point {
            font-size: 42px;
            color: #ea2222;
            letter-spacing: 0;
            line-height: 42px;
            font-weight: 700;
            display: flex;
            align-items: flex-end;
            justify-content: flex-start;
            margin-top: 20px;
            .last {
              background-image: linear-gradient(90deg, #ffeddd 0%, #ffeddd 100%);
              border-radius: 6px;
              width: auto;
              height: 36px;
              margin-left: 10px;
              font-size: 20px;
              color: #b57d20;
              letter-spacing: 0;
              line-height: 34px;
              font-weight: 400;
              text-align: center;
              text-align: left;
              padding: 0 10px;
            }
          }
        }
      }

      .btn-tip {
        width: 100%;
        background-image: linear-gradient(270deg, #6cbeff 0%, #2493ff 100%);

        font-size: 24px;
        color: #ffffff;
        letter-spacing: 0;
        line-height: 68px;
        font-weight: 400;
        width: 690px;
        height: 68px;
        text-align: left;
        padding-left: 30px;
      }
    }
  }
  .rank-list {
    width: 690px;
    margin: 0 auto;
    background: #fff;
    border-radius: 10px;
    padding-bottom: 30px;
    position: relative;
    .btn-hide {
      width: 100px;
      height: 70px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      position: absolute;
      right: 30px;
      top: 0;
      cursor: pointer;
      z-index: 333;

      .dot {
        width: 6px;
        height: 6px;
        margin: 0 4px;
        border-radius: 100%;
        background-color: #4291e0;
      }
      .btn {
        position: absolute;
        right: 0;
        bottom: -70px;
        width: 200px;
        box-sizing: content-box;
        font-size: 26px;
        color: #364a95;
        height: 70px;
        border-radius: 10px;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
        text-align: center;
        line-height: 70px;
        background: #fff;
        padding: 0 30px;
        cursor: pointer;
        &:active {
          opacity: 0.6;
        }
      }
    }
    .updatetime {
      font-size: 26px;
      line-height: 1.5;
      text-align: center;
      width: 100%;
      margin-top: 20px;
      color: #8991b6;
    }
    .header {
      width: 278px;
      height: 60px;
      margin: 0 auto;
      display: block;
    }
    .main {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 630px;
      height: 134px;
      background: linear-gradient(to bottom, #d6edfd, #d9f0fe);
      margin: 30px auto 0;
      border-radius: 10px;
      .main-item {
        flex: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        cursor: pointer;
        .top {
          font-size: 42px;
          color: #ea2222;
          letter-spacing: 0;
          line-height: 42px;
          font-weight: 700;
        }
        .bottom {
          font-size: 24px;
          color: #364a95;
          line-height: 24px;
          letter-spacing: 0;
          font-weight: 400;
          margin-top: 10px;
        }
      }
    }
    .nav {
      width: 630px;
      margin: 30px auto 0;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-left: 40px;
      padding-right: 40px;
      box-sizing: border-box;
      &::after {
        content: '';
        width: 100%;
        height: 1px;
        background: rgba(63, 162, 255, 0.2);
        display: block;
        position: absolute;
        bottom: 0;
        left: 0;
      }
      .nav-item {
        width: 213px;
        border-bottom: 4px solid transparent;
        box-sizing: border-box;
        font-size: 30px;
        color: #364a95;
        letter-spacing: 0;
        line-height: 45px;
        padding-bottom: 10px;
        cursor: pointer;
        &.actived {
          font-weight: 600;
          border-color: #3c78fc;
        }
      }
    }
    .list {
      width: 630px;
      margin: 0 auto;
      .item {
        width: 100%;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        border-bottom: 1px solid rgba(63, 162, 255, 0.2);
        font-size: 24px;
        color: #364a95;
        letter-spacing: 0;
        line-height: 45px;
        font-weight: 400;
        .index {
          width: 66px;
          height: 75px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex: none;
          &.index_0,
          &.index_1,
          &.index_2 {
            color: transparent;
            background: url('../img/icon_index_0.png') no-repeat;
            background-size: 100% 100%;
          }
          &.index_1 {
            background-image: url('../img/icon_index_1.png');
          }
          &.index_2 {
            background-image: url('../img/icon_index_2.png');
          }
        }
        .name {
          flex: auto;
          text-align: left;
          margin-left: 10px;
        }
        .point {
          text-align: right;
          span {
            color: #099aff;
            text-align: right;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
