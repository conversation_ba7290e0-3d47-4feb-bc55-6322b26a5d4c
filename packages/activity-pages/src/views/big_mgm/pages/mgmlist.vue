<template>
  <div class="mgm-list">
    <div class="navs">
      <div
        class="item"
        :class="{ actived: curNavIndex === `${index}` }"
        v-for="(item, index) in navs"
        :key="index"
        @click="curNavIndex = `${`${index}` as '0' | '1' | '2'}`"
      >
        {{ item }}
      </div>
    </div>
    <div class="desc-top" v-if="dataIsReady">
      <div class="count">
        <span>{{ curListCount }}</span
        >人
      </div>
      <p>{{ curNavTip }}</p>
      <p class="tip" v-if="curNavIndex === '1'">*超过3人才会显示真实头像及昵称，亲友如未授权微信头像和昵称将无法展示</p>
      <p class="tip" v-else>*亲友如未授权微信头像和昵称将无法展示</p>
    </div>
    <div class="list-item" v-for="(item, index) in curListData" :key="`${curNavIndex}_${index}`">
      <img class="avator" v-if="item.avator" :src="item.avator" />
      <img class="avator" v-else src="../img/avator_default.png" />
      <div class="info">
        <div class="name">{{ item.nickName ? item.nickName : '你的亲友' }}</div>
        <div class="time">{{ item.showTime }} {{ curDesc }}</div>
        <div class="reward-time" v-show="curNavIndex === '1'">
          <p class="text">奖励期：{{ item.bonusStartTime }}至{{ item.bonusEndTime }}</p>
          <img src="../img/price_1.png" v-show="item.bonusTimes === '1'" alt="" class="icon" />
          <img src="../img/price_2.png" v-show="item.bonusTimes === '2'" alt="" class="icon" />
        </div>
      </div>
      <div class="btn" @click="clickShare('active')" v-show="curNavIndex === '0'">邀请使用</div>
      <div class="btn" @click="clickShare('invite')" v-show="curNavIndex === '2'">邀请开户</div>
    </div>

    <div class="empty" v-if="!curListCount && dataIsReady">
      <img src="../img/empty.png" alt="" class="icon" />
      <p>还没有朋友浏览过分享链接，试试多邀请几个朋友吧～</p>
    </div>
  </div>
  <!-- focus no render -->
  <focus-render :needLogin="true" @onLoginSucc="afterLogin"></focus-render>
</template>

<script setup lang="ts">
import { computed, provide, readonly, ref, watchEffect, onActivated } from 'vue'
import { focusCore, focusUtils, focusServices, focusStore } from '@focus/render'
import service from '../service/service'
import { useRouter, useRoute } from 'vue-router'
const { jumpService, userService, mgmService } = focusServices

// import avatorMale1 from '../img/mail-0.jpg'

const { modalStore, dynimicDataStore } = focusStore.stores
const route = useRoute()
const navs = ['未激活', '已激活', '未开户']
const curNavIndex = ref<'0' | '1' | '2'>('0')
const limitMoney = ref<string | number>('--')
const dataIsReady = ref(false)
const hasAccount = ref(false)
console.log('🚀 ~ route.query.fid:', route.query.fid)

const bigMgmFocusData = focusCore.getTargetFidData(route.query.fid)

const originListData = ref<{
  '0': any[]
  '1': any[]
  '2': any[]
}>({
  '0': [],
  '1': [],
  '2': [],
})
const curListData: any = computed(() => {
  return originListData.value[curNavIndex.value]
})

const curListCount = computed(() => {
  return curListData.value.length
})

const curNavTip = computed(() => {
  const msgs = [
    `已完成开户，但未入金>=${limitMoney.value}的亲友`,
    '已完成开户且贡献资产积分奖励的亲友',
    '指近30天点击邀请链接，但未完成开户的朋友',
  ]
  return msgs[curNavIndex.value]
})

const curDesc = computed(() => {
  const msgs = ['已接受邀请', '已激活', '查看过邀请链接']
  return msgs[curNavIndex.value]
})

function clickShare(type: string) {
  let link = ''
  if (type === 'active') {
    link = service.activeLink
  } else if ((type = 'invite')) {
    link = service.inviteLink
  }
  jumpService.jump({
    path: link,
    method: 'wxUrl',
  })
  // todo
  // bigMgmFocusData.then((res: any) => {
  //   console.log('🚀 ~ bigMgmFocusData.then ~ res:', res)
  //   const shareData = res.shareConfigById(1)
  //   console.log('🚀 ~ bigMgmFocusData.then ~ shareData:', shareData)
  //   mgmService.clickShare('mgmlist_notactive', shareData)
  // })
}

onActivated(() => {
  if (hasAccount.value) {
    getData()
  }
})

watchEffect(() => {
  if (hasAccount.value) {
    getData()
  }
})

function afterLogin(data: any) {
  hasAccount.value = data.hasAccount
  // if (data.hasAccount) {
  //   getData()
  // }
}

function getData() {
  modalStore.loadingStart('mgmlist')
  service.queryInviteList().then((res) => {
    console.log('🚀 ~ file: mgmlist.vue:74 ~ service.queryInviteList ~ res:', res)
    const { active = [], notActive = [], notRegister = [], m2DepositActiveNumber } = res
    originListData.value = {
      '0': notActive,
      '1': active,
      '2': notRegister,
    }
    limitMoney.value = m2DepositActiveNumber || '--'
    dataIsReady.value = true
    modalStore.loadingEnd('mgmlist')
  })
}

function getAvator(gender: string, index: number) {
  const males = [require('../img/male-0.jpg'), require('../img/male-1.jpg'), require('../img/male-2.jpg')]
  const females = [require('../img/female-0.jpg'), require('../img/female-1.jpg'), require('../img/female-2.jpg')]
  const _index = index % 3
  console.log('🚀 ~ getAvator ~ females[_index]:', females[_index])
  console.log('🚀 ~ getAvator ~ females[_index]:', males[_index])
  if (gender === '2') {
    return females[_index]
  } else {
    return males[_index]
  }
}
</script>

<style lang="scss" scoped>
.mgm-list {
  min-height: 100vh;
  width: 100%;
  background-color: #fff;
  z-index: 2;
  position: relative;
  .navs {
    height: 85px;
    display: flex;
    align-items: center;
    justify-content: center;
    .item {
      width: 33.333%;
      height: 100%;
      border-bottom: 4px solid #fff;
      font-size: 30px;
      color: #364a95;
      font-weight: 500;
      line-height: 85px;
      cursor: pointer;
      &.actived {
        border-color: #3c78fc;
        color: #3c78fc;
      }
    }
  }
  .desc-top {
    width: 100%;
    height: 236px;
    box-sizing: border-box;
    padding-top: 30px;
    padding-bottom: 40px;
    background: linear-gradient(to bottom, #fff, #ecf7ff);
    position: relative;
    &::before {
      content: '';
      width: 300px;
      height: 150px;
      background: url('../img/circle-right.png') no-repeat;
      background-size: 100%;
      position: absolute;
      z-index: 1;
      top: 0;
      right: 0;
    }
    &::after {
      content: '';
      width: 280px;
      height: 140px;
      background: url('../img/circle-left.png') no-repeat;
      background-size: 100%;
      position: absolute;
      z-index: 1;
      left: 0;
      bottom: 0;
    }
    .count {
      font-size: 24px;
      color: #868fb8;
      line-height: 1.5;
      span {
        font-size: 46px;
        color: #ea2222;
        font-weight: bold;
      }
    }
    p {
      font-size: 28px;
      color: #364a95;
      letter-spacing: 0;
      line-height: 42px;
      font-weight: 600;
      position: relative;
      z-index: 2;
      &.tip {
        width: 600px;
        margin: 0 auto;
        font-size: 24px;
        color: #868fb8;
        letter-spacing: 0;
        line-height: 36px;
        font-weight: 400;
      }
    }
  }

  .list-item {
    border-bottom: 1px solid rgba(134, 143, 184, 0.192);
    box-sizing: border-box;
    padding: 20px 30px;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    .avator {
      width: 70px;
      height: 70px;
      border-radius: 100%;
      margin-right: 20px;
    }
    .info {
      flex: auto;
      text-align: left;
      margin-top: -5px;
      .name {
        font-size: 30px;
        color: #364a95;
        letter-spacing: 0;
        line-height: 45px;
        font-weight: 400;
      }
      .time {
        font-size: 24px;
        color: #868fb8;
        letter-spacing: 0;
        line-height: 36px;
        font-weight: 400;
      }
      .reward-time {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-top: 5px;
        .text {
          background: #dadde685;
          border-radius: 5px;
          margin-right: 10px;
          font-size: 20px;
          color: #808bab;
          letter-spacing: 0;
          line-height: 38px;
          font-weight: 400;
          border-radius: 5px;
          padding: 0 10px;
          box-sizing: border-box;
        }
        .icon {
          width: 105px;
          height: 40px;
        }
      }
    }
  }
  .empty {
    margin-top: 30px;
    .icon {
      width: 250px;
      height: 260px;
      margin: 0 auto;
    }
    p {
      font-size: 24px;
      color: #868fb8;
      letter-spacing: 0;
      line-height: 45px;
      font-weight: 400;
      margin-top: 20px;
    }
  }
  .btn {
    background-image: linear-gradient(180deg, #f1f9ff 0%, #dff0ff 100%);
    border: 2px solid #0c88ff;
    border-radius: 32.5px;
    font-size: 24px;
    color: #0c88ff;
    letter-spacing: 0;
    font-weight: 500;
    width: 153px;
    height: 65px;
    cursor: pointer;
    line-height: 63px;
  }
}
</style>
