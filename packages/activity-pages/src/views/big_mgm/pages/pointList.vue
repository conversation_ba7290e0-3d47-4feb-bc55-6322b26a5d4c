<template>
  <div class="point-list">
    <div class="header">
      <div class="item">
        <p class="title">昨日积分奖励<span>(分)</span></p>
        <p class="score">{{ pointData.lastDayPoint }}</p>
      </div>
      <div class="item">
        <p class="title">累计积分奖励<span>(分)</span></p>
        <p class="score">{{ pointData.amountPoint }}</p>
      </div>
    </div>
    <div class="list-opt">
      <div class="selector" @click="showSelector = true" :class="{ up: showSelector }">{{ showSelectedDate }}</div>
      <div class="selector-list" v-if="showSelector">
        <div class="item" v-for="(item, index) in selectorData" :key="index" @click="changeDate(item.val)">
          {{ item.text }}
        </div>
      </div>
      <div class="info">总计: {{ monthTotal }}积分</div>
    </div>
    <div class="list">
      <div class="item" v-for="(item, index) in showList" :key="index">
        <p class="time">{{ item.time }}</p>
        <p class="time">
          <span>+{{ item.point }}</span
          >积分
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { compile, ref, computed, watchEffect } from 'vue'
import { focusCore, focusStore, focusServices, Mask, focusUtils } from '@focus/render'
import service from '../service/service'

const pointData = ref<any>({
  lastDayPoint: '--',
  amountPoint: '--',
})

const thisYear = new Date().getFullYear()
const thisMonth = new Date().getMonth() + 1
const selectedDate = ref(`${thisYear}${thisMonth > 9 ? thisMonth : `0${thisMonth}`}`)

const monthTotal = ref<number | string>(0)
const showSelector = ref(false)
const selectorData = computed(() => {
  const result: {
    val: string
    text: string
  }[] = []
  // 近24个月
  new Array(3).fill(0).forEach((i, index) => {
    const cur = thisYear - index
    let allMonth = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].reverse()
    if (cur === thisYear) {
      allMonth = allMonth.filter((i) => i <= thisMonth)
    }
    allMonth.forEach((month) => {
      const val = `${cur}${month > 9 ? month : `0${month}`}`
      const text = `${cur}年${month}月`
      result.push({ val, text })
    })
  })
  // 只要近24个月
  return result.slice(0, 24)
})

const showSelectedDate = computed(() => {
  const d = selectorData.value.find((i) => {
    return i.val === selectedDate.value
  })
  return d ? d.text : ''
})
const showList = ref<any>([])

watchEffect(() => {
  if (selectedDate.value) {
    service.queryPointList(selectedDate.value).then((res) => {
      console.log('🚀 ~ service.queryPointList ~ res:', res)
      showList.value = res.voList
      monthTotal.value = res.pointCount
      pointData.value = {
        lastDayPoint: res.lastDayPoint,
        amountPoint: res.totalPoint,
      }
    })
  }
})

function changeDate(dateVal: string) {
  selectedDate.value = dateVal
  showSelector.value = false
}
</script>

<style scoped lang="scss">
.point-list {
  width: 100%;
  min-height: 100vh;
  background: #fffeff;
  z-index: 2;
  position: relative;
  .header {
    background: -webkit-linear-gradient(#fff, #ecf3ff);
    background: linear-gradient(#fff, #ecf3ff);
    height: 200px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    &::before {
      content: '';
      width: 300px;
      height: 150px;
      background: url('../img/circle-right.png') no-repeat;
      background-size: 100%;
      position: absolute;
      z-index: 1;
      top: 0;
      right: 0;
    }
    &::after {
      content: '';
      width: 280px;
      height: 140px;
      background: url('../img/circle-left.png') no-repeat;
      background-size: 100%;
      position: absolute;
      z-index: 1;
      left: 0;
      bottom: 0;
    }
    .item {
      width: 50%;
      height: fit-content;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      flex-direction: column;
      z-index: 2;
      &:first-child {
        border-right: 1px solid #ebedf4;
      }
      p.title {
        font-size: 28px;
        color: #364b8d;
        line-height: 1;
        font-weight: bold;
        line-height: 42px;
        span {
          font-size: 24px;
        }
      }
      p.score {
        font-size: 46px;
        line-height: 1;
        margin-top: 25px;
        color: #ea2222;
        letter-spacing: 0;
      }
    }
  }
  .list-opt {
    height: 72px;
    font-size: 26px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 30px;
    position: relative;
    .selector-list {
      position: absolute;
      background: #fff;
      box-shadow: 10px 1px 10px rgba(153, 153, 153, 0.5);
      border-radius: 10px;
      top: 100%;
      left: 0;
      padding: 30px;
      height: 300px;
      overflow-y: scroll;
      text-align: left;
      .item {
        line-height: 2;
        color: #575a6e;
      }
    }
    .selector {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      font-size: 26px;
      color: #0c88ff;
      letter-spacing: 0;
      line-height: 37px;
      font-weight: 400;
      padding-left: 30px;
      p {
        color: #0c88ff;
      }

      &::after {
        content: '';
        display: block;
        margin-left: 5px;
        width: 18px;
        height: 13px;
        background: url('../img/arrow_down.png') no-repeat;
        background-size: 100%;
        transform-origin: center center;
      }
      &.up {
        &::after {
          transform: rotateX(180deg);
        }
      }
    }
    .info {
      line-height: 72px;
      text-align: right;
      color: #868fb8;
    }
  }

  .list {
    width: 100%;
    .item {
      border-top: 1px solid #eee;
      width: 100%;
      height: 72px;
      line-height: 72px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30px;
      p {
        font-size: 28px;
        color: #868fb8;
        span {
          color: #f29661;
        }
      }
    }
  }
}
</style>
