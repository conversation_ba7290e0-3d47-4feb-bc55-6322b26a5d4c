<script setup lang="ts">
import { focusCore, focusStore, focusServices, Mask, focusUtils } from '@focus/render'

const { UrlParser } = focusUtils
const urlQuery = new UrlParser().query
if (urlQuery.focus_review === '1' || urlQuery.focus_preview === '1') {
  focusCore.usePreviewMode()
}
</script>

<template>
  <router-view v-slot="{ Component }">
    <KeepAlive>
      <component :is="Component" />
    </KeepAlive>
  </router-view>
</template>

<style lang="scss">
@import '../../styles/normalize.scss';
#app {
  width: 100%;
  min-height: 100vh;
  text-align: center;
  overflow-x: hidden;
  line-height: 0;
  font-size: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  position: relative;
}
</style>
