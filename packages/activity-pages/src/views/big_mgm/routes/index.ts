import home from '../pages/home.vue'
// import photoDetail from '../components/photograph/PhotoDetail.vue'
import { createRouter, createWebHashHistory } from 'vue-router'
import { focusCore, focusServices } from '@focus/render'
import mgmlist from '../pages/mgmlist.vue'
import pointList from '../pages/pointList.vue'
const { loginService } = focusServices
const routes = [
  {
    path: '/',
    redirect: '/home',
  },
  {
    path: '/!/home',
    redirect: '/home',
  },
  {
    path: '/home',
    component: home,
    meta: {
      noNeedLogin: true,
      title: '微众朋友圈',
    },
  },
  {
    path: '/mgmlist',
    component: mgmlist,
    meta: {
      noNeedLogin: true,
      title: '我的亲友',
    },
  },
  {
    path: '/pointlist',
    component: pointList,
    meta: {
      noNeedLogin: true,
      title: '奖励记录',
    },
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes, // `routes: routes` 的缩写
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  },
})
router.beforeEach((to, from, next) => {
  focusCore.routerBeforeEach(to, from, () => {
    console.log('🚀 ~ file: index.ts:32 ~ focusCore.routerBeforeEach ~ to:', to)
    const { title, noNeedLogin = false } = to.meta
    focusCore.setPageTitle(title)
    next()
  })
})

export default router
