import { defineStore } from 'pinia'

export const familyStore = defineStore('family', {
  // other options...
  state: (): {
    familyDaylySteps: number
    familyMonthPoint: number
    familyTotalPoint: number
    personalTotalPoint: number
    hadMonthReward: boolean //获得过月度奖励
    receiver_name: string
    stepConfig?: {
      awardDaily: number
      awardDescMonthly: string
      cumulativeDaysMonthly: number
      familyCountLimit: number
      familyStepDaily: number
      firstAwardDaily: number
      personalStepDaily: number
      privilegeCardId: number
      redPacketLimit: number
    }
  } => ({
    familyDaylySteps: 0,
    familyMonthPoint: 0,
    familyTotalPoint: 0,
    personalTotalPoint: 0,
    hadMonthReward: false,
    receiver_name: '',
    stepConfig: {
      awardDaily: 1,
      awardDescMonthly: '1',
      cumulativeDaysMonthly: 1,
      familyCountLimit: 0,
      familyStepDaily: 1,
      firstAwardDaily: 1,
      personalStepDaily: 1,
      privilegeCardId: 1,
      redPacketLimit: 1,
    },
  }),
  getters: {},
  actions: {
    setStepConfig(data: {
      awardDaily: number
      awardDescMonthly: string
      cumulativeDaysMonthly: number
      familyCountLimit: number
      familyStepDaily: number
      firstAwardDaily: number
      personalStepDaily: number
      privilegeCardId: number
      redPacketLimit: number
    }) {
      this.stepConfig = data
    },
    setFamilyWalkData(data: {
      family_day_steps: number
      family_month_points: number
      family_total_points: number
      personal_total_points: number
      receiver_name: string
      month_award_status: string
    }) {
      console.log('🚀 ~ file: store.ts ~ line 17 ~ data', data)

      this.familyDaylySteps = data.family_day_steps
      this.familyMonthPoint = data.family_month_points
      this.familyTotalPoint = data.family_total_points
      this.personalTotalPoint = data.personal_total_points
      this.hadMonthReward = data.month_award_status === '1'
      this.receiver_name = data.receiver_name || ''
    },
  },
})

export default familyStore
