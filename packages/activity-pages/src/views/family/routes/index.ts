import homeVue from '../pages/home.vue'
import productDetail from '../pages/product-detail.vue'
import { createRouter, createWebHashHistory } from 'vue-router'
import walkingVue from '../pages/walking.vue'
import walkingMailVue from '../pages/walking-mail.vue'
import bigImgVue from '../pages/big-img.vue'
import walkingSpecailVue from '../pages/walking-specail.vue'
const routes = [
  {
    path: '/',
    redirect: '/home',
    meta: {
      title: '我的家庭',
    },
  },
  {
    path: '/home',
    component: homeVue,
    meta: {
      title: '我的家庭',
    },
  },
  {
    path: '/productDetail',
    component: productDetail,
  },
  {
    path: '/walking',
    component: walkingVue,
  },
  {
    path: '/walking-mail',
    component: walkingMailVue,
  },
  {
    path: '/walking-specail',
    component: walkingSpecailVue,
  },
  {
    path: '/bigimg',
    component: bigImgVue,
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes, // `routes: routes` 的缩写
})

export default router
