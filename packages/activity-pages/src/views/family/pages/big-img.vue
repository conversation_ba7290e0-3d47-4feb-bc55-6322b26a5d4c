<template>
  <div class="big-img">
    <img src="../img/walking/bg-bigimg.png" alt="" />
    <div class="name">{{ receiver_name }}</div>
  </div>
  <div class="btn" @click="save"></div>
  <focus-render :needLogin="false"></focus-render>
</template>

<script setup lang="ts">
import { ref, onMounted, watchEffect, computed } from 'vue'
import { storeToRefs } from 'pinia'
import familyService from '../service/familyService'
import { focusStore } from '@focus/render'
import { familyStore } from '../store/store'
import { useRouter } from 'vue-router'
import html2canvas from 'html2canvas'

const store = familyStore()
const { modalStore, userStore } = focusStore.stores
const { receiver_name, hadMonthReward } = storeToRefs(store)
familyService.getFamilyWalkListData()

const dataUrl = ref('')

function loadImg(cb?: Function) {
  modalStore.loadingStart('make_pic')
  setTimeout(() => {
    const dom = document.querySelector('.big-img')
    dom &&
      html2canvas(dom, {
        useCORS: true,
        scale: 3,
        allowTaint: true,
      }).then((canvas) => {
        dataUrl.value = canvas.toDataURL('img/jpg')
        modalStore.loadingEnd('make_pic')
        console.log(dataUrl.value)

        cb && cb(dataUrl.value)
      })
  }, 100)
}

function save() {
  modalStore.loadingStart('save_img')
  const hjCoreIab = window.hjCoreIab

  if (dataUrl.value) {
    hjCoreIab &&
      hjCoreIab.saveImage(
        {
          imgData: dataUrl.value,
          isToastDisabled: true,
        },
        () => {
          modalStore.loadingEnd('save_img')

          modalStore.toastShow('图片保存成功！')
        },
        (error: any) => {
          modalStore.loadingEnd('save_img')

          console.log(error)
          modalStore.toastShow('图片保存失败！')
        }
      )
  } else {
    loadImg((dataUrl: string) => {
      hjCoreIab &&
        hjCoreIab.saveImage(
          {
            imgData: dataUrl,
            isToastDisabled: true,
          },
          () => {
            modalStore.loadingEnd('save_img')

            modalStore.toastShow('图片保存成功！')
          },
          (error: any) => {
            console.log(error)
            modalStore.loadingEnd('save_img')

            modalStore.toastShow('图片保存失败！')
          }
        )
    })
  }
}
</script>

<style lang="scss" scoped>
.big-img {
  height: 1625px;
  width: 100%;
  background-size: 100%;
  box-sizing: border-box;
  position: relative;
  .cover {
    width: 100%;
    height: 100%;
  }
  .name {
    font-size: 40px;
    color: #515962;
    letter-spacing: 1.67px;
    line-height: 48px;
    font-weight: 700;
    position: absolute;
    top: 627px;
    left: 250px;
    width: 167px;
    text-align: center;
  }
}
.btn {
  width: 100%;
  height: 134px;
  margin: 0 auto;
  cursor: pointer;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 222;
  background: url('../img/walking/btn-save.png') no-repeat;
  background-size: 100%;
}
</style>
