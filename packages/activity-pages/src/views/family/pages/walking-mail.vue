<template>
  <div class="mail">
    <div class="bg"></div>
    <div class="reward-book">
      <div class="name">{{ formData.name }}</div>
    </div>
    <div class="info-list">
      <div class="item">
        <div class="label">收货人</div>
        <div class="value">
          <input type="text" v-model="formData.name" placeholder="填入收货人姓名" :maxlength="4" />
        </div>
      </div>
      <div class="item">
        <div class="label">手机号码</div>
        <div class="value">
          <input
            type="phone"
            v-model="formData.phone"
            placeholder="填入收货人手机号"
            :maxlength="11"
            @blur="checkShowToast"
          />
        </div>
      </div>
      <div class="item">
        <div class="label">所在城市</div>
        <div class="value" @click="showDialog = true" :class="{ nodata: !formData.address }">
          {{ formData.address || '选择所在城市' }}
        </div>
      </div>
      <div class="item">
        <div class="label">详细地址</div>
        <div class="value">
          <input type="text" v-model="formData.addressDesc" placeholder="填入详细地址到门牌号" :maxlength="30" />
        </div>
      </div>
    </div>
    <div class="btn-submit" @click="submit">确定</div>
  </div>

  <DialogMask :show="showDialog" :justifyContent="'flex-end'" @touchmove.prevent>
    <template #dialog_contain>
      <addressSelector :hide="hideDialog" :setAddress="inputAddress"></addressSelector>
    </template>
  </DialogMask>

  <focus-render :needLogin="false"></focus-render>
</template>

<script setup lang="ts">
import { DialogMask } from '@focus/render'
import { ref, onMounted, watchEffect, computed } from 'vue'
import { storeToRefs } from 'pinia'
import addressSelector from '../components/addressSelector.vue'
import familyService from '../service/familyService'
import { focusStore, focusServices, Mask, focusCore } from '@focus/render'
import { familyStore } from '../store/store'
import { useRouter } from 'vue-router'
const router = useRouter()
const store = familyStore()
const { modalStore, userStore } = focusStore.stores
const { receiver_name, hadMonthReward } = storeToRefs(store)

const showDialog = ref(false)

const formData = ref({
  name: '',
  phone: '',
  address: '',
  addressDesc: '',
})

onMounted(() => {
  familyService.getFamilyWalkListData()
})
watchEffect(() => {
  console.log()
  console.log(formData.value)
})

function hideDialog() {
  showDialog.value = false
}
function inputAddress(str: string) {
  console.log('🚀 ~ file: walking-mail.vue ~ line 68 ~ inputAddress ~ str', str)
  formData.value.address = str
  hideDialog()
}

const phoneReady = computed(() => {
  const reg = /^[1][3,4,5,7,8][0-9]{9}$/
  console.log(
    '🚀 ~ file: walking-mail.vue ~ line 89 ~ phoneReady ~ reg.test(formData.value.phone)',
    reg.test(formData.value.phone)
  )
  return reg.test(formData.value.phone)
})

function checkShowToast() {
  if (!phoneReady.value) {
    modalStore.toastShow('请输入正确的手机号！')
  }
}

function submit() {
  if (Object.keys(formData.value).some((key) => !formData.value[key])) {
    modalStore.toastShow('请填写所有内容！')
    return false
  }
  if (!phoneReady.value) {
    modalStore.toastShow('请输入正确的手机号！')
    return false
  }
  modalStore.confirmContrl({
    show: true,
    title: '确认收货地址',
    contents: ['请确认您的地址准确无误，我们将尽快为您寄出实体奖状'],
    btnConfirmText: '确认',
    confirmCb: () => {
      modalStore.loadingStart('submit')
      familyService.saveMailInfo(formData.value).then((flag: boolean) => {
        modalStore.loadingEnd('submit')
        if (flag) {
          modalStore.toastShow('保存成功！')
          router.replace({
            path: 'walking',
          })
        } else {
          modalStore.toastShow('保存失败！')
        }
        familyService.getFamilyWalkListData()
      })
    },
  })
}
</script>

<style lang="scss" scoped>
.mail {
  font-size: 28px;
  line-height: 1.5;
  width: 100%;
  height: 100%;
  color: #445c95;
  font-weight: 400;
  position: relative;
  padding-top: 50px;
  background: #fafafa;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  &::after {
    content: '';
    background: #3d60cc;
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 200px;
    z-index: 1;
  }
  .reward-book {
    z-index: 2;
    position: relative;
    width: 702px;
    height: 470px;
    margin: 0 auto;
    background: url('../img/walking/award-book-big.png') no-repeat;
    background-size: 100% 100%;
    .name {
      position: absolute;
      width: 140px;
      height: 32px;
      font-size: 28px;
      line-height: 32px;
      color: #525961;
      left: 40px;
      top: 158px;
    }
  }
  .info-list {
    z-index: 2;
    width: 100%;
    padding-left: 30px;
    position: relative;
    background: #fff;
    margin-top: 30px;
    .item {
      width: 100%;
      box-sizing: border-box;
      padding: 30px;
      padding-left: 0;
      border-bottom: 1px solid #e5e5e5;
      font-size: 28px;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;

      .label {
        margin-right: 30px;
        flex: none;
      }
      .value {
        flex: auto;
        text-align: left;
        &.nodata {
          color: #b5b5b5;
        }
        input {
          width: 100%;
          &::placeholder {
            color: #b5b5b5;
          }
        }
      }
    }
  }
  .btn-submit {
    width: 690px;
    background: #f29661;
    border-radius: 44px;
    height: 88px;
    line-height: 86px;
    font-size: 32px;
    font-weight: bold;
    color: #fff;
    text-align: center;
    position: fixed;
    bottom: 30px;
    left: 30px;
    &.disabled {
      opacity: 0.5;
    }
  }
}
</style>
