<template>
  <!-- 产品分享落地页 -->
  <focus-render :focusFid="focusFid" class="focus-config" @onLoginSucc="afterLoginSucc">
    <template v-slot:1>
      <div class="container">
        <div class="title">
          <img class="title-image" :src="productDetail.extra_info.bank_logo_url" alt="" />
          <span v-for="str in productDetail.product_name" class="title-text">{{ str }}</span>
        </div>
        <div v-if="tag.length" class="tag-list">
          <div class="tag" v-for="item in tag">{{ item }}</div>
        </div>
        <div class="content-top">
          <div class="rate">{{ productDetail.rate_value }}</div>
          <div class="period">{{ productDetail.product_period }}</div>
        </div>
        <div class="content-bottom">
          <div class="desc">{{ productDetail.extra_info.rate_desc }}</div>
          <div class="min-amount">{{ productDetail.extra_info.min_amount_desc_recommend }}</div>
        </div>
        <div
          class="text-more"
          @click="showMore"
          :class="{ active: isShowMore }"
          v-if="productDetail.extra_info.static_rate_tips"
        >
          <p>
            {{ productDetail.extra_info.static_rate_tips }}
          </p>
          <div class="icon"></div>
        </div>
        <div class="btn" @click="handleClick">去看看</div>
      </div>
    </template>
  </focus-render>
</template>

<script setup lang="ts">
import { Ref, ref, watch } from 'vue'
import { UrlParser } from '../../../utils'
import { focusServices, focusStore } from '@focus/render'
const { productService } = focusServices
let focusFid = '1285'
let jumpLink = 'https://personal.webank.com/s/hj/focus2/client/index.html?fid=891&bg=_ffffff'
if (BUILD_TEST) {
  focusFid = '315'
  jumpLink = 'https://personal.test.webank.com/s/hj/focus2/client/index.html?fid=229&bg=_ffffff'
}
const handleClick = () => {
  focusServices.jumpService.jump({ path: code, method: 'productCode' })
}
const { userStore } = focusStore.stores
const RISK_LEVEL_MAP = {
  0: '低',
  1: '较低',
  2: '中等',
  3: '较高',
  4: '高',
}
const hasAccount = ref(false)
const { query = {} } = new UrlParser() || {}
const { code = '' } = query
const tag: Ref<string[]> = ref([])
const isShowMore = ref(false)
/* TODO:兜底 */
const productDetail: Ref<Product> = ref({
  product_period: '',
  sale_status: '',
  product_name: '',
  product_code: '',
  product_type: '',
  templet_type: '',
  rate_value: '',
  show_start_buy_tag: '',
  marketing_text: '',
  risk_level: '',
  extra_info: {
    bank_logo_url: '',
    bank_short_name: '',
    header_tags: [],
    tag: [],
    min_amount_desc: '',
    rate_desc: '',
    is_bank_finance: '',
    min_amount_desc_recommend: '',
  },
})
function showMore() {
  isShowMore.value = !isShowMore.value
}

watch<Product>(productDetail, (detail) => {
  const riskStr = RISK_LEVEL_MAP[detail.risk_level] ? `${RISK_LEVEL_MAP[detail.risk_level]}风险` : ''
  const minAmount = detail.extra_info.min_amount_desc_recommend || ''
  const validTag = [riskStr, minAmount].filter((s) => Boolean(s))
  tag.value = validTag
})

function afterLoginSucc(loginData: any) {
  console.log('.....login,', loginData)
  if (loginData.hasAccount) {
    hasAccount.value = true
  }
  /* 未登录也能访问 */
  const getProductListByCodes = loginData.isLogined
    ? productService.getProductListByCodesLogin.bind(productService)
    : productService.getProductListByCodesWithoutLogin.bind(productService)
  getProductListByCodes([code]).then((res: Product[]) => {
    if (res[0]) {
      productDetail.value = res[0]
    }
  })
}
</script>

<style lang="scss" scoped>
.text-more {
  background: #f3f7f8;
  width: 100%;
  color: #9b9b9b;
  text-align: left;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  margin-top: 30px;
  border-radius: 10px;
  .icon {
    width: 40px;
    height: 40px;
    background: url('../img/btn_down.png') no-repeat;
    background-size: 100% 100%;
    flex: none;
    margin-right: 20px;
    margin-top: 10px;
  }
  p {
    flex: auto;
    font-size: 32px;
    line-height: 1.5;
    padding: 10px 20px;
    overflow: hidden;
    margin-right: 20px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &.active {
    .icon {
      transform: rotate(180deg);
    }
    p {
      word-wrap: break-word;
      white-space: normal;
    }
  }
}
.container {
  position: relative;
  width: 690px;
  min-height: 539px;
  background-color: #ffffff;
  border-radius: 10px;
  padding: 0 40px;
  line-height: 1;
  overflow: hidden;
}

.title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  margin-top: 94px;
  font-weight: bold;
}
$iconWidth: 48px;
.title-image {
  width: $iconWidth;
  height: $iconWidth;
  margin-right: 20px;
}

.title-text {
  font-size: 40px;
}

.tag-list {
  margin-top: 20px;
  display: flex;
}

.tag {
  margin-right: 16px;
  padding: 8px;
  background-color: #ecf0fc;
  border-radius: 4px;
  font-size: 28px;
  font-weight: normal;
  font-stretch: normal;
  color: #456ce6;
  box-sizing: border-box;
}

.content-top {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin-top: 32px;
}

.content-bottom {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.rate {
  font-size: 60px;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #ff2433;
  font-weight: bold;
}

.period {
  font-size: 48px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 2px;
  color: #242d3a;
  font-weight: bold;
}

.desc {
  font-size: 32px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #666666;
  opacity: 0.7;
}

.min-amount {
  font-size: 32px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #666666;
  opacity: 0.7;
}

.btn {
  margin: 62px auto 69px auto;
  width: 520px;
  height: 81px;
  font-size: 48px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 81px;
  text-align: center;
  letter-spacing: 0px;
  color: #ffffff;
  background: url('../img/product-go-detai.png') no-repeat;
  background-size: contain;
  text-shadow: 0 0 3px #f68d5c;
}
</style>
