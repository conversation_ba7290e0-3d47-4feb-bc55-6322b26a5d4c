<template>
  <template v-if="!useNewWalking">
    <family-list @dataIsReady="ready" @useNewWalking="checkWalkingStatus"></family-list>

    <!-- 未建立家庭 -->
    <focus-render
      v-if="!isEnterMyFamlily"
      :focusFid="focusFid"
      class="focus-config"
      @onLoginSucc="afterLoginSucc"
      @onFocusConfigSucc="getFocusData"
      :renderWhenParentIsReadyStatus="readyStatus"
    ></focus-render>
    <focus-render
      v-if="isEnterMyFamlily && !userIsM2"
      :focusFid="enterFamilyFid"
      class="focus-config"
      @onLoginSucc="afterLoginSucc"
      @onFocusConfigSucc="getFocusData"
    >
      <template #family_walking_pannel="slotData">
        <WalkingPannel></WalkingPannel>
      </template>
    </focus-render>

    <focus-render v-if="isEnterMyFamlily && userIsM2" :focusFid="enterFamilyFidForM2" class="focus-config">
      <template #family_walking_pannel="slotData">
        <WalkingPannel></WalkingPannel>
      </template>
    </focus-render>
  </template>

  <!-- 节日版 -->
  <template v-if="useNewWalking">
    <!-- 未建立家庭 -->
    <focus-render
      v-if="!isEnterMyFamlily"
      :focusFid="focusFidNew"
      class="focus-config"
      @onLoginSucc="afterLoginSucc"
      @onFocusConfigSucc="getFocusData"
    >
      <template #familylist>
        <FamilyListSpecial></FamilyListSpecial>
      </template>
    </focus-render>

    <!-- 已建立家庭-M1 -->
    <focus-render
      v-if="isEnterMyFamlily && !userIsM2"
      :focusFid="enterFamilyFidNew"
      class="focus-config"
      @onLoginSucc="afterLoginSucc"
      @onFocusConfigSucc="getFocusData"
    >
      <template #familylist>
        <FamilyListSpecial></FamilyListSpecial>
      </template>

      <template #family_walking_pannel="slotData">
        <WalkingPannel :useNewWalking="useNewWalking"></WalkingPannel>
      </template>
    </focus-render>

    {{ userIsM2 }}

    <!-- 已建立家庭-M2 -->
    <focus-render v-if="isEnterMyFamlily && userIsM2" :focusFid="enterFamilyFidForM2New" class="focus-config">
      <template #familylist>
        <FamilyListSpecial></FamilyListSpecial>
      </template>
      <template #family_walking_pannel="slotData">
        <WalkingPannel :useNewWalking="useNewWalking"></WalkingPannel>
      </template>
    </focus-render>
  </template>
</template>

<script setup lang="ts">
import { provide, readonly, ref } from 'vue'
import { focusCore, focusUtils } from '@focus/render'
import FamilyList from '../components/FamilyList.vue'
import WalkingPannel from '../components/WalkingPannel.vue'
import { useRouter } from 'vue-router'
import FamilyListSpecial from '../components/FamilyListSpecial.vue'

const router = useRouter()

let focusFid = '888' // 我的家庭-首页-未建立家庭
let enterFamilyFid = '1284' // 我的家庭-首页-未建立家庭
let ruleLink = 'https://personal.webank.com/s/hj/focus2/client/index.html?fid=891&bg=_ffffff' // 我的家庭-首页-规则
let equityPage = 'https://personal.webank.com/s/hj/focus2/client/index.html?fid=1083&bg=_ffffff' // 我的家庭-首页-权益
let enterFamilyFidForM2 = '1772' // 我的家庭-首页-已建立家庭-M2

// 节日版
let focusFidNew = '2859'
let enterFamilyFidNew = '2861'
let ruleLinkNew = 'https://personal.webank.com/s/hj/focus2/client/index.html?fid=2867'
let equityPageNew = 'https://personal.webank.com/s/hj/focus2/client/index.html?fid=1083'
let enterFamilyFidForM2New = '2862'

// 测试环境链接
if (BUILD_TEST) {
  focusFid = '221'
  enterFamilyFid = '238'
  enterFamilyFidForM2 = '396'
  ruleLink = 'https://personal.test.webank.com/s/hj/focus2/client/index.html?fid=229&bg=_ffffff'
  equityPage = 'https://personal.test.webank.com/s/hj/focus/index.html?fid=223&aid=6831&bg=_ffffff'

  // 节日版
  focusFidNew = '2296'
  enterFamilyFidNew = '2297'
  ruleLinkNew = 'https://personal.webank.com/s/hj/focus2/client/index.html?fid=891&bg=_ffffff'
  equityPageNew = 'https://personal.webank.com/s/hj/focus2/client/index.html?fid=1083&bg=_ffffff'
  enterFamilyFidForM2New = '2298'
}

const familyMgmAid = 6771
const hasAccount = ref(false)
const shareConfigData = ref({})
const isEnterMyFamlily = ref(false)
const userIsM2 = ref(false)
provide('familyMgmAid', familyMgmAid)
provide('hasAccount', hasAccount)
provide('shareConfigData', shareConfigData)
provide('ruleLink', ruleLink)
provide('userIsInFamilyStatus', userIsInFamilyStatus)
provide('setUserIsM2', setUserIsM2)
provide('equityPage', equityPage)
provide('equityPageNew', equityPageNew)
provide('userIsM2', userIsM2)

const useNewWalking = ref(false)
const readyStatus = ref(1)

function userIsInFamilyStatus() {
  isEnterMyFamlily.value = true
}

function checkWalkingStatus(status: number) {
  console.log('🚀 ~ file: home.vue:91 ~ checkWalkingStatus ~ status:', status)
  useNewWalking.value = status === 2
}

function ready() {
  readyStatus.value = 2
}

function setUserIsM2(isM2: boolean) {
  userIsM2.value = isM2
}
function afterLoginSucc(loginData: any) {
  console.log('.....login,', loginData)
  if (loginData.hasAccount) {
    hasAccount.value = true
    console.log('🚀 ~ file: home.vue:150 ~ afterLoginSucc ~ hasAccount.value :', hasAccount.value)
  }
}

function getFocusData(focueRenderCtl) {
  console.log('.....focusData,', focueRenderCtl)
  shareConfigData.value = focueRenderCtl.getShareConfigByConfigId(1)

  focusCore.useVueRouterRedirect(router, '/family/index.html', ['/walking', '/home'])
}
</script>

<style lang="scss" scoped>
.wrap {
  width: 100%;
  height: auto;
  opacity: 0;
}

.logo-bottom {
  flex: none;
  box-sizing: border-box;
  border: none;
  display: block;
  line-height: 0;
  width: 100%;
  font-size: 0;
  overflow: hidden;
  margin: auto auto 0;
  .img {
    width: 100%;
    height: 100%;
    display: block;
  }
}
</style>
