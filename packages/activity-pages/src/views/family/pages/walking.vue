<template>
  <!-- 未组建家庭 -->
  <focus-render
    :focusFid="focusFid1"
    class="focus-config"
    @onFocusConfigSucc="getFocusData"
    v-if="!isEnterMyFamlily"
    @onLoginSucc="afterLoginSucc"
    :hideFocusModules="!dataIsReady || isEnterMyFamlily"
    :renderWhenParentIsReadyStatus="readyStatus"
  ></focus-render>

  <family-list :hideList="'0'" v-if="!isEnterMyFamlily" @useNewWalking="checkWalkingStatus"></family-list>
  <!-- 已组建家庭 & 未参与 9694 -->
  <focus-render :focusFid="focusFid2" class="focus-config" v-if="isEnterMyFamlily && isShowFirstUI && dataIsReady">
  </focus-render>
  <div class="walking">
    <!-- 已组建家庭 -->
    <focus-render
      :focusFid="focusFid3"
      class="focus-config"
      @onFocusConfigSucc="getFocusData"
      v-if="isEnterMyFamlily && !isShowFirstUI && dataIsReady"
    >
      <template #family_walking_main="slotData">
        <div class="box today-info">
          <div class="avator">
            <img :src="userAvator" alt="" />
          </div>
          <div class="content">
            <div class="today-item">
              <p class="step">{{ todaySteps }}</p>
              <p>今日步数</p>
            </div>
          </div>
          <template v-if="!canUseIosSteps || isEncryptErr">
            <div class="btn" @click="gotoMini"></div>
          </template>
          <template v-else>
            <div v-if="showSubmitNow" class="btn submit-now" @click="submit"></div>
            <template v-else>
              <div class="btn" @click="gotoMini"></div>
              <div class="no-permission">
                推荐您去开启“运动与健身”权限，更加轻松打卡上传步数。您也可以通过设置 - 微众银行路径下打开权限
                <span @click="gotoSetting">去开启&gt;</span>
              </div>
            </template>
          </template>
        </div>

        <div class="box family-info">
          <img src="../img/walking/header-1.png" alt="" class="header" />
          <p class="title">
            本月家庭已获得<span>{{ familyMonthPoint }}</span
            >积分
          </p>
          <div class="content progress">
            <p>
              今日累计<span>{{ familyDaylySteps }}</span
              >步，目标{{ maxSteps }}步。
            </p>
            <div class="bar" :class="{ hasdaylistep: todaySteps > 0 }">
              <div class="bar-content">
                <div class="pro-step" :style="{ width: `${progressPercent}%` }">
                  <div class="dot"></div>
                  <div class="steps" v-if="todaySteps > 0">
                    <p>+{{ todaySteps }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="family-list">
            <div class="item" v-for="(item, index) in familyWalkingList">
              <img class="avator" :src="item.avator" />
              <p class="name">{{ item.name }}</p>
              <p class="step">
                今日<span>{{ item.steps }}</span
                >步
              </p>
            </div>
            <p class="tip">*当日全家上传步数累计达到{{ maxSteps }}步(每人限定5000步以上)，即送每人18积分</p>
          </div>
        </div>

        <!-- 月度没有奖励 -->
        <!-- <div class="box monthly-reward" v-if="monthTaskStatus === 0">
          <img src="../img/walking/header-2.png" alt="" class="header" />
          <p class="title">
            家庭成员每日完成上传步数2万步，单月累计<span style="color: #df5f4e; font-weight: bold">25</span
            >天即可获得“全家健步，齐赢积分”月度挑战奖状，<span style="color: #df5f4e">首次可获实体奖状</span>。
          </p>
          <div class="content info">
            <p>
              本月已完成<span>{{ monthCountTimes }}</span
              >次每日步数挑战
            </p>
            <div class="bar">
              <div class="bar-content">
                <div class="pro-step" :style="{ width: `${progressPercentMonth}%` }">
                  <div class="dot"></div>
                </div>
              </div>
            </div>
          </div>
        </div> -->

        <!-- 月度有奖励未填写地址 -->
        <!-- <div class="box monthly-reward" v-if="monthTaskStatus === 1" :class="`status_${monthTaskStatus}`">
          <img src="../img/walking/header-2.png" alt="" class="header" />
          <p class="title">
            恭喜您的家庭完成了月度挑战！当月累计25天完成每日任务可获得月度挑战奖状，首次可领取实体奖状。请点击下方按钮填写收货地址。
          </p>
          <img src="../img/walking/award-book.png" alt="" class="cover" />
          <div class="btn" @click="jumpToMail"></div>
        </div> -->

        <!-- 月度有奖励已填写地址 -->
        <div class="box monthly-reward" v-if="receiver_name" :class="`status_2`">
          <img src="../img/walking/header-2.png" alt="" class="header" />
          <p class="title" style="text-align: center">恭喜您的家庭完成了月度挑战</p>
          <img src="../img/walking/award-book-2.png" alt="" class="cover" />
          <div class="name">{{ receiver_name }}</div>
          <div class="btn" @click="jumpToBigimg"></div>
        </div>
      </template>
    </focus-render>

    <Mask v-if="showDialogData.id && showDialogData.type === 1">
      <!-- 今日积分 -->
      <div class="dialog-content">
        <div class="cancel" @click="readMsg(showDialogData.id)">取消</div>
        <div class="confirm" @click="readMsg(showDialogData.id, 'point')">去看看</div>
        <!-- <div class="confirm" v-if="showDialogData.type === 2 && receiver_name" @click="readMsg(showDialogData.id)">
          好的
        </div> -->
      </div>
    </Mask>
  </div>
</template>

<script setup lang="ts">
import FamilyList from '../components/FamilyList.vue'
import { focusCore, focusStore, focusServices, Mask, focusUtils } from '@focus/render'
import { onMounted, provide, ref, computed, watchEffect } from 'vue'
import familyService from '../service/familyService'
import { storeToRefs } from 'pinia'
import { familyStore } from '../store/store'
import { useRouter } from 'vue-router'

const { UrlParser } = focusUtils
const router = useRouter()
const store = familyStore()
const { familyDaylySteps, familyMonthPoint, familyTotalPoint, personalTotalPoint, receiver_name, hadMonthReward } =
  storeToRefs(store)
const { modalStore, userStore } = focusStore.stores
const { userService, jumpService, activityService } = focusServices
const props = defineProps({
  userInfo: Object,
})
const isEnterMyFamlily = ref(false)
const isEncryptErr = ref(false)
const device = ref()
const canUseIosSteps = ref(false)
const showSubmitNow = ref(true)

const shareConfigData = ref({})
const familyMgmAid = 6771
const hasAccount = ref(false)
let focusFid1 = 1773 //健步走 未组建家庭
let focusFid2 = 1786 //健步走 已组建家庭-第一次
let focusFid3 = 1776 //健步走 已组建家庭-非第一次
let focusFid1New = 0 //健步走 未组建家庭
let focusFid2New = 0 //健步走 已组建家庭-第一次
let focusFid3New = 0 //健步走 已组建家庭-非第一次
const dataIsReady = computed(() => {
  return walkingDataIsReady.value && familyDataIsReady.value
})
const walkingDataIsReady = ref(false)
const familyDataIsReady = ref(false)
const maxSteps = 20000
const readyStatus = ref(1) // 转为2就是可以渲染focus了

if (BUILD_TEST) {
  focusFid1 = 391
  focusFid2 = 409
  focusFid3 = 397
  focusFid1New = 2293
  focusFid2New = 2294
  focusFid3New = 2295
}

provide('shareConfigData', shareConfigData)
provide('familyMgmAid', familyMgmAid)
provide('userIsInFamilyStatus', userIsInFamilyStatus)
provide('hasAccount', hasAccount)
provide('familyDataIsReady', familyDataIsReady)

const userAvator = ref('')
const todaySteps = ref(0)
const familyWalkingList = ref()
const monthCountTimes = ref(0)
// const monthTaskStatus = computed(() => {
//   if (monthCountTimes.value < 25) {
//     return 0
//   }
//   if (monthCountTimes.value >= 25 && !receiver_name.value) {
//     return 1
//   }
//   if (monthCountTimes.value >= 25 && receiver_name.value) {
//     return 2
//   }
//   return 0
// })
//0 未完成；1 已完成-未填地址；2 已完成-已填地址

// 参与了9694
const isAdding9694 = ref(false)
// 已弹出过填地址的弹窗
const hadDilagAdress = ref(false)

//ios不区分是否参与9694
const isShowFirstUI = computed(() => {
  return !isAdding9694.value && device.value !== 'ios'
})

const progressPercent = computed(() => {
  return ((familyDaylySteps.value >= maxSteps ? maxSteps : familyDaylySteps.value) / maxSteps) * 100
})

const progressPercentMonth = computed(() => {
  return ((monthCountTimes.value >= 25 ? 25 : monthCountTimes.value) / 25) * 100
})

const originDialogList = ref<
  {
    awards_receive_status: number
    id: number
    msg_order: number
    msg_status: number
    type: number
  }[]
>([])

const showDialogData = computed<{
  awards_receive_status?: number
  id?: number
  msg_order?: number
  msg_status?: number
  type?: number
}>(() => {
  if (originDialogList.value.length) {
    return originDialogList.value[0]
  }
  return {}
})

function userIsInFamilyStatus() {
  isEnterMyFamlily.value = true
}

watchEffect(() => {
  console.log('🐬 ~ file: walking.vue:270 ~ userIsInFamilyStatus ~ isEnterMyFamlily.value:', isEnterMyFamlily.value)
  console.log('🐬 ~ file: walking.vue:24 ~ !isShowFirstUI:', !isShowFirstUI)
  console.log('🐬 ~ file: walking.vue:24 ~ dataIsReady:', dataIsReady.value)
})

watchEffect(() => {
  if (familyDataIsReady.value) {
    if (focusCore.env.isInWx) {
      showToApp()
    }
  }
})

function checkWalkingStatus(status: number) {
  console.log('🚀 ~ file: walking.vue:281 ~ checkWalkingStatus ~ status:', status)
  if (status === 2) {
    router.replace({
      path: '/walking-specail',
    })
  } else {
    readyStatus.value = 2
  }
}

function showToApp() {
  const { shareFrom = '', testWx } = new UrlParser().query
  const querys = shareFrom ? `entryFrom=${shareFrom}_wx` : ''
  if (testWx) {
    return
  }
  modalStore.loadingStart('jumpToApp')
  let link = `https://personal.webank.com/s/hj/focus2/family/index.html?${querys}#/walking`
  if (BUILD_TEST) {
    link = `https://personal.test.webank.com/s/hj/focus2/family/index.html?${querys}#/walking`
  }
  const url = link
  setTimeout(() => {
    jumpService.jump(
      {
        path: url,
        method: 'url',
      },
      true
    )
    modalStore.loadingEnd('jumpToApp')
  }, 200)
}

function getFocusData(focueRenderCtl: any) {
  console.log('🚀 ~ file: walking.vue ~ line 21 ~ getFocusData ~ focueRenderCtl', focueRenderCtl)
  shareConfigData.value = focueRenderCtl.getShareConfigByConfigId(1)
  console.log('🚀 ~ file: walking.vue ~ line 22 ~ getFocusData ~  shareConfigData.value', shareConfigData.value)
}

function afterLoginSucc(loginData: any) {
  console.log('.....login,', loginData)
  if (loginData.hasAccount) {
    console.log('🐬 ~ file: walking.vue:330 ~ afterLoginSucc ~ loginData.hasAccount:', loginData.hasAccount)
    hasAccount.value = true
    device.value = focusCore.env.device
    initData()
  } else {
    modalStore.errorMaskContrl('is_not_family_white_account', ['您不是受邀用户，无法参与此活动'], '', true)
  }
}
// 上传步数
function submit() {
  if (focusCore.env.isInApp) {
    modalStore.loadingStart('submit')
    familyService
      .uploadDaySteps()
      .then((res) => {
        console.log('上传步数返回', res)
        showSubmitNow.value = res.hasPermission
        if (res.hasPermission) {
          isEncryptErr.value = res.isEncryptErr || false
          if (res.status && !res.isEncryptErr) {
            modalStore.toastShow('打卡成功')
            // todaySteps.value = Number(res.count)
            initWalkingData()
          } else {
            modalStore.toastShow('打卡失败')
          }
        } else {
          modalStore.toastShow('未获取运动与健身权限，授权失败')
        }
      })
      .catch(() => {
        //用户App版本不支持/设备不支持  页面初始化时getPermission已经判断过了 此处可作为兜底
        canUseIosSteps.value = false
        modalStore.toastShow('不支持获取运动与健身权限')
      })
      .finally(() => {
        modalStore.loadingEnd('submit')
      })
  }
}

function gotoMini() {
  const env: any = {}
  if (BUILD_TEST) {
    env.miniEnv = 'develop'
    env.miniUsername = 'gh_f7447a1fcd79'
  }

  const minilink = `/packageActivity/pages/walking/walking?t=${Date.now()}&family_id=${
    familyService.familyId
  }&activity_id=${familyService.walkingAid}`

  modalStore.confirmContrl({
    show: true,
    contents: ['即将打开“微众银行”小程序'],
    btnConfirmText: '允许',
    btnConfirmJumpConfig: {
      path: `/pages/index/index?type=page&page=${encodeURIComponent(minilink)}`,
      method: 'mini',
      ...env,
    },
  })
}

function gotoSetting() {
  focusCore.invokeApp.lunchSystemSetting()
}

onMounted(() => {
  if (window.hjCoreIab && window.hjCoreIab.onAppResume) {
    window.hjCoreIab.onAppResume(() => {
      console.error('从App外面回来了！！')
      modalStore.confirmContrl({
        show: false,
      })
      initData()
    })
  }
})

function jumpToMail() {
  router.push({
    path: '/walking-mail',
  })
}
function jumpToBigimg() {
  router.push({
    path: '/bigimg',
  })
}
function readMsg(id?: number | string, path?: string) {
  if (!id) {
    return false
  }
  // if (id === 'address') {
  //   hadDilagAdress.value = true
  //   originDialogList.value.shift()
  //   if (path === 'rewardbook') {
  //     router.push({
  //       path: '/walking-mail',
  //     })
  //   }
  //   return
  // }
  modalStore.loadingStart('readMsg')
  familyService.readRewardMsg(Number(id)).then((status: boolean) => {
    if (!status) {
      modalStore.toastShow('网络异常！请重试！')
      modalStore.loadingEnd('readMsg')

      return
    }

    setTimeout(() => {
      modalStore.loadingEnd('readMsg')

      initData()
      if (path === 'point') {
        focusServices.jumpService.commonUse.userPoint()
      }
      // if (path === 'rewardbook') {
      //   router.push({
      //     path: '/walking-mail',
      //   })
      // }
    }, 100)
  })
}

function initWalkingData() {
  modalStore.loadingStart('initWalkingData')
  familyService.getWalkingReward().then(() => {
    modalStore.loadingStart('reward')
    familyService.getRewadToast().then((msgList: Array<any>) => {
      modalStore.loadingEnd('reward')
      let result = msgList
      // const defaultList = []

      // if (!hadDilagAdress.value && monthTaskStatus.value === 1) {
      //   defaultList.push({
      //     id: 'address',
      //     type: 2,
      //   })
      //   result = result.filter((i) => i.type === 1)
      // }
      //处理奖励弹窗
      // originDialogList.value = [...result, ...defaultList]
      originDialogList.value = [...result]
    })
  })
  familyService.getFamilyWalkListData().then(({ listData, curUserSteps, familyMonthCount }) => {
    familyWalkingList.value = listData
    todaySteps.value = Number(curUserSteps)
    monthCountTimes.value = familyMonthCount
    modalStore.loadingEnd('initWalkingData')
  })
}

function initData() {
  //判断是否支持获取步数和授权状态
  familyService.appHealthHandler
    .getPermission()
    .then((res) => {
      canUseIosSteps.value = true
      showSubmitNow.value = res.showSubmitNow
    })
    .catch(() => {
      canUseIosSteps.value = false
    })

  modalStore.loadingStart('check_9694')

  activityService.checkWrInfoStatus(9694).then((aid: number) => {
    isAdding9694.value = !!aid
    modalStore.loadingEnd('check_9694')

    modalStore.loadingStart('initdata')
    userService.getWxUserInfo().then(({ avator, defaultAvator }: { avator: string; defaultAvator: string }) => {
      console.log('🚀 ~ file: walking.vue ~ line 209 ~ userService.getWxUserInfo ~ avator', avator)
      userAvator.value = avator || defaultAvator
    })
    focusCore.onRedirectToSpecialPath('/family_mini', () => {
      console.log(shareConfigData.value)
      gotoMini()
    })
    initWalkingData()
    modalStore.loadingEnd('initdata')
    walkingDataIsReady.value = true
  })
}
</script>

<style lang="scss" scoped>
.bar {
  width: 500px;
  height: 20px;
  // border: 4px solid #f7e5cc;
  border-radius: 20px;
  background-color: #fbe5c9;
  position: relative;
  margin: 0 auto;
  margin-top: 45px;

  .bar-content {
    width: 100%;
    height: 20px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-radius: 20px;
    box-sizing: border-box;

    .pro-step {
      width: 30.3%;
      height: 20px;
      background: linear-gradient(to bottom, #f6be5c, #f3a851);
      border-radius: 20px;
      position: relative;
      .steps {
        height: 50px;
        border-radius: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 26px;
        padding: 0 20px;
        position: absolute;
        top: -80px;
        left: 100%;
        transform: translateX(-50%);
        p {
          position: relative;
          z-index: 2;
          color: #fff;
        }
        &::before {
          content: '';
          width: 100%;
          height: 50px;
          border-radius: 50px;
          background: linear-gradient(to bottom, #f29d50, #eb572c);
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          top: 0;
          z-index: 1;
        }
        &::after {
          content: '';
          width: 12px;
          height: 12px;
          background: #eb572c;
          transform: rotate(45deg) translateX(-50%);
          position: absolute;
          bottom: -8px;
          left: 50%;
          z-index: 0;
        }
      }
      .dot {
        width: 64px;
        height: 64px;
        border-radius: 100%;
        position: absolute;
        right: -32px;
        top: 50%;
        transform: translateY(-50%);
        background: url('../img/walking/dot.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }
}
.walking {
  width: 100vw;
  flex: none;
  overflow-x: hidden;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  height: auto;
  box-sizing: border-box;
  position: relative;
  p {
    color: #525961;
    font-size: 36px;
    line-height: 1.5;
  }

  .header {
    width: 397px;
    height: 82px;
    margin: 0 auto;
    margin-bottom: 30px;
    display: block;
  }
  .box {
    background-color: #fff;
    box-shadow: 0 0 8px 0 rgba(66, 174, 214, 0.5);
    border-radius: 10px;
    width: 690px;
    margin: 0 auto;
    margin-top: 30px;
    padding: 30px;
    font-size: 36px;
    color: #515962;
    line-height: 1.5;
  }
  .content {
    background-color: #fef9ed;
    border: 2px solid #f7e5cc;
    border-radius: 10px;
    padding: 30px;
    p {
      color: #867566;
      font-size: 28px;
      line-height: 28px;
      vertical-align: bottom;
      text-align: center;
      span {
        color: #df5f4e;
        font-size: 36px;
      }
    }
  }

  .today-info {
    position: relative;
    padding: 100px 30px 40px 30px;
    .avator {
      width: 196px;
      height: 196px;
      background: url('../img/walking/bg-avator.png');
      background-size: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      left: 50%;
      top: -108px;
      z-index: 2;
      transform: translateX(-50%);

      img {
        width: 120px;
        height: 120px;
        border-radius: 100%;
        display: block;
      }
    }
    .btn {
      cursor: pointer;
      width: 586px;
      height: 104px;
      background: url('../img/walking/btn-submit.png');
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: 40px;
      &:active {
        opacity: 0.7;
      }
      &.submit-now {
        background: url('../img/walking/btn-submit-now.png');
        width: 586px;
        height: 104px;
        background-size: 100% 100%;
      }
    }
    .no-permission {
      padding: 10px 10px 0 10px;
      font-size: 28px;
      color: #959ca6;
      text-align: justify;
      line-height: 42px;
      font-weight: 400;
      span {
        color: #40a7f4;
        font-weight: 600;
      }
    }
    .content {
      display: flex;
      align-items: center;
      justify-content: center;
      p {
        text-align: center;
      }
      .today-item {
        flex: auto;
        width: 100%;
        text-align: center;
        padding: 30px 0;
        p.step {
          color: #df5f4e;
          font-size: 48px;
          line-height: 72px;
          font-weight: bold;
        }
        &:nth-child(2) {
          position: relative;
          &::after {
            content: '';
            width: 1px;
            height: 118px;
            position: absolute;
            left: 0;
            top: 50%;
            transform: scaleX(0.5) translateY(-50%);
            background-color: #f7e5cc;
          }
        }
      }
    }
  }

  .family-info {
    padding: 30px;
    .title {
      span {
        color: #df5f4e;
        font-weight: bold;
      }
    }
    .progress {
      text-align: left;
      padding-top: 40px;
      padding-left: 40px;
      margin-top: 30px;
      width: 100%;
      .bar {
        &.hasdaylistep {
          margin-top: 100px;
        }
      }
      p {
        span {
          font-size: 48px;
          font-weight: bold;
        }
      }
    }
  }

  .family-list {
    width: 630px;
    margin-top: 30px;
    p {
      font-size: 32px;
    }
    .tip {
      color: #969ca5;
      margin-top: 30px;
      text-align: left;
    }
    .item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 20px 0;
      border-bottom: 1px solid #e9eeec;
      .avator {
        width: 80px;
        height: 80px;
        border-radius: 100%;
        background-color: gray;
        border: 2px solid #b0dff2;
        margin-right: 20px;
      }
      .name {
        flex: auto;
        text-align: left;
      }
      .step {
        span {
          color: #df5f4e;
        }
      }
    }
  }

  .monthly-reward {
    text-align: left;
    .btn {
      cursor: pointer;
      width: 630px;
      height: 109px;
      background: url('../img/walking/btn-getreward.png') no-repeat;
      background-size: 100%;
      margin-top: 20px;
    }
    .content {
      margin-top: 30px;
      &.info {
        p {
          text-align: center;
          font-size: 32px;
          span {
            font-weight: bold;
          }
        }
      }
    }
    &.status_1 {
    }
    &.status_2 {
      position: relative;
      .name {
        position: absolute;
        left: 80px;
        top: 344px;
        z-index: 22;
        width: 100px;
        font-size: 22px;
        text-align: center;
      }
      .btn {
        background: url('../img/walking/btn-checkpic.png') no-repeat;
        background-size: 100%;
      }
    }
  }
}
.dialog-content {
  width: 630px;
  height: 720px;
  background: url('../img/walking/reward-dayly.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
  margin-top: -200px;
  &.monthly {
    background-image: url('../img/walking/reward-monthly.png');
  }

  .cancel,
  .confirm {
    background-image: linear-gradient(270deg, rgba(255, 234, 188, 0.3) 0%, rgba(255, 246, 232, 0.3) 100%);
    border: 2px solid #fbe5c9;
    border-radius: 40px;
    width: 260px;
    height: 80px;
    font-size: 36px;
    text-align: center;
    line-height: 78px;
    font-weight: 600;
    color: #ff882b;
    position: absolute;
    left: 35px;
    bottom: 60px;
    &:active {
      opacity: 0.7;
    }
  }
  .confirm {
    background-image: linear-gradient(180deg, #ffd752 0%, #ff882b 100%);
    box-shadow: 0 2px 8px 0 rgba(255, 143, 82, 0.5);
    border-radius: 40px;
    color: #ffffff;
    left: 335px;
  }
  &.monthly-done {
    background-image: url('../img/walking/reward-monthly-done.png');
    .confirm {
      width: 480px;
      height: 80px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
</style>
