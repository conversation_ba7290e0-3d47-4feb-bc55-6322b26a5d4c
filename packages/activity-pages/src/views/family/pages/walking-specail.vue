<template>
  <!-- 未组建家庭 -->
  <focus-render
    :focusFid="focusFid1New"
    class="focus-config"
    @onFocusConfigSucc="getFocusData"
    v-if="!isEnterMyFamlily"
    @onLoginSucc="afterLoginSucc"
    :hideFocusModules="!dataIsReady || isEnterMyFamlily"
    :renderWhenParentIsReadyStatus="readyStatus"
  ></focus-render>

  <family-list :hideList="'0'" v-if="!isEnterMyFamlily" @useNewWalking="checkWalkingStatus"></family-list>
  <!-- 已组建家庭 & 未参与 9694 -->
  <focus-render :focusFid="focusFid2New" class="focus-config" v-if="isEnterMyFamlily && isShowFirstUI && dataIsReady">
  </focus-render>

  <!-- 已组建家庭 -->
  <focus-render
    :focusFid="focusFid3New"
    class="focus-config"
    @onFocusConfigSucc="getFocusData"
    v-if="isEnterMyFamlily && !isShowFirstUI && dataIsReady"
  >
    <template #family_walking_main="slotData">
      <div class="box today-info">
        <div class="avator">
          <img :src="userAvator" alt="" />
        </div>
        <div class="content">
          <div class="today-item">
            <p class="step">{{ todaySteps }}</p>
            <p>今日步数</p>
          </div>
        </div>
        <template v-if="!canUseIosSteps || isEncryptErr">
          <div class="btn" @click="gotoMini"></div>
        </template>
        <template v-else>
          <div v-if="showSubmitNow" class="btn submit-now" @click="submit"></div>
          <template v-else>
            <div class="btn" @click="gotoMini"></div>
            <div class="no-permission">
              推荐您去开启“运动与健身”权限，更加轻松打卡上传步数。您也可以通过设置 - 微众银行路径下打开权限
              <span @click="gotoSetting">去开启&gt;</span>
            </div>
          </template>
        </template>
      </div>

      <div class="box family-info">
        <img src="../img/walking/header-1_new.png" alt="" class="header" />
        <p class="title">
          本月家庭已获得<span>{{ showData.familyMonthPoint }}</span
          >积分
        </p>
        <div class="content progress">
          <p>
            今日累计<span>{{ showData.familyDaylySteps }}</span
            >步，目标{{ stepConfig.familyStepDaily }}步。
          </p>
          <div class="bar" :class="{ hasdaylistep: todaySteps > 0 }">
            <div class="bar-content">
              <div class="pro-step" :style="{ width: `${progressPercent}%` }">
                <div class="dot"></div>
                <!-- <div class="steps" v-if="todaySteps > 0">
                    <p>+{{ todaySteps }}</p>
                  </div> -->
              </div>
            </div>
          </div>
        </div>
        <div class="family-list">
          <div class="item" v-for="(item, index) in familyWalkingList">
            <img class="avator" :src="item.avator" />
            <p class="name">{{ item.name }}</p>
            <p class="step">
              今日<span>{{ item.steps }}</span
              >步
            </p>
          </div>
          <p class="tip">
            * 个人每日上传步数{{ stepConfig.personalStepDaily }}步及以上，家庭成员上传总步数{{
              stepConfig.familyStepDaily
            }}及以上，首次达标每人得{{ stepConfig.firstAwardDaily }}积分，之后每次达标每人得{{
              stepConfig.awardDaily
            }}积分。
          </p>
        </div>
      </div>

      <!-- 月度没有奖励 -->
      <div class="box monthly-reward" v-if="monthTaskStatus === 0">
        <img src="../img/walking/header-2_new.png" alt="" class="header" />
        <p class="title">
          月度累计<span style="color: #df5f4e; font-weight: bold">{{ stepConfig.cumulativeDaysMonthly }}天</span
          >完成每日任务，可得以下其一奖励：
        </p>

        <div class="price">
          <div class="desc">
            任一家庭成员当前财富值为<span class="red">50万</span>及以上，每人领<span class="red"
              >{{ stepConfig.awardDescMonthly }}元红包奖励</span
            >；
          </div>
          <div class="desc">家庭成员当前财富值都低于50万，每人各获得<span class="red">500积分</span>。</div>
        </div>

        <div class="btn-wealth" @click="jumpWealth">查看您当前财富值 ></div>

        <div class="content info">
          <p>
            本月已完成<span>{{ monthCountTimes }}</span
            >次每日步数挑战
          </p>
          <div class="bar">
            <div class="bar-content">
              <div class="pro-step" :style="{ width: `${progressPercentMonth}%` }">
                <div class="dot"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 月度获得了红包 -->
      <div class="box monthly-reward" v-if="monthTaskStatus === 1">
        <img src="../img/walking/header-2_new.png" alt="" class="header" />
        <p class="title" v-if="monthTaskIsReadpack">
          恭喜您的家庭完成了月度挑战，您获得<span style="color: #df5f4e"
            >{{ stepConfig.awardDescMonthly }}元红包奖励</span
          >！
        </p>

        <p class="title" v-else>恭喜您的家庭完成了月度挑战，您获得<span style="color: #df5f4e">500积分奖励</span>！</p>

        <img src="../img/walking/icon_red.png" alt="" class="pic" />
        <div class="btn monthly-done" v-if="monthTaskIsReadpack" @click="jumpCouponList"></div>
      </div>

      <!-- 红包领完了 -->
      <div class="box monthly-reward none" v-if="monthTaskStatus === 2">
        <img src="../img/walking/header-2_new.png" alt="" class="header" />
        <p class="title">很遗憾，您来晚一步，奖励已被领取完，感谢您参与月度挑战。</p>
        <img src="../img/walking/icon_red_none.png" alt="" class="pic" />
      </div>
    </template>
  </focus-render>

  <Mask v-if="showDialogData.id">
    <!-- 今日积分 -->
    <div
      class="dialog-content"
      :class="{
        monthly: showDialogData.type === 4,
        'monthly-done': showDialogData.type === 4,
        monthlypoint: showDialogData.type === 4,
      }"
    >
      <div class="title">
        快去领取<span>{{ showDialogData.rewardText }}</span
        >奖励吧
      </div>

      <div class="cancel" @click="readMsg(showDialogData.id, showDialogData.type)"></div>

      <div
        class="confirm"
        @click="clickConfirm(showDialogData.id, showDialogData.type)"
        v-if="showDialogData.type === 4"
      ></div>
    </div>
  </Mask>
</template>

<script setup lang="ts">
import FamilyList from '../components/FamilyList.vue'
import { focusCore, focusStore, focusServices, Mask, focusUtils } from '@focus/render'
import { onMounted, provide, ref, computed, watchEffect } from 'vue'
import familyService from '../service/familyService'
import { storeToRefs } from 'pinia'
import { familyStore } from '../store/store'
import { useRouter } from 'vue-router'

const { UrlParser } = focusUtils
const router = useRouter()
const store = familyStore()
const {
  familyDaylySteps,
  familyMonthPoint,
  familyTotalPoint,
  personalTotalPoint,
  receiver_name,
  hadMonthReward,
  stepConfig,
} = storeToRefs(store)
const { modalStore, userStore } = focusStore.stores
const { userService, jumpService, activityService } = focusServices
const props = defineProps({
  userInfo: Object,
})
const isEnterMyFamlily = ref(false)
const isEncryptErr = ref(false)
const device = ref()
const canUseIosSteps = ref(false)
const showSubmitNow = ref(true)

const shareConfigData = ref({})
const familyMgmAid = 6771
const hasAccount = ref(false)
let focusFid1 = 1773 //健步走 未组建家庭
let focusFid2 = 1786 //健步走 已组建家庭-第一次
let focusFid3 = 1776 //健步走 已组建家庭-非第一次
let focusFid1New = 2864 //健步走 未组建家庭
let focusFid2New = 2865 //健步走 已组建家庭-第一次
let focusFid3New = 2866 //健步走 已组建家庭-非第一次

const showData = computed(() => {
  const {
    familyDaylySteps,
    familyMonthPoint,
    familyTotalPoint,
    personalTotalPoint,
    receiver_name,
    hadMonthReward,
    stepConfig,
  } = storeToRefs(store)

  return {
    familyDaylySteps: typeof familyDaylySteps.value === 'number' ? familyDaylySteps.value : '--',
    familyMonthPoint: typeof familyMonthPoint.value === 'number' ? familyMonthPoint.value : '--',
    familyTotalPoint: typeof familyTotalPoint.value === 'number' ? familyTotalPoint.value : '--',
    personalTotalPoint: typeof personalTotalPoint.value === 'number' ? personalTotalPoint.value : '--',
  }
})

const dataIsReady = computed(() => {
  return walkingDataIsReady.value && familyDataIsReady.value
})
const walkingDataIsReady = ref(false)
const familyDataIsReady = ref(false)
const maxSteps = computed(() => {
  return (stepConfig && stepConfig.value && stepConfig.value.familyStepDaily) || 0
})
const readyStatus = ref(1) // 转为2就是可以渲染focus了

if (BUILD_TEST) {
  focusFid1 = 391
  focusFid2 = 409
  focusFid3 = 397
  focusFid1New = 2293
  focusFid2New = 2294
  focusFid3New = 2295
}

provide('shareConfigData', shareConfigData)
provide('familyMgmAid', familyMgmAid)
provide('userIsInFamilyStatus', userIsInFamilyStatus)
provide('hasAccount', hasAccount)
provide('familyDataIsReady', familyDataIsReady)

const userAvator = ref('')
const todaySteps = ref(0)
const familyWalkingList = ref()
const monthCountTimes = ref(0)
const monthTaskStatus = ref(0)
const monthTaskIsReadpack = ref(false) // 月度奖励发红包
// computed(() => {
//   const monthlyMax = (stepConfig && stepConfig.value && stepConfig.value.cumulativeDaysMonthly) || 0
//   if (monthCountTimes.value < monthlyMax) {
//     return 0
//   }
//   if (monthCountTimes.value >= monthlyMax && !receiver_name.value) {
//     return 1
//   }
//   if (monthCountTimes.value >= monthlyMax && receiver_name.value) {
//     return 2
//   }
//   return 0
// }) //0 未完成；1 已完成-未填地址；2 已完成-已填地址

// 参与了9694
const isAdding9694 = ref(false)
// 已弹出过填地址的弹窗
const hadDilagAdress = ref(false)

//ios不区分是否参与9694
const isShowFirstUI = computed(() => {
  return !isAdding9694.value && device.value !== 'ios'
})

function checkWalkingStatus(status: number) {
  console.log('🚀 ~ file: walking-specail.vue:269 ~ checkWalkingStatus ~ status:', status)
  if (status === 2) {
    readyStatus.value = 2
  } else {
    router.replace({
      path: '/walking',
    })
  }
}

const progressPercent = computed(() => {
  return maxSteps.value
    ? ((familyDaylySteps.value >= maxSteps.value ? maxSteps.value : familyDaylySteps.value) / maxSteps.value) * 100
    : 0
  // return 0
})

const progressPercentMonth = computed(() => {
  const maxMonthly = (stepConfig && stepConfig.value && stepConfig.value.cumulativeDaysMonthly) || 0
  return maxMonthly
    ? ((monthCountTimes.value >= maxMonthly ? maxMonthly : monthCountTimes.value) / maxMonthly) * 100
    : 0
})

const originDialogList = ref<
  {
    awards_receive_status: number
    id: number
    msg_order: number
    msg_status: number
    type: number
  }[]
>([])

const showDialogData = computed<{
  awards_receive_status?: number
  id?: number
  msg_order?: number
  msg_status?: number
  type?: number
  reward?: number
  rewardText?: string
}>(() => {
  if (originDialogList.value.length) {
    return originDialogList.value[0]
  }
  return {}
})

function userIsInFamilyStatus() {
  isEnterMyFamlily.value = true
}

watchEffect(() => {
  if (familyDataIsReady.value) {
    if (focusCore.env.isInWx) {
      showToApp()
    }
  }
})

function showToApp() {
  const { shareFrom = '', testWx } = new UrlParser().query
  if (testWx) {
    return
  }
  modalStore.loadingStart('jumpToApp')
  const querys = shareFrom ? `entryFrom=${shareFrom}_wx` : ''

  let link = `https://personal.webank.com/s/hj/focus2/family/index.html?${querys}#/walking`
  if (BUILD_TEST) {
    link = `https://personal.test.webank.com/s/hj/focus2/family/index.html?${querys}#/walking`
  }
  const url = link
  setTimeout(() => {
    jumpService.jump(
      {
        path: url,
        method: 'url',
      },
      true
    )
    modalStore.loadingEnd('jumpToApp')
  }, 200)
}

function getFocusData(focueRenderCtl: any) {
  console.log('🚀 ~ file: walking.vue ~ line 21 ~ getFocusData ~ focueRenderCtl', focueRenderCtl)
  shareConfigData.value = focueRenderCtl.getShareConfigByConfigId(1)
  console.log('🚀 ~ file: walking.vue ~ line 22 ~ getFocusData ~  shareConfigData.value', shareConfigData.value)
}

function afterLoginSucc(loginData: any) {
  console.log('.....login,', loginData)
  if (loginData.hasAccount) {
    hasAccount.value = true
    device.value = focusCore.env.device
    initData()
  } else {
    modalStore.errorMaskContrl('is_not_family_white_account', ['您不是受邀用户，无法参与此活动'], '', true)
  }
}
// 上传步数
function submit() {
  if (focusCore.env.isInApp) {
    modalStore.loadingStart('submit')
    familyService
      .uploadDaySteps()
      .then((res) => {
        console.log('上传步数返回', res)
        showSubmitNow.value = res.hasPermission
        if (res.hasPermission) {
          isEncryptErr.value = res.isEncryptErr || false
          if (res.status && !res.isEncryptErr) {
            modalStore.toastShow('打卡成功')
            // todaySteps.value = Number(res.count)
            initWalkingData()
          } else {
            modalStore.toastShow('打卡失败')
          }
        } else {
          modalStore.toastShow('未获取运动与健身权限，授权失败')
        }
      })
      .catch(() => {
        //用户App版本不支持/设备不支持  页面初始化时getPermission已经判断过了 此处可作为兜底
        canUseIosSteps.value = false
        modalStore.toastShow('不支持获取运动与健身权限')
      })
      .finally(() => {
        modalStore.loadingEnd('submit')
      })
  }
}

function gotoMini() {
  const env: any = {}
  if (BUILD_TEST) {
    // env.miniEnv = 'preview'
    env.miniEnv = 'develop'
  }

  const minilink = `/packageActivity/pages/walking/walking?t=${Date.now()}&family_id=${
    familyService.familyId
  }&activity_id=${familyService.walkingAid}`

  modalStore.confirmContrl({
    show: true,
    contents: ['即将打开“微众银行”小程序'],
    btnConfirmText: '允许',
    btnConfirmJumpConfig: {
      path: `/pages/index/index?type=page&page=${encodeURIComponent(minilink)}`,
      method: 'mini',
      ...env,
    },
  })
}

function gotoSetting() {
  focusCore.invokeApp.lunchSystemSetting()
}

onMounted(() => {
  if (window.hjCoreIab && window.hjCoreIab.onAppResume) {
    window.hjCoreIab.onAppResume(() => {
      console.error('从App外面回来了！！')
      modalStore.confirmContrl({
        show: false,
      })
      initData()
    })
  }
})

function jumpWealth() {
  jumpService.jump({
    path: '/memberEquity/MemberEquityCenterScene',
  })
}

function jumpToMail() {
  router.push({
    path: '/walking-mail',
  })
}
function jumpToBigimg() {
  router.push({
    path: '/bigimg',
  })
}
function readMsg(id?: number | string, type?: string) {
  if (!id) {
    return false
  }
  modalStore.loadingStart('readMsg')
  familyService.readRewardMsg(Number(id), type).then((status: boolean) => {
    if (!status) {
      modalStore.toastShow('网络异常！请重试！')
      modalStore.loadingEnd('readMsg')

      return
    }

    setTimeout(() => {
      modalStore.loadingEnd('readMsg')
      initData()
    }, 100)
  })
}

function initWalkingData() {
  modalStore.loadingStart('initWalkingData')
  familyService.getWalkingReward().then(() => {
    modalStore.loadingStart('reward')
    familyService.getRewadToast().then((msgList: Array<any>) => {
      modalStore.loadingEnd('reward')
      let result = msgList
      // const defaultList = []
      // if (!result.length) {
      //   defaultList.push({
      //     id: 3,
      //     type: 4,
      //   })
      // }

      //处理奖励弹窗
      // originDialogList.value = [...result, ...defaultList]
      originDialogList.value = result
      console.log(
        '🚀 ~ file: walking.vue ~ line 331 ~ familyService.getRewadToast ~ originDialogList.value ',
        originDialogList.value
      )
    })
  })
  familyService
    .getFamilyWalkListData()
    .then(({ listData, curUserSteps, familyMonthCount, familyMonthStatus = 0, monthTaskPriceIsRedpack }) => {
      console.log('🚀 ~ file: walking-specail.vue:485 ~ familyService.getFamilyWalkListData ~ listData:', listData)

      familyWalkingList.value = listData
      todaySteps.value = Number(curUserSteps)
      monthCountTimes.value = familyMonthCount
      monthTaskStatus.value = familyMonthStatus >= 2 ? 2 : familyMonthStatus
      monthTaskIsReadpack.value = monthTaskPriceIsRedpack
      console.log(
        '🚀 ~ file: walking-specail.vue:496 ~ familyService.getFamilyWalkListData ~ monthTaskStatus.value:',
        monthTaskStatus.value
      )

      modalStore.loadingEnd('initWalkingData')
    })
}

function initData() {
  //判断是否支持获取步数和授权状态
  familyService.appHealthHandler
    .getPermission()
    .then((res) => {
      canUseIosSteps.value = true
      showSubmitNow.value = res.showSubmitNow
    })
    .catch(() => {
      canUseIosSteps.value = false
    })

  modalStore.loadingStart('check_9694')

  activityService.checkWrInfoStatus(9694).then((aid: number) => {
    isAdding9694.value = !!aid
    modalStore.loadingEnd('check_9694')

    modalStore.loadingStart('initdata')
    userService.getWxUserInfo().then(({ avator, defaultAvator }: { avator: string; defaultAvator: string }) => {
      console.log('🚀 ~ file: walking.vue ~ line 209 ~ userService.getWxUserInfo ~ avator', avator)
      userAvator.value = avator || defaultAvator
    })
    focusCore.onRedirectToSpecialPath('/family_mini', () => {
      console.log(shareConfigData.value)
      gotoMini()
    })
    initWalkingData()
    modalStore.loadingEnd('initdata')
    walkingDataIsReady.value = true
  })
}

function jumpCouponList() {
  jumpService.commonUse.couponList()
}

function clickConfirm(id: any, type?: string) {
  readMsg(id, type)
  setTimeout(() => {
    jumpCouponList()
  }, 200)
}
</script>

<style lang="scss" scoped>
.price {
  width: 660px;
  height: 344px;
  background: url('../img/walking/bg_month_walking.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex-direction: column;
  margin: 0 auto;
  margin-top: 25px;
  .desc {
    width: 376px;
    font-size: 28px;
    line-height: 38px;
    padding: 40px 0;
    margin-left: 40px;
    color: #6f5b60;
    text-align: left;
    .red {
      color: #f05446;
    }
  }
}
.btn-wealth {
  height: 30px;
  font-size: 26px;
  color: #3476f2;
  margin-top: 30px;
  padding: 20px;
  text-align: center;
  width: 100%;
  margin: 0 auto;
}
.bar {
  width: 433px;
  height: 20px;
  background: url('../img/walking/bg-bar.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
  margin-top: 70px;
  margin-left: 32px;

  .bar-content {
    width: 100%;
    height: 20px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-radius: 20px;
    box-sizing: border-box;
    &::after {
      content: '';
      width: 140px;
      height: 120px;
      display: block;
      background: url('../img/walking/bg-bar-end.png') no-repeat;
      background-size: 100%;
      position: absolute;
      right: -140px;
      top: 50%;
      margin-top: 1px;
      transform: translateY(-50%);
      z-index: 2;
    }
    &::before {
      content: '';
      width: 168px;
      height: 168px;
      display: block;
      background: url('../img/walking/pic_coin.png') no-repeat;
      background-size: 100%;
      position: absolute;
      right: -168px;
      top: 50%;
      margin-top: 1px;
      transform: translateY(-50%);
      z-index: 3;
    }
    .pro-step {
      width: 30.3%;
      height: 20px;
      background: linear-gradient(to right, #ff993c, #fd3f08);
      border-radius: 20px;
      position: relative;
      .steps {
        height: 50px;
        border-radius: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 26px;
        padding: 0 20px;
        position: absolute;
        top: -80px;
        left: 100%;
        transform: translateX(-50%);
        p {
          position: relative;
          z-index: 2;
          color: #fff;
        }
        &::before {
          content: '';
          width: 100%;
          height: 50px;
          border-radius: 50px;
          background: linear-gradient(to right, #f29d50, #eb572c);
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          top: 0;
          z-index: 1;
        }
        &::after {
          content: '';
          width: 12px;
          height: 12px;
          background: #eb572c;
          transform: rotate(45deg) translateX(-50%);
          position: absolute;
          bottom: -8px;
          left: 50%;
          z-index: 0;
        }
      }
      .dot {
        width: 64px;
        height: 64px;
        border-radius: 100%;
        position: absolute;
        right: -32px;
        top: 50%;
        transform: translateY(-50%);
        background: url('../img/walking/dot_new.png') no-repeat;
        background-size: 100% 100%;
        z-index: 22;
      }
    }
  }
}

p {
  color: #525961;
  font-size: 36px;
  line-height: 1.5;
}

.header {
  width: 702px;
  height: 64px;
  margin: 0 auto;
  margin-bottom: 30px;
  display: block;
}
.box {
  background-color: #fff;
  border-radius: 10px;
  width: 700px;
  margin: 0 auto;
  margin-top: 28px;
  padding: 30px 24px 40px 24px;
  font-size: 36px;
  color: #515962;
  line-height: 1.5;
}
.content {
  //    background-color: #fef9ed;
  //  border: 2px solid #f7e5cc;
  background: url('../img/walking/bg-content.png') no-repeat;
  background-size: 100% 100%;
  width: 654px;
  height: 174px;
  box-sizing: border-box;
  border-radius: 10px;
  padding: 24px;
  margin: 0 auto;
  p {
    color: #867566;
    font-size: 32px;
    line-height: 54px;
    vertical-align: bottom;
    text-align: center;
    span {
      color: #df5f4e;
      font-size: 36px;
    }
  }
}

.today-info {
  position: relative;
  padding: 100px 0 40px 0;
  .avator {
    width: 196px;
    height: 196px;
    background: url('../img/walking/bg-avator_new.png');
    background-size: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 50%;
    top: -108px;
    z-index: 2;
    transform: translateX(-50%);

    img {
      width: 120px;
      height: 120px;
      border-radius: 100%;
      display: block;
    }
  }
  .btn {
    cursor: pointer;
    width: 586px;
    height: 104px;
    background: url('../img/walking/btn-submit.png');
    background-size: 100% 100%;
    margin: 0 auto;
    margin-top: 40px;
    &:active {
      opacity: 0.7;
    }
    &.submit-now {
      background: url('../img/walking/btn-submit-now.png');
      width: 586px;
      height: 104px;
      background-size: 100% 100%;
    }
  }
  .no-permission {
    padding: 10px 10px 0 10px;
    font-size: 28px;
    color: #959ca6;
    text-align: justify;
    line-height: 42px;
    font-weight: 400;
    span {
      color: #40a7f4;
      font-weight: 600;
    }
  }
  .content {
    display: flex;
    align-items: center;
    justify-content: center;

    p {
      text-align: center;
    }
    .today-item {
      flex: auto;
      width: 100%;
      text-align: center;
      p.step {
        color: #df5f4e;
        font-size: 48px;
        line-height: 72px;
        font-weight: bold;
      }
      &:nth-child(2) {
        position: relative;
        &::after {
          content: '';
          width: 1px;
          height: 118px;
          position: absolute;
          left: 0;
          top: 50%;
          transform: scaleX(0.5) translateY(-50%);
          background-color: #f7e5cc;
        }
      }
    }
  }
}

.family-info {
  .title {
    font-size: 36px;
    color: #6f5b60;
    span {
      color: #df5f4e;
      font-weight: bold;
    }
  }
  .progress {
    text-align: left;
    padding-left: 24px;
    padding-bottom: 90px;
    margin-top: 30px;
    width: 654px;
    height: 252px;
    background-image: url('../img/walking/bg-content-2.png');
    .bar {
      &.hasdaylistep {
      }
    }
    p {
      span {
        font-size: 32px;
        font-size: 48px;
        font-weight: bold;
      }
    }
  }
}

.family-list {
  width: 650px;
  margin: 0 auto;
  margin-top: 30px;
  p {
    font-size: 32px;
  }
  .tip {
    color: #6f5b60;
    margin-top: 28px;
    text-align: left;
    opacity: 0.6;
  }
  .item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 0;
    border-bottom: 1px solid #ffccb430;
    .avator {
      width: 80px;
      height: 80px;
      border-radius: 100%;
      background-color: gray;
      border: 2px solid #ffd8af;
      margin-right: 20px;
    }
    .name {
      flex: auto;
      text-align: left;
    }
    .step {
      span {
        color: #df5f4e;
      }
    }
  }
}

.monthly-reward {
  text-align: left;
  .title {
    font-size: 36px;
    color: #6f5b60;
    text-align: justify;
    line-height: 54px;
    font-weight: 400;
  }
  .pic {
    width: 348px;
    height: 348px;
    margin: 0 auto;
    display: block;
  }
  .btn {
    cursor: pointer;
    width: 570px;
    height: 101px;
    background: url('../img/walking/btn-getreward_new.png') no-repeat;
    background-size: 100%;
    margin-top: 20px;
    margin: 0 auto;
  }
  .content {
    margin-top: 30px;
    &.info {
      padding-bottom: 60px;
      color: #6f5b60;
      width: 654px;
      height: 252px;
      background-image: url('../img/walking/bg-content-2.png');
      p {
        text-align: center;
        font-size: 36px;
        span {
          font-weight: bold;
        }
      }
      .bar {
        .bar-content {
          &::before {
            background-image: url('../img/walking/pic_red.png');
          }
        }
      }
    }
  }
}
.dialog-content {
  width: 630px;
  height: 720px;
  background: url('../img/walking/reward-dayly_new.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
  margin-top: -200px;
  .title {
    margin-top: 140px;
    font-size: 40px;
    color: #6f5b60;
    text-align: center;
    line-height: 54px;
    font-weight: 400;
    width: 100%;
    span {
      color: #f05446;
      font-weight: bold;
    }
  }
  .cancel,
  .confirm {
    width: 520px;
    height: 80px;
    position: absolute;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
    background-image: url('../img/walking/btn-cancel_new2.png');
    background-size: 100%;
    &:active {
      opacity: 0.7;
    }
  }
  .confirm {
    left: 335px;
    width: 260px;
    transform: none;
    background-image: url('../img/walking/btn-confirm_new.png');
  }
  &.monthly {
    .confirm {
      left: 335px;
      width: 260px;
      background-image: url('../img/walking/btn-confirm_new.png');
    }
  }
  &.monthly-done {
    background-image: url('../img/walking/reward-monthly-done_new.png');

    .cancel {
      left: 35px;
      width: 260px;
      transform: none;
      background-image: url('../img/walking/btn-cancel_new.png');
    }
  }
  &.monthlypoint {
    background-image: url('../img/walking/reward-monthly-done_new.png');
  }
}
</style>
