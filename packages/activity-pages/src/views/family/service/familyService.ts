import { focusStore, focusServices, focusCore } from '@focus/render'
import { storeToRefs } from 'pinia'

import { familyStore } from '../store/store'

const { mgmService, kvService, userService } = focusServices
enum TAcceptShareFunctionType {
  Family = 'my_family',
}
enum EnumKvDataType {
  FAMILY = 'family',
}
export enum EnumFamilyKeys {
  IsInMyFamily = 'isInMyFamily',
}
const cgis = {
  myFalilyList: {
    url: '/wm-htrserver/op/mgm2/my_family_list',
    name: '查询我的家庭',
    canSaveTimes: true,
    cacheTimeOut: 3000,
  },
  agreeBuildFamily: {
    url: '/wm-htrserver/op/mgm2/agree_build_family',
    name: '同意组建家庭',
  },
  familyEquity: {
    url: '',
    name: '我的家庭权益',
  },
  walkingReward: {
    url: '/wm-htrserver/op/mgm2/query_reward_detail',
    name: '健步走-家庭奖励',
    canSaveTimes: true,
  },
  submitStepsData: {
    url: '/wm-htrserver/op/mgm2/upload_day_steps',
    name: '健步走-上传步数',
  },
  familyStepsDetail: {
    url: '/wm-htrserver/op/mgm2/query_family_step_detail',
    name: '健步走-家庭每日步数详情',
  },
  rewardTost: {
    url: '/wm-htrserver/op/mgm2/query_activity_complete_status',
    name: '获取奖励消息通知',
  },
  readRewardMsg: {
    url: '/wm-htrserver/op/mgm2/msg_status_update',
    name: '读取奖励消息',
  },
  saveMailInfo: {
    url: '/wm-htrserver/op/mgm2/register_address_info',
    name: '保存地址',
  },
  checkFamilyEquety: {
    url: '/wm-htrserver/op/mgm2/check_activity',
    name: '检查是否有家庭权限',
    canSaveTimes: true,
    cacheTimeOut: 3000,
  },
  uploadDaySteps: {
    url: '/wm-htrserver/op/mgm2/upload_day_steps',
    name: '上传用户步数打卡',
    method: 'post',
    headers: {
      encrypt: 'Y',
    },
  },
}
const ERROR_MAP = {
  '1001': '被邀请人年龄不符合邀请要求',
  '1002': '很抱歉，被邀请人已与其他人组建成功',
}

class AppHealthHandler {
  step: string = ''
  getSteps(): Promise<{ hasPermission: boolean; count: number }> {
    return new Promise((resolve, reject) => {
      focusCore.invokeApp.getStepCount({
        success: (count: number) => {
          console.log('从App获取的步数', count)
          resolve({ hasPermission: true, count })
        },
        error: (errCode: number) => {
          //用户拒绝/取消授权
          if (errCode === -5) {
            resolve({ hasPermission: false, count: -1 })
          } else {
            //用户App版本不支持/设备不支持
            reject()
          }
        },
      })
    })
  }
  getPermission(): Promise<{ showSubmitNow: boolean }> {
    return new Promise((resolve, reject) => {
      focusCore.checkAppFn('getPermissionEC').then((status: boolean) => {
        if (status) {
          focusCore.invokeApp.getPermission({
            options: {
              type: 'CMPedometer',
            },
            success: (status: string) => {
              console.log('授权状态', status)
              if (status === 'not_determined' || status === 'authorized') {
                resolve({ showSubmitNow: true })
              } else if (status === 'denied') {
                resolve({ showSubmitNow: false })
              } else {
                //系统错误/ios拒绝授权
                reject()
              }
            },
            error: (errCode: number) => {
              //用户App版本不支持/设备不支持/安卓系统/不支持的权限类型
              reject()
            },
          })
        } else {
          reject()
        }
      })
    })
  }
}
class FamilyServie {
  appHealthHandler = new AppHealthHandler()
  familyId: string = ''
  walkingAid: number = 9694
  familyMgmAid: number = 6771
  familyList: any[] = []
  getMyFamilyList() {
    return new Promise(async (resolve) => {
      let isInFamilyWhiteList: Boolean = false
      try {
        isInFamilyWhiteList = await this.checkUserHadFamily()
      } catch {
        isInFamilyWhiteList = false
      }

      focusCore
        .request(cgis.myFalilyList, { activity_id: this.familyMgmAid })
        .then((res: any) => {
          console.log('family_list....', res)
          const {
            my_account = { head_img_url: '', user_name: '', gender: '1' },
            my_family = [],
            is_M2 = 'N',
            my_rank = 1,
            share_rank = 1,
            share_rank_user = [],
            step_config = {
              topic: '',
              startDate: 0,
              endDate: 0,
              familyStepDaily: 0,
              personalStepDaily: 0,
              firstAwardDaily: 0,
              awardDaily: 0,
              awardDescMonthLy: '',
            },
          } = res
          console.log('my_family_list', my_family)
          console.log('🚀 ~ file: familyService.ts:156 ~ FamilyServie ~ .then ~ step_config:', step_config)
          const store = familyStore()
          store.setStepConfig(step_config)

          let hasPapa = false
          let hasMama = false
          let parentList: any[] = []
          let hasParentNums = 0
          const equityNamesList = ['白银', '黄金', '铂金', '钻石']
          const equityNumsList = ['3+', '5+', '6+', '9+']
          const familyEquety: {
            equityNums?: string
            topEquityName?: string
            shareMan?: string
            selfEquityName?: string
          } = {
            shareMan: '',
            topEquityName: equityNamesList[share_rank - 1],
            equityNums: equityNumsList[share_rank - 1],
            selfEquityName: equityNamesList[my_rank - 1],
          }
          const shareRankSidList = share_rank_user.map((i: any) => i.share_rank_sid)
          console.log(
            '🚀 ~ file: familyService.ts ~ line 63 ~ FamilyServie ~ .then ~ shareRankSidList',
            shareRankSidList
          )
          const isM2 = is_M2 === 'Y'
          let childData = {
            avator: my_account.head_img_url && my_account.head_img_url.replace('http://', 'https://'),
            name: my_account.user_name,
            gender: my_account.gender,
            isTopEquity: !!share_rank_user.find((i: any) => i.share_rank_sid === my_account.sid),
          }

          let userAvator = my_account.head_img_url && my_account.head_img_url.replace('http://', 'https://')
          if (my_family.length) {
            this.familyId = (my_family[0] && my_family[0].family_dto && my_family[0].family_dto.family_id) || ''
            this.getWalkingReward()
          }
          if (isM2) {
            if (!my_family.length) {
              const { modalStore } = focusStore.stores
              modalStore.errorMaskContrl('m2_no_family')
              return resolve({ isM2, isInFamilyWhiteList })
            }
            const data = my_family.find((i: any) => i.is_M1 === 'Y')
            const parentData = my_family.find((i: any) => i.is_M1 === 'N')

            childData = {
              avator: data.account_dto.head_img_url && data.account_dto.head_img_url.replace('http://', 'https://'),
              name: data.account_dto.user_name,
              gender: data.account_dto.gender,
              isTopEquity: !!share_rank_user.find((i: any) => i.share_rank_sid === data.account_dto.sid),
            }
            // 自己的信息
            parentList.push({
              gender: my_account.gender,
              name: my_account.user_name[0] + `${my_account.gender === '1' ? '爸爸' : '妈妈'}`,
              avator: my_account.head_img_url && my_account.head_img_url.replace('http://', 'https://'),
              isTopEquity: !!share_rank_user.find((i: any) => i.share_rank_sid === my_account.sid),
            })
            // 另一个家长
            if (parentData && parentData.account_dto) {
              const otherParentData = {
                gender: parentData.account_dto.gender,
                name: `${parentData.account_dto.user_name[0]}${
                  parentData.account_dto.gender === '1' ? '爸爸' : '妈妈'
                }`,
                avator:
                  parentData.account_dto.head_img_url &&
                  parentData.account_dto.head_img_url.replace('http://', 'https://'),
                isTopEquity: !!share_rank_user.find((i: any) => i.share_rank_sid === parentData.account_dto.sid),
              }
              parentList.push(otherParentData)
            }
          } else {
            my_family.forEach((i: { account_dto: any }, index: number) => {
              const { account_dto = {} } = i
              const { head_img_url, user_name = '', gender, sid } = account_dto
              const data = {
                avator: head_img_url && head_img_url.replace('http://', 'https://'),
                name: '',
                gender,
                isTopEquity: !!share_rank_user.find((i: any) => i.share_rank_sid === sid),
              }
              const firstName = (user_name && user_name[0]) || ''
              if (gender === '1') {
                hasPapa = true
                hasParentNums += 1
                data.name = firstName + '爸爸'
                parentList.push(data)
              } else {
                hasMama = true
                hasParentNums += 1
                data.name = firstName + '妈妈'
                parentList.push(data)
              }
            })
            if (!hasPapa && !hasMama) {
              parentList = [
                { avator: '', name: '邀请爸爸', gender: '1', isInvite: true },
                { avator: '', name: '邀请妈妈', gender: '2', isInvite: true },
              ]
            } else if (!hasPapa) {
              parentList.push({ avator: '', name: '邀请爸爸', gender: '1', isInvite: true, isTopEquity: false })
            } else if (!hasMama) {
              parentList.push({ avator: '', name: '邀请妈妈', gender: '2', isInvite: true, isTopEquity: false })
            }
          }
          const list = [childData, ...parentList].map((i) => {
            return {
              ...i,
              avator: i.avator && i.avator.replace('http:', 'https:'),
            }
          })
          this.familyList = list
          console.log('🚀 ~ file: familyService.ts ~ line 149 ~ FamilyServie ~ list ~ list', list)

          return resolve({
            hasParentNums,
            isM2,
            list,
            userAvator,
            stepConfig: step_config,
            hasWalkingConfig: !!step_config.startDate,
            // hasWalkingConfig: true,
            isInFamilyWhiteList,
            familyEquety: {
              ...familyEquety,
              shareMan: list
                .map((i) => (i.isTopEquity ? i.name : ''))
                .filter((i) => i)
                .join('、'),
            },
          })
        })
        .catch((err: any) => {
          console.error(err)
          resolve({})
        })
    })
  }

  getFamilyEquety() {
    return Promise.resolve({})
    // return new Promise((resolve)=>{
    //   focusCore.request(cgis.familyEquity)
    // })
  }

  getFamilyId() {
    if (this.familyId) {
      return Promise.resolve(this.familyId)
    }
    return new Promise((resolve) => {
      this.getMyFamilyList().then(() => {
        return resolve(this.familyId)
      })
    })
  }
  getFamilyRelationshipList(activityId: number): Promise<TRelationShipItem[]> {
    return new Promise((resolve) => {
      mgmService.getRelationshipList(activityId, TAcceptShareFunctionType.Family).then((res: any) => {
        const { register = [] } = res
        console.log(register)
        return resolve(register)
      })
    })
  }

  agreeBuildFamily(id: number) {
    return new Promise((resolve, reject) => {
      focusCore
        .request(cgis.agreeBuildFamily, { id })
        .then((res: any) => {
          console.log(res)
          const { agree_status_code = '', agree_status_msg = '' } = res
          if (agree_status_code && agree_status_code !== '0000') {
            const errMsg = ERROR_MAP[agree_status_code] || '组建家庭失败！请重试'
            console.log('🚀 ~ file: familyService.ts ~ line 139 ~ FamilyServie ~ .then ~ errMsg', errMsg)
            return reject({ errMsg })
          }
          resolve(true)
        })
        .catch((err: any) => {
          console.log(err)
          reject({ errMsg: '组建家庭失败！请重试' })
        })
    })
  }
  checkUserIsInFamilyStatus() {
    return new Promise((resolve) => {
      kvService.getUerKv([EnumKvDataType.FAMILY]).then((res: any) => {
        console.log('🚀 ~ file: familyService.ts ~ line 103 ~ FamilyServie ~ kvService.getUerKv ~ res', res)
        if (!res) {
          return resolve(false)
        }
        return resolve(!!(res[EnumKvDataType.FAMILY] && res[EnumKvDataType.FAMILY][EnumFamilyKeys.IsInMyFamily]))
      })
    })
  }
  setInFamilyStatus() {
    return new Promise((resolve) => {
      kvService
        .setUserKv(EnumKvDataType.FAMILY, {
          [EnumFamilyKeys.IsInMyFamily]: 1,
        })
        .then((res: boolean) => {
          resolve(res)
        })
    })
  }
  getWalkingReward() {
    return new Promise(async (resolve) => {
      const familyId = await this.getFamilyId()
      console.log('🚀 ~ file: familyService.ts ~ line 287 ~ FamilyServie ~ returnnewPromise ~ familyId', familyId)
      if (!familyId) {
        return Promise.resolve({ errMsg: 'cuowu' })
      }
      focusCore
        .request(cgis.walkingReward, { family_id: familyId, activity_id: this.walkingAid })
        .then(
          (res: {
            family_total_points: number
            family_month_points: number
            personal_total_points: number
            family_day_steps: number
            receiver_name: string
            month_award_status: string
          }) => {
            console.log('🚀 ~ file: familyService.ts ~ line 282 ~ FamilyServie ~ .then ~ res', res)
            // const {
            //   family_total_points = 0,
            //   family_month_points = 0,
            //   personal_total_points = 0,
            //   family_day_steps = 0,
            //   receiver_name = '',
            //   month_award_status = '',
            // } = res
            const store = familyStore()
            store.setFamilyWalkData(res)

            resolve(true)
          }
        )
        .catch((err: any) => {
          console.log(err)
          resolve({ errMsg: 'cuowu' })
        })
    })
  }

  submitSteps(steps: number) {
    return new Promise(async (resolve) => {
      const familyId = await this.getFamilyId()
      if (!familyId) {
        return Promise.resolve(false)
      }
      focusCore
        .request(cgis.submitStepsData, {
          family_id: familyId,
          activity_id: this.walkingAid,
          steps,
        })
        .then((res: { status: boolean }) => {
          console.log('🚀 ~ file: familyService.ts ~ line 285 ~ FamilyServie ~ .then ~ res', res)
          resolve(res && res.status)
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: familyService.ts ~ line 288 ~ FamilyServie ~ returnnewPromise ~ err', err)
          resolve(false)
        })
    })
  }

  getFamilyWalkListData(): Promise<{
    listData: { avator: string; name: string; steps: number }[]
    curUserSteps: number
    familyMonthCount: number
    familyMonthStatus: number
    monthTaskPriceIsRedpack?: boolean
  }> {
    return new Promise(async (resolve) => {
      const familyId = await this.getFamilyId()
      if (!familyId) {
        return resolve({
          listData: [],
          curUserSteps: 0,
          familyMonthCount: 0,
          familyMonthStatus: 0,
        })
      }
      focusCore
        .request(cgis.familyStepsDetail, {
          family_id: familyId,
          activity_id: this.walkingAid,
        })
        .then(
          (res: {
            day_flag: boolean
            family_day_steps: number
            family_member_steps: {
              head_img_url: string
              steps: number
              user_name: string
              gender: '1' | '2'
              m1_flag: boolean
              current_flag: boolean
            }[]
            month_count: number
            month_flag: boolean // 月度是否完成
            curUserSteps: number
            // receive_redpackage_flag: number
            family_redpackage_flag: boolean //本月是否发放红包
            limit_over_flag: boolean
            month_price_type: string // 0: 红包；2：500积分
          }) => {
            console.log('🚀 ~ file: familyService.ts ~ line 324 ~ FamilyServie ~ .then ~ res', res)
            const {
              day_flag,
              family_day_steps,
              family_member_steps,
              month_count,
              month_flag,
              // receive_redpackage_flag,
              family_redpackage_flag = false,
              limit_over_flag = false,
              month_price_type = '',
            } = res || {}
            let curUserSteps: number = 0
            const listData = family_member_steps.map((i) => {
              const firstName = i.user_name[0]
              const userType = !i.m1_flag ? i.gender : '0'
              const name = userType === '1' ? firstName + '爸爸' : userType === '2' ? firstName + '妈妈' : i.user_name
              if (i.current_flag) {
                curUserSteps = i.steps
              }
              return {
                avator: i.head_img_url ? i.head_img_url.replace('http://', 'https://') : '',
                name,
                steps: i.steps,
              }
            })

            const familyMonthStatus = family_redpackage_flag ? 1 : limit_over_flag ? 2 : 0

            return resolve({
              listData,
              curUserSteps,
              familyMonthCount: month_count || 0,
              familyMonthStatus,
              monthTaskPriceIsRedpack: month_price_type === '0',
            })
          }
        )
        .catch(() => {
          resolve({
            listData: [],
            curUserSteps: 0,
            familyMonthCount: 0,
            familyMonthStatus: 0,
          })
        })
    })
  }

  getRewadToast(): Promise<
    {
      awards_receive_status: number //领取状态 1 已领取；0 未领取
      id: number // 消息id
      msg_order: number // 优先级 越大越高
      msg_status: number // 是否已读
      type: number //奖励类型 1 积分；2 奖状；3 节日版-每日积分；4 节日版-月奖励
      reward: string
      rewardText: string
    }[]
  > {
    return new Promise(async (resolve) => {
      const familyId = await this.getFamilyId()
      if (!familyId) {
        return Promise.resolve([])
      }
      focusCore
        .request(cgis.rewardTost, {
          family_id: familyId,
          activity_id: this.walkingAid,
        })
        .then(
          (res: {
            msg_list: {
              awards_receive_status: number //领取状态 1 已领取；0 未领取
              id: number // 消息id
              msg_order: number // 优先级 越大越高
              msg_status: number // 是否已读
              type: number //奖励类型 1 积分；2 奖状 ；3 节日版-日积分；4 节日版-月红包
              reward: number
            }[]
          }) => {
            console.log('🚀 ~ file: familyService.ts ~ line 381 ~ FamilyServie ~ returnnewPromise ~ res', res)
            const resData: any = res.msg_list || []
            // const resData = [{ awards_receive_status: 0, id: 1, msg_order: 2, msg_status: 0, type: 3, reward: '66' }]
            return resolve(
              resData.map((i: any) => {
                return {
                  ...i,
                  rewardText: `${i.reward}${i.type === 3 || i.type === 5 ? '积分' : '元红包'}`,
                }
              })
            )
          }
        )
        .catch(() => {
          return resolve([])
        })
    })
  }
  readRewardMsg(id: number, type?: string): Promise<boolean> {
    return new Promise(async (resolve) => {
      focusCore
        .request(cgis.readRewardMsg, {
          id,
          type: type || '',
        })
        .then((res: any) => {
          console.log('🚀 ~ file: familyService.ts ~ line 413 ~ FamilyServie ~ returnnewPromise ~ res', res)

          return resolve(res.status || false)
        })
        .catch(() => {
          return resolve(false)
        })
    })
  }

  //   receiver_name	string
  // 必须
  // 收件人姓名
  // receiver_address	string
  // 必须
  // 收件人地址
  // family_id	string
  // 必须
  // 家庭ID
  // activity_id	integer
  // 必须
  // 活动ID
  // receiver_phone
  saveMailInfo({
    name,
    phone,
    address,
    addressDesc,
  }: {
    name: string
    phone: string
    address: string
    addressDesc: string
  }): Promise<boolean> {
    return new Promise(async (resolve) => {
      const familyId = await this.getFamilyId()
      if (!familyId) {
        return resolve(false)
      }
      focusCore
        .request(cgis.saveMailInfo, {
          receiver_name: name,
          receiver_address: addressDesc,
          receiver_phone: phone,
          receiver_area: address,
          family_id: familyId,
          activity_id: this.walkingAid,
        })
        .then((res: any) => {
          resolve(true)
        })
        .catch(() => {
          resolve(false)
        })
    })
  }

  checkUserHadFamily(): Promise<Boolean> {
    return new Promise((resolve) => {
      userService.getUserInfoForUpload().then((res: { nickNameEncode: string; avator: string }) => {
        const { nickNameEncode = '', avator = '' } = res
        focusCore
          .request(cgis.checkFamilyEquety, {
            activity_id: this.familyMgmAid,
            function_type: TAcceptShareFunctionType.Family,
            nick_name: nickNameEncode,
            head_img_url: avator,
          })
          .then((res: any) => {
            console.log('🚀 ~ file: familyService.ts:530 ~ FamilyServie ~ returnnewPromise ~ res:', res)
            const { status } = res
            return resolve(status)
          })
          .catch(() => {
            resolve(false)
          })
      })
    })
  }

  uploadDaySteps(): Promise<{ hasPermission: boolean; status: boolean; count?: number; isEncryptErr?: boolean }> {
    return new Promise((resolve, reject) => {
      this.appHealthHandler
        .getSteps()
        .then(async (res) => {
          console.log('从App获取到步数数据', res)
          const { hasPermission, count } = res
          if (hasPermission) {
            const familyId = await this.getFamilyId()
            focusCore
              .request(
                cgis.uploadDaySteps,
                {},
                {
                  family_id: familyId,
                  activity_id: this.walkingAid,
                  encrypted_data: count,
                  source_type: 'ios',
                }
              )
              .then((res: any) => {
                console.log('🚀 ~ file: familyService.ts ~ line 413 ~ FamilyServie ~ returnnewPromise ~ res', res)
                const result = {
                  status: res.status || false,
                  count: res.steps,
                  hasPermission,
                }
                console.log('打卡接口.then', result)
                return resolve(result)
              })
              .catch((err: any) => {
                console.log('打卡接口.catch', err)
                return resolve({ hasPermission, status: false, isEncryptErr: err.isEncryptErr || false })
              })
          } else {
            resolve({ hasPermission, status: false })
          }
        })
        .catch(() => {
          //用户App版本不支持/设备不支持
          return reject()
        })
    })
  }
}
export default new FamilyServie()
