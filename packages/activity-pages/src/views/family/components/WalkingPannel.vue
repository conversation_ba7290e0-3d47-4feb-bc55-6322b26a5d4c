<template>
  <div class="walking-pannel" v-if="!useNewWalking">
    <img src="../img/walking/pannel-header.png" alt="" class="header" />
    <div class="info">
      <div class="item">
        <p class="count">
          {{
            familyDaylySteps === undefined ||
            familyDaylySteps === null ||
            (familyDaylySteps && familyDaylySteps.toString()) === ''
              ? '--'
              : familyDaylySteps
          }}
        </p>
        <p class="name">家庭今日步数</p>
      </div>
      <div class="item">
        <p class="count">
          {{
            familyMonthPoint === undefined ||
            familyMonthPoint === null ||
            (familyMonthPoint && familyMonthPoint.toString()) === ''
              ? '--'
              : familyMonthPoint
          }}
        </p>
        <p class="name">家庭本月积分</p>
      </div>
      <div class="total">
        <img src="../img/walking/icon-point.png" alt="" class="icon" />
        <p>
          家庭累计已获得<span>{{
            familyTotalPoint === undefined ||
            familyTotalPoint === null ||
            (familyTotalPoint && familyTotalPoint.toString()) === ''
              ? '--'
              : familyTotalPoint
          }}</span
          >积分
        </p>
      </div>
    </div>
  </div>

  <div class="walking-pannel-new" v-if="useNewWalking">
    <div class="step-desc">
      <div class="item">
        <div class="index"></div>
        <div class="word">
          每日任务：个人每日上传步数
          <span class="red">{{ stepConfig.personalStepDaily }}步</span>以上，家庭成员上传总步数<span class="red">{{
            stepConfig.familyStepDaily
          }}</span
          >及以上，首次达标时家庭每人得<span class="red">{{ stepConfig.firstAwardDaily }}积分</span
          >；之后每次达标每人得<span class="red">{{ stepConfig.awardDaily }}积分</span>
        </div>
      </div>
      <div class="item">
        <div class="index"></div>
        <div class="word">
          月度任务：当月累计<span class="red">{{ stepConfig.cumulativeDaysMonthly }}天</span
          >完成每日任务，每人可得以下其一的奖励（限前{{ stepConfig.familyCountLimit }}个达标家庭）
        </div>
      </div>
    </div>
    <div class="monthly-price">
      <div class="desc">
        任意家庭成员当前财富值为<span class="red">50万</span>及以上，每人领
        <span class="red">{{ stepConfig.awardDescMonthly }}元红包奖励</span>；
      </div>
      <div class="desc">
        家庭成员当前财富值都低于50万，每人各获得
        <span class="red">500积分</span>。
      </div>
    </div>
    <div class="info">
      <div class="score">
        <p class="count">{{ familyDaylySteps }}</p>
        <p class="count">{{ familyMonthPoint }}</p>
      </div>
      <div class="total">
        <img src="../img/walking/icon-point_new.png" alt="" class="icon" />
        <p>
          家庭累计已获得<span>{{ familyTotalPoint }}</span
          >积分
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { storeToRefs } from 'pinia'

import { familyStore } from '../store/store'
const props = defineProps({
  useNewWalking: {
    type: Boolean,
    default: false,
  },
})
const store = familyStore()
const { familyDaylySteps, familyMonthPoint, familyTotalPoint, stepConfig = {} } = storeToRefs(store)
</script>

<style lang="scss" scoped>
.walking-pannel {
  font-size: 28px;
  color: #666666;
  line-height: 42px;
  font-weight: 400;
  width: 690px;
  .header {
    width: 100%;
  }
  p.title {
    width: 100%;
    text-align: left;
    span {
      font-size: 28px;
      color: #ed7f71;
      line-height: 42px;
      font-weight: 500;
    }
  }
  .info {
    background: #f5fafc;
    width: 630px;
    height: 278px;
    margin: 0 auto;
    margin-top: 30px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    .item {
      width: 50%;
      padding-top: 30px;
      text-align: center;
      p {
        width: 100%;
        font-size: 48px;
        color: #f05446;
        text-align: center;
        line-height: 72px;
        font-weight: 500;
        &.name {
          font-size: 28px;
          color: #666666;
          text-align: center;
          line-height: 42px;
          font-weight: 400;
        }
      }
    }
    .total {
      width: 100%;
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      &::after {
        content: '';
        width: 100%;
        height: 100%;
        background-image: linear-gradient(180deg, #e9f4f8 0%, #8ac5ea3f 100%);
        z-index: 2;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
      }
      &::before {
        width: 20px;
        height: 20px;
        background: #b6e3e929;
        content: '';
        position: absolute;
        transform: rotate(45deg);
        top: -12px;
        right: 150px;
        z-index: 1;
      }
      .icon {
        width: 80px;
        height: 80px;
        margin-right: 5px;
        z-index: 3;
      }
      p {
        z-index: 3;
      }
      span {
        color: #f05446;
      }
    }
  }
}

.walking-pannel-new {
  font-size: 28px;
  line-height: 1.5;
  width: 690px;
  margin: 0 autp;

  .monthly-price {
    width: 650px;
    height: 294px;
    background: url('../img/walking/bg_month_price.png') no-repeat;
    background-size: 100%;
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
    margin: 0 auto;
    margin-top: 25px;
    .desc {
      width: 415px;
      font-size: 28px;
      line-height: 38px;
      padding: 35px 0;
      margin-left: 40px;
      color: #6f5b60;
      text-align: left;
      .red {
        color: #f05446;
      }
    }
  }

  .step-desc {
    width: 100%;

    .item {
      width: 100%;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      padding-right: 30px;
      margin-top: 20px;
      color: #6f5b60;
      font-size: 30px;
      .index {
        flex: none;
        margin-right: 10px;
        margin-left: 20px;
        width: 12px;
        height: 12px;
        margin-top: 15px;
        background: url('../img/walking/icon-pannel-word.png') no-repeat;
        background-size: 100% 100%;
      }
      .word {
        text-align: left;
        .red {
          color: #f05446;
          font-weight: bold;
        }
      }
    }
  }
  .info {
    height: 209px;
    width: 650px;
    position: relative;
    background: url('../img/walking/bg-pannel-info.png') no-repeat;
    background-size: 100%;
    color: #cd9f8a;
    margin: 0 auto;
    margin-top: 30px;

    .score {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      bottom: 78px;
      .count {
        width: 100%;
        text-align: center;
        font-size: 48px;
        color: #ff2121;
        letter-spacing: 0;
        line-height: 48px;
        font-weight: 600;
      }
    }
    .total {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      bottom: 6px;
      width: 100%;
      .icon {
        width: 32px;
        height: 32px;
        margin-right: 10px;
        font-size: 28px;
        color: #cd9f8a;
        letter-spacing: 0;
        line-height: 28px;
        font-weight: 400;
        span {
          color: #ff2121;
        }
      }
    }
  }
}
</style>
