<template>
  <div class="invite-list" v-if="dataList.length">
    <div class="contain">
      <p class="nodata" v-if="!dataList.length">暂未发出任何邀请</p>
      <template v-else>
        <p class="list-head">申请加入列表</p>

        <div class="list-item" v-for="(item, index) in dataList" :key="index">
          <div class="avator">
            <img :src="item.head_img_url || AvatorDefault" alt="" />
          </div>
          <div class="info">
            <div class="top">
              {{ item.nick_name }}
              <img :src="`${item.gender === '1' ? iconMan : iconWoman}`" alt="" class="gender" />
            </div>
            <p class="bottom">申请加入家庭</p>
          </div>
          <div class="btn-agree" @click="clickAgree(item)">同意</div>
        </div>
        <div class="btn-more" v-show="!showListMoreFlag && dataListMore.length" @click="showListMore">查看更多 ></div>
        <template v-if="showListMoreFlag">
          <div class="list-item" v-for="(item, index) in dataListMore" :key="index">
            <div class="avator">
              <img :src="item.head_img_url || AvatorDefault" alt="" />
            </div>
            <div class="info">
              <div class="top">
                {{ item.nick_name }}
                <img :src="`${item.gender === '1' ? iconMan : iconWoman}`" alt="" class="gender" />
              </div>
              <p class="bottom">申请加入家庭</p>
            </div>
            <div class="btn-agree" @click="clickAgree(item)">同意</div>
          </div>
        </template>
      </template>
    </div>
  </div>
  <Mask v-if="showDialog">
    <div class="dialog">
      <div class="avator">
        <img :src="M2Avator" alt="" />
      </div>
      <div class="title">
        确定建立家庭关系吗？
        <p>
          对方是：<span>{{ M2Name }}</span>
        </p>
      </div>
      <p class="context">
        确定组建“我的家庭”，<span>不可再接受其他{{ M2SexName }}</span
        >用户的申请。
      </p>
      <p class="tip">（注：建立家庭关系后，该长辈会获得邀请奖励）</p>
      <div class="btns">
        <div class="btn-cancel" @click="hideDialog">取消</div>
        <div class="btn-confirm" @click="confirmFamilyRelationship">确定</div>
      </div>
    </div>
  </Mask>
</template>

<script setup lang="ts">
import iconMan from '../img/icon-man.png'
import iconWoman from '../img/icon-woman.png'
import AvatorDefault from '../img/avator-default.png'
import familyService from '../service/familyService'
import { ref, watch, inject } from 'vue'
import { focusStore, focusServices, Mask } from '@focus/render'

const { modalStore } = focusStore.stores
const { loadingStart, loadingEnd } = modalStore
const emit = defineEmits(['updateFamilyList'])

const familyMgmAid = inject<number>('familyMgmAid') || 0
const hasAccount = inject<boolean>('hasAccount')

const dataList = ref<any[]>([])
const dataListMore = ref<any[]>([])
const showListMoreFlag = ref(false)

console.log('......>>>>> InviteList>>>>>>....')

// watch(hasAccount, (hasAccount) => {
//   console.log('datalist hasAccount...', hasAccount)

//   if (hasAccount) {
//     getListData()
//   }
// })

getListData()

function getListData() {
  loadingStart('famliylist')
  familyService.getFamilyRelationshipList(familyMgmAid).then((res) => {
    console.log('🚀 ~ file: InviteList.vue ~ line 92 ~ familyService.getFamilyRelationshipList ~ res', res)
    loadingEnd('famliylist')
    const list: any[] = []
    const listMore: any[] = []
    res.forEach((i, index) => {
      if (index < 3) {
        list.push({
          ...i,
        })
      } else {
        listMore.push({ ...i })
      }
    })
    dataList.value = list
    dataListMore.value = listMore
  })
}

function confirmFamilyRelationship() {
  modalStore.loadingStart('confirmRelationship')
  familyService
    .agreeBuildFamily(M2ListId.value)
    .then((res) => {
      modalStore.loadingEnd('confirmRelationship')

      hideDialog()
      modalStore.toastShow('已成功组建家庭！')
    })
    .catch((err) => {
      modalStore.loadingEnd('confirmRelationship')

      const { errMsg } = err
      console.log('🚀 ~ file: InviteList.vue ~ line 119 ~ confirmFamilyRelationship ~ errMsg', errMsg)
      // modalStore.toastShow(errMsg||'组建家庭失败！请重试')
      modalStore.confirmContrl({
        show: true,
        contents: [errMsg],
        hideConfirm: true,
        btnCancelText: '知道了',
      })
    })
    .finally(() => {
      setTimeout(() => {
        hideDialog()
        getListData()
        emit('updateFamilyList')
      }, 300)
    })
}

const showDialog = ref(false)
const hideDialog = function () {
  showDialog.value = false
}

const showListMore = function () {
  showListMoreFlag.value = true
}

const clickAgree = function (itemData) {
  const { nick_name = '', head_img_url = '', gender = '', id = 0 } = itemData
  M2Name.value = nick_name
  M2Avator.value = head_img_url
  M2SexName.value = gender === '1' ? '男性' : '女性'
  M2ListId.value = id
  showDialog.value = true
}

const M2Name = ref('')
const M2Avator = ref('')
const M2SexName = ref('男性')
const M2ListId = ref(0)
</script>

<style lang="scss" scoped>
.invite-list {
  padding-top: 30px;
  position: relative;
  z-index: 5;
  width: 690px;
  margin: 0 auto;
  &::after,
  &::before {
    content: '';
    width: 22px;
    height: 30px;
    display: block;
    position: absolute;
    background: url('../img/icon-line.png') no-repeat;
    background-size: 100%;
  }
  &::before {
    left: 60px;
    top: 0;
  }
  &::after {
    right: 60px;
    top: 0;
  }
  .contain {
    width: 690px;
    background: #fff;
    border-radius: 10px;
    color: #675b5a;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    justify-content: center;
    flex-direction: column;
    overflow: hidden;
    .list-head {
      width: 100%;
      height: 94px;
      font-size: 30px;
      color: #324960;
      background: #f5f9fc;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .list-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      position: relative;
      padding: 30px;
      width: 100%;
      box-sizing: border-box;
      &::after {
        content: '';
        height: 1px;
        width: 630px;
        position: absolute;
        left: 30px;
        bottom: 0;
        background: #fafcfe;
      }
      .avator {
        width: 80px;
        height: 80px;
        border: 5px solid #cee3f2;
        overflow: hidden;
        border-radius: 100%;
        margin-right: 10px;
        flex: none;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .info {
        width: 100%;
        flex: auto;
        font-size: 24px;
        text-align: left;
        .top {
          font-weight: bold;
          color: #666666;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .gender {
            width: 48px;
            height: 24px;
          }
        }
      }
      .btn-agree {
        flex: none;
        width: 128px;
        height: 60px;
        border-radius: 30px;
        border: 2px solid #f58a5a;
        line-height: 60px;
        font-size: 28px;
        font-weight: bold;
        text-align: center;
        color: #f58a5a;
        &:active {
          background: #f58a5a;
          color: #fff;
        }
      }
    }
    .btn-more {
      font-size: 24px;
      margin-top: 20px;
      color: #5f9bfa;
      padding-bottom: 20px;
    }
  }
}

.dialog {
  width: 630px;
  height: 720px;
  position: relative;
  background: linear-gradient(to bottom, #f4f5f4, #d5f9ff);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  box-sizing: border-box;
  padding: 30px;
  padding-top: 60px;
  border-radius: 10px;
  .avator {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 188px;
    width: 188px;
    position: relative;
    border-radius: 100%;
    z-index: 2;
    background: url('../img/avatar-deractor.png') no-repeat;
    background-size: cover;
    img {
      border-radius: 50%;
      width: 120px;
      height: 120px;
      position: relative;
    }
  }
  .title {
    margin-top: 40px;
    margin-bottom: 40px;
    color: #642d09;
    font-weight: bold;
    font-size: 32px;
    span {
      color: #fe776b;
    }
  }
  .context,
  .tip {
    font-size: 29px;
    color: #642d09;
    text-align: left;
    width: 100%;
    span {
      font-weight: bold;
    }
  }
  .tip {
    font-size: 24px;
    line-height: 1.5;
    margin-top: 10px;
  }
  .btns {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    left: 0;
    bottom: 60px;
    padding: 0 30px;
    .btn-cancel {
      width: 270px;
      height: 80px;
      border-radius: 40px;
      line-height: 76px;
      font-size: 32px;
      border: 2px solid #f58a5a;
      text-align: center;
      color: #f58a5a;
      &:active {
        background: #f58a5a;
        color: #fff;
      }
    }
    .btn-confirm {
      flex: none;
      width: 270px;
      height: 80px;
      border-radius: 40px;
      line-height: 80px;
      font-size: 32px;
      font-weight: bold;
      text-align: center;
      color: #fff;
      background: linear-gradient(to bottom, #ffc773, #f58a5a);
      &:active {
        background: #f58a5a;
        color: #fff;
      }
    }
  }
}
</style>
