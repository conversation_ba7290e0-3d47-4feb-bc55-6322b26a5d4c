<template>
  <div class="address-list">
    <div class="header">
      <div
        class="btn-close"
        @click="
          () => {
            hide && hide()
          }
        "
      ></div>
      <div class="btn-confirm" @click="confirm" :class="{ disabled: btnDisabled }">确定</div>
    </div>
    <div class="selected">
      <span :class="{ hasname: leftName.fullname }">{{ leftName.fullname || '请选择' }}</span>
      <span :class="{ hasname: centerName.fullname }">{{ centerName.fullname || '请选择' }}</span>
      <span :class="{ hasname: rightName.fullname }">{{
        rightList.length ? (!rightName.fullname ? '请选择' : rightName.fullname) : ''
      }}</span>
    </div>
    <div class="wrap">
      <div class="left">
        <div class="item" v-for="(item, index) in leftList" :key="item.id" @click="select('left', item)">
          {{ item.fullname }}
        </div>
      </div>
      <div class="center">
        <div class="item" v-for="(item, index) in centerList" :key="item.id" @click="select('center', item)">
          {{ item.fullname }}
        </div>
      </div>
      <div class="right">
        <div class="item" v-for="(item, index) in rightList" :key="item.id" @click="select('right', item)">
          {{ item.fullname }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { gDistrictInfo } from '../service/COMMON'

const props = defineProps({
  setAddress: {
    default() {
      return (str: string) => {}
    },
  },
  hide: {
    require: true,
    default() {
      return () => {}
    },
  },
})

const leftName = ref({ id: '', fullname: '' })
const centerName = ref({ id: '', fullname: '' })
const rightName = ref({ id: '', fullname: '' })
const leftList = gDistrictInfo[0]
const btnDisabled = ref(true)
const centerList = computed(() => {
  const cernter = gDistrictInfo[1]
  const targetList = cernter.filter((i) => {
    const parentId = leftName.value.id && leftName.value.id.substring(0, 2)
    const isChild = parentId && i.id.indexOf(parentId) === 0
    if (isChild) {
      return true
    }
  })
  return targetList
})
const rightList = computed(() => {
  const right = gDistrictInfo[2]
  const targetList = right.filter((i) => {
    const parentId = centerName.value.id && centerName.value.id.substring(0, 4)
    const isChild = parentId && i.id.indexOf(parentId) === 0
    if (isChild) {
      return true
    }
    return false
  })
  return targetList
})

function select(pos: string, data: any) {
  const empty = {
    id: '',
    fullname: '',
  }
  if (pos === 'left') {
    leftName.value = data
    centerName.value = empty
    rightName.value = empty
    btnDisabled.value = true
  }
  if (pos === 'center') {
    centerName.value = data
    rightName.value = empty
    btnDisabled.value = rightList.value.length ? true : false
  }
  if (pos === 'right') {
    rightName.value = data
    btnDisabled.value = false
  }
}

function confirm() {
  if (props.setAddress && !btnDisabled.value) {
    let str = `${leftName.value.fullname}-${centerName.value.fullname}`
    if (rightName.value.fullname) {
      str += `-${rightName.value.fullname}`
    }
    console.log('🚀 ~ file: addressSelector.vue ~ line 111 ~ confirm ~ str', str)
    if (str) {
      props.setAddress(str)
    }
  }
}
</script>

<style lang="scss" scoped>
.address-list {
  width: 100%;
  background: #fff;
  position: relative;
  padding-bottom: 60px;
  .header {
    height: 80px;
    line-height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    font-size: 32px;
    color: #3476f2;
    border-bottom: 1px solid #eee;
    .btn-close {
      background: url('../img/btn-close-line.png') no-repeat;
      background-size: 100% 100%;
      height: 28px;
      width: 28px;
    }
    .btn-confirm {
      &.disabled {
        opacity: 0.5;
      }
    }
  }
  .selected {
    width: 100%;
    font-size: 28px;
    line-height: 1.5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    padding: 20px 0;
    color: #3434347d;
    span {
      display: block;
      width: 33.3%;
      text-align: center;
      line-height: 1;
      &.hasname {
        color: #3477f2bc;
      }
    }
  }
  .wrap {
    width: 100%;
    height: 400px;
    overflow: hidden;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    border-top: 1px solid #eee;
  }
  .left,
  .center,
  .right {
    width: 33.3%;
    border-right: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    font-size: 26px;
    height: 400px;
    overflow-y: scroll;
    padding-bottom: 50px;
    .item {
      height: 50px;
      line-height: 50px;
    }
  }
}
</style>
