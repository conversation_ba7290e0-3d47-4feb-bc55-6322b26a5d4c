<template>
  <!-- 通用版UI -->
  <div class="family" v-if="!(hideList === '0') && dataIsReady && walkingConfigType === 1">
    <img class="top-img" src="../img/M1-2.png" alt="" />
    <div class="btn-rule" @click="jumpToRuleLink"></div>
    <!-- 邀请状态 -->
    <div class="family-list" :class="{ isfamily: isEnterFamilyStatus }">
      <div class="user-avator">
        <img :src="userAvator" alt="" />
      </div>
      <img src="../img/header-img3.png" alt="" class="header-img" />
      <div class="list" :class="{ 'two-list': familyList.length === 2 }">
        <div
          class="item"
          v-for="(item, index) in familyList"
          :key="index"
          @click="showInvite(item.name)"
          :class="{ istop: item.isTopEquity }"
        >
          <div class="avator" :class="`sex_${item.gender} ${item.isInvite ? 'invite' : ''}`">
            <!-- 还没组建成家庭 -->
            <img :src="item.avator || AvatorInvite" alt="" v-if="index > 0 && item.isInvite" />
            <img :src="item.avator || AvatorDefault" alt="" v-if="index > 0 && !item.isInvite" />
            <img :src="item.avator || AvatorDefault" alt="" v-if="index == 0" />
          </div>
          <p class="name">{{ item.name }}</p>
          <div class="m1" v-if="index === 0">邀请者</div>
        </div>
      </div>
      <div class="equity" v-if="isEnterFamilyStatus" :class="{ hasequity: hasFamilyEquity }">
        <template v-if="hasFamilyEquity">
          <p class="title">{{ familyEquety.shareMan }}正在共享微众{{ familyEquety.topEquityName }}会员权益</p>
          <p class="desc">
            家庭内所有成员，当前都可享{{ familyEquety.equityNums }}项{{ familyEquety.topEquityName }}及专属权益
          </p>
          <img class="cover" src="../img/equity-zuanshi2.png" alt="" />
        </template>
        <template v-if="!hasFamilyEquity">
          <p class="title">全家使用微众银行,共享家庭权益</p>
          <p class="desc">家庭内成员等级铂金及以上等级，加入家庭后次月可以共享权益</p>
        </template>
        <div class="link" @click="goEquety">更多共享权益详情 ></div>
      </div>
      <template v-if="!userIsM2 && !isEnterFamilyStatus">
        <div class="btn-invite" @click="showInvite('邀请')">立即邀请</div>
      </template>
    </div>

    <invite-list v-if="!userIsM2 && hasAccount" @updateFamilyList="updateFamilyList"></invite-list>
  </div>

  <!-- 弹窗 -->
  <Mask v-if="showDialogInvite || showDialogEnterFamily">
    <!-- 确定邀请的弹窗 -->
    <div class="dialog invite" v-if="showDialogInvite">
      <div class="btns">
        <div class="btn-cancel" @click="hideDialog">取消</div>

        <div class="btn-confirm" @click="startShare">确定，去邀请</div>
      </div>
    </div>
    <!-- 进入我的家庭的弹窗 -->
    <div class="dialog myfamily" v-if="showDialogEnterFamily">
      <div class="contain">家庭内仅邀请了一位长辈，是否继续？</div>
      <div class="btns">
        <div class="btn-cancel" @click="hideDialog">取消</div>

        <div class="btn-confirm" @click="enterMyfalimy">确定</div>
      </div>
    </div>
  </Mask>
</template>

<script setup lang="ts">
import InviteList from './InviteList'
import AvatorMan from '../img/avator-man.png'
import AvatorWoman from '../img/avator-woman.png'
import AvatorInvite from '../img/avator-invite.png'
import AvatorDefault from '../img/avator-default.png'
import familyService from '../service/familyService'
import { focusStore, focusServices, Mask, focusCore } from '@focus/render'
import { stringLiteral } from '@babel/types'
const { mgmService, userService, jumpService } = focusServices
import { useRouter } from 'vue-router'
import { ref, inject, watch, computed, Ref, onMounted } from 'vue'

enum TAcceptShareFunctionType {
  Family = 'my_family',
}
const props = defineProps({
  hideList: '1',
})
const emit = defineEmits(['dataIsReady', 'useNewWalking'])
const cdnPrefix = CDN_PREFIX_FOCUS_RENDER
const familyMgmAid = inject<number>('familyMgmAid') || 0
const hasAccount = inject<boolean>('hasAccount')
const shareConfigData = inject<any>('shareConfigData')
const ruleLink = inject<string>('ruleLink')
const userIsInFamilyStatus = inject<Function>('userIsInFamilyStatus')
const equityPage = inject<string>('equityPage')
const userIsM2 = inject<Ref<boolean>>('userIsM2', ref(false))
const setUserIsM2 = inject<Function>('setUserIsM2') || (() => {})
const familyDataIsReady = inject('familyDataIsReady') || null

const familyIsFull = ref(false)

const { modalStore, userStore } = focusStore.stores
const { loadingStart, loadingEnd } = modalStore

const userAvator = ref(AvatorDefault)

const familyEquety = ref<{
  equityNums: string
  topEquityName: string
  lowEquityName: string
  shareMan: string
  selfEquityName: string
}>({
  equityNums: '',
  topEquityName: '',
  lowEquityName: '',
  shareMan: '',
  selfEquityName: '',
})

const hasFamilyEquity = computed<boolean>(() => {
  return !!familyEquety.value.shareMan
})

// const equityImg = computed(() => {
//   return familyEquety.value.topEquityName === '钻石' ? require('../img/equity-zuanshi.png') : ''
// })
const familyList = ref<any>([
  { avator: '', name: '我', gender: '1', isTopEquity: false },
  { avator: '', name: '邀请爸爸', gender: '1', isInvite: true, isTopEquity: false },
  { avator: '', name: '邀请妈妈', gender: '2', isInvite: true, isTopEquity: false },
])

const showDialogEnterFamily = ref(false)
const isEnterFamilyStatus = ref(false)
const familyIsSetup = ref(false)
const router = useRouter()
const dataIsReady = ref(false)
const walkingConfigType = ref(0)

watch(hasAccount, (hasAccount) => {
  console.log('hasAccount...', hasAccount)

  if (hasAccount) {
    loadingStart('getmyfamily')
    updateFamilyList()
  }
})

const updateFamilyList = function () {
  familyService.getMyFamilyList().then((res: any) => {
    console.log('family。。。。。', res)
    const { list = [], isM2 = false, hasParentNums = 0, isInFamilyWhiteList, hasWalkingConfig = false } = res
    console.log(
      '🚀 ~ file: FamilyList.vue:152 ~ familyService.getMyFamilyList ~ isInFamilyWhiteList:',
      isInFamilyWhiteList
    )
    if (!isInFamilyWhiteList && focusCore.env.isInWx) {
      modalStore.errorMaskContrl('is_not_family_white', ['您不是受邀用户，无法参与此活动'], '', true)
      return
    }
    walkingConfigType.value = hasWalkingConfig ? 2 : 1
    emit('useNewWalking', walkingConfigType.value)
    dataIsReady.value = true
    emit('dataIsReady', true)
    userAvator.value = res.userAvator || AvatorDefault
    familyEquety.value = res.familyEquety
    familyList.value = list
    setUserIsM2(isM2)
    familyIsSetup.value = hasParentNums === 2
    familyIsFull.value = !list.some((i) => i.name.indexOf('邀请') > -1)

    if (hasParentNums > 0 || isM2) {
      enterMyFamily()
    }

    if (familyDataIsReady) {
      familyDataIsReady.value = true
    }
    loadingEnd('getmyfamily')
  })
}

const enterMyFamily = function () {
  isEnterFamilyStatus.value = true
  userIsInFamilyStatus && userIsInFamilyStatus()
}

const showInvite = function (name: string) {
  if (name.indexOf('邀请') < 0 || userIsM2.value || familyIsSetup.value) {
    return
  }
  modalStore.loadingStart('showFamilyInvite')
  mgmService
    .checkUserIsM2(familyMgmAid, TAcceptShareFunctionType.Family)
    .then((isM2: boolean) => {
      if (isM2) {
        modalStore.confirmContrl({
          show: true,
          contents: ['您已经申请与其他人建立家庭关系', '无法再发起邀请！'],
          hideConfirm: true,
        })
        return
      }
      showDialogInvite.value = true
    })
    .catch(() => {
      modalStore.errorMaskContrl('checkM2Fail')
    })
    .finally(() => {
      modalStore.loadingEnd('showFamilyInvite')
    })
}
const startShare = function () {
  console.log(shareConfigData.value)
  modalStore.loadingStart('family-share')
  setTimeout(() => {
    modalStore.loadingEnd('family-share')
    hideDialog()
  }, 400)
  mgmService.clickShare(`family-share`, shareConfigData.value)
}
const showDialogInvite = ref(false)

const hideDialog = function () {
  showDialogInvite.value = false
  showDialogEnterFamily.value = false
}

const jumpToRuleLink = function () {
  // window.location.href = ruleLink
  console.log('🚀 ~ file: FamilyList.vue ~ line 102 ~ jumpToRuleLink ~ ruleLink', ruleLink)
  jumpService.jump({
    path: ruleLink,
  })
}
const goEquety = function () {
  if (equityPage) {
    // location.href = equityPage
    jumpService.jump({
      path: equityPage,
    })
  }
}
const showCheckInFamily = function () {
  showDialogEnterFamily.value = true
}

const enterMyfalimy = function () {
  familyService.setInFamilyStatus().then((res) => {
    console.log('🚀 ~ file: FamilyList.vue ~ line 131 ~ familyService.setInFamilyStatus ~ res', res)
    if (res) {
      showDialogEnterFamily.value = false
      enterMyFamily()
    } else {
      modalStore.toastShow('网络繁忙，请重新尝试~')
    }
  })
}

onMounted(() => {
  focusCore.onRedirectToSpecialPath('/family_invite', () => {
    console.log(shareConfigData.value)
    showInvite('邀请')
  })
})
</script>

<style lang="scss" scoped>
.family {
  font-size: 32px;
  line-height: 1.5;
  width: 100%;
  margin-bottom: 30px;
  position: relative;
  .top-img {
    z-index: 0;
    width: 100%;
  }

  .btn-rule {
    position: absolute;
    right: 0;
    top: 79px;
    width: 45px;
    height: 109px;
    background: url('../img/btn-rule2.png') no-repeat;
    background-size: contain;
    cursor: pointer;
  }
  .family-list {
    width: 690px;
    background: #fff;
    border-radius: 10px;

    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    padding: 30px;
    padding-bottom: 60px;
    z-index: 2;
    position: relative;
    margin-top: -510px;
    padding-top: 80px;

    .user-avator {
      height: 120px;
      width: 120px;
      position: absolute;
      left: 50%;
      top: -58px;
      transform: translateX(-50%);
      z-index: 2;
      img {
        width: 100%;
        height: 100%;
        border: 5px solid transparent;
        background-clip: padding-box, border-box;
        background-origin: padding-box, border-box;
        background-image: linear-gradient(90deg, #f5fbfd, #f5fbfd), linear-gradient(180deg, #b6e3e9, #8ac5ea);
        border-radius: 100%;
        overflow: hidden;
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0.5);
      }
    }
    .header-img {
      height: 48px;
      width: 342px;
      &.isinvite {
        width: 570px;
        height: 33px;
      }
    }
    .list {
      width: 630px;
      justify-content: space-between;
      display: flex;
      align-items: center;
      margin-top: 40px;
      padding-bottom: 21px;
      &.two-list {
        padding: 0 128px 21px;
      }

      .item {
        margin: 0 30px;
        width: 100px;
        position: relative;
        .m1 {
          position: absolute;
          left: 50%;
          top: -30px;
          height: 40px;
          background: linear-gradient(to bottom, #f4af4d, #ed6b3c);
          border-radius: 5px;
          color: #fff;
          line-height: 40px;
          font-size: 20px;
          width: 80px;
          transform: translateX(-50%);
          &::after {
            content: '';
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: -5px;

            width: 0;
            height: 0;
            border-top: 5px solid #ed6b3c;
            border-right: 5px solid transparent;
            border-left: 5px solid transparent;
          }
        }
        .avator {
          width: 100px;
          height: 100px;
          border-radius: 100%;
          border: 5px solid #cee3f2;
          overflow: hidden;
          &.invite {
            border: none;
          }
          img {
            width: 100%;
            height: 100%;
          }
          &.noavator {
            border: none;
          }
          &.sex_1,
          &.sex_2 {
            background-size: 100%;
            background-image: url('../img/avator-man.png');
          }
          &.sex_2 {
            background-image: url('../img/avator-woman.png');
          }
        }
        .name {
          text-align: center;
          font-size: 24px;
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: rgb(102, 102, 102);
        }
      }
    }
    &.isfamily {
      .list {
        .item {
          &.istop {
            &::after {
              content: '';
              width: 30px;
              height: 30px;
              border: 5px solid #b6e3e9;
              border-right: none;
              border-bottom: none;
              background-color: #f5fbfd;
              position: absolute;
              bottom: -48px;
              left: 50%;
              transform-origin: center center;
              transform: rotate(45deg) translateX(-50%);
            }
          }
        }
      }
    }

    .equity {
      border-radius: 20px;
      border: 5px solid transparent;
      background-clip: padding-box, border-box;
      background-origin: padding-box, border-box;
      background-image: linear-gradient(to right, #f5fbfd, #f5fbfd), linear-gradient(180deg, #b6e3e9, #8ac5ea);

      width: 630px;
      padding: 36px 40px 40px;
      box-sizing: border-box;
      .title {
        font-size: 28px;
        color: #2c4962;
        font-weight: bold;
      }
      .desc {
        margin-top: 10px;
        font-size: 24px;
        color: #666666;
        text-align: left;
      }
      .cover {
        margin-top: 40px;
      }
      .link {
        height: 30px;
        font-size: 26px;
        color: #3476f2;
        margin-top: 30px;
      }
    }
    .btn-invite {
      height: 97px;
      width: 536px;
      background: url('../img/btn-invite.png') no-repeat;
      background-size: 100%;
      font-size: 0;
      cursor: pointer;
      margin-top: 60px;
      &:active {
        opacity: 0.6;
      }
    }
    .btn-hide-list {
      margin-top: 30px;
      font-size: 24px;
      color: #3875f2;
      border-bottom: 1px solid #3875f2;
      line-height: 1;
    }
  }
}
.dialog {
  width: 630px;
  position: relative;

  .btns {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    position: absolute;
    left: 0;
    bottom: 60px;
    .btn-cancel {
      width: 260px;
      height: 80px;
      border-radius: 40px;
      line-height: 80px;
      font-size: 36px;
      border: 2px solid #f58a5a;
      text-align: center;
      color: #f4895a;
      font-weight: bold;
      &:active {
        background: #f58a5a;
        color: #fff;
      }
    }
    .btn-confirm {
      flex: none;
      width: 260px;
      height: 80px;
      border-radius: 40px;
      line-height: 78px;
      font-size: 36px;
      font-weight: bold;
      text-align: center;
      color: #fff;
      background: linear-gradient(to bottom, #ffc773, #f58a5a);
      &:active {
        background: #f58a5a;
        color: #fff;
      }
    }
  }

  &.invite {
    height: 720px;
    background: url('../img/M1_d_1.png') no-repeat;
    background-size: 100%;
  }
  &.myfamily {
    background: linear-gradient(140deg, #fffdf6, #c2f0fd);
    height: 320px;
    border-radius: 10px;
    font-size: 28px;
    color: #5d3013;
    text-align: center;
    .contain {
      margin-top: 60px;
      font-weight: bold;
    }
  }
}
</style>
