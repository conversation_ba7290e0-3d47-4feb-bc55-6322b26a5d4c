BUILD_TEST &&
  import(/* webpackChunkName: "vconsole" */ 'vconsole').then((cls) => {
    const Cls = cls.default
    return new Cls()
  })

import { createApp } from 'vue'
import App from './App.vue'
import focusRender, { focusCore } from '@focus/render'
import '@focus/render/build-lib/focus-render.css'
import { createPinia } from 'pinia'
import router from './routes'
const app = createApp(App)
const pinia = createPinia()
app.use(pinia).use(focusRender, { $pinia: pinia }).use(router)
// if (BUILD_TEST) {
//   // 使用测试环境
//   focusCore.useTestMode()
// }

app.mount('#app')
