import { focusStore, focusServices, focusCore, focusUtils } from '@focus/render'

export interface TProductCardItem {
  bankLogoUrl?: string // 银行logo
  bankShortName?: string // 银行机构名称
  productCode?: string // 产品code
  productName?: string // 产品名称
  rateValue?: string // 收益率数值
  rateDesc?: string // 收益率描述
  productPeriod?: string // 产品周期
  recommendation?: string // 推荐语
  saleStatus?: string // 销售状态，01去购买；02已售罄；03已截售
  establishDate?: string // 成立日期
  rewardStatus?: string // 奖励期状态，01奖励期结束，02奖励期未结束
  earningsRateDate?: string // 收益率统计日期
}

interface TTemplateData {
  templateType?: string // 模板类型：PRODUCT_BUY（产品购买）
  rewardRule?: {
    templateSubType?: string // 模板子类型：
    ruleType?: string // 规则类型：SINGLE_BUY-单次购买, CUMULATIVE_BUY-累计购买, AIP-基金定投
    buyAmountCalcWay?: string // 购买金额计算方式，0-申购，1-净申购
    allowRedeem?: string // 是否允许赎回：1-是，0-否
    multiProductCalcTogether?: string // 多产品是否合并计算：1-是，0-否 购买规模和购买达标用
    multiProductRewardOnce?: string // 多产品是否只贡献一次积分: 1-是，0-否,多产品是否合并计算取否时
    singleProductMultiPlanRewardOnce?: string // 单产品多计划是否只贡献一次积分: 1-是，0-否
    isBonus?: string // 是否加码：1-是，0-否
    bonusType?: string // 加码类型，加码奖励类型：PERCENTAGE-比例，ABSOLUTE-绝对值
    bonusGroupType?: string // 加码人群，CUST_GROUP-人群包, MEMBER_LEVEL-会员等级, ENTERPRISE_AUTH-企业认证, NEW_CUST 新户
    bonusPercentage?: number // 加码比例
    wealthRank?: string // 新财富+用户等级,1:白银,2:黄金,3:铂金,4:钻石
    rewardList?: {
      aipWay?: string // 定投方式，0-手动，1-自动
      requiredAmount?: number // 需要达到的金额（x元）
      rewardPoints?: number // 奖励积分值,奖励基础积分值+奖励加码积分值
      rewardBasePoints?: number // 奖励基础积分值
      rewardBonusPoints?: number // 奖励加码积分值
      productList?: TProductCardItem[] // 产品卡片list
    }[]
    productTypeDesc?: string //产品类型说明；1-只有理财子 2-只有基金产品 3-理财子&基金产品
  }
}

interface TPageConfig {
  // 模板类型：PRODUCT_BUY（产品购买）
  templateType?: string
  participateMethod?: string // 参与方式，1: 点击参与；2: 浏览参与；3: 无条件参与
  // 页面模板数据
  pageInfo?: {
    // 页面可访问开始时间
    accessStartTime?: string
    // 页面可访问结束时间
    accessEndTime?: string
    // 页面标题
    pageTitle?: string
    // 页面主题色调
    pageThemeColor?: string
    pageAccessDesc?: string // 页面访问时间判断 1: 未开始；2: 超过访问时间；3: 可访问
    // 分享信息
    shareInfo?: {
      // 右上角分享按钮状态，1-开启；2-关闭
      shareButton?: string
      // 分享标题
      shareTitle?: string
      // 分享内容
      shareContent?: string
      // 分享链接
      shareLink?: string
      // 分享图片URL
      shareImageUrl?: string
      // APP分享面板配置，1-分享面板[微信好友+朋友圈],2-分享面板[微信好友],3-分享面板[朋友圈],4-直接分享[微信好友]
      appSharePanel?: string
      // 分享海报URL
      sharePosterUrl?: string
    }

    // 头部信息
    topInfo?: {
      headImageUrl?: string // 头部图片URL
      activityCode?: string // 活动编号
      rewardWay?: string // 奖励规则
      bonusInfo?: {
        isBonus?: string // 是否加码，1-是，0-否
        bonusType?: 'PERCENTAGE' | 'ABSOLUTE' // 加码类型，加码奖励类型：PERCENTAGE-比例，ABSOLUTE-绝对值
        bonusGroupType?: 'CUST_GROUP' | 'MEMBER_LEVEL' | 'ENTERPRISE_AUTH' | 'NEW_CUST' // 加码人群，CUST_GROUP-人群包, MEMBER_LEVEL-会员等级, ENTERPRISE_AUTH-企业认证, NEW_CUST 新户
        bonusPercentage?: number // 加码比例（对应加码类型为PERCENTAGE）
        bonusAmount?: number // 加码绝对值（对应加码类型为ABSOLUTE）
        wealthRank?: string // 新财富+用户等级,1:白银,2:黄金,3:铂金,4:钻石
      }
    }
    // banner卡片list
    bannerCardList?: BannerCardItem[]
    // 积分兑好礼开关，1-开启；2-关闭
    pointsRedeem?: string
    // 选择理由开关，1-开启；2-关闭
    selectionReason?: string
    // 合作理财子开关，1-开启；2-关闭
    partnerWealth?: number
  }
}

interface BannerCardItem {
  // 展示图片URL
  displayImageUrl?: string
  // 展示开始时间
  displayStartTime?: string
  // 展示结束时间
  displayEndTime?: string
  // 1-不跳转；2-跳APP网页；3-跳微信网页；4-跳APP模块（最多添加5个额外参数）；5-跳APP产品页；6-跳小程序
  jumpType?: string
  // 跳转链接
  jumpLink?: string
  // 加密跳转链接
  jumpLinkSign?: string
  // 额外参数，Map形式，key value
  extraParams?: object
}

const cgis = {
  getTemplateInfo: {
    name: '查询活动模板信息-已开户',
    url: '/op-fmfront/activity/hj/fm/activityPageTemplateDataOpened',
  },
  getTemplateInfoNoAuth: {
    name: '查询活动模板信息-未开户',
    url: '/op-fmfront/activity/hj/fm/activityPageTemplateDataNotOpened',
  },
  getTemplateRewardInfo: {
    name: '查询活动模板奖励进度',
    url: '/op-fmfront/activity/hj/fm/activityTemplateRewardInfo',
  },
  getTemplateProductInfo: {
    name: '查询活动模板产品信息-已开户',
    url: '/op-fmfront/activity/hj/fm/activityPageRewardRuleDataOpened',
  },
  getTemplateProductInfoNoAuth: {
    name: '查询活动模板产品信息-未开户',
    url: '/op-fmfront/activity/hj/fm/activityPageRewardRuleDataNotOpened',
  },
}

class TemplateService {
  constructor() {}

  getTemplateInfo(aid: string | number) {
    return new Promise((resolve) => {
      let cgi = focusCore.hasAccount ? cgis.getTemplateInfo : cgis.getTemplateInfoNoAuth

      focusCore
        .request(cgi, { activityId: aid })
        .then((res: TPageConfig) => {
          console.log('🚀 ~ TemplateService ~ getTemplateInfo ~ res:', res)
          const { pageInfo = {}, templateType = 'PRODUCT_BUY' } = res
          const { topInfo = {}, bannerCardList = [], shareInfo } = pageInfo

          const pageBgs: any = {
            PRODUCT_BUY: '#deefff',
            SINGLE_BUY: '#ffe3c0',
            AIP: '#ffe8e3',
          }

          resolve({
            actNumber: topInfo.activityCode,
            topBanner: topInfo.headImageUrl,
            bannersInfo: bannerCardList,
            ruleText: topInfo.rewardWay,
            pageBg: pageBgs[templateType],
            shareInfo,
          })
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: service.ts:110 ~ TemplateService ~ getTemplateInfo ~ err:', err)
          resolve({
            pageName: '活动详情',
            pageBg: '#ffe0c2',
            bannerInfo: [
              {
                imgUrl: '111',
                clickEvent: {},
              },
              {
                imgUrl: '111',
                clickEvent: {},
              },
              {
                imgUrl: '111',
                clickEvent: {},
              },
            ],
          })
        })
    })
  }

  getTemplateRewardInfo(aid: string | number) {
    return new Promise((resolve) => {
      focusCore
        .request(cgis.getTemplateRewardInfo, { activityId: aid })
        .then((res: TPageConfig) => {
          console.log('🚀 ~ TemplateService ~ getTemplateRewardInfo ~ res:', res)
          resolve(res)
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: service.ts:110 ~ TemplateService ~ getTemplateRewardInfo ~ err:', err)
          resolve({})
        })
    })
  }

  getTemplateProductInfo(aid: string | number) {
    return new Promise((resolve) => {
      const cgi = focusCore.hasAccount ? cgis.getTemplateProductInfo : cgis.getTemplateProductInfoNoAuth
      focusCore
        .request(cgi, { activityId: aid })
        .then((res: TTemplateData) => {
          console.log('🚀 ~ TemplateService ~ getTemplateProductInfo ~ res:', res)
          const { rewardRule = {} } = res
          const { rewardList = [], templateSubType = '', productTypeDesc = '1', ruleType } = rewardRule
          console.log('🚀 ~ TemplateService ~ getTemplateProductInfo ~ rewardList:', rewardList)
          console.log('🚀 ~ TemplateService ~ getTemplateProductInfo ~ templateSubType:', templateSubType)

          let prodCardList = rewardList.map((item) => {
            const btnTexts: any = {
              '01': '去买入',
              '02': '已售罄',
              '03': '已截售',
            }

            return {
              ...item,
              productList:
                (item.productList &&
                  item.productList.map((i) => {
                    return {
                      ...i,
                      rateValue:
                        i.rateValue && i.rateValue.indexOf('%') > -1 ? i.rateValue.replace('%', '') : i.rateValue,
                      btnText: i.saleStatus ? btnTexts[i.saleStatus] : '去买入',
                    }
                  })) ||
                [],
            }
          })

          resolve({
            prodCardList,
            templateSubType,
            productTypeDesc,
            ruleType,
          })
        })
        .catch((err: any) => {
          resolve({
            rateStatisicsDate: '2022-10-10',
            codeDatas: [
              {
                icon: '111',
                prodCode: '1111',
                prodName: '名字啦啦啦',
                rate: '2.78%',
                rateText: '历史年化收益率2.78%',
                btnText: '去买入',
                isActEnd: true,
                isSaleOut: true,
                days: '7天',
                setupTime: '2022-10-10',
                saleTip: '超短期限！！',
              },
              {
                icon: '111',
                prodCode: '1111',
                prodName: '名字啦啦啦',
                rate: '2.78%',
                rateText: '历史年化收益率2.78%',
                btnText: '去买入',
                isActEnd: true,
                days: '7天',
                setupTime: '2022-10-10',
                saleTip: '超短期限！！',
              },
              {
                icon: '111',
                prodCode: '1111',
                prodName: '名字啦啦啦',
                rate: '2.78%',
                rateText: '历史年化收益率2.78%',
                btnText: '去买入',
                isActEnd: false,
                days: '7天',
                setupTime: '2022-10-10',
                saleTip: '超短期限！！',
              },
              {
                icon: '111',
                prodCode: '1111',
                prodName: '名字啦啦啦',
                rate: '2.78%',
                rateText: '历史年化收益率2.78%',
                btnText: '去买入',
                isActEnd: false,
                days: '7天',
                setupTime: '2022-10-10',
                saleTip: '超短期限！！',
              },
              {
                icon: '111',
                prodCode: '1111',
                prodName: '名字啦啦啦',
                rate: '2.78%',
                rateText: '历史年化收益率2.78%',
                btnText: '去买入',
                isActEnd: true,
                days: '7天',
                setupTime: '2022-10-10',
                saleTip: '超短期限！！',
              },
              {
                icon: '111',
                prodCode: '1111',
                prodName: '名字啦啦啦',
                rate: '2.78%',
                rateText: '历史年化收益率2.78%',
                btnText: '去买入',
                isActEnd: true,
                days: '7天',
                setupTime: '2022-10-10',
                saleTip: '超短期限！！',
              },
              {
                icon: '111',
                prodCode: '1111',
                prodName: '名字啦啦啦',
                rate: '2.78%',
                rateText: '历史年化收益率2.78%',
                btnText: '去买入',
                isActEnd: true,
                days: '7天',
                setupTime: '2022-10-10',
                saleTip: '超短期限！！',
              },
            ],
          })
        })
    })
  }
}
export default new TemplateService()
