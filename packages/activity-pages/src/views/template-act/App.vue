<script setup lang="ts">
import { focusCore, focusStore, focusServices, Mask, focusUtils } from '@focus/render'
</script>

<template>
  <router-view v-slot="{ Component, route }">
    <keep-alive exclude="photoDetail,uploadPhoto">
      <component :is="Component" :key="route.meta.usePathKey ? route.path : undefined" />
    </keep-alive>
  </router-view>
</template>

<style lang="scss">
@import '../../styles/normalize.scss';
#app {
  width: 100%;
  min-height: 100vh;
  text-align: center;
  overflow-x: hidden;
  line-height: 0;
  font-size: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  overflow: hidden;
}
</style>
