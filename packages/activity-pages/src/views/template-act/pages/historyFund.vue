<template>
  <div class="history-fund">
    <div class="topbar">
      <p>产品信息</p>

      <div class="info">
        <p>
          累计净申购总金额: <span>{{ totalMoney }}元</span>
        </p>
        <p class="sm">净申购金额=申购金额-赎回金额</p>
      </div>
    </div>
    <div class="list">
      <div class="item" v-for="item in listData" :key="item.id">
        <div class="top">
          <p class="name">{{ item.name }}</p>
          <p class="money">{{ item.money }}元</p>
        </div>
        <div class="bottom">
          <p class="time">{{ item.time }}</p>
        </div>
      </div>
    </div>
    <div class="date">数据更新至：{{ updateTime }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const props = defineProps<{}>()

const totalMoney = ref(0)

const listData = ref<
  {
    id: number
    name: string
    money: number
    time: string
  }[]
>([
  {
    id: 1,
    name: '产品1',
    money: 1000,
    time: '2027-23-22',
  },
  {
    id: 2,
    name: '产品2',
    money: 2000,
    time: '2027-23-22',
  },
  {
    id: 3,
    name: '产品3',
    money: 3000,
    time: '2027-23-22',
  },
])

const updateTime = ref('022027-23-22-3')
</script>

<style scoped lang="scss">
.history-fund {
  background-color: #fff;
  min-height: 100vh;
  width: 100%;
  .topbar {
    padding: 22px 32px;
    background-color: #f5f5f5;
    width: 100%;
    height: 112px;
    box-sizing: border-box;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    font-size: 28px;
    color: #808bab;
    line-height: 42px;
    .info {
      text-align: right;
      span {
        color: #f29655;
      }
      .sm {
        margin-top: 4px;
        font-size: 20px;
      }
    }
  }
  .date {
    font-size: 20px;
    color: #b4bacc;
    letter-spacing: 0;
    line-height: 20px;
    margin-top: 80px;
  }
  .list {
    width: 100%;
    padding: 0 32px;
    box-sizing: border-box;
    .item {
      width: 100%;
      height: 180px;
      box-sizing: border-box;
      border-bottom: 1px solid #f0f0f0;
      font-size: 20px;
      color: #333;
      font-size: 28px;
      color: #405080;
      line-height: 42px;
      padding: 40px 0;
      .top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .money {
          color: #f29655;
        }
      }
      .bottom {
        font-size: 20px;
        color: #405080;
        line-height: 30px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-top: 22px;
      }
    }
  }
}
</style>
