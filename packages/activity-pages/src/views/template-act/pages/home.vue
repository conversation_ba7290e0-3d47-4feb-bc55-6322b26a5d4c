<template>
  <skeleton :show="showSkelenton"></skeleton>
  <div class="wrap type1">
    <div class="top-area">
      <img class="top-banner" :src="topBanner" />
      <div class="btn-rule" @click="clickRule">规则</div>
    </div>

    <!-- 规则类型：SINGLE_BUY-单次购买, CUMULATIVE_BUY-累计购买, AIP-基金定投 -->

    <!-- 净申购/申购 -->
    <div class="act-data">
      <div class="left">
        <p class="title">累计{{ rewrdData.typeText }}</p>
        <p class="money">{{ rewrdData.money }}</p>
      </div>
      <div class="right">
        <p class="title">预估积分</p>
        <p class="point" :class="{ zero: rewrdData.point === 0 }">{{ rewrdData.point }}</p>
      </div>
      <p class="is-signed" v-show="rewrdData.isSigned">已报名</p>

      <div class="btn-tip" @click="clickTip">
        <div class="tip-cover" @click.stop="clickCover" @touchmove.prevent v-if="showRewardTip"></div>
        <div class="tip-content" v-if="showRewardTip">需满足2025-07013前赎回无奖励 {{ showRewardTip }}</div>
      </div>
    </div>

    <!-- 导航锚点 -->
    <anchor-nav></anchor-nav>

    <!-- 产品卡片 -->
    <prodcode-cards
      :codeDatas="item.productList"
      :rateStatisicsDate="productData.rateStatisicsDate"
      v-for="(item, index) in productData.prodCardList"
      :key="index"
      :id="`product_${index}`"
    ></prodcode-cards>

    <div class="banner-card" v-for="(item, index) in bannerCardList" :key="index" @click="clickBannerCard(item)">
      <img :src="item.imgUrl" alt="" />
    </div>

    <!-- 积分兑好礼 -->
    <div class="static-card point-card">
      <p class="title">积分兑好礼</p>
      <div class="list">
        <div class="item">
          <img src="../imgs/goods-wechat.png" alt="" />
          <p>微信立减金</p>
          <number-space-around-text :text="'500积分'" class="sub"></number-space-around-text>
        </div>
        <div class="item">
          <img src="../imgs/goods-phone-bill.png" alt="" />
          <number-space-around-text :text="'100元话费'"></number-space-around-text>

          <number-space-around-text :text="'10000积分'" class="sub"></number-space-around-text>
        </div>
        <div class="item">
          <img src="../imgs/goods-jd.png" alt="" />
          <number-space-around-text :text="'100元京东卡'"></number-space-around-text>

          <number-space-around-text :text="'10000积分'" class="sub"></number-space-around-text>
        </div>
      </div>
      <div class="btn-link" @click="clickPointLink">更多礼品兑换</div>
    </div>

    <!-- 选择微众银行 -->
    <div class="static-card about-us">
      <p class="title">为什么选择微众银行</p>
      <div class="list">
        <div class="item">
          <img class="icon" src="../imgs/aboutus-leading.png" alt="" />
          <p>全球领先</p>
          <p class="sub">《亚洲银行家》评选</p>
          <p class="sub">超4亿客户合作机构</p>
          <BtnTipCover
            class="btn-tip"
            :iconColor="uiStyle.tipColor"
            :tipText="'*来源自THE ASIAN BANKER（亚洲银行家）2022-2023年度国际卓越零售金融服务奖项'"
            :boxWidth="520"
            :contentLeftTranslate="220"
          ></BtnTipCover>
        </div>
        <div class="item">
          <img class="icon" src="../imgs/aboutus-top100.png" alt="" />
          <p>百强品牌</p>
          <p class="sub">连续六年入选</p>
          <p class="sub">中国银行业100强</p>
        </div>
        <div class="item">
          <img class="icon" src="../imgs/aboutus-customers.png" alt="" />
          <p>亿万客户</p>
          <p class="sub">超4亿客户合作机构</p>
          <p class="sub">资产管理规模超万亿元</p>
        </div>
      </div>
      <div class="btn-link" @click="clickAbouteUs">了解微众银行</div>
    </div>

    <!-- 理财小知识 -->
    <div class="card-xiaozhishi">
      <p v-show="!hasFundProd">
        <span>【理财小知识】</span
        >银行理财子公司的产品基本采用净值化管理，投资端更透明，客户也能清晰地了解产品投资的真实涨跌。
      </p>

      <p v-show="hasFundProd">
        <span>【理财小知识】</span
        >鸡蛋不能放在一个篮子里，购买基金需要建立多元投资组合，利用不同资产间的风险差异，起到分散和降低风险的作用。
      </p>
    </div>

    <div class="static-card companys">
      <p class="title">合作银行理财子公司达25家</p>
      <div class="bank-list">
        <div class="item" v-for="(item, index) in bankList" :key="index">
          <img :src="item.bankIcon" alt="" />
          <p>{{ item.bankName }}</p>
        </div>
      </div>
      <div class="btn-link">了解详情</div>
    </div>

    <!-- 风险提示 -->
    <div class="warning-text">
      <p class="title">风险提示：</p>
      <p v-show="hasWealthProd">
        &nbsp;&nbsp;&nbsp;&nbsp;理财产品由管理人发行与管理，微众银行不承担产品的投资、兑付和风险管理责任，过往业绩不代表其未来表现、不构成对收益的承诺，理财非存款、产品有风险、投资须谨慎。
      </p>
      <p v-show="hasFundProd">
        &nbsp;&nbsp;&nbsp;&nbsp;基金有风险，投资需谨慎。基金产品由基金管理人发行与管理，微众银行股份有限公司作为代销机构不承担产品的投资、兑付责任。基金管理人承诺以诚实信用、勤勉尽责的原则管理和运用基金资产，但不保证一定盈利，也不保证最低收益，基金的过往业绩及其净值高低并不预示其未来业绩表现。您在做出投资决策之前，请仔细阅读基金合同、基金招募说明书和基金产品资料概要等产品法律文件和风险揭示书，充分认识基金产品的风险收益特征和产品特性，并根据自身的投资目的、投资期限、投资经验、资产状况等因素，充分考虑自身的风险承受能力，在了解产品情况及销售适当性意见的基础上，理性判断并谨慎做出投资决策。
      </p>
    </div>

    <!-- 活动编号 -->
    <div class="active-number">
      <p>活动编号：{{ actNumber }}</p>
    </div>

    <img
      class="logo"
      v-show="pageUi.logoColor"
      src="https://dbd.webankwealthcdn.net/wm-resm/hjupload/common/logo/logo-color.png"
      alt=""
    />
    <img
      class="logo"
      v-show="pageUi.logoWhite"
      src="https://dbd.webankwealthcdn.net/wm-resm/hjupload/common/logo/logo-white.png"
      alt=""
    />

    <focus-render :hideWxGuest="true" @onLoginSucc="afterLogin" @onFocusConfigSucc="setFocus"></focus-render>
    <dialog-mask :show="showRule" :canScroll="true">
      <template #dialog_contain>
        <div class="rule-dialog">
          <div class="btn-close" @click="showRule = false">关闭</div>
          <div class="dialog-header">
            <p class="title">活动规则</p>
          </div>
          <div class="dialog-body">
            <div class="dialog-body-content" v-html="ruleHtml"></div>
          </div>
        </div>
      </template>
    </dialog-mask>
  </div>
</template>

<script setup lang="ts">
import { focusCore, focusStore, focusServices, Mask, focusUtils, DialogMask } from '@focus/render'
import { computed, reactive, ref } from 'vue'
import prodcodeCards from '../components/ProdcodeCard.vue'
import AnchorNav from '../components/AnchorNav.vue'
import NumberSpaceAroundText from '../components/NumberSpaceAroundText.vue'
import templateService from '../service/service'
import Skeleton from '../components/Skeleton.vue'
import BtnTipCover from '../components/BtnTipCover.vue'
const { jumpService, activityService } = focusServices
const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { activityId = 0, activityUIType = '1' } = locationHrefPared.query
const { modalStore } = focusStore.stores
const isAddingAid = ref(false)

const showRule = ref(false)
const showSkelenton = ref(true)
const bannerCardList = ref([
  {
    imgUrl: '',
    clickEvent: {},
  },
])
const pageUi = reactive({
  logoColor: true,
  logoWhite: false,
})
// 预估积分的小弹窗
const showRewardTip = ref(false)
// 活动规则
const ruleText = ref('')
// 活动编号
const actNumber = ref('')
const hasWealthProd = ref(false)
const hasFundProd = ref(false)
const uiStyle = reactive({
  tipColor: '#FFB852',
})

const ruleHtml = computed(() => {
  const strs = ruleText.value.split('\n')
  strs.forEach((item, index) => {
    if (item.indexOf('<br/>') > -1) {
      strs[index] = '<br/>'
    }
    strs[index] = `<p>${item}</p>`

    if (item.indexOf('<b>') > -1) {
      strs[index] = item.replace('<b>', '<span class="bold">').replace('</b>', '</span>')
    }
  })
  return strs.join('')
})

const userStatus = reactive({
  hasAccount: false,
  isBoosted: false,
})

const topBanner = ref('')

const bankList = [
  { bankName: '兴银理财', bankIcon: require('../imgs/icon-banks/兴银理财.png') },
  { bankName: '光大理财', bankIcon: require('../imgs/icon-banks/光大理财.png') },
  { bankName: '交银理财', bankIcon: require('../imgs/icon-banks/交银理财.png') },
  { bankName: '平安理财', bankIcon: require('../imgs/icon-banks/平安理财.png') },
  { bankName: '信银理财', bankIcon: require('../imgs/icon-banks/信银理财.png') },
  { bankName: '中银理财', bankIcon: require('../imgs/icon-banks/中银理财.png') },
  { bankName: '青银理财', bankIcon: require('../imgs/icon-banks/青银理财.png') },
  { bankName: '华夏理财', bankIcon: require('../imgs/icon-banks/华夏理财.png') },
  { bankName: '南银理财', bankIcon: require('../imgs/icon-banks/南银理财.png') },
  { bankName: '苏银理财', bankIcon: require('../imgs/icon-banks/苏银理财.png') },
  { bankName: '杭银理财', bankIcon: require('../imgs/icon-banks/杭银理财.png') },
  { bankName: '渝农商理财', bankIcon: require('../imgs/icon-banks/渝农商理财.png') },
  { bankName: '招银理财', bankIcon: require('../imgs/icon-banks/招银理财.png') },
  { bankName: '宁银理财', bankIcon: require('../imgs/icon-banks/宁银理财.png') },
  { bankName: '徽银理财', bankIcon: require('../imgs/icon-banks/徽银理财.png') },
  { bankName: '中邮理财', bankIcon: require('../imgs/icon-banks/中邮理财.png') },
  { bankName: '农银理财', bankIcon: require('../imgs/icon-banks/农银理财.png') },
  { bankName: '民生理财', bankIcon: require('../imgs/icon-banks/民生理财.png') },
  { bankName: '浦银理财', bankIcon: require('../imgs/icon-banks/浦银理财.png') },
  { bankName: '广银理财', bankIcon: require('../imgs/icon-banks/广银理财.png') },
  { bankName: '渤银理财', bankIcon: require('../imgs/icon-banks/渤银理财.png') },
  { bankName: '上银理财', bankIcon: require('../imgs/icon-banks/上银理财.png') },
  { bankName: '北银理财', bankIcon: require('../imgs/icon-banks/北银理财.png') },
  { bankName: '恒丰理财', bankIcon: require('../imgs/icon-banks/恒丰理财.png') },
  { bankName: '工银理财', bankIcon: require('../imgs/icon-banks/工银理财.png') },
]

const rewrdData = reactive({
  typeText: '',
  money: '',
  point: 0,
  isSigned: false,
})

const productData = reactive({
  prodCardList: [],
  rateStatisicsDate: '',
})

function clickRule() {
  console.log('1111')
  showRule.value = true
}

function clickCover(e) {
  console.log('🚀 ~ clickCover ~ e:', e)
  console.log('🚀 ~ clickCover ~ showRewardTip.value :', showRewardTip.value)
  showRewardTip.value = false
  console.log('🚀 ~ clickCover ~ showRewardTip.value :', showRewardTip.value)
}

function clickTip() {
  showRewardTip.value = true
}

function init() {
  if (!activityId) {
    modalStore.errorMaskContrl('noactivityId')
    return
  }
  if (isAddingAid.value) {
    return
  }
  modalStore.loadingStart('init')
  activityService.checkWrInfoStatus(activityId).then((res) => {
    isAddingAid.value = !!res
  })

  // 并行处理接口
  Promise.all([
    activityService.checkAidsIsReady(activityId),
    templateService.getTemplateInfo(activityId),
    templateService.getTemplateRewardInfo(activityId),
    templateService.getTemplateProductInfo(activityId),
  ])
    .then((res) => {
      console.log('🚀 ~ init ~ res:', res)
      const [checkactivityIdsIsReady, templateInfo = {}, rewardInfo, productInfo] = res
      console.log('🚀 ~ init ~ templateInfo:', templateInfo)
      console.log('🚀 ~ init ~ productInfo:', productInfo)
      if (checkactivityIdsIsReady.status === 1) {
        modalStore.errorMaskContrl('activityNotReady')
        return
      }

      if (templateInfo.pageBg) {
        document.querySelector('body')?.setAttribute('style', `background: ${templateInfo.pageBg};`)
      }

      bannerCardList.value = templateInfo.bannersInfo || []
      ruleText.value = templateInfo.ruleText || ''
      topBanner.value = templateInfo.topBanner || ''
      actNumber.value = templateInfo.actNumber || ''
      if (rewardInfo.status === 1) {
        rewrdData.typeText = rewardInfo.typeText
        rewrdData.money = rewardInfo.money
        rewrdData.point = rewardInfo.point
        rewrdData.isSigned = rewardInfo.isSigned
        userStatus.isBoosted = rewardInfo.isBoosted
      }
      productData.prodCardList = productInfo.prodCardList
      productData.rateStatisicsDate = productInfo.rateStatisicsDate

      hasWealthProd.value = productInfo.productTypeDesc !== '2'
      hasFundProd.value = productInfo.productTypeDesc !== '1'

      console.log('🚀 ~ init ~ productData.codeDatas:', productData.codeDatas)
    })
    .catch((err) => {
      console.log('🚀 ~ init ~ promise all err:', err)
      // modalStore.errorMaskContrl('initError')
    })
    .finally(() => {
      console.log('finally.....')
      modalStore.loadingEnd('init')
      showSkelenton.value = false
    })
}

function afterLogin(logindata: any) {
  console.log('🚀 ~ afterLogin ~ afterLogin:', logindata)
  console.log('afterLogin')
  userStatus.hasAccount = logindata.hasAccount
  init()
}
function setFocus(focusCtrl: any) {
  console.log('🚀 ~ setFocus ~ setFocus:', focusCtrl)
  console.log('setFocus')
  if (window.hjCoreIab && window.hjCoreIab.setNavBar) {
    window.hjCoreIab.setNavBar({
      useAutoFixNavBar: true,
      customerServiceQuestion: '测试customerServiceQuestion',
      rightButtonConfig: {
        type: 'share',
        shareConfig: {},
      },
    })
  }
}

function clickBannerSinge() {
  console.log('clickBannerSinge')
  jumpService.jump(banner1.clickEvent)
}

function clickPointLink() {
  jumpService.jumpNewWebview('https://m.test.webank.com/s/hj/focus2/client/index.html?fid=168')
}

function clickAbouteUs() {
  jumpService.jump({
    path: 'https://m.test.webank.com/s/hj/focus2/client/index.html?fid=168',
  })
}
</script>

<style lang="scss" scoped>
.type1 {
  --text-color-1: #405080;
  --text-color-2: #b4bacc;
  --text-color-3: #808bab;
  // 产品卡片颜色
  --prodcard-color-1: #405080;
  --prodcard-color-2: #b4bacc;
  --prodcard-color-3: #b0818d;
  --prodcard-color-4: #f24c3d;
  // 奖励卡颜色
  --reward-color-1: #9b431e;
  --reward-color-2: #c6a391;
  --reward-color-3: #f6d0c1;
  --reward-color-4: #f24c3d;
}

.type2 {
  --text-color-1: #61041b;
  --text-color-2: #c6afb4;
  --text-color-3: #b0818d;
  // 产品卡片颜色
  --prodcard-color-1: #61041b;
  --prodcard-color-2: #c6afb4;
  --prodcard-color-3: #b0818d;
  --prodcard-color-4: #f24c3d;
  // 奖励卡颜色
  --reward-color-1: #9b431e;
  --reward-color-2: #c6a391;
  --reward-color-3: #f6d0c1;
  --reward-color-4: #f24c3d;
}
.banner-card {
  width: 706px;
  height: 140px;
  margin: 0 auto;
  margin-top: 24px;
  background-color: #eee;
  border-radius: 16px;
  overflow: hidden;
  img {
    width: 100%;
    height: auto;
  }
}

.wrap {
  width: 100%;
  margin: 0 auto;
  height: auto;
}

.top-area {
  background-color: #eee;
  width: 100%;
  height: 300px;
  position: relative;
  .btn-rule {
    width: 33px;
    height: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 0;
    bottom: 100px;
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px 0 0 10px;
    font-size: 20px;
    color: #61041b;
    letter-spacing: 0;
    line-height: 22px;
    cursor: pointer;
  }
}

.rule-dialog {
  width: 702px;
  box-sizing: border-box;
  max-height: 80vh;
  background-color: #fdfdfd;
  border-radius: 16px;
  position: relative;
  font-size: 24px;
  color: #61041b;
  letter-spacing: 0;
  :deep(span.bold) {
    font-weight: bold;
  }
  .btn-close {
    width: 60px;
    height: 60px;
    position: absolute;
    right: 0;
    top: -100px;
    background-color: red;
  }
  .dialog-header {
    font-size: 32px;
    color: #61041b;
    letter-spacing: 0;
    line-height: 48px;
    padding-bottom: 20px;
    font-weight: bold;
    text-align: center;
    padding-top: 32px;
  }
  .dialog-body {
    height: calc(100% - 100px);
    overflow-y: auto;
    box-sizing: border-box;
    padding: 0 17px;
    box-sizing: border-box;
    padding-bottom: 32px;
  }
}

.static-card {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 30px;
  padding: 32px 16px;

  box-sizing: border-box;
  p.title {
    color: #4b5985;
    font-size: 28px;
    font-weight: 500;
    line-height: 32px; /* 114.286% */
    letter-spacing: 3.2px;
    padding-bottom: 24px;
  }
  .list {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
    .item {
      width: 215px;
      border-radius: 20px;
      background-color: #fff;
      color: var(--text-color-1);
      font-size: 24px;
      font-weight: 500;
      line-height: 1.5;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      padding: 20px 0;
      position: relative;
      .btn-tip {
        position: absolute;
        right: 15px;
        top: 15px;
      }
      .sub {
        font-size: 20px;
        font-weight: 400;
        word-spacing: 2px;
      }
    }
  }
  .btn-link {
    width: 100%;
    height: 24;
    line-height: 24px;
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3566ff;
    font-size: 20px;
    font-weight: 400;
    line-height: 24px; /* 120% */
    cursor: pointer;
    &:active {
      opacity: 0.7;
    }
    &::after {
      content: '';
      display: inline-block;
      width: 24px;
      height: 24px;
      background: url('../imgs/icon-arrow-down.png') no-repeat;
      background-size: 100% 100%;
      transform: rotate(-90deg);
      transform-origin: center center;
    }
  }
}

.point-card {
  width: 702px;
  height: 355px;
  cursor: pointer;
  margin: 0 auto;
  margin-top: 32px;
  img {
    width: 117px;
    height: 75px;
    margin-bottom: 12px;
  }
}

.about-us {
  width: 702px;
  height: 355px;
  cursor: pointer;
  margin: 0 auto;
  margin-top: 32px;
  img {
    width: 44px;
    height: 44px;
    margin-bottom: 12px;
  }
  .item {
    padding-top: 24px;
  }
}

.companys {
  width: 702px;
  height: 778px;
  cursor: pointer;
  margin: 0 auto;
  margin-top: 32px;
  padding: 32px;
  .bank-list {
    display: grid;
    grid-template-columns: repeat(3, 33.33%);
    grid-template-rows: repeat(3, 52px);
    grid-row-gap: 16px;
    .item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-size: 24px;
      line-height: 36px;
      color: var(--text-color-1);
      img {
        width: 52px;
        height: 52px;
        margin-right: 16px;
      }
    }
  }
}

.card-xiaozhishi {
  width: 702px;
  margin: 0 auto;
  margin-top: 32px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 20px;
  position: relative;
  &::after {
    content: '';
    display: inline-block;
    width: 114px;
    height: 75px;
    background: url('../imgs/icon-complete-study.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 0;
    bottom: 0;
  }
  p {
    font-size: 24px;
    color: var(--text-color-1);
    letter-spacing: 0;
    line-height: 36px;
    text-align: left;
    z-index: 2;
    span {
      color: #f24c3d;
      font-weight: bold;
    }
  }
}

.warning-text {
  width: 702px;
  margin: 0 auto;
  margin-top: 32px;
  box-sizing: border-box;
  font-size: 24px;
  color: var(--text-color-1);
  letter-spacing: 0;
  line-height: 36px;
  text-align: left;

  .title {
    font-weight: bold;
    margin-bottom: 16px;
  }
}
.logo {
  width: 100%;
  height: 232px;
}

.act-data {
  width: 702px;
  background: #fff;
  border-radius: 20px;
  .btn-tip {
    width: 20px;
    height: 20px;
    background-color: red;
    cursor: pointer;
    position: relative;
    margin: 0 auto;
    .tip-cover {
      position: fixed;
      left: 0;
      top: 0;
      z-index: 22222;
      right: 0;
      bottom: 0;
    }
    .tip-content {
      width: 459px;
      height: 128px;
      background-color: #fff;
      border-radius: 16px;
      font-size: 24px;
      color: #61041b;
      letter-spacing: 0;
      line-height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      left: 50%;
      top: 100%;
      transform: translateX(-50%);
      z-index: 222;
      box-shadow: 0 0 0 1000px rgba(0, 0, 0, 0.4);
    }
  }
}
.active-number {
  margin-top: 48px;
  color: var(--text-color-1);
  font-size: 20px;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
  position: relative;
  padding: 0 32px;
  &:after,
  &::before {
    content: '';
    width: 242px;
    height: 1px;
    background-color: var(--text-color-3);
    opacity: 0.3;
  }
  &::before {
    margin-right: 18px;
    left: 0;
  }
  &::after {
    margin-left: 18px;
  }
  p {
    flex: none;
    width: fit-content;
  }
}
</style>
