<template>
  <div class="history-wealth">
    <div class="topbar">
      <p>已连续定投达标产品数</p>
      <p class="count">{{ countNums }}</p>
    </div>
    <div class="list">
      <div class="head">
        <p>产品信息</p>
        <p>连续定投达标期数</p>
      </div>
      <div class="item" v-for="item in listData" :key="item.id">
        <div class="top">
          <p class="name">{{ item.name }}</p>
        </div>
        <div class="bottom">
          <p>{{ item.typeText }}</p>
          <p v-if="!item.isDone">{{ item.times }}期</p>
          <p v-if="item.isDone">已达标</p>
        </div>
      </div>
    </div>
    <div class="date">数据更新至：{{ updateTime }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const props = defineProps<{}>()

const totalMoney = ref(0)

const listData = ref<
  {
    id: number
    name: string
    times: number
    typeText: string
    isDone?: boolean
  }[]
>([
  {
    id: 1,
    name: '产品1',
    times: 1,
    typeText: '每周投',
    isDone: true,
  },
  {
    id: 2,
    name: '产品2',
    times: 1,
    typeText: '每双周投',
  },
  {
    id: 3,
    name: '产品3',
    times: 1,
    typeText: '每双周投',
  },
])

const countNums = ref(0)

const updateTime = ref('022027-23-22-3')
</script>

<style scoped lang="scss">
.history-wealth {
  background-color: #fff;
  min-height: 100vh;
  width: 100%;
  .topbar {
    padding: 20px 32px 10px 32px;
    background-color: #f5f5f5;
    width: 100%;
    height: 72px;
    box-sizing: border-box;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    font-size: 28px;
    color: #808bab;
    line-height: 42px;
    .count {
      color: #f29655;
      font-weight: bold;
    }
  }
  .date {
    font-size: 20px;
    color: #b4bacc;
    letter-spacing: 0;
    line-height: 20px;
    margin-top: 80px;
  }
  .list {
    width: 100%;
    padding: 0 32px;
    box-sizing: border-box;
    .head {
      display: flex;
      height: 80px;
      box-sizing: border-box;
      align-items: center;
      justify-content: space-between;
      font-size: 24px;
      color: #808bab;
      line-height: 36px;
      border-bottom: 1px solid #f0f0f0;
    }
    .item {
      width: 100%;
      box-sizing: border-box;
      border-bottom: 1px solid #f0f0f0;
      font-size: 20px;
      color: #333;
      font-size: 28px;
      color: #405080;
      line-height: 42px;
      padding: 40px 0;
      .top {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .bottom {
        font-size: 28px;
        color: #f29655;
        line-height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 14px;
      }
    }
  }
}
</style>
