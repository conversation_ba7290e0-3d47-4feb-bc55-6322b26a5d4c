<template>
  <div class="prodcode-cards">
    <div class="prod-list">
      <div class="rule-card">
        <div class="left">
          <p class="title">
            {{ ruleData.costTitle }} <span>{{ ruleData.costMoney }}</span
            >元
          </p>
          <p class="desc">{{ ruleData.desc }}</p>
        </div>
        <div class="right">
          <p>奖{{ ruleData.rewardPoint }}<span>积分</span></p>
        </div>
      </div>

      <div
        class="item"
        v-for="(item, index) in showData"
        :key="index"
        @click="jumpCode(item)"
        :class="{ 'is-end': item.isActEnd }"
      >
        <div class="title">
          <img :src="item.bankLogoUrl" alt="" class="icon" v-show="item.bankLogoUrl" />
          <p class="name">{{ item.productName }}</p>
        </div>
        <div class="middle-info">
          <p class="rate">{{ item.rateValue }} <span>%</span></p>
          <p class="days" v-if="item.days">{{ item.productPeriod }}天</p>
          <div
            class="btn"
            :class="{ 'is-saleout': item.isSaleOut }"
            @click="jumpCode(item)"
            v-focuslog.click="{
              definedValue: `fundCoupon-btn-go`,
              definedInfo: {
                prodCode: item.productCode,
              },
            }"
          >
            {{ item.btnText }}
          </div>
        </div>
        <div class="bottom-info">
          <p>{{ item.rateDesc }}</p>
          <p>{{ item.rateText || '再买xx元奖励xx积分' }}</p>
          <p>{{ item.establishDate }}成立</p>
        </div>
        <div class="sale-tip">{{ item.recommendation }}</div>
      </div>
    </div>
    <div class="btn-more" @click="isShowMore = !isShowMore" :class="{ 'is-showmore': isShowMore }">
      {{ isShowMore ? '收起' : '更多指定产品' }}
    </div>
  </div>
  <p class="rate-date">收益率统计至：{{ rateStatisicsDate }}</p>
</template>

<script setup lang="ts">
import { focusServices } from '@focus/render'
import { computed, ref } from 'vue'
import type { TProductCardItem } from '../service/service'
const { jumpService } = focusServices

const props = defineProps<{
  ruleData: {
    type: string
    costTitle: string
    costMoney: string
    desc: string
    rewardPoint: string
  }
  codeDatas: Array<
    TProductCardItem & {
      btnText: string
      isActEnd: boolean
      days: string
      saleTip: string
      setupTime: string
      isSaleOut: boolean
    }
  >
  rateStatisicsDate: string
}>()

const isShowMore = ref(false)

const ruleData = computed(() => {
  return (
    props.ruleData || {
      type: '1',
      costTitle: '以下产品累计新买入满',
      costMoney: '10000',
      desc: '需活动期间新买入且持有至活动结束',
      rewardPoint: '333',
    }
  )
})

const rateStatisicsDate = computed(() => {
  return props.rateStatisicsDate || ''
})

const showData = computed(() => {
  const datas = props.codeDatas || []
  console.log('🚀  ~ prod datas:', datas)
  return isShowMore.value ? datas : datas.slice(0, 3)
})

function jumpCode(item: any) {
  if (!item.isActEnd) {
    jumpService.jump({
      path: item.prodCode,
    })
  }
}

console.log('.....prodcode')
</script>

<style lang="scss" scoped>
.prodcode-cards {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20px;
  padding: 24px 16px;
  height: auto;
  .rule-card {
    width: 670px;
    margin: 0 auto;
    border-radius: 16px;
    height: 130px;
    background: #ffc9c4;
  }

  .prod-list {
    background-color: #fff;
    border-radius: 20px;
    margin: 0 auto;
    width: 670px;
    margin-top: 10px;
    overflow: hidden;

    .rule-card {
      width: 100%;
      border-radius: 16px;
      background-color: #fae3e2;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 24px;
      line-height: 36px;
      padding: 1px;
      min-height: 166px;
      box-sizing: border-box;
      overflow: hidden;

      .left {
        border-radius: 12px 0 0 12px;
        border-right: 1px dotted rgba(255, 181, 160, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        background: #fdf5f3;
        flex: 1;
        height: 100%;
        position: relative;
        text-align: left;

        &::before,
        &::after {
          content: '';
          position: absolute;
          width: 16px;
          height: 16px;
          border-radius: 100%;
          background-color: transparent;
          border-color: #fae3e2;
          border-style: solid;
          border-width: 1px;
          z-index: 2;
          right: -8px;
        }
        &::before {
          top: -6px;
        }
        &::after {
          bottom: -6px;
        }

        color: #9b431e;

        .title {
          padding-left: 20px;
          font-size: 24px;
          font-weight: 400;
          line-height: 36px; /* 150% */
        }
        .desc {
          padding-left: 20px;
          font-size: 20px;
          line-height: 30px; /* 150% */
          opacity: 0.5;
        }
      }
      .right {
        border-radius: 0 15px 15px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fdf5f3;
        width: 247px;
        height: 100%;
      }
    }

    .item {
      width: 100%;
      box-sizing: border-box;
      padding: 24px;
      position: relative;
      overflow: hidden;
      &.is-end {
        &::before {
          content: '';
          position: absolute;
          right: 0;
          top: 0;
          width: 110px;
          height: 56px;
          background-color: red;
        }
      }
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 622px;
        height: 1px;
        background: #f0f0f0;
      }
      &:last-child {
        &::after {
          display: none;
        }
      }
      .title {
        display: flex;
        align-items: centern;
        justify-content: flex-start;
        width: 100%;
        padding: 0 8px;
        .icon {
          width: 32px;
          height: 32px;
          background: red;
          margin-right: 7px;
        }
        .name {
          font-size: 24px;
          color: var(--prodcard-color-1);
          line-height: 36px;
          font-weight: 600;
          width: 100%;
          text-align: left;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-box-orient: vertical;
        }
      }
      .middle-info {
        padding: 0 8px;
        padding-top: 8px;
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        .rate {
          font-size: 36px;
          color: var(--prodcard-color-4);
          line-height: 36px;
          font-weight: 500;
        }
        .days {
          font-size: 24px;
          color: #61041b;
          line-height: 24px;
        }
        .btn {
          width: 136px;
          height: 54px;
          background: var(--prodcard-color-4);
          border-radius: 32px;
          color: #fff;
          line-height: 54px;
          text-align: center;
          margin-right: 8px;
          font-size: 24px;
          align-self: center;
          &:active {
            opacity: 0.7;
          }
          &.is-saleout {
            opacity: 0.7;
            &:active {
              opacity: 0.7;
            }
          }
        }
      }
      .bottom-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 8px;
        padding-top: 16px;
        p {
          font-size: 20px;
          color: var(--prodcard-color-2);
          line-height: 30px;
        }
      }
      .sale-tip {
        width: 100%;
        height: 44px;
        background: linear-gradient(to right, rgba(255, 243, 239, 1), rgba(255, 243, 239, 0));
        border-radius: 15px;
        font-size: 18px;
        line-height: 18px;
        color: var(--prodcard-color-3);
        text-align: left;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-top: 16px;
        &::before {
          content: ' ';
          display: inline-block;
          width: 21px;
          height: 18px;
          background: url('../imgs/icon-tip.png') no-repeat;
          background-size: 100% 100%;
          margin-right: 5px;
          margin-left: 16px;
        }
      }
    }
  }

  .btn-more {
    width: 100%;
    height: 28px;
    line-height: 28px;
    padding-bottom: 10px;
    text-align: center;
    font-size: 20px;
    color: #456ce6;
    margin-top: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    &::after {
      content: '';
      display: inline-block;
      width: 24px;
      height: 24px;
      background: url('../imgs/icon-arrow-down.png');
      background-size: 100% 100%;
      margin-left: 8px;
    }
    &.is-showmore {
      &::after {
        transform: rotate(180deg);
      }
    }
  }
}
.rate-date {
  font-size: 20px;
  color: #b0818d;
  letter-spacing: 0;
  line-height: 30px;
  margin-top: 32px;
}
</style>
