<template>
  <p class="number-space-around-text" v-html="parsedText"></p>
</template>

<script setup lang="ts">
//   <!-- 将文本中的数字前后添加4px空格 -->

import { computed } from '@vue/reactivity'

const props = defineProps({
  text: {
    type: String,
    default: '',
  },
})

const parsedText = computed(() => {
  // 测试：这里有10000元京东卡，优惠持续30天，要花费100,000积分，获得-1,323,222积分，剩余0.3%的积分；其他无效字符12,11和121,1和0.000

  //   const regex = /([-+]?(?:\d{1,3}(?:,\d{3})*(?:\.\d+)?%?|\d+))/g
  //   const regex = /([-+]?(?:\d{1,3}(?:,\d{3})*(?:\.\d+)?%?|\d+(?!\d)))/g
  //   const regex = /([+-]?\d{1,3}(?:,\d{3})*(?:\.\d+)?%?(?:[eE][-+]?\d+)?)/g
  const regex = /([+-]?(?:\d{1,3}(?:,\d{3})+|\d+)(?:\.\d+)?%?(?:[eE][-+]?\d+)?)/g
  const result = props.text.replace(regex, '<span class="number-text">$1</span>')

  return result
})
</script>

<style lang="scss">
.number-space-around-text {
  span.number-text {
    margin: 0 4px;
  }
}
</style>
