<template>
  <div
    class="skeleton type-1"
    ref="skeletonRef"
    :class="hideSkeleton ? 'hide' : ''"
    v-show="!isRemoveDom"
    @touchmove.prevent
  >
    <div class="box box-1"></div>
    <div class="box box-2">
      <div class="box"></div>
      <div class="box"></div>
      <div class="box"></div>
    </div>
    <div class="box box-3">
      <div class="box wrap">
        <div class="box area-1"></div>
        <div class="box area-2">
          <div class="box area-2-1"></div>
          <div class="box area-2-2">
            <div class="box area-2-2-1"></div>
            <div class="box area-2-2-2"></div>
            <div class="box area-2-2-3"></div>
          </div>
          <div class="box area-2-3"></div>
        </div>
        <div class="box area-2">
          <div class="box area-2-1"></div>
          <div class="box area-2-2">
            <div class="box area-2-2-1"></div>
            <div class="box area-2-2-2"></div>
            <div class="box area-2-2-3"></div>
          </div>
          <div class="box area-2-3"></div>
        </div>
        <div class="box area-2">
          <div class="box area-2-1"></div>
          <div class="box area-2-2">
            <div class="box area-2-2-1"></div>
            <div class="box area-2-2-2"></div>
            <div class="box area-2-2-3"></div>
          </div>
          <div class="box area-2-3"></div>
        </div>
      </div>
    </div>
    <div class="box box-3">
      <div class="box wrap">
        <div class="box area-1"></div>
        <div class="box area-2">
          <div class="box area-2-1"></div>
          <div class="box area-2-2">
            <div class="box area-2-2-1"></div>
            <div class="box area-2-2-2"></div>
            <div class="box area-2-2-3"></div>
          </div>
          <div class="box area-2-3"></div>
        </div>
        <div class="box area-2">
          <div class="box area-2-1"></div>
          <div class="box area-2-2">
            <div class="box area-2-2-1"></div>
            <div class="box area-2-2-2"></div>
            <div class="box area-2-2-3"></div>
          </div>
          <div class="box area-2-3"></div>
        </div>
        <div class="box area-2">
          <div class="box area-2-1"></div>
          <div class="box area-2-2">
            <div class="box area-2-2-1"></div>
            <div class="box area-2-2-2"></div>
            <div class="box area-2-2-3"></div>
          </div>
          <div class="box area-2-3"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const skeletonRef = ref<HTMLDivElement>()
import { computed, watch, watchEffect } from 'vue'
import { ref } from 'vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
})
const hideSkeleton = ref(false)
const isRemoveDom = ref(false)
watchEffect(() => {
  if (!props.show) {
    hideSkeleton.value = true
    setTimeout(() => {
      isRemoveDom.value = true
    }, 300)
  }
})
</script>

<style scoped lang="scss">
:root {
  --color-1: #f5f5f5;
  --color-2: #fff;
  --color-3: #f9f9f9;
  --color-4: #fbfbfb;
}

.skeleton {
  width: 100%;
  min-height: 100vh;
  background-color: var(--color-3);
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 999;
  transition: all 0.3s ease-in-out;
}
.hide {
  opacity: 0;
  visibility: hidden;
}

.type-1 {
  .box {
    position: relative;
    width: 100%;
    background-color: var(--color-1);
    box-sizing: border-box;
  }
  .box-1 {
    height: 192px;
    &::after {
      content: '';
      position: absolute;
      top: 45px;
      right: 0;
      width: 33px;
      height: 64px;
      background-color: var(--color-2);
    }
  }
  .box-2 {
    height: 100px;
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    > .box {
      width: 100%;
      height: 100%;
      flex: 1;
      border-right: 5px solid var(--color-3);
      box-sizing: border-box;
      &:last-child {
        border-right: none;
      }
    }
  }
  .box-3 {
    height: 1069px;
    width: 702px;
    margin: 0 auto;
    background-color: var(--color-2);
    border-radius: 20px;
    box-sizing: border-box;
    padding: 20px 16px;
    margin-bottom: 32px;
    .wrap {
      height: 100%;
      background-color: var(--color-4);
      border-radius: 20px 20px 0 0;
      .area-1 {
        height: 148px;
        background-color: var(--color-1);
        border-radius: 20px;
      }
      .area-2 {
        height: 268px;
        padding: 28px;
        background-color: transparent;
        .area-2-1 {
          height: 36px;
          width: 438px;
          background-color: var(--color-1);
        }
        .area-2-2 {
          height: 54px;
          width: 100%;
          background-color: transparent;
          margin-top: 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .box {
            width: 90px;
            height: 100%;
            &:nth-child(2) {
              width: 90px;
              height: 36px;
            }
            &:nth-child(3) {
              width: 136px;
            }
          }
        }
        .area-2-3 {
          height: 44px;
          width: 100%;
          margin-top: 56px;
          background-color: var(--color-1);
        }
      }
    }
  }
}
</style>
