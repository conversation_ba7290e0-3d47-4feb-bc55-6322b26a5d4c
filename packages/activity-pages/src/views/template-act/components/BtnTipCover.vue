<template>
  <div class="btn-tip" @click="handleClick">
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        d="M9.99902 0C15.5136 1.03477e-06 19.999 4.48764 19.999 10C20.0013 15.5146 15.5136 20 9.99902 20C4.48442 20 -0.000976562 15.5146 -0.000976562 10C-0.000976562 4.48539 4.48666 0 9.99902 0ZM9.99902 0.739258C4.89341 0.739258 0.738281 4.89243 0.738281 9.99805C0.738441 15.1035 4.8935 19.2588 9.99902 19.2588C15.1045 19.2588 19.2596 15.1058 19.2598 9.99805C19.2598 4.89018 15.1069 0.739259 9.99902 0.739258ZM10.1025 8.07715C10.4994 8.02549 10.8986 8.13842 11.207 8.39062C11.4995 8.66989 11.6653 9.05494 11.6699 9.45801C11.6813 9.72831 11.6671 9.99885 11.624 10.2646C11.5764 10.5958 11.5015 10.972 11.3994 11.3955L10.5762 14.6504C10.5421 14.7856 10.5126 14.9212 10.4922 15.0586C10.4763 15.1418 10.4717 15.2273 10.4717 15.3105C10.4785 15.4187 10.5267 15.5178 10.6084 15.5898C10.6855 15.6597 10.7873 15.6965 10.8916 15.6875C11.2001 15.6199 11.463 15.4253 11.624 15.1572C11.9347 14.7361 12.1848 14.2748 12.373 13.7861L12.8564 13.9971C12.5661 14.8396 12.1074 15.6154 11.5107 16.2822C11.0661 16.7709 10.451 17.0726 9.79102 17.1289C9.38729 17.1829 8.97633 17.0789 8.64746 16.8379C8.35056 16.5856 8.17838 16.2164 8.17383 15.8291C8.16249 15.5633 8.17565 15.2948 8.21875 15.0312C8.26864 14.6823 8.34159 14.3357 8.42773 13.9912L9.20605 11.0889C9.26952 10.875 9.31689 10.6562 9.34863 10.4355C9.37353 10.2691 9.38471 10.1004 9.37793 9.93164C9.37339 9.81225 9.33104 9.69685 9.25391 9.60449C9.1904 9.52792 9.09491 9.48808 8.99512 9.49707C8.77272 9.54803 8.70496 9.69671 8.50195 10.1426C8.46274 10.2287 8.41758 10.3259 8.36621 10.4355L7.85645 10.29C7.98824 9.73334 8.11367 9.35828 8.4209 8.92188C8.8542 8.43984 9.45602 8.13797 10.1025 8.07715ZM9.94531 4.00879C10.4171 3.47493 11.2338 3.42344 11.7715 3.88965C12.0369 4.11042 12.198 4.42809 12.2207 4.77051C12.2524 5.11733 12.1392 5.46401 11.9102 5.72754L11.8965 5.74316C11.4247 6.277 10.6083 6.33362 10.0684 5.86523C9.80066 5.63546 9.63924 5.3065 9.62109 4.95508C9.59161 4.61492 9.70545 4.27731 9.93457 4.02051L9.94531 4.00879Z"
        :fill="iconColor"
      />
    </svg>
    <div class="tip-cover" @click.stop="clickCover" @touchmove.prevent v-if="showRewardTip"></div>
    <div class="tip-content" v-if="showRewardTip" :style="contentStyle">{{ tipText }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const props = defineProps({
  tipText: {
    type: String,
    default: '',
  },
  iconColor: {
    type: String,
    default: '#C6AFB4',
  },
  boxWidth: {
    type: Number,
    default: 520,
  },
  contentLeftTranslate: {
    type: Number,
    default: 0,
  },
})

const showRewardTip = ref(false)
const clickCover = () => {
  showRewardTip.value = false
}
function handleClick() {
  showRewardTip.value = true
}

const contentStyle = computed(() => {
  return {
    width: `${(props.boxWidth / 750) * 100}vw`,
    transform: `translateX(-${(props.contentLeftTranslate / props.boxWidth) * 100}%)`,
  }
})
</script>

<style scoped lang="scss">
.btn-tip {
  width: 20px;
  height: 20px;
  cursor: pointer;
  position: relative;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  &::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 20px;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-bottom: 20px solid #fff;
    z-index: 99999;
  }
  svg {
    width: 100%;
    height: 100%;
  }
  .tip-cover {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 999999;
    right: 0;
    bottom: 0;
  }
  .tip-content {
    width: 520px;
    height: 128px;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 16px;
    font-size: 24px;
    color: var(--color-text-3);
    letter-spacing: 0;
    line-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 50%;
    top: 150%;
    z-index: 9999;
    box-shadow: 0 0 0 1000px rgba(0, 0, 0, 0.4);
    font-weight: normal;
  }
}
</style>
