<template>
  <!-- 占位元素，用于防止滚动吸附时页面跳动 -->
  <div v-if="isFixed" class="anchor-placeholder"></div>
  <div ref="anchorRef" class="anchor-nav" :class="{ 'fixed-top': isFixed }">
    <div
      class="nav-item"
      v-for="(item, index) in navList"
      :key="item.id"
      @click="handleClick(item.id, item.targetTop)"
      :class="{ actived: selectedIndex === index }"
    >
      <number-space-around-text
        v-for="text in item.texts"
        :text="text"
        class="nav-item-title"
      ></number-space-around-text>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ref, onMounted, onUnmounted } from 'vue'
import NumberSpaceAroundText from './NumberSpaceAroundText.vue'
const navList = computed(() => {
  return [
    {
      texts: ['111', '222'],
      id: 0,
      targetTop: 100,
    },
    {
      texts: ['333', '444'],
      id: 1,
      targetTop: 400,
    },
    {
      texts: ['333', '444'],
      id: 2,
      targetTop: 800,
    },
  ]
})

const selectedIndex = ref(0)

// 引用组件根元素
const anchorRef = ref<HTMLElement | null>(null)
// 跟踪是否固定定位
const isFixed = ref(false)
// 记录初始偏移量
let initialOffset = 0

// 计算固定定位状态
const handleScroll = () => {
  if (!anchorRef.value) return

  const scrollPosition = window.scrollY
  // 当滚动超过组件初始位置时固定
  isFixed.value = scrollPosition >= initialOffset
}

function handleClick(id: number, targetTop: number) {
  if (selectedIndex.value === id) {
    return
  }
  selectedIndex.value = id
  doAnimateScroll(targetTop)
}

function doAnimateScroll(targetTop: number) {
  // const targetPosition = anchorRef.value.offsetTop
  window.scrollTo({
    top: targetTop,
    behavior: 'smooth',
  })
}

// 挂载时设置初始位置和添加滚动监听
onMounted(() => {
  if (anchorRef.value) {
    initialOffset = anchorRef.value.offsetTop
  }
  window.addEventListener('scroll', handleScroll)
})

// 卸载时移除滚动监听
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped lang="scss">
.anchor-nav {
  display: flex;
  justify-content: space-around;
  transition: all 0.3s ease;
  align-items: center;
  width: 100%;
  height: 100px;
  box-sizing: border-box;
  background: var(----, linear-gradient(0deg, #deefff 0%, #e7f3ff 100%));
}

/* 占位元素样式 */
.anchor-placeholder {
  width: 100%;
  height: 100px;
  box-sizing: border-box;
}

.nav-item {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  .nav-item-title {
    font-size: 22px;
    line-height: 30px;
    letter-spacing: 1px;
    color: #4b5985;
  }
  &.actived {
    background: var(---, linear-gradient(181deg, #bce1ff 0.88%, #def0ff 118.44%));
    .nav-item-title {
      font-weight: 600;
    }
  }
}

/* 固定定位样式 */
.fixed-top {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
</style>
