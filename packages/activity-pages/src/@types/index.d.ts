// import type { Dayjs } from 'dayjs'

enum TAcceptShareFunctionType {
  Family = 'my_family',
}

enum EnumKvDataType {
  FAMILY = 'family',
}
enum EnumFamilyKeys {
  IsInMyFamily = 'isInMyFamily',
}

interface TFocusCoreInitConfig {
  noNeedLogin?: boolean
  focusFid?: number
  focusAid?: number
  ajaxConfig?: any
  commonConfig?: any
}
interface TFocusInitResult {
  focusRenderCtr: any
  loginData: any
  nowTime: Dayjs
}

interface TFocusDataResolve {
  shareConfig: {
    base?: any
    configList: Array<{ desc: string; imgUrl: string; mgmAid: string; path: string; title: string }>
  }
  popupConfig: any[]
  hideModuleIds: number[]
  allModulesData: any[]
  popupConfig: any[]
  hideModuleIds: number[]
  getShareConfigByConfigId: (id: number | string) => TShareConfig | any
}

interface TFocusCore {
  env: TypeEnv
  init(config?: TFocusCoreInitConfig): Promise<TFocusInitResult>
}
interface TFcousData {
  fid: Number
  apiList: Array<any>
  config: Array<any>
  shareConfig: any
  grayList: Array<any>
  serviceConfig: any
  popupConfig: Array<any>
}

interface TShareConfig {
  shareTitle?: string
  shareDesc?: string
  shareImg?: string
  shareUrl?: string
  shareMgmAid?: string
}

interface TAcceptShareParam {
  m1Sid: string
  mgmAid: string
  function_type?: TAcceptShareFunctionType
}

interface TRelationShipItem {
  sid: string
  nick_name: string
  head_img_url: string
  gender: string
  user_name: string
  id: string
  time: string
}

interface Product {
  product_period: string
  sale_status: string
  product_name: string
  product_code: string
  product_type: string
  templet_type: string
  rate_value: string
  show_start_buy_tag: string
  marketing_text: string
  risk_level: string
  extra_info: {
    bank_logo_url: string
    bank_short_name: string
    header_tags: string[]
    tag: string[]
    min_amount_desc: string
    rate_desc: string
    is_bank_finance: string
    min_amount_desc_recommend: string
    static_rate_tips: string
  }
}
