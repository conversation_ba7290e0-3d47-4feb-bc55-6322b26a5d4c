interface TModuleData {
  moduleId: number
  moduleName: string
  moduleType: string
  data: any
  styles: TModuleStyles
  moduleSubType?: string
  children?: TModuleData[]
}

interface TModuleStyles {
  width?: number | string // 100/auto
  height?: number | string // 100/auto
  margin?: string // top,right,bottom,left
  /**
   * postion,top,left
   * 可选值
   * none
   * fixed-t
   * fixed-b
   * absolute,0,0
   */
  position?: string
  borderRadius?: number
  font?: string // size,weight,text-align,color
  bg?: string
  time?: string // off/on
}

interface TModuleStylesResolved {
  boxSize?: any
  bg?: any
  margins?: any
  fontStyles?: any
  fontSize?: any
  position?: any
  borderRadius?: any
}

namespace FocusConditionBox {
  type conditionList = {
    conditionId: number
    moduleIds: number[]
  }[]
}

enum allModuleTypes {
  MImage = 'MImage',
}

type targetModules = {
  MImage: NFocusImage.moduleData
}

type MapTModuleData<T> = TModuleData & targetMmodules[T]

type ObjectFromList<T extends ReadonlyArray<string>, V = string> = {
  [K in T extends ReadonlyArray<infer U> ? U : never]: V
}
/**
 * 具体某个组件的参数
 */

namespace NFocusImage {
  type moduleData = TModuleData & {
    moduleType: 'MImage'
    data: configData
  }

  type configData = {
    imgUrl: string
  }

  export const dynamicKey: ['imgUrl'] = ['imgUrl']
  type dynamicData = ObjectFromList<typeof dynamicKey>
}

namespace NFocusBox {
  type moduleData = TModuleData & {
    moduleType: 'MBox'
    data: configData
  }

  type configData = {
    // imgUrl: string
    flex: string
    boxh: string
  }
}

namespace NFocusText {
  type moduleData = TModuleData & {
    moduleType: 'MText'
    data: configData
  }

  type configData = {
    text: string
  }

  export const dynamicKey: ['text'] = ['text']
  type dynamicData = ObjectFromList<typeof dynamicKey>
}

namespace NFocusBtnMgm {
  type moduleData = TModuleData & {
    moduleType: 'BtnMgm'
    data: configData
  }

  type configData = {
    fromAid: string
    configId: string
    imgUrl2: string
    imgUrl1: string
  }
}
namespace NFocusMgmList {
  type moduleData = TModuleData & {
    moduleType: 'MgmList'
    data: configData
  }

  type configData = {
    activedColor: string
    mgmAid: string
    titleBgColor: string
    titleFont: string
  }
}
namespace NFocusBtnAcceptShare {
  type moduleData = TModuleData & {
    moduleType: 'BtnAcceptShare'
    data: configData
  }

  type configData = {
    activedColor: string
    mgmAid: string
    titleBgColor: string
    titleFont: string
  }
}

namespace NFocusLottery {
  type moduleData = TModuleData & {
    moduleType: 'BtnAcceptShare'
    data: configData
  }

  type configData = {
    configId: string
  }
}

namespace NFocusBtnClick {
  type moduleData = TModuleData & {
    moduleType: 'BtnClick'
    data: configData
  }

  type configData = {
    uiType: string
    imgUrl: string
    ariaLabel: string
    text: string
    clickEvent: any
  }
}
