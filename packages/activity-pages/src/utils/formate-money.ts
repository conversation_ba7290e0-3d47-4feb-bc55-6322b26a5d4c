export function formatAmount(money = '--', withFix2: boolean): string {
  let tempMoney: string | number = money.toString()

  if (tempMoney === '--') return '--'
  let symbol = ''
  if (tempMoney.indexOf('-') > -1) {
    symbol = '-'
    tempMoney = Math.abs(Number(tempMoney))
  }
  if (withFix2) {
    if (tempMoney.toString().indexOf('.') > -1) {
      tempMoney = parseFloat(tempMoney.toString()).toFixed(2)
    } else {
      const numStr = ((Number(tempMoney) * 1000) / 1000).toString()
      tempMoney = parseFloat(parseInt(numStr).toString()).toFixed(2)
    }
  }
  const moneyArr = tempMoney.toString().split('.')
  let moneyLeft = moneyArr[0].toString()
  let result = moneyArr[1] ? '.' + moneyArr[1] : ''
  while (moneyLeft.length > 3) {
    result = ',' + moneyLeft.slice(-3) + result
    moneyLeft = moneyLeft.slice(0, -3)
  }
  return symbol + moneyLeft + result
}
