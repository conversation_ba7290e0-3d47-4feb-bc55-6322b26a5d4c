import { UrlParser } from './url'
import dayjs from './time-utils'
import { formatAmount } from './formate-money'
export function utf16toEntities(str: string): string {
  const patt = /[\ud800-\udbff][\udc00-\udfff]/g // 检测utf16字符正则
  if (!str) return ''
  str = str.replace(patt, (char) => {
    let H
    let L
    let code
    let s

    if (char.length === 2) {
      H = char.charCodeAt(0) // 取出高位
      L = char.charCodeAt(1) // 取出低位
      code = (H - 0xd800) * 0x400 + 0x10000 + L - 0xdc00 // 转换算法
      s = `&#${code};`
    } else {
      s = char
    }

    return s
  })
  return str
}
export function base64encode(str: string): string {
  const base64EncodeChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
  let out, i, c1, c2, c3
  const len = str.length

  i = 0
  out = ''
  while (i < len) {
    c1 = str.charCodeAt(i++) & 0xff
    if (i === len) {
      out += base64EncodeChars.charAt(c1 >> 2)
      out += base64EncodeChars.charAt((c1 & 0x3) << 4)
      out += '=='
      break
    }
    c2 = str.charCodeAt(i++)
    if (i === len) {
      out += base64EncodeChars.charAt(c1 >> 2)
      out += base64EncodeChars.charAt(((c1 & 0x3) << 4) | ((c2 & 0xf0) >> 4))
      out += base64EncodeChars.charAt((c2 & 0xf) << 2)
      out += '='
      break
    }
    c3 = str.charCodeAt(i++)
    out += base64EncodeChars.charAt(c1 >> 2)
    out += base64EncodeChars.charAt(((c1 & 0x3) << 4) | ((c2 & 0xf0) >> 4))
    out += base64EncodeChars.charAt(((c2 & 0xf) << 2) | ((c3 & 0xc0) >> 6))
    out += base64EncodeChars.charAt(c3 & 0x3f)
  }
  return out
}

export const px2vw = function (px: number): string {
  const viewPort = 750
  const vw = ((Number(px) / viewPort) * 100).toFixed(3)
  return `${vw}vw`
}

export { dayjs, UrlParser, formatAmount }
