const qs = require('query-string')
/**
 * @description 解析url
 * @param {String} url
 * @param {String} key
 * @return {Object} parsed[key]
 */

export function obj2QueryStr(obj: { [key: string]: any }) {
  return qs.stringify(obj)
}

export class UrlParser {
  href: string
  path: string
  query: {
    [key: string]: string
  }

  constructor(_href?: string) {
    this.href = _href || window.location.href
    const { path, query } = this.parse(this.href)
    this.path = path
    this.query = query
  }
  get fullPath() {
    return `${this.path}?${this.queryStr}`
  }
  get queryStr() {
    return qs.stringify(this.query)
  }
  private parse(url: string): {
    href: string
    path: string
    query: {
      [key: string]: string
    }
  } {
    if (!url) {
      console.error('UrlParser 没有传入url！')
      return {
        href: '',
        path: '',
        query: {},
      }
    }
    let search = url.split('?')[1]
    let path = url.split('?')[0]
    const query = qs.parse(search)
    this.path = path
    this.query = query
    return this
  }

  appendQuery(
    keys: {
      [key: string]: any
    } = {}
  ) {
    const _query = JSON.parse(JSON.stringify(this.query))
    Object.keys(keys).forEach((key) => {
      _query[key] = keys[key]
    })
    this.query = _query
    return this
  }
  removeQuery(keys: string[]) {
    const _query = JSON.parse(JSON.stringify(this.query))
    keys.forEach((key) => {
      if (Object.prototype.hasOwnProperty.call(_query, key)) {
        delete _query[key]
      }
    })
    this.query = _query
    return this
  }
}

// export { UrlParser, parseUrl };
export default UrlParser
