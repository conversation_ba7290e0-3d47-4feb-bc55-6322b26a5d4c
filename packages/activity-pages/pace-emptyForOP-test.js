var shell = require('shelljs')

const dirList = [
  'a11y_knowledge_competition/index.html',
  'a11y_recitation/detail.html',
  'a11y_recitation/index.html',
  'a11y_red_packet/index.html',
  'a11y_story_collection/detail.html',
  'a11y_story_collection/index.html',
  'abnormal/index.html',
  'accessibility/index.html',
  'activity_trans/app.html',
  'ad_new/index.html',
  'ad/index.html',
  'annual_bonus/app.html',
  'bonus/index.html',
  'buy_deposit_get_gold_and_wine/share.html',
  'buy_deposit_get_gold_v2/index.html',
  'buy_deposit_get_gold_v2/static.html',
  'buy_deposit_get_gold_v3/index.html',
  'buy_deposit_get_gold_v4/app.html',
  'buy_deposit_get_gold/index.html',
  'buy_deposit_get_qqv_v2/guide.html',
  'buy_deposit_get_qqv_v2/share.html',
  'buy_deposit_get_qqv/guide.html',
  'buy_deposit_get_qqv/share.html',
  'buy_gold_get_gold/index.html',
  'c_deposit/index.html',
  'cd_intro/index.html',
  'channel_landing/index.html',
  'cjzc/index.html',
  'course/index.html',
  'course/receive.html',
  'course/share.html',
  'deposit_point_reward/index.html',
  'dmall_lottery/index.html',
  'ei_product/share.html',
  'finance_QA/answer_keys.html',
  'finance_QA/index.html',
  'finance_QA/question.html',
  'finance_QA/result.html',
  'finance_QA/rule.html',
  'fund_product/share.html',
  'gf_funds/index.html',
  'gift_insurance/detail.html',
  'gift_insurance/index.html',
  'gold_guess/result.html',
  'gold_guess/share.html',
  'gold/app.html',
  'gold/share.html',
  'gold20g/app.html',
  'group/app.html',
  'group/share.html',
  'hotlist/index.html',
  'hotlist/list.html',
  'hqplus2017/app.html',
  'hqplus2017/share.html',
  'huaneng_pre/index.html',
  'installment_loan_elong/index.html',
  'installment_loan_invitation/share.html',
  'insurance/axys/index.html',
  'intelli_deposit/index.html',
  'jd_coupon_list/index.html',
  'jdcard/card.html',
  'jdcard/index.html',
  'jdcard/invite.html',
  'jdcard/main.html',
  'jdcard/share.html',
  'jdcard2/index.html',
  'jdcard2/mp.html',
  'jdcard2/share.html',
  'join_colib/app.html',
  'jump_trans/app.html',
  'jump_trans/share.html',
  'landing/index.html',
  'lottery_wb6/index.html',
  'lottery_wb7/index.html',
  'lottery/index.html',
  'maoyan/index.html',
  'mark_dh/index.html',
  'may_finance_act/index.html',
  'may_finance_act2/index.html',
  'merge_yz/index.html',
  'mgm_account_update/index.html',
  'mgm_account_update/invitee.html',
  'mgm_corp_loan/index.html',
  'mgm_deposit/share.html',
  'mgm_group/index.html',
  'mgm_introduce/index.html',
  'mgm_offline/app.html',
  'mgm_offline/share.html',
  'mgm_parent_invite/index.html',
  'mgm_parent_invite/list.html',
  'mgm_parent_invite/share.html',
  'mgm_parent/index.html',
  'mgm_point/index.html',
  'mgm_point/share.html',
  'mgm_rank/app.html',
  'mgm_rank/share.html',
  'mgm_rank2/index.html',
  'mgm_rank2/share.html',
  'mgm_safe_box/index.html',
  'mgm_safe_box/share.html',
  'mgm_wine/app.html',
  'mgm_wine/share.html',
  'mgm_wine/wine.html',
  'mgm_wmp_a11y/index.html',
  'mgm_wmp_a11y/normalAd.html',
  'mgm_wmp_a11y/normalAdV2.html',
  'mgm_wmp_a11y/share.html',
  'mgm_wmp_a11y/shareToA11y.html',
  'mgm_wmp/index.html',
  'mgm_wmp/share.html',
  'mgm-internal/index.html',
  'mgm-internal/share.html',
  'mgm-invite/index.html',
  'mgm/app.html',
  'mgm/share.html',
  'mgm2017/index.html',
  'mgm2017/mp.html',
  'mgm2017/share.html',
  'mgm2018/index.html',
  'mgm3/app.html',
  'mgm3/share.html',
  'mgm4/app.html',
  'mgm4/detail.html',
  'mgm4/share.html',
  'mina/yz_service_guide/index.html',
  'monetary_fund/index.html',
  'mp_lottery/index.html',
  'new_weday/index.html',
  'newbie_guide/app.html',
  'newbie_mission/index.html',
  'newbie_mission/wepay.html',
  'newbie_promote/app.html',
  'newbie_promote2/app.html',
  'oauth/index.html',
  'pay_code/app.html',
  'plan_intro/app.html',
  'pledge_loan/share.html',
  'pre-reg2/index.html',
  'prize_wheel/index.html',
  'product_topics_new/index.html',
  'product_topics_newyear/index.html',
  'product_topics/index.html',
  'programmer_activity/index.html',
  'programmer_certify/certify.html',
  'programmer_certify/index.html',
  'programmer_certify/intro.html',
  'programmer_certify/operate.html',
  'qq_super_bdb/index.html',
  'register_tips/app.html',
  'register_tips/share.html',
  'salary_plan/index.html',
  'salary_plan/intro.html',
  'salary_plan/otp.html',
  'screenslide/index.html',
  'sf_finance_subject/app.html',
  'sf_finance_subject/share.html',
  'small_business_booking_loan/index.html',
  'small_business_booking_loan/index2.html',
  'small_business_booking_loan/share.html',
  'small_business_booking_loan/shareApp.html',
  'small_business_booking_loan/sharefocus.html',
  'sme_adv/index.html',
  'sme_intro/index.html',
  'sme_inv/index.html',
  'sme_qa/article-detail.html',
  'sme_qa/article-list.html',
  'sme_qa/index.html',
  'spring_festival_emoticon/index.html',
  'sq_red_packet/index.html',
  'sq_red_packet/webank.html',
  'sq_sports_deposit/index.html',
  'sq_sports_red_packet/detail.html',
  'sq_sports_red_packet/index.html',
  'svip_coupon/index.html',
  'teach_parent/index.html',
  'tencent_offline_card/index.html',
  'tenpay/index.html',
  'tenpay/merchants.html',
  'trans_in_intro/index.html',
  'trans-notification/app.html',
  'trans-notification/hw.html',
  'trans-notification/interest_to_friend.html',
  'upgrade2to1/index.html',
  'vday_gold/share.html',
  'w2kpdf/index.html',
  'wb6/index.html',
  'wd_newyear/index.html',
  'we_parking/index.html',
  'webankCardNewSkin_new/index.html',
  'wechat_pay_red_packet/get_ticket.html',
  'wechat_pay_red_packet/invite.html',
  'wechat_pay_red_packet/share.html',
  'wechat_pay_red_packet/ticket_result.html',
  'wechat_pay_red_packet/ticket_share.html',
  'weday/pause.html',
  'weday/share.html',
  'weday/spread.html',
  'weday/weday.html',
  'weday2/share.html',
  'weday2/spread.html',
  'weday2/weday.html',
  'welfare/welfare_detail.html',
  'wepay_spread/app.html',
  'wx_bind_card/app.html',
  'wx_read/index.html',
  'wx_scheme_for_ad/index.html',
  'wxscheme/index.html',
  'wzdz/index.html',
  'xmly_fm/share.html',
  'yz_get_coupon/index.html',
  'yz_get_coupon/walmart.html',
  'yz_lottery/index.html',
  'yz_mgm/index.html',
]

shell.exec('pnpm run empty-test --filter @focus/activity')

// 0410空白
const otherList = [
  'coop/card/index.html ',
  'coop/class1/index.html ',
  'coop/class3/index.html ',
  'coop/ginwa/index.html',
  'coop/nmd/index.html',
  'coop/payment/index.html ',
  'coop/picc/index.html',
  'coop/recharge/index.html',
  'coop/smeloan/index.html',
  'coop/ums/index.html',
  'coop/unionpay/index.html',
  'index.html',
  'pro/ngPageFragmentShell/index.html',
  'pro/ngPageFragmentShell/pageFragmentTest.html',
  'productVerify/index.html',
  'utils/wepage/index.html',
  'we2kpay/index.html',
  'wemap/wemap_with_data.html',
  'www/proxy/proxy_personal.html',
]

function newDir() {
  const pack = `${__dirname}`
  console.log('🚀 ~ newDir ~ pack:', pack)
  const sourceFileDir = `${pack}/client-test/empty`
  otherList.forEach((i) => {
    const arr = i.split('/')
    const htmlName = arr[arr.length - 1]
    arr.pop()
    console.log('🚀 ~ dirList.forEach ~ arr:', arr)
    const dir = arr.join('/')
    console.log('🚀 ~ dirList.forEach ~ dir:', dir)
    const fileDir = `${__dirname}/build-empty/${dir}`
    const targetFileDir = `${__dirname}/build-empty/${i}`
    shell.exec(`mkdir -p ${fileDir}`)
    // shell.exec(`mkdir -p ${fileDir}/css`)
    // shell.exec(`mkdir -p ${fileDir}/js`)

    // shell.exec(`cp -r ${sourceFileDir}/css/* ${fileDir}/css`)
    // shell.exec(`cp -r ${sourceFileDir}/js/* ${fileDir}/js`)
    shell.exec(`cp -r ${sourceFileDir}/index.html ${targetFileDir}`)
  })
}

// function newDir() {
//   const pack = `${__dirname}`
//   console.log('🚀 ~ newDir ~ pack:', pack)
//   const sourceFileDir = `${pack}/client-test/empty`
//   dirList.forEach((i) => {
//     const arr = i.split('/')
//     const htmlName = arr[arr.length - 1]
//     arr.pop()
//     console.log('🚀 ~ dirList.forEach ~ arr:', arr)
//     const dir = arr.join('/')
//     console.log('🚀 ~ dirList.forEach ~ dir:', dir)
//     const fileDir = `${__dirname}/build-empty/op/${dir}`
//     const targetFileDir = `${__dirname}/build-empty/op/${i}`
//     shell.exec(`mkdir -p ${fileDir}`)
//     // shell.exec(`mkdir -p ${fileDir}/css`)
//     // shell.exec(`mkdir -p ${fileDir}/js`)

//     // shell.exec(`cp -r ${sourceFileDir}/css/* ${fileDir}/css`)
//     // shell.exec(`cp -r ${sourceFileDir}/js/* ${fileDir}/js`)
//     shell.exec(`cp -r ${sourceFileDir}/index.html ${targetFileDir}`)
//   })
// }
newDir()
