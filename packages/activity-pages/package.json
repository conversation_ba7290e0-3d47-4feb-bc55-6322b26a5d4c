{"name": "@focus/activity", "version": "0.1.0", "scripts": {"b-test": "vue-cli-service build --no-clean --mode test -p ", "b-prod": "vue-cli-service build --no-clean --mode prod -p ", "build:test": "node ./pace-ci-test.js ", "predev": "pnpm run update-version && pnpm run ready-test", "dev": "vue-cli-service serve -d dev --mode test --vconsole hide --watch -p", "build:prod": "vue-cli-service build -p client --mode prod --no-clean", "build:prod-log": "vue-cli-service build --no-clean --mode prod --log log -p", "empty-test": "vue-cli-service build --no-clean --mode test --hash no  -p empty ", "empty-prod": "vue-cli-service build --no-clean --mode prod --hash no -p empty "}, "main": "./dist/focus-render.umd.js", "dependencies": {"@babel/plugin-proposal-optional-chaining": "^7.20.7", "@focus/render": "workspace:^2.23.0921", "@vue/reactivity": "3.2.31", "@webank/wa-sdk": "1.16.0", "address": "^1.2.0", "axios": "^0.26.1", "core-js": "^3.24.1", "cssnano-preset-advanced": "^4.0.8", "dayjs": "1.11.5", "html2canvas": "^1.4.1", "isBetween": "link:dayjs/plugin/isBetween", "js-base64": "^3.7.7", "js-md5": "^0.7.3", "pinia": "^2.0.18", "query-string": "^7.1.1", "swiper": "^8.2.6", "vconsole": "3.15.1", "vue": "^3.2.33", "vue-router": "4"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/js-md5": "^0.4.3", "@types/jest": "^27.5.2", "@vue/cli-plugin-babel": "~4", "@vue/cli-plugin-typescript": "^4", "@vue/cli-plugin-vuex": "~4", "@vue/cli-service": "~4", "@vue/compiler-sfc": "^3.2.38", "animate.css": "^4.1.1", "gsap": "^3.9.1", "nodejs-argv": "^1.0.2", "postcss-aspect-ratio-mini": "^1.1.0", "postcss-cssnext": "^3.1.1", "postcss-px-to-viewport": "^1.1.1", "postcss-write-svg": "^3.0.1", "regenerator-runtime": "^0.13.9", "sass": "^1.49.11", "sass-loader": "^8.0.2", "shelljs": "^0.8.5", "svga": "^2.0.7", "ts-loader": "^9.2.8", "typescript": "~4.1.6", "@types/webpack-env": "^1.18.0"}, "peerDependencies": {"pinia": "^2.0.13", "vue": ">=3.0.0"}, "typings": "./dist/index.d.ts"}