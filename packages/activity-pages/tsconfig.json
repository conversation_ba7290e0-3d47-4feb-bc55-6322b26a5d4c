{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "types": ["jest", "webpack-env"], "typeRoots": ["src/@types", "node_modules/@types"], "paths": {"@/*": ["src/*"], "@focus/render": ["../focus-render/build-lib/packages/focus-render"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["*.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx", "tests/**/*.d.ts", "src/service/web-base-core/Wa.js"], "exclude": ["node_modules"]}