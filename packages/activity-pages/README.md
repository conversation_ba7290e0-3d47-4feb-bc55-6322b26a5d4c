# focus-activity

这个项目是将`<focus-render></focus-render>`作为组件使用，将打包成一个单独的活动页独立运营

本文主要是以开发占位组件 once-act 页面作为参考讲述

# 安装和启动

执行 pnpm install --filter @focus/activity 装包

可以使用 pnpm i --frozen-lockfile 会按照 pnpm-lock.yaml 文件严格安装

接下来启动项目：

pnpm run dev -p once-act --filter @focus/activity

如果更改的是 focus-render 组件的内容，那么需要先更新 focus-activity 导入的 focus-render 的包，导入的包是通过 build:test-lib 放到 build-lib 文件夹并且下方的 focus-render.umd.js 是入口文件，所以要更改这个就要执行以下命令：

pnpm run ready-test

接下来说一下-p once-act 的用法：

1. 在 vue.config.js 中通过显式指定 family2 和 seven_star 两个 page 入口，然后利用 readdirSync 读取在 src/views 下的文件目录，并创建一个自定义的 pages 对象其中包括所有 views 下的目录的节点配置

2. module.exports 的 pages 节点用于多页面应用程序模式 MPA，可以为项目设置多个入口和模板，pages 节点是一个对象，每个键代表一个页面，每个页面可以有不同的入口文件、模板和输出文件等

3. chunks 是指定要包含的代码块，默认会包含 entry 文件指定的 js 文件以及提取出来的公共代码块，通过调整 chunks 的值，可以在多页面应用中控制每个页面需要加载的代码，指定每个页面只加载它所需的代码块，避免不必要的资源浪费，提取公共代码块（如 `'chunk-vendors'` 和 `'chunk-common'`），可以提高缓存利用率，减少页面首次加载的体积。

4. 然后通过 argv.get('-p')拿到后面的参数，赋值给 pageName 并通过第 1 点中的自定义的 pages 全部配置对象中拿到 pages[pageName]然后赋值给 module.exports 的 pages 节点，从而完成-p 指令和页面渲染打包的转化

# 网络代理和配置

## whistle 代理配置

```
https://personal.test.webank.com/dev/    http://127.0.0.1:8888/dev/
https://m.test.webank.com/dev/    http://127.0.0.1:8888/dev/
https://personal.test.webankwealth.com/dev/    http://127.0.0.1:8888/dev/
https://personalv6.test.webankwealth.com/dev http://127.0.0.1:8888/dev/


https://dbd.test.webankcdn.net/wm-resm/hj/focus2/ http://127.0.0.1:8888/dev/
https://dbd.test.webankwealthcdn.net/wm-resm/hj/focus2 http://127.0.0.1:8888/dev/
```

## 微信开发者工具访问

访问`https://personal.test.webank.com/dev/once-act/index.html?fid=xxx#{活动主题}`

活动主题是根据 focus-activity 中的 routes 规则得到的，这里利用传统的 vue-router 的路由 hash 的方式

在`/views/once-act/pages`目录下新建一个对应的主题页面，然后在`/views/once-act/route`中添加对应规则，即可使用上面的链接进行页面访问。

# 开发过程

目前的 views 下包括下面的页面：

big_mgm、family、fund_exp_token、money_first、money_first_test、once-act、preview、salary_week、seven_star、time_travel、user_equity

接下来以其中的 once-act 页面作为参考讲述：

## 开发占位组件

main.ts 中可以导入 focus-render 中的内容

import focusRenderPlugin, { focusCore } from '@focus/render'

在 routes 下配置路由和组件的对应关系

可以参考`/once-act/pages/kmh.vue`的使用，较为简洁

1. 在管理端配置新增`开发占位组件`

2. 配置`开发占位组件`的几项内容

- 枚举值：这部分和具名插槽是关联在一起的，kmh 的插槽名为#ranklist，所以如果想要使用 kmh 组件的话，枚举值必须填写 ranklist
- 组件备注：一串长文本，用英文分号`;`来分段写清楚这个组件的功能；只是一个备注功能，不具备实际业务能力。格式为`{组件名称};{参数1的功能};{参数2的功能};xxx`
- 附加参数：填入后，是一个`字符串数组`，可以在`组件渲染后`从`slotMountedCb`方法的`同名回调`中获取到对应参数。参考后续使用。

3. 在代码中使用`开发占位组件`

   使用`<focus-render></focus-render>`组件，并传入对应`fid`

   ```html
   <focus-render v-if="focusFid" :focusFid="focusFid" @onLoginSucc="afterLogin" @onFocusConfigSucc="setFocus">
     <!-- do something -->
   </focus-render>
   ```

   在组件中传入`slotMountedCb`方法，在 focus-render 中的 focus-place-holder 组件中会调用同名函数：

   在 focus-place-holder 中会通过 inject 拿到传入的 slotMountedCb，并通过 slotMountedCb[slotName]来调用该回调函数

   ```jsx
     <template>
       <focus-render
         v-if="focusFid"
         :focusFid="focusFid"
         @onLoginSucc="afterLogin"
         :slotMountedCb="slotMountedCb()"
         @onFocusConfigSucc="setFocus"
       >
       <!-- do something -->
       </focus-render>
     </template>
   
     <script>
       function slotMountedCb() {
         return {
           ranklist: (data: any) => {
             console.log('🚀 ~ file: kmh.vue:78 ~ slotMountedCb ~ data:', data)
   
             mgmAid.value = data[0] || 0
             moreRankList.value = data[1]
             rankMaxLenth.value = Number(data[2]) || 0
           },
         }
       }
     </script>
   
   ```

   在 focus-place-holder 中会通过 inject 拿到插槽的内容，并通过 focusSlots[slotName]来拿到插槽函数，通过 h 函数和插槽函数的调用来渲染插槽

   ```js
   return () =>
     h(
       'div',
       {
         class: `focus-place-holder box_${slotName}`,
         style: curStyles,
       },
       target({
         extendParams,
         configData: configData.value,
         styles: { boxSize },
       })
     )
   ```

# 活动页 activity-pages 发布

1. 修改pace-ci-prod.js ，在打包当天写入需要打包的项目

   在pace中的构建中的基础构建点击查看

   ![image-20241216163541246](https://p.ipic.vip/prv310.jpg)

   ![image-20241216164052547](https://p.ipic.vip/xs0ypo.jpg)

   这样后会执行focus-mono项目中的package.json中的命令，并执行对应的文件

   ![image-20241216164509725](https://p.ipic.vip/9ihvls.jpg)

   

2. 提交release分支到master， 检查merge-request，code-review

   

3. 修改流水线http://pace.weoa.com/#/integrationDetail/1039300005036625?fromRf=0，把分支调整到release分支，对应子系统版本调整到发布所需版本（找pm确认）

   ![image-20241216171513799](https://p.ipic.vip/0z0f3l.jpg)

   ![企业微信截图_629ce43a-56ec-4a03-b638-2f910998772a](https://p.ipic.vip/pwtv7z.jpg)

4. 打包结束后，检查打包结果包含需要项目

   ![企业微信截图_1ed78cd3-d57d-4432-acfd-033d30af6604](https://p.ipic.vip/zgy1bf.jpg)发布的

5. 预发布环境发布后，预览物料完成后就可以利用代理，在微信开发者工具中访问生产中的效果，这步是把生产的效果发到了测试环境，代理看测试环境就可以看到生产的效果

   ![企业微信截图_6bcca6d6-9abf-48dc-9812-83093129af6b](https://p.ipic.vip/smohac.jpg)

   开发可以用代理到测试环境代理路径查看自己的代码是否打包进去，以下是代理路径

   微信开发者工具中访问链接为https://m.webank.com/s/hj/focus2/once-act/index.html?fid=3877&cdn=0#/kmh2025
   注意once-act是带开发占位组件的，可以更换，fid是活动标识，#kmh2025是路由

   ```
   https://m.webank.com/s/hj/focus2/once-act/index.html  https://m.test.webank.com/s/hj/focus2-preview/once-act/index.html
   https://dbd.webankwealthcdn.net/wm-resm/hj/op/assets/lib https://dbd.test.webankwealthcdn.net/wm-resm/hj/op/assets/lib
   https://dbd.webankwealthcdn.net/wm-resm/hj/ https://dbd.test.webankwealthcdn.net/wm-resm/hj-preview/
   ```

6. app环境的生产验证

   ![企业微信截图_7afaf79c-ffe6-4308-a169-76cb990ac27c](https://p.ipic.vip/7bo6rn.jpg)
   
7. 下面的图是最关键的！！！！！！！！！！！！！！！！！！！！！！！

![image-*****************](https://p.ipic.vip/dpb3ix.jpg)

# 业务产研流程

1. 需求评审
2. 确认需求排期到指定【发布日期】
3. 和测试确认冒烟、提测时间
4. 开发方案设计
5. 编写开发方案设计
6. 代码开发
7. 冒烟（就是开发完成自测）
8. 测试（由测试人员测试，然后开发来修复问题）
9. 生产发布
10. 生产验证

利用 pace+平台编译打包远端分支代码，然后部署到测试服务器中，通过测试服务器的链接去访问测试环境的效果
