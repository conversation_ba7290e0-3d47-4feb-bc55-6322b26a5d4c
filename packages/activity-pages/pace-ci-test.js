var shell = require('shelljs')
// import shell from 'shelljs'

// const startPackDir = process.argv[process.argv.length - 2] === '-s' && process.argv[process.argv.length - 1] === '1'

/**
 * pace的流水线，只打包以下目录到测试环境，请按要求添加！！
 * @param key {time} 发版时间  :代表在这个日期以后，不再发送该版本的目录到测试环境； 比如要 2021年6月15日发版，那么key= 2021/06/15
 * @param value {Array} 要发布的包目录名，相对 src的路径，比如 app/aboutus 或者 jd_coupon_list
 *
 * e.g. '2021/10/14':['app/aboutus','jd_coupon_list']
 * ⚠️注意！！！！ 此处仅支持到 app下的一级，不能有2级
 * ✔️正确： app/common
 * ❌错误：app/common/bridge-download
 */
const packDir = {
  // '2025/07/09': ['preview'], // 默认更新preview,勿删
  // '2024/08/08': ['salary_week', 'money_first', 'family', 'salary_week'],
  // '2024/09/24': ['big_mgm'],
  // '2024/11/15': ['money_first_2', 'big_mgm', 'family', 'once-act'],
  // '2024/11/29': ['once-act'],
  // '2024/12/20': ['company', 'once-act'],
  // '2024/01/02': ['salary_week', 'money_first_2', 'family', 'big_mgm', 'fund_exp_token', 'user_equity'],
  // '2024/12/29': ['salary_week', 'money_first_2', 'once-act'],
  // '2025/01/18': ['salary_week', 'money_first_2', 'family', 'big_mgm', 'fund_exp_token', 'user_equity', 'once-act'],
  '2025/02/25': ['family', 'once-act', 'company'],
  '2025/03/29': ['empty'],
  '2025/04/10': ['salary_week'],
  '2025/05/17': ['once-act'],
  '2025/06/17': ['once-act'],
  '2025/06/12': ['once-act'],
  '2025/07/10': ['once-act', 'family'],
  '2025/10/10': ['once-act', 'template-act'],
}

function packTargetDir() {
  let dirs = getAllApckDirs()
  // shell.exec('pnpm i --filter @focus/render')

  // shell.exec('pnpm run build:test-lib --filter @focus/render')

  // shell.exec('pnpm i --filter @focus/activity')
  // 默认打包preview
  dirs.push('preview')
  dirs.forEach((name) => {
    if (name === 'empty') {
      shell.exec('pnpm run empty-test --filter @focus/activity')
    }
    shell.exec('pnpm run b-test' + ` ${name}` + ' --filter @focus/activity')
  })
}
packTargetDir()

function checkPackDirs(targets = []) {
  const dirs = getAllApckDirs()
  console.log('🚀 如果你使用 test 分支进行测试环境的发布，PACE+ 仅打包以下目录', dirs)
  if (
    targets.some((dir) => {
      if (dirs.indexOf(dir) < 0) {
        console.error('\n \x1b[91m ❕未包含你的目录！' + dir)
        return true
      }
      return false
    })
  ) {
    console.log('\n请把需要打包测试环境的目录添加到  ./pace-ci.js 中！')
    console.log('\n 如果不需要用 PACE+ 打包，请忽略')
  } else {
    console.info('\n ✅已包含你的目录！' + targets)
  }
}

function getAllApckDirs() {
  const now = Date.now()
  return Array.from(
    new Set(
      Object.keys(packDir)
        .filter((time) => {
          return now <= new Date(time).getTime() + 86400000
        })
        .map((key) => {
          return packDir[key]
        })
        .reduce((pre, next) => {
          return pre.concat(next)
        }, [])
    )
  )
}

module.exports = checkPackDirs
