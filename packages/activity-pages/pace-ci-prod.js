var shell = require('shelljs')
// import shell from 'shelljs'

// const startPackDir = process.argv[process.argv.length - 2] === '-s' && process.argv[process.argv.length - 1] === '1'

/**
 * pace的流水线，只打包以下目录到测试环境，请按要求添加！！
 * @param key {time} 发版时间 + 1天 :代表在这个日期以后，不再发送该版本的目录到测试环境； 比如要 2021年6月15日发版，那么key= 2021/06/16
 * @param value {Array} 要发布的包目录名，相对 src/views的目录名，比如目录是 src/views/family 的话，就写 family
 *
 * e.g. '2021/10/14':['family','big_mgm']
 * ⚠️注意！！！！ 此处仅支持到 app下的一级，不能有2级
 * ✔️正确： app/common
 * ❌错误：app/common/bridge-download
 */
const packDir = {
  '2025/05/15': ['once-act'],
  '2025/06/12': ['once-act'],
  '2025/07/10': ['once-act', 'family'],
  '2025/07/09': [],
}

function packTargetDir() {
  let dirs = getAllApckDirs()
  // 默认打包preview
  dirs.push('preview')
  dirs.forEach((name) => {
    if (name === 'empty') {
      shell.exec('pnpm run empty-prod --filter @focus/activity')
    } else {
      shell.exec('pnpm run b-prod' + ` ${name}` + ' --filter @focus/activity')
    }
    // shell.exec('pnpm runbuild:prod-log' + ` ${name}` + ' --filter @focus/activity')
  })
}
packTargetDir()

function getAllApckDirs() {
  const now = new Date()
  return Array.from(
    new Set(
      Object.keys(packDir)
        .filter((time) => {
          const that = new Date(time)
          return now.getMonth() === that.getMonth() && now.getDate() === that.getDate()
        })
        .map((key) => {
          return packDir[key]
        })
        .reduce((pre, next) => {
          return pre.concat(next)
        }, [])
    )
  )
}
