/* eslint-env es6 */
// 页面模块
const PageModule = {
  moduleId: 1,
  pid: 0,
  moduleType: "PAGE",
  moduleName: "页面配置",
  styles: {
    bg: "#ffffff",
  },
  data: {
    pageTitle: "微众银行", // 页面标题
    shareTitle: "", // 分享标题（废弃 2024-05-28）
    shareImage: "", // 分享图片（废弃 2024-05-28）
    shareDesc: "", // 分享描述（废弃 2024-05-28）
    shareStatus: "0", // 是否可以分享
    needLogin: "1", // 1 需要登陆，0 无需登录（废弃 2024-05-28）
    pageChannel: "0", // 0 :hj-h5,1:we2000小程序;2:App原生页
    autoJoinAid: "", // 自动参与
    customerService: "", // 客服按钮关键字（废弃 2024-05-28 只有we2000用）
    shareButtonTip: "", // 分享按钮气泡（废弃 2024-05-28 App某些场景用，现在应该不用了）
    useCN: "F", // 小程序使用沉浸式（废弃 2024-05-28 只有we2000用）
    w2kstr: "", // 青春版渠道字段（废弃 2024-05-28 只有we2000用）
    setM2: {
      status: "0", // 0 关闭邀请关系；1 自动接收邀请；2 参与指定活动后接收邀请
      mgmAid: "", // 用于建立邀请关系的aid
      targetAid: "", // 指定关联的aid
    },
    showWxGuest: "0", // 微信分享面板
    startTime: "", // 页面可访问开始
    endTime: "", // 页面可访问结束时间
    relogin: "0", // 强制重新登录 '0' '1'
    forceLoginInApp: "1", // 在App里强制登录
  },
  whiteList: [{ id: "1", modules: "", isPage: "0" }], // 废弃 2024-05-28
};
export { PageModule };
export default PageModule;
