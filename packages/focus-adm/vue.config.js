const { NODE_ENV, VUE_APP_BUILD_MODE, VUE_APP_IMG_URL, VUE_APP_CDN_PREFIX, VUE_APP_F2_EDITOR } = process.env

module.exports = {
  publicPath: '/focusv3',

  devServer: {
    host: '127.0.0.1',
    hot: true, //模块热替换
    hotOnly: true, //只有热更新不会刷新页面
    //mock数据接口部分 关键部分
    proxy: {
      '^/focus-api': {
        target: 'http://************',
        pathRewrite: { '^/focus-api': '/dev/focus-api' },
        secure: false,
      },
    },
  },

  chainWebpack: (config) => {
    // 增加全局变量
    config.plugin('define').tap((args) => {
      args[0].BUILD_MODE = `"${VUE_APP_BUILD_MODE}"`
      args[0].F2EDITOR = `"${VUE_APP_F2_EDITOR}"`
      // args[0].LOCAL_IP = `"${loaclIp}"`;
      // args[0].IMG_URL = `"${VUE_APP_IMG_URL}"`;
      // args[0].CDN_PREFIX_FOCUS_RENDER = `"${cdnPrefixFocusRender}"`;
      // args[0].AJAX_BASE_URL = `"${ajaxBaseUrl}"`;
      // args[0].BUILD_TEST = serverIsTest;
      // args[0].FOCUS_CORE_VER = `"${focusCoreVer}"`;
      // args[0].SHOW_VCONSOLE = showVConsole;
      return args
    })

    config.module.rule('ts').use('ts-loader')
  },
}
