{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "suppressImplicitAnyIndexErrors": true, "sourceMap": true, "baseUrl": ".", "types": ["webpack-env", "@types/node"], "typeRoots": ["src/@types"], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["*.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx", "tests/**/*.d.ts"], "exclude": ["node_modules"]}