const cgis = {
  configList: {
    url: '/focus-api/v2/config/page/list',
    name: '获取配置列表',
    method: 'POST',
  },
}
import http from '../utils/http'

class ListService {
  curPage = 1
  readonly pageSize = 50
  pages = 0
  total = 0
  fid = ''
  aid = ''
  desc = ''
  department = ''
  constructor() {
    this.curPage = 1
    this.pages = 0
    this.fid = ''
    this.aid = ''
    this.desc = ''
    this.department = ''
  }

  tooglePage(targetPage: number, id: string, aid: string, desc: string, department: string) {
    this.curPage = targetPage
    this.fid = id
    this.aid = aid
    this.desc = desc
    this.department = department
    return this.getConfigList()
  }

  getConfigList(): Promise<{
    list: Array<any>
    total: number
    curPage: number
    pages: number
  }> {
    return new Promise((resolve) => {
      http
        .post(cgis.configList, {
          pageNum: this.curPage,
          pageSize: this.pageSize,
          params: {
            id: this.fid,
            aid: this.aid,
            desc: this.desc,
            version: '2.0',
            department: this.department,
          },
        })
        .then((res: { list: Array<any>; total: number; pages: number }) => {
          console.log('🚀 ~ file: listService.ts:20 ~ ListService ~ returnnewPromise ~ res:', res)
          const { list = [], total = 0, pages = 0 } = res
          // 只要2.0的页面
          const result = list.map((i) => {
            const {
              aid,
              desc,
              createBy,
              createTime,
              groupList = [],
              id,
              updateBy,
              updateTime,
              ver,
              auditReviewId,
              auditReviewVer,
              applyReviewVer,
              publishReviewId,
              publishReviewVer,
              department,
              applyReviewId,
              customerVer,
            } = i
            return {
              aid,
              desc,
              createBy,
              updateBy,
              createTime,
              groupList: groupList.map((_i: any) => _i.departmentName),
              id,
              updateTime,
              ver,
              auditReviewId,
              applyReviewId,
              auditReviewVer,
              publishReviewId,
              publishReviewVer,
              applyReviewVer,
              department,
              customerVer,
            }
          })
          return resolve({ pages, list: result, total, curPage: this.curPage })
        })
    })
  }
}

export default new ListService()
