const cgis = {
  getUserAuth: {
    url: '/focus-api/user/find_myself',
    name: '获取当前用户权限',
  },
}
import http from '../utils/http'
import userStore from '../store/userStore'
class UserService {
  userName = ''
  getUserInfo() {
    const store = userStore()
    if (store.userName) {
      return Promise.resolve(true)
    }
    return new Promise((resolve) => {
      http.get(cgis.getUserAuth).then((res: any) => {
        console.log('🚀 ~ file: userService.ts:13 ~ UserService ~ http.get ~ res:', res)
        const { roleAuthority = {}, user_name = '', role = {} } = res
        console.log('🚀 ~ file: userService.ts:15 ~ UserService ~ http.get ~ user_name:', user_name)
        console.log('🚀 ~ file: userService.ts:16 ~ UserService ~ http.get ~ role:', role)

        const { op_permission = '', path_permission = '', role_name } = roleAuthority
        let isDevReview = false
        const optionRoles: any = {}
        try {
          const pathRoleList = role.pathList || []
          console.log('🚀 ~ file: userService.ts:21 ~ UserService ~ http.get ~ pathRoles:', pathRoleList)
          const optionRoleList = role.operateList || []
          if (pathRoleList.some((i: any) => i.pathUrl === '9999')) {
            isDevReview = true
          }

          optionRoleList.forEach((i: any) => {
            console.log('🚀 ~ file: userService.ts:27 ~ UserService ~ optionRoles.forEach ~ i:', i)
            if (i.operateCode) {
              optionRoles[`${i.operateCode}`] = true
            }
          })
          console.log('🚀 ~ file: userService.ts:23 ~ UserService ~ http.get ~ optionRoles:', optionRoles)
        } catch (err) {
          console.log(err)
        }

        const result = {
          userName: user_name,
          roleName: role_name,
          optionRoles,
        }
        this.userName = user_name

        const store = userStore()
        console.log('🚀 ~ file: userService.ts:39 ~ UserService ~ http.get ~ result:', result)
        store.setUserInfo(user_name, role_name)
        if (isDevReview) {
          store.setRoles(false, false, false, true)
        } else {
          store.setRoles(optionRoles.edit || false, optionRoles.publish || false, optionRoles.review || false, false)
        }

        return resolve(result)
      })
    })
  }

  checkUserAuth(nextPage: any) {
    console.log('🚀 ~ file: userService.ts:22 ~ UserService ~ checkUserAuth ~ nextPage:', nextPage)
    return new Promise((resolve) => {
      this.getUserInfo().then(() => {
        const store = userStore()
        const { meta = {} } = nextPage

        let result = false
        const caseEmpty = !meta.edit && !meta.review && !meta.publish
        console.log('🚀 ~ file: userService.ts:60 ~ UserService ~ this.getUserInfo ~ caseEmpty:', caseEmpty)
        const case1 = meta.edit && store.optionRoles.edit
        const case2 = meta.review && store.optionRoles.review
        const case3 = meta.publish && store.optionRoles.publish
        if (case1 || case2 || case3 || caseEmpty) {
          result = true
        }
        resolve(result)
      })
    })
  }
}

class SSOService {
  ssoBackUrl = 'http://opms.webank.com/focus-api/user/login'
  systemId = 2063
  ssoUrlPrefix = 'http://sso.webank.com:8080'
  ssoLogOut = 'http://wealth.weoa.com/focus-api/user/logout'
  constructor() {
    if (location.hostname === 'wealth.weoa.com') {
      this.ssoBackUrl = 'http://wealth.weoa.com/focus-api/user/login'
    } else {
      if (BUILD_MODE === 'test') {
        this.ssoUrlPrefix = 'http://uat.sso.webank.com:8080'
        this.ssoBackUrl = 'http://************/focus-api/user/login'
      }
      if (BUILD_MODE === 'dev') {
        this.ssoUrlPrefix = 'http://uat.sso.webank.com:8080'
      }
    }
  }

  // sso
  redirectToSSOLogin() {
    window.location.replace(
      `${this.ssoUrlPrefix}/cas/login?service=${encodeURIComponent(
        `${this.ssoBackUrl}?path=${location.href}`
      )}&systemId=${this.systemId}`
    )
  }

  redirectToSSOLogout(redirectBackUrl = '') {
    window.location.replace(`${this.ssoUrlPrefix}/cas/logout?service=${encodeURIComponent(`${this.ssoLogOut}`)}`)
  }
}

const userService = new UserService()
const ssoService = new SSOService()
export default userService
export { ssoService, userService }
