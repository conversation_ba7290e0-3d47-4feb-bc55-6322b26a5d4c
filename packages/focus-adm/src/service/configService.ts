import http from '../utils/http'
import { PageModule } from '@focus/modules/module-configs'

const cgis = {
  saveConfg: {
    url: '/focus-api/v2/config/save',
    name: '保存配置',
    method: 'POST',
  },
  setReview: {
    url: '/focus-api/review/apply/FocusActivityConfig',
    name: '提交复核',
    method: 'POST',
  },
  getConfig: {
    url: '/focus-api/v2/config/detail',
    name: '获取配置详情',
    method: 'GET',
  },
  copyNewConfig: {
    url: '/focus-api/v2/config/copy',
    name: '复制并新建',
    method: 'POST',
  },
  updateBase: {
    url: '/focus-api/v2/config/updateSimpleInfo',
    name: '更新分类、描述',
    method: 'POST',
  },
  copyACoverB: {
    url: '/focus-api/v2/config/replace',
    name: '复制页面覆盖指定fid配置',
    method: 'POST',
  },
}

class ConfigService {
  copyNewConfig(fid: string, aid: string, department: string, desc: string, versionType: string) {
    return new Promise((resolve) => {
      http
        .post(cgis.copyNewConfig, {
          id: fid,
          aid,
          department,
          desc,
          versionType,
        })
        .then((res) => {
          console.log('🚀 ~ ConfigService ~ returnnewPromise ~ res:', res)
          if (res.id) {
            return resolve(res.id)
          }
          return resolve(false)
        })
        .catch(() => {
          return resolve(false)
        })
    })
  }

  copyAcoverB(sourceFid: string, targetFid: number, versionType: string) {
    return new Promise((resolve) => {
      http
        .post(cgis.copyACoverB, {
          sourceId: sourceFid,
          targetId: targetFid,
          versionType,
        })
        .then((res) => {
          console.log('🚀 ~ ConfigService ~ .then ~ res:', res)
          if (res.id) {
            return resolve(res.id)
          }
          return resolve(false)
        })
        .catch(() => {
          return resolve(false)
        })
    })
  }

  updateBaseConfig(fid: string, department: string | any, desc: string | any) {
    return new Promise((resolve) => {
      http
        .post(cgis.updateBase, {
          id: fid,
          department,
          desc,
        })
        .then((res) => {
          console.log('🚀 ~ ConfigService ~ .then ~ res:', res)
          if (res.id) {
            return resolve(res.id)
          }
          return resolve(false)
        })
    })
  }

  createNewConfig(aid: string, department: string, desc: string, createAppPage?: boolean) {
    console.log('🚀 ~ ConfigService ~ createNewConfig ~ createAppPage:', createAppPage)
    const pageData = PageModule
    if (createAppPage) {
      pageData.data.pageChannel = '2'
    }
    const config = [{ ...pageData }]
    console.log('🚀 ~ file: configService.ts:15 ~ ConfigService ~ createNewConfig ~ config:', config)
    return new Promise((resolve) => {
      http
        .post(cgis.saveConfg, {
          aid,
          department,
          desc,
          version: '2.0',
          config: JSON.stringify(config),
        })
        .then((res) => {
          console.log('🚀 ~ file: configService.ts:14 ~ ConfigService ~ http.post ~ res:', res)
          if (res.id) {
            return resolve(res.id)
          }
          return resolve(false)
        })
    })
  }

  updateConfig(data: any) {
    console.log('🚀 ~updateConfig:', data)
    return new Promise((resolve) => {
      http
        .post(cgis.saveConfg, {
          ...data,
        })
        .then((res) => {
          console.log('🚀 ~ ConfigService ~ .then ~ res:', res)
          if (res.id) {
            return resolve(res)
          }
          return resolve(false)
        })
    })
  }

  setReviewConfig(data: any) {
    return new Promise((resolve) => {
      http
        .post(cgis.setReview, {
          ...data,
        })
        .then((res) => {
          console.log('🚀 ~ file: configService.ts:14 ~ ConfigService ~ http.post ~ res:', res)

          return resolve(true)
        })
        .catch((err) => {
          return resolve(false)
        })
    })
  }
  getConfig(id: number): Promise<{
    aid: number
    apiList: string
    config: string
    createBy: string
    customerVer: number
    createTime: string
    updateBy: string
  }> {
    console.log('🚀 ~ file: configService.ts:80 ~ ConfigService ~ getConfig ~ id:', id)
    return new Promise((resolve, reject) => {
      http
        .get(cgis.getConfig, {
          id,
        })
        .then((res) => {
          console.log('🚀 ~ file: configService.ts:14 ~ ConfigService ~ http.post ~ res:', res)

          return resolve(res || {})
        })
        .catch((err) => {
          return reject({})
        })
    })
  }
  getPageDataByFid(id: number) {
    return new Promise((resolve) => {
      this.getConfig(id).then((res: any) => {
        console.log('🚀 ~ ConfigService ~ this.getConfig ~ res:', res)
        const { config = '[]' } = res
        let _c = []
        try {
          _c = JSON.parse(config)
        } catch (err) {
          console.log('🚀 ~ ConfigService ~ this.getConfig ~ err:', err)
        }
        return resolve(_c[0])
      })
    })
  }

  getLinkByFid(id: number) {
    return new Promise((resolve) => {
      this.getConfig(id).then((res: any) => {
        console.log('🚀 ~ ConfigService ~ this.getConfig ~ res:', res)
        const { config = '[]' } = res
        let _c = []
        try {
          _c = JSON.parse(config)
        } catch (err) {
          console.log('🚀 ~ ConfigService ~ this.getConfig ~ err:', err)
        }

        const pageConfig = _c[0]
        console.log('🚀 ~ ConfigService ~ this.getConfig ~ pageConfig:', pageConfig)

        if (pageConfig.data.customUrl) {
          console.log('🚀 ~ ConfigService ~ this.getConfig ~ pageConfig.data.customUrl:', pageConfig.data.customUrl)
          const [url, hash] = pageConfig.data.customUrl.split('#/')
          return resolve(`${url}?fid=${id}#/${hash}`)
        }

        let path = 'client'

        const path2CustomModules: any = {
          bigmgm_ranklist: 'big_mgm',
          money_task_list: 'money_first_2',
          user_level: 'user_equity',
          salary: 'salary_week',
        }

        _c.forEach((i: any) => {
          if (i.moduleType === 'PlaceHolder') {
            const { data } = i
            Object.keys(path2CustomModules).find((_i: any) => {
              if (data.text === _i) {
                path = path2CustomModules[_i]
              }
            })
          }
        })
        console.log(path)

        const host =
          BUILD_MODE !== 'prod'
            ? `https://m.test.webank.com/s/hj/focus2/${path}/index.html?fid=${id}`
            : `https://m.webank.com/s/hj/focus2/${path}/index.html?fid=${id}`

        return resolve(host)
      })
    })
  }
}

export default new ConfigService()
