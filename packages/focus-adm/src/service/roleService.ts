const cgis = {
  getRoles: {
    url: '/focus-api/v2/config/group/query',
    name: '获取配置的权限列表',
    method: 'GET',
  },
  setRoles: {
    url: '/focus-api/v2/config/group/update',
    name: '更新配置的权限列表',
    method: 'GET',
  },
  allRolesList: {
    url: '/focus-api/user/module/list',
    name: '获取所有权限组',
    method: 'GET',
  },
}
import http from '../utils/http'

class RoleService {
  getAllRoles() {
    console.log('111')
    return new Promise((resolve) => {
      http.get(cgis.allRolesList).then((res) => {
        console.log('🚀 ~ RoleService ~ .then ~ res:', res)

        return resolve(
          res.map((i: any) => {
            const { path_name, id } = i
            return {
              label: path_name,
              value: id,
            }
          })
        )
      })
    })
  }

  updateRoles(id: number, roles: number[]) {
    return new Promise((resolve) => {
      http
        .post(cgis.setRoles, {
          configId: id,
          idList: roles,
        })
        .then((res: any) => {
          console.log('🚀 ~ RoleService ~ returnnewPromise ~ res:', res)
          return resolve(res)
        })
    })
  }

  getRolesByFid(id: number) {
    return new Promise((resolve) => {
      http
        .get(cgis.getRoles, {
          configId: id,
        })
        .then((res) => {
          console.log('🚀 ~ RoleService ~ returnnewPromise ~ res:', res)
          return resolve(
            res.map((i: any) => {
              const { departmentName, departmentId } = i
              return departmentId
            })
          )
        })
    })
  }
}

export default new RoleService()
