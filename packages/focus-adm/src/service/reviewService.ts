import http from '../utils/http'
import { ElMessage, ElStep } from 'element-plus'

const cgis = {
  reivewList: {
    url: '/focus-api/review/page/list',
    name: '复核列表',
    method: 'POST',
  },
  reviewDetail: {
    url: '/focus-api/review/detail',
    name: '复核详情',
    method: 'GET',
  },
  reviewOption: {
    url: '/focus-api/review/audit',
    name: '复核操作',
    method: 'POST',
  },
  techLeadOption: {
    url: '/focus-api/review/devAudit',
    name: '复核操作',
    method: 'POST',
  },
  publishOption: {
    url: '/focus-api/review/publish',
    name: '发布操作',
    method: 'POST',
  },
}

export enum EnumListStatus {
  requireReview = '0', // 申请复核
  published = '1', // 已发布
  acceptReview = '2', // 复核通过
  rejectReview = '3', // 复核不通过
  publishInTime = '4', // 定时发布
  rejectPublish = '5', // 拒绝发布
  revokeReview = '6', // 撤销复核
  revokePublish = '7', // 撤销发布
}

export enum EnumReviewOption {
  accept = 'agree',
  reject = 'refuse', // 复核拒绝
  withdraw = 'withdraw', // 撤销复核
}

class ReviewService {
  getReviewData(id: number) {
    return new Promise((resolve) => {
      http
        .get(cgis.reviewDetail, {
          id,
        })
        .then((res) => {
          console.log('🚀 ~ file: reviewService.ts:34 ~ ReviewService ~ returnnewPromise ~ res:', res)
          const { modifyContent = '{}', originalContent = '{}', extraInfo = {} } = res || {}

          const customVer = extraInfo.customerVer
            ? `${extraInfo.customerVer}.${extraInfo.publishReviewVer || extraInfo.ver}`
            : '--'
          console.log('🚀 ~ ReviewService ~ .then ~ customVer:', customVer)
          let reviewConfig = {}
          let curCustomConfig = {}
          try {
            reviewConfig = JSON.parse(modifyContent)
            curCustomConfig = JSON.parse(originalContent)
          } catch (err) {
            console.log('🚀 ~ file: reviewService.ts:56 ~ ReviewService ~ .then ~ err:', err)
          }

          return resolve({
            reviewConfig,
            curCustomConfig,
            customVer,
          })
        })
    })
  }

  acceptReview(
    id: number,
    desc: string,
    custVersion: string,
    reviewVersion: string,
    reviewUrl: string,
    custUrl: string,
    loginType: string,
  ) {
    return new Promise((resolve) => {
      http
        .post(cgis.reviewOption, {
          id,
          operateType: EnumReviewOption.accept,
          desc,
          custVersion,
          reviewVersion,
          reviewUrl,
          custUrl,
          loginType,
        })
        .then((res) => {
          console.log('🚀 ~ file: reviewService.ts:62 ~ ReviewService ~ .then ~ res:', res)
          // agree
          return resolve(true)
        })
        .catch((err) => {
          return resolve(false)
        })
    })
  }

  rejectReview(id: number, desc: string) {
    return new Promise((resolve) => {
      http
        .post(cgis.reviewOption, {
          id,
          operateType: EnumReviewOption.reject,
          desc,
        })
        .then((res) => {
          console.log('🚀 ~ file: reviewService.ts:60 ~ ReviewService ~ .then ~ res:', res)
          // agree
          return resolve(true)
        })
        .catch((err) => {
          return resolve(false)
        })
    })
  }

  leadAcceptReview(id: number, desc: string) {
    return new Promise((resolve) => {
      http
        .post(cgis.techLeadOption, {
          id,
          operateType: EnumReviewOption.accept,
          desc,
        })
        .then((res) => {
          console.log('🚀 ~ file: reviewService.ts:62 ~ ReviewService ~ .then ~ res:', res)
          // agree
          return resolve(true)
        })
        .catch((err) => {
          return resolve(false)
        })
    })
  }

  leadRejectReview(id: number, desc: string) {
    return new Promise((resolve) => {
      http
        .post(cgis.techLeadOption, {
          id,
          operateType: EnumReviewOption.reject,
          desc,
        })
        .then((res) => {
          console.log('🚀 ~ file: reviewService.ts:62 ~ ReviewService ~ .then ~ res:', res)
          // agree
          return resolve(true)
        })
        .catch((err) => {
          return resolve(false)
        })
    })
  }

  withdrawReview(id: number, desc: string) {
    return new Promise((resolve) => {
      http
        .post(cgis.reviewOption, {
          id,
          operateType: EnumReviewOption.withdraw,
          desc,
        })
        .then((res) => {
          console.log('🚀 ~ file: reviewService.ts:60 ~ ReviewService ~ .then ~ res:', res)
          // agree
          return resolve(true)
        })
        .catch((err) => {
          ElMessage.error(err.ret_msg)

          resolve(false)
        })
    })
  }
}

export const reviewService = new ReviewService()

export class PublishService {
  acceptPublish(id: number, desc: string) {
    return new Promise((resolve) => {
      http
        .post(cgis.publishOption, {
          id,
          operateType: EnumReviewOption.accept,
          desc,
        })
        .then((res) => {
          console.log('🚀 ~ file: reviewService.ts:143 ~ PublishService ~ .then ~ res:', res)
          // agree
          // if (res && res.id) {
          //   return resolve(true)
          // }
          return resolve(true)
        })
        .catch((err) => {
          ElMessage.error(err.ret_msg)

          return resolve(false)
        })
    })
  }

  rejectPublish(id: number, desc: string) {
    return new Promise((resolve) => {
      http
        .post(cgis.publishOption, {
          id,
          operateType: EnumReviewOption.reject,
          desc,
        })
        .then((res) => {
          console.log('🚀 ~ file: reviewService.ts:165 ~ PublishService ~ .then ~ res:', res)
          // agree
          return resolve(true)
        })
        .catch((err) => {
          console.log('🚀 ~ file: reviewService.ts:170 ~ PublishService ~ returnnewPromise ~ err:', err)
          ElMessage.error(err.ret_msg)

          return resolve(false)
        })
    })
  }

  withdrawPublish(id: number, desc: string) {
    return new Promise((resolve) => {
      http
        .post(cgis.publishOption, {
          id,
          operateType: EnumReviewOption.withdraw,
          desc,
        })
        .then((res) => {
          console.log('🚀 ~ file: reviewService.ts:184 ~ PublishService ~ .then ~ res:', res)

          // agree
          return resolve(true)
        })
        .catch((err) => {
          ElMessage.error(err.ret_msg)

          return resolve(false)
        })
    })
  }
}

export const publishService = new PublishService()

export class ListService {
  pageNum = 1
  readonly pageSize = 20
  pages = 0
  total = 0
  statusList: any | Array<EnumListStatus>
  constructor(statusList: Array<EnumListStatus>) {
    this.statusList = statusList || []
    this.pageNum = 1
    this.pages = 0
  }

  getListData(
    targetPage: number,
    searchData: {
      dataId: string // 配置id
      id: string // 复核id
    },
    devOwnerAuditStatus?: number,
  ): Promise<{
    list: Array<any>
    pages: number
    total: number
  }> {
    console.log('🚀 ~ ListService ~ searchData:', searchData)
    this.pageNum = targetPage || 1
    return new Promise((resolve) => {
      let params: Record<string, any> = {
        statusList: this.statusList,
        reviewType: 'FocusActivityConfig',
        id: searchData.id && Number(searchData.id),
        dataId: searchData.dataId && Number(searchData.dataId),
      }
      if (devOwnerAuditStatus === 1) {
        params = {
          ...params,
          devOwnerAuditStatus: 1,
        }
      }
      console.log('🚀 ~ ListService ~ params:', params)

      http
        .post(cgis.reivewList, {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          params,
        })
        .then((res) => {
          console.log('🚀 ~ file: reviewService.ts:51 ~ ListService ~ .then ~ res:', res)
          const { total, list, pages } = res
          return resolve({
            list: list.map((i: any) => {
              const { status, extraInfo = { ver: 0 }, dataVer, devOwnerAuditStatus } = i
              const statusTexts = [
                '待复核',
                '已发布对客',
                '复核通过待发布',
                '复核拒绝',
                '已发布待对客',
                '拒绝发布',
                '撤销复核',
                '撤销发布',
              ]
              const statusTextType = ['', 'success', 'primary', 'danger', 'warning', 'danger', 'danger', 'danger']

              let text = statusTexts[status] || ''
              const type = statusTextType[status] || ''
              let devReviewText = ''
              let devReviewType = ''
              // 复核新加逻辑
              if (status === 0) {
                if (devOwnerAuditStatus === 1) {
                  text = '待业务复核'
                  devReviewText = 'ITSM审批通过'
                  devReviewType = 'primary'
                } else {
                  text = '待业务复核'
                  devReviewText = '待ITSM审批'
                  devReviewType = ''
                }
              } else if (status === 2) {
                if (devOwnerAuditStatus === 1) {
                  text = '复核通过待发布'
                } else {
                  text = '业务复核通过'
                  devReviewText = '待ITSM审批'
                  devReviewType = ''
                }
              }

              return {
                ...i,
                devReviewText,
                devReviewType,
                statusText: text,
                statusTextType: type,
                extraInfo: extraInfo || {},
              }
            }),
            pages,
            total,
          })
        })
    })
  }
}

export default ListService
