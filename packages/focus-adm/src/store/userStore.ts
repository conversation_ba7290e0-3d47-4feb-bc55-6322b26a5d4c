import { defineStore } from 'pinia'

// interface State {
//   userName: string
//   roleName: string
//   optionRoles: {
//     edit: boolean
//     publish: boolean
//     review: boolean
//   }
// }

export const useUserStore = defineStore('user', {
  // other options...
  state: (): any => {
    return {
      userName: '',
      roleName: '',
      optionRoles: {
        edit: false,
        publish: false,
        review: false,
        devReview: false,
      },
    }
  },
  actions: {
    setUserInfo(userName: string, roleName: string) {
      this.userName = userName
      this.roleName = roleName
    },
    setRoles(edit: boolean, publish: boolean, review: boolean, devReview: boolean) {
      this.optionRoles = {
        edit,
        publish,
        review,
        devReview,
      }
    },
  },
})

export default useUserStore
