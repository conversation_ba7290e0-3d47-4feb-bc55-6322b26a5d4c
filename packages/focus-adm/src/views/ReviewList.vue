<template>
  <!-- 待复核列表 -->
  <div class="preview-list">
    <TableList :status="status" ref="tabelRef">
      <template #option="scope" :status="status">
        <el-button
          type="primary"
          @click="
            startReview(
              scope.data.id,
              scope.data.status,
              scope.data.applyUser,
              scope.data.devOwnerAuditStatus,
              scope.data.reviewDesc,
              scope.data.publishType,
              scope.data.publishConfig?.publishTime,
              scope.data.title,
            )
          "
          >查看详情</el-button
        >
      </template>
    </TableList>
  </div>

  <!-- 复核对比弹窗 -->
  <el-dialog
    v-model="showDiffDialog"
    title="复核详情"
    width="90%"
    :before-close="closeEdit"
    align-center
    modal-class="review-modal"
  >
    <DiffTable
      :reviewId="reviewId"
      :key="reviewId"
      v-if="reviewId"
      :pageType="'review'"
      @updateReviewItsmMsg="updateReviewItsmMsg"
    ></DiffTable>
    <div class="bottom-msg">
      <el-card>
        <p>
          发布类型：{{ publishTypeObj.publishType
          }}{{ publishTypeObj.publishTime ? ` ${publishTypeObj.publishTime}` : '' }}
        </p>
        <p>复核内容： {{ reviewTitle }}</p>
      </el-card>
      <div class="input-bar" v-if="reviewStatus === 0 || reviewStatus === 2 || reviewStatus === 3">
        业务复核意见(100字以内)：<el-input
          type="textarea"
          v-model.trim="reviewText"
          maxlength="100"
          show-word-limit
          :disabled="!(reviewStatus === 0 && canClickBtn)"
        ></el-input>
      </div>
    </div>

    <template #footer>
      <el-button
        type="danger"
        @click="revokePublish"
        :disabled="isLoading"
        v-if="((reviewStatus === 2 && isDevReview) || reviewStatus === 4) && canClickBtn"
        >撤销发布</el-button
      >
      <el-button
        type="danger"
        @click="rejectReview"
        :disabled="isLoading || (!reviewText.length && canClickBtn) || reviewText.length > 100"
        v-if="reviewStatus === 0 && canClickBtn"
        >业务复核拒绝</el-button
      >

      <el-button
        type="primary"
        @click="acceptReview"
        :disabled="isLoading || !reviewText.length || reviewText.length > 100"
        v-if="reviewStatus === 0 && canClickBtn"
        >业务复核通过</el-button
      >
      <el-button
        type="danger"
        @click="leadRejectReview"
        :disabled="isLoading || !(!isDevReview && (reviewStatus == 0 || reviewStatus == 2) && canDevClickBtn)"
        >研发负责人复核拒绝</el-button
      >
      <el-button
        type="primary"
        @click="leadAcceptReview"
        :disabled="isLoading || !(!isDevReview && (reviewStatus == 0 || reviewStatus == 2) && canDevClickBtn)"
        >研发负责人复核通过</el-button
      >
      <el-button @click="closeEdit" :disabled="isLoading">取消</el-button>
    </template>
  </el-dialog>

  <!-- 发布对比弹窗 -->
</template>

<script setup lang="ts">
import { EnumListStatus } from '@/service/reviewService'
import TableList from '../components/TableList.vue'
import { ref, defineEmits, computed, h } from 'vue'
import { reviewService, publishService } from '@/service/reviewService'
import { ElMessage, ElMessageBox } from 'element-plus'
import DiffTable from '@/components/DiffTable.vue'
import useUserStore from '@/store/userStore'

const userStore = useUserStore()
const reviewId = ref(0)
const tabelRef = ref<any>(null)
const isLoading = ref(false)
const reviewStatus = ref(0)
const reviewText = ref('')
const reviewTitle = ref('')
const publishTypeObj = ref<{
  publishType?: string
  publishTime?: string
}>({})
const isDevReview = ref(false)
const status = [
  EnumListStatus.requireReview,
  EnumListStatus.acceptReview,
  EnumListStatus.rejectReview,
  // EnumListStatus.published,
  EnumListStatus.publishInTime,
  EnumListStatus.rejectPublish,
  EnumListStatus.revokePublish,
]
const curClickApplyUser = ref('')
const showDiffDialog = computed(() => {
  return !!reviewId.value
})
function checkReview(data: any) {
  console.log('🚀 ~ file: ReviewList.vue:61 ~ checkReview ~ data:', data)
  console.log(userStore)
  const { applyUser } = data
  console.log('🚀 ~ file: ReviewList.vue:64 ~ checkReview ~ applyUser:', applyUser)
  console.log()
  console.log('🚀 ~ file: ReviewList.vue:66 ~ checkReview ~ userStore.userName:', userStore.userName)
  const isNotSameUser = applyUser !== userStore.userName
  return isNotSameUser && userStore.optionRoles.review
}

const canClickBtn = computed(() => {
  console.log('🚀 ~ canClickBtn ~ curClickApplyUser.value:', curClickApplyUser.value)
  console.log('🚀 ~ canClickBtn ~ userStore.userName:', userStore.userName)
  const isNotSameUser = curClickApplyUser.value !== userStore.userName
  console.log('🐬 ~ canClickBtn ~ userStore.optionRoles.devReview:', userStore.optionRoles.devReview)
  return isNotSameUser && userStore.optionRoles.review
})

const canDevClickBtn = computed(() => {
  const isNotSameUser = curClickApplyUser.value !== userStore.userName
  return isNotSameUser && userStore.optionRoles.devReview
})

function startReview(
  fid: number,
  status: number,
  applyUser: string,
  devOwnerAuditStatus: number,
  reviewDesc: string,
  publishType: number,
  publishTime: string,
  _reviewTitle: string,
) {
  console.log('🚀 ~ file: ReviewList.vue:29 ~ startReview ~ fid:', fid)
  reviewId.value = fid
  reviewStatus.value = status
  curClickApplyUser.value = applyUser
  reviewText.value = reviewDesc
  reviewTitle.value = _reviewTitle
  publishTypeObj.value = {
    publishType: publishType === 1 ? '立即发布' : '定时发布',
    publishTime,
  }
  if (devOwnerAuditStatus === 1) {
    isDevReview.value = true
  }
}

const reviewITSMMsg = ref<any>({})

function updateReviewItsmMsg(data: any) {
  console.log('🚀 ~ updateReviewItsmMsg ~ data:', data)
  reviewITSMMsg.value = {
    ...data,
    reviewUser: userStore.userName,
    reviewTitle: reviewTitle.value,
  }
}

function acceptReview() {
  if (isLoading.value) {
    return
  }
  isLoading.value = true
  ElMessageBox({
    title: '业务复核通过后生成ITSM单;审批状态1分钟同步一次（5天内的服务单）',
    customClass: 'msg-box',
    message: () => {
      const msg = [
        `页面描述：${reviewITSMMsg.value.desc}`,
        `复核内容：${reviewTitle.value}`,
        `复核预览链接：${reviewITSMMsg.value.reviewLink}`,
        `页面ID：${reviewITSMMsg.value.fid}`,
        `对客版本号：${reviewITSMMsg.value.oldPageVer}`,
        `复核中版本号：${reviewITSMMsg.value.pageVer}`,
        `活动对客链接：${reviewITSMMsg.value.pageLink}`,
        `页面类型：${reviewITSMMsg.value.loginType}`,
        `发布类型：${publishTypeObj.value.publishType} ${publishTypeObj.value.publishTime || ''}`,
      ]

      return h('div', null, [
        h(
          'div',
          {
            class: 'el-card el-card__header',
          },
          [
            h('p', null, `1. 提单人员必须有ITSM权限，否则提单失败！`),
            h('p', null, `2. ITSM审批流任意节点驳回都视为拒绝，需要提单人员自行处理表单作废`),
            h('p', null, `3. 提单人员执行表单作废后，复核状态转为【复核拒绝】`),
          ],
        ),
        h('br'),
        h('p', null, '提单人员（业务复核人员）：'),
        h(
          'div',
          {
            class: 'el-card el-card__header',
          },
          [h('p', null, `${reviewITSMMsg.value.reviewUser}`)],
        ),
        h('br'),
        h('p', null, '表单内容：'),
        h(
          'div',
          {
            class: 'el-card el-card__header',
          },
          msg.map((i) => {
            return h('p', null, `${i}`)
          }),
        ),
        h('br', null, ''),
        h('p', null, '审批链:'),
        h(
          'div',
          {
            class: 'el-card el-card__header',
          },
          [
            h('p', null, `1.业务复核人员室经理审批：提单人直属室经理`),
            h(
              'p',
              null,
              `2.研发负责人审批：协同审批-(millerwang)王蒙/ethanlin(林毓铮)/xlxiong(熊晓林)/yupongwu(吴毓鹏)`,
            ),
          ],
        ),
      ])
    },
    showCancelButton: true,
    confirmButtonText: '确认复核通过',
    cancelButtonText: '取消',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        //     custVersion: string,
        // reviewVersion: string,
        // reviewUr: string,
        // custUrl: string,
        // loginType: string
        reviewService
          .acceptReview(
            reviewId.value,
            reviewText.value,
            reviewITSMMsg.value.oldPageVer,
            reviewITSMMsg.value.pageVer,
            reviewITSMMsg.value.reviewLink,
            reviewITSMMsg.value.pageLink,
            reviewITSMMsg.value.loginType,
          )
          .then((status) => {
            if (status) {
              tabelRef.value.reset()
              ElMessage.success('业务复核通过成功！')
              setTimeout(() => {
                closeEdit()
              }, 300)
            } else {
              console.error('出错了！')
              ElMessage.error('复核失败！')
            }
            isLoading.value = false
            done()
          })
      } else {
        isLoading.value = false
        done()
      }
    },
  })
  // ElMessageBox.alert('This is a message', '业务复核通过后生成ITSM单', {
  //   // if you want to disable its autofocus
  //   // autofocus: false,
  //   confirmButtonText: '确认复核通过',
  //   callback: () => {
  //     reviewService.acceptReview(reviewId.value, reviewText.value).then((status) => {
  //       if (status) {
  //         tabelRef.value.reset()
  //         ElMessage.success('业务复核通过成功！')
  //         setTimeout(() => {
  //           closeEdit()
  //         }, 300)
  //       } else {
  //         console.error('出错了！')
  //         ElMessage.error('复核失败！')
  //       }
  //       isLoading.value = false
  //     })
  //     // ElMessage({
  //     //   type: 'info',
  //     //   message: `action: ${action}`,
  //     // })
  //   },
  // })
}

function rejectReview() {
  isLoading.value = true
  reviewService.rejectReview(reviewId.value, reviewText.value).then((status) => {
    isLoading.value = false
    if (status) {
      tabelRef.value.reset()
      ElMessage.success('复核拒绝成功！')
      setTimeout(() => {
        closeEdit()
      }, 300)
    } else {
      console.error('出错了！')
      ElMessage.error('复核拒绝失败！')
    }
  })
}

function leadAcceptReview() {
  isLoading.value = true
  reviewService.leadAcceptReview(reviewId.value, reviewText.value).then((status) => {
    if (status) {
      tabelRef.value.reset()
      ElMessage.success('研发负责人复核通过成功！')
      setTimeout(() => {
        closeEdit()
      }, 300)
    } else {
      console.error('出错了！')
      ElMessage.error('复核失败！')
    }
    isLoading.value = false
  })
}

function leadRejectReview() {
  isLoading.value = true
  reviewService.leadRejectReview(reviewId.value, reviewText.value).then((status) => {
    if (status) {
      tabelRef.value.reset()
      ElMessage.success('研发负责人复核拒绝成功！')
      setTimeout(() => {
        closeEdit()
      }, 300)
    } else {
      console.error('出错了！')
      ElMessage.error('复核失败！')
    }
    isLoading.value = false
  })
}

function revokePublish() {
  isLoading.value = true
  publishService.withdrawPublish(reviewId.value, reviewText.value).then((status) => {
    isLoading.value = false

    if (status) {
      tabelRef.value.reset()
      ElMessage.success('撤销发布成功！')
      setTimeout(() => {
        closeEdit()
      }, 300)
    } else {
      console.error('出错了！')
      ElMessage.error('撤销发布失败！')
    }
  })
}

function closeEdit() {
  reviewId.value = 0
  isLoading.value = false
  reviewText.value = ''
  isDevReview.value = false
}
</script>
<style lang="scss">
.review-modal {
  overflow: hidden;
  .el-dialog__body {
    padding-top: 0;
    padding-bottom: 0;
  }
}
.msg-box {
  --el-messagebox-width: 800px !important;
}
</style>

<style scoped lang="scss">
.input-bar {
  &::before {
    content: '**';
    color: red;
  }
}
.input-bar {
  position: relative;
  margin-left: 20px;
  .new-buttons {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: flex;
    gap: 10px;

    .el-button {
      height: 30px;
      line-height: 30px;
    }
  }
}
.bottom-msg {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  > div {
    width: 100%;
    flex: auto;
  }
}
</style>
