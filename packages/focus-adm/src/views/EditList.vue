<template>
  <el-header class="header">
    <el-input
      v-model="searchInputData.fid"
      class="searchinput"
      placeholder="精确匹配"
      clearable
      @input="handleInputChange"
      @clear="reset"
    >
      <template #prepend>页面配置ID(fid)</template>
    </el-input>
    <!-- <el-input v-model="searchInputData.aid" class="searchinput" placeholder="精确匹配" clearable @clear="reset">
      <template #prepend>活动ID(aid)</template>
    </el-input> -->

    <el-input v-model="searchInputData.desc" class="searchinput" placeholder="模糊匹配" clearable @clear="reset">
      <template #prepend>页面描述</template>
    </el-input>
    <el-input v-model="searchInputData.department" class="searchinput" placeholder="模糊匹配" clearable @clear="reset">
      <template #prepend>页面分类</template>
    </el-input>
    <el-button type="primary" :suffix-icon="Search" @click="getListBySearch" :disabled="isLoading">搜索</el-button>

    <div class="header-options">
      <el-button type="primary" @click="clickCreateBtn(false)" :disabled="!canClickBtn">新建活动页</el-button>
      <el-button type="text" @click="clickCreateBtn(true)" :disabled="!canClickBtn">新建App原生页</el-button>
    </div>
  </el-header>
  <div class="list">
    <el-table :data="tableData" style="width: 100%" height="700">
      <el-table-column prop="id" label="页面配置ID(fid)" width="140" />
      <!-- <el-table-column prop="aid" label="活动ID(aid)" width="100" /> -->
      <el-table-column prop="desc" label="页面描述" />
      <el-table-column align="center" label="对客访问链接">
        <template #default="scope">
          <el-text>{{ showLinks[scope.row.id] }}</el-text>
          <el-button @click="getLink(scope.row.id)" v-if="!showLinks[scope.row.id]">查看链接</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="department" label="分类" width="150" />

      <el-table-column label="更新时间/创建时间" width="200">
        <template #default="scope">
          <el-text type="primary">{{ scope.row.updateTime }}</el-text>
          <br />
          <el-text type="info">{{ scope.row.createTime }}</el-text>
        </template>
      </el-table-column>
      <el-table-column label="更新人员/创建人员" width="200">
        <template #default="scope">
          <el-text type="primary">{{ scope.row.updateBy }}</el-text>
          <br />
          <el-text type="info">{{ scope.row.createBy }}</el-text>
        </template>
      </el-table-column>
      <el-table-column prop="desc" label="版本状态" width="200">
        <template #default="scope">
          <div class="version">
            预览版本号：<el-tag type="primary"
              >V{{ `${(scope.row.customerVer || 0) + 1}.${scope.row.ver || 0}` }}</el-tag
            >
          </div>
          <div class="version">
            <el-text class="mx-1" type="warning"> 复核中版本号：</el-text>
            <el-tag type="warning" v-if="scope.row.applyReviewVer"
              >V{{ `${(scope.row.customerVer || 0) + 1}.${scope.row.applyReviewVer || 0}` }}
            </el-tag>
            <el-tag type="warning" v-else>V--</el-tag>
          </div>

          <div class="version">
            <el-tooltip class="box-item" effect="dark" content="待发布包含设置了定时发布未生效的版本" placement="top">
              <el-text class="mx-1" type="warning">
                <el-icon><Warning /></el-icon>待发布版本号：</el-text
              >
            </el-tooltip>
            <el-tag type="warning" v-if="scope.row.auditReviewVer"
              >V{{ (scope.row.customerVer || 0) + 1 }}.{{ scope.row.auditReviewVer || 0 }}</el-tag
            >
            <el-tag type="warning" v-else>V--</el-tag>
          </div>

          <div class="version">
            对客版本号：<el-tag type="success" v-if="scope.row.customerVer"
              >V{{ `${scope.row.customerVer}.${scope.row.publishReviewVer}` }}</el-tag
            >
            <el-tag type="success" v-else>V--</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="right" width="260">
        <template #default="scope">
          <el-link
            type="primary"
            class="btn-text"
            @click="jumpToEdit(scope.row.id, scope.row.aid)"
            :disabled="!canClickBtn"
            >调整UI和功能</el-link
          >
          <el-link
            type="warning"
            class="btn-text"
            @click="pasteData(scope.row.id, `V${(scope.row.customerVer || 0) + 1}.${scope.row.ver + 1}`)"
            :disabled="isLoading || !hasPasteData || !canClickBtn"
            >粘贴配置</el-link
          >
          <el-link
            type="primary"
            class="btn-text"
            @click="submitPreview(scope.row.id)"
            :disabled="scope.row.applyReviewVer || !scope.row.ver || !canClickBtn"
            >提交复核</el-link
          >
          <el-link
            type="danger"
            class="btn-text"
            @click="
              revokeReview(
                scope.row.id,
                scope.row.applyReviewId,
                `V${(scope.row.customerVer || 0) + 1}.${scope.row.applyReviewVer}`,
              )
            "
            :disabled="!scope.row.applyReviewVer || !canClickBtn"
            >撤销复核</el-link
          >
          <el-button
            type="text"
            @click="
              copyNew(
                scope.row.id,
                'preview',
                scope.row.ver,
                `${(scope.row.customerVer || 0) + 1}.${scope.row.ver || 0}`,
              )
            "
            :disabled="!scope.row.ver || !canClickBtn"
            >复制预览版本</el-button
          >
          <el-button
            type="text"
            @click="
              copyNew(
                scope.row.id,
                'publish',
                scope.row.customerVer,
                `${scope.row.customerVer}.${scope.row.publishReviewVer}`,
              )
            "
            :disabled="!scope.row.customerVer || !canClickBtn"
            >复制对客版本</el-button
          >
          <el-button
            type="text"
            @click="updateBaseData(scope.row.id, scope.row.desc, scope.row.department)"
            :disabled="userStore.optionRoles.devReview"
          >
            变更基础信息</el-button
          >
          <el-button type="text" @click="editRoles(scope.row.id)" :disabled="userStore.optionRoles.devReview"
            >变更权限
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div class="btns">
    <el-button
      :icon="RefreshRight"
      circle
      style="margin-right: 10px"
      type="primary"
      :disabled="isLoading"
      @click="reset()"
    />
    <el-pagination
      background
      layout="prev, pager, next"
      v-model:current-page.sync="currentPage"
      @current-change="changePage"
      :disabled="isLoading"
      :page-count="totalPages"
      :page-size="50"
    />
  </div>

  <!-- 新建按钮弹窗 -->

  <el-dialog
    v-model="createDialog.show"
    :title="copyData.title || '新建页面'"
    width="800"
    :before-close="createDialog.close"
  >
    <CreateEdit :key="Date.now()" @close="createDialog.close" :copyData="copyData" :createApp="createDialog.createApp">
    </CreateEdit>
  </el-dialog>

  <!-- 提交复核 -->

  <el-dialog
    v-model="previewData.show"
    :title="`提交复核 - 配置ID(fid)【${previewData.fid}】-版本V${previewData.ver}`"
    width="800"
    :before-close="closePreview"
  >
    <PreviewEdit
      :key="Date.now()"
      :propsData="previewData"
      @close="closePreview"
      @updateList="getListData"
    ></PreviewEdit>
  </el-dialog>

  <!-- 撤销复合 -->
  <el-dialog
    v-model="rejectPreviewData.show"
    :title="`撤销复核 - 配置ID【${rejectPreviewData.fid}】 - 复核版本号【${rejectPreviewData.applyReviewVer}】`"
    :before-close="closeRejectPreview"
  >
    确认撤销复核 - 配置ID【{{ rejectPreviewData.fid }}】 - 复核版本号【{{
      rejectPreviewData.applyReviewVer
    }}】吗？该操作不可逆！
    <template #footer>
      <el-button @click="closeRejectPreview" :disabled="isLoading">取消</el-button>
      <el-button @click="setRevokeReview" type="danger" :disabled="isLoading">确定</el-button>
    </template>
  </el-dialog>

  <!-- 粘贴配置 -->
  <el-dialog
    v-model="pasteDataDialog.show"
    :title="`粘贴配置到 - 配置ID(fid)【${pasteDataDialog.targetId}】`"
    :before-close="closePasteDialog"
  >
    确认把
    <el-text type="danger">配置ID(fid)【{{ pasteDataDialog.pasteFid }}】-{{ pasteDataDialog.pasteVer }}</el-text>
    的“所有配置”粘贴到选中的<el-text type="danger"
      >配置ID(fid)【{{ pasteDataDialog.targetId }}】的预览版本 {{ pasteDataDialog.targetVer }}</el-text
    >
    吗？
    <br />
    <el-text type="danger">该操作不可逆！</el-text>
    <el-text type="danger">该操作不可逆！</el-text>
    <el-text type="danger">该操作不可逆！</el-text>
    <template #footer>
      <el-button @click="closePasteDialog">取消</el-button>
      <el-button @click="updatePaste" type="danger">确定</el-button>
    </template>
  </el-dialog>

  <!-- 变更配置信息 -->
  <el-dialog
    v-model="baseDataDialog.show"
    :title="`编辑配置 - 配置ID(fid)【${baseDataDialog.fid}】-基础信息`"
    :before-close="baseDataDialog.close"
  >
    <el-form-item label="页面分类">
      <el-input
        v-model.trim="baseDataDialog.department"
        placeholder="填入页面分类，一般可以按活动分"
        :disabled="isLoading"
      />
    </el-form-item>

    <el-form-item label="页面描述">
      <el-input
        v-model.trim="baseDataDialog.desc"
        placeholder="填入页面描述，写下这个页面是做什么用的"
        :disabled="isLoading"
      />
    </el-form-item>

    <template #footer>
      <el-button @click="baseDataDialog.close" :disabled="isLoading">取消</el-button>
      <el-button @click="baseDataDialog.submit" type="danger" :disabled="isLoading">确定</el-button>
    </template>
  </el-dialog>

  <el-dialog
    v-model="rolesDialog.show"
    :title="`修改权限 - 配置ID(fid)【${rolesDialog.fid}】`"
    :before-close="rolesDialog.close"
  >
    <RolesList :fid="rolesDialog.fid || 0" @close="rolesDialog.close" :key="rolesDialog.fid"></RolesList>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, toRefs, ref, watchEffect, watch, reactive, onMounted } from 'vue'
console.log('222')
import listServie from '../service/listService'
import { RefreshRight, Search } from '@element-plus/icons-vue'
import CreateEdit from '../components/CreateEdit.vue'
import configService from '../service/configService'
import PreviewEdit from '../components/PreviewEdit.vue'
import { reviewService } from '@/service/reviewService'
import { ElMessage } from 'element-plus'
import RolesList from '@/components/RolesList.vue'
import useUserStore from '@/store/userStore'

const userStore = useUserStore()
const totalPages = ref(0)
const currentPage = ref(1)
const isLoading = ref(false)
// const searchFid = ref('')
// const searchAid = ref('')
// const searchDesc = ref('')

const searchInputData = ref({
  fid: '',
  aid: '',
  desc: '',
  department: '',
})
const isInputing = ref(false)
const inputFlag = ref<any>(null)
const tableData = ref<any>([])
const clientPath = computed(() => {
  return BUILD_MODE !== 'prod'
    ? 'https://m.test.webank.com/s/hj/focus2/client/index.html?fid='
    : 'https://m.webank.com/s/hj/focus2/client/index.html?fid='
})
const createConfgData = ref<{
  aid: string
  department: string
  desc: string
}>({
  aid: '',
  department: '',
  desc: '',
})
// 弹窗
const configChangeFlag = ref(false)
// 提交复核弹窗
const previewData = ref<any>({
  fid: 0,
})
const rejectPreviewData = ref<any>({
  fid: 0,
})

const showLinks: any = reactive({})

const createDialog = ref<{
  show?: boolean
  close?: (...arg: any[]) => void
  createApp?: boolean
}>({})

const copyData = ref<{
  title?: string
  fid?: string
  versionType?: string
  ver?: string
  showVer?: string
}>({})

const baseDataDialog = ref<{
  show?: boolean
  close?: () => void
  submit?: () => void
  department?: string
  desc?: string
  fid?: string
}>({})

const rolesDialog = ref<{
  show?: boolean
  close?: () => void
  submit?: () => void
  department?: string
  desc?: string
  fid?: number
}>({})

const pasteDataDialog = ref<any>({})

const localPasteData = ref('')

const canClickBtn = computed(() => {
  return userStore.optionRoles.edit
})

getListData()
watchEffect(() => {
  console.log('...watchEffect')
})
watch(currentPage, () => {
  getListData()
})

// const searchKey= computed(()=>{
//   return
// })

// watchEffect(() => {
// })

// watch(isInputing, () => {
//   getListBySearch()
// })

function handleInputChange() {
  searchInputData.value.fid = searchInputData.value.fid.replace(/\D/g, '') // 过滤非数字字符
}

onMounted(() => {
  checkPasteData()
})

const hasPasteData = computed(() => {
  return !!localPasteData.value
})

function copyNew(fid: string, versionType: string, ver: string, showVer: string) {
  // configService.copyNewConfig()
  copyData.value = {
    title: `复制[FID=${fid}] [${versionType === 'preview' ? '预览版本' : '对客版本'} V${showVer}] 作为新页面的内容`,
    fid,
    versionType,
    ver,
    showVer,
  }
  createDialog.value = {
    show: true,
    close: (data) => {
      createDialog.value = {}
      copyData.value = {}

      const { fid, aid } = data
      if (!fid) {
        return
      }
      jumpToEdit(fid, aid)
      reset()
    },
  }
}

function checkPasteData() {
  let data: any = localStorage.getItem('FOCUS_V3_COPYALL') || ''
  try {
    data = data && JSON.parse(data)
  } catch (err) {
    console.log('🚀 ~ pasteData ~ err:', err)
    data = ''
  }
  localPasteData.value = data
}

function getLink(fid: number) {
  configService.getLinkByFid(fid).then((res) => {
    console.log('🚀 ~ configService.getConfig ~ res:', res)
    showLinks[fid] = res
  })
}

async function getListData() {
  isLoading.value = true
  listServie
    .tooglePage(
      currentPage.value,
      searchInputData.value.fid,
      searchInputData.value.aid,
      searchInputData.value.desc,
      searchInputData.value.department,
    )
    .then((res) => {
      console.log('🚀 ~ file: List.vue:16 ~ listServie.tooglePage ~ res:', res)
      const { total, list, pages } = res
      tableData.value = list
      totalPages.value = pages
      isLoading.value = false
    })
}

function changePage(curPage: number) {
  currentPage.value = curPage
}

function reset() {
  currentPage.value = 1
  // getListData()
}

function getListBySearch() {
  console.log('尝试调用search。。。')
  console.log('可以调用哦！')
  currentPage.value = 1
  getListData()
}

function closeEdit() {
  console.log('关闭')
  configChangeFlag.value = false
  copyData.value = {}
}

function closeCreateEditor(data: any) {
  configChangeFlag.value = false
  copyData.value = {}

  if (!data) {
    return
  }
  const { fid, aid } = data
  jumpToEdit(fid, aid)
  reset()
}

function updateBaseData(fid: string, desc: string, department: string) {
  baseDataDialog.value = {
    show: true,
    fid,
    desc,
    department,
    close: () => {
      baseDataDialog.value = {}
    },
    submit: () => {
      configService
        .updateBaseConfig(fid, baseDataDialog.value.department, baseDataDialog.value.desc)
        .then((res: any) => {
          if (res) {
            ElMessage.success('变更信息成功！')
            getListData()
            baseDataDialog.value = {}
          } else {
            ElMessage.success('变更信息失败！')
          }
        })
    },
  }
}

function jumpToEdit(fid: string, aid: string) {
  setTimeout(() => {
    const host = location.href.indexOf('127.0.0.1') > -1 ? '127.0.0.1:8000' : location.host
    window.open(`http://${host}/focusv2/?t=${Date.now()}#/editor?fid=${fid}&aid=${aid || 0}&usev3=1`)
  }, 300)
}

function submitPreview(fid: number) {
  configService.getConfig(fid).then((res: any) => {
    console.log('🚀 ~ configService.getConfig ~ res:', res)
    const { customerVer, ver } = res
    previewData.value = {
      show: true,
      fid,
      ver: `${(customerVer || 0) + 1}.${ver}`,
    }
  })
}

function closePreview() {
  previewData.value = {}
}

function revokeReview(fid: number, applyReviewId: number, applyReviewVer: string) {
  rejectPreviewData.value = {
    show: true,
    fid,
    applyReviewVer,
    applyReviewId,
  }
}

function closeRejectPreview() {
  rejectPreviewData.value = {
    show: false,
    fid: 0,
    applyReviewVer: '',
  }
}

function setRevokeReview() {
  reviewService.withdrawReview(rejectPreviewData.value.applyReviewId, '').then((res: any) => {
    console.log('🚀 ~ file: EditList.vue:281 ~ reviewService.rejectReview ~ res:', res)
    if (res) {
      ElMessage.success('撤销复核成功！')
      getListData()
      closeRejectPreview()
    } else {
      ElMessage.success('撤销复核失败！')
    }
  })
}

function pasteData(fid: number, targetVer: string) {
  console.log('🚀 ~ pasteData ~ fid:', fid)
  // configService.updateConfig({})
  const data = localPasteData.value
  console.log('🚀 ~ pasteData ~ data:', data)
  if (data) {
    const { apiList, config, grayList, id, popupConfig, serviceConfig, shareConfig, configVer } = data
    const pasteData = {
      apiList,
      config,
      grayList,
      id: fid,
      popupConfig,
      serviceConfig,
      shareConfig,
    }
    pasteDataDialog.value = {
      show: true,
      targetId: fid,
      pasteData,
      pasteVer: configVer,
      pasteFid: id,
      targetVer,
    }
  }
}

function clickCreateBtn(useApp?: boolean) {
  console.log('🚀 ~ clickCreateBtn ~ useApp:', useApp)
  console.log('🚀 ~ clickCreateBtn ~ createDialog.value :', createDialog.value)
  createDialog.value = {
    show: true,
    close: (data: any) => {
      createDialog.value = {}

      const { fid, aid } = data
      if (!fid) {
        return
      }
      jumpToEdit(fid, aid)
      reset()
    },
    createApp: useApp,
  }
}

function closePasteDialog() {
  pasteDataDialog.value = { show: false }
}

function editRoles(fid: number) {
  console.log('🚀 ~ editRoles ~ fid:', fid)
  rolesDialog.value = {
    show: true,
    fid,
    close: () => {
      rolesDialog.value = {}
    },
  }
}

function updatePaste() {
  isLoading.value = true
  const pasteData = pasteDataDialog.value.pasteData
  const fid = pasteDataDialog.value.targetId
  console.log('🚀 ~ updatePaste ~ data:', pasteData)
  configService.getConfig(fid).then((res: any) => {
    console.log('🚀 ~ configService.getConfig ~ res:', res)
    const submitData = {
      ...res,
      ...pasteData,
    }
    console.log('🚀 ~ configService.getConfig ~ submitData:', submitData)
    configService.updateConfig(submitData).then((res: any) => {
      console.log('🚀 ~ configService.getConfig ~ res:', res)
      const { id, aid } = res
      setTimeout(() => {
        reset()
        localPasteData.value = ''
        localStorage.removeItem('FOCUS_V3_COPYALL')
        isLoading.value = false
        pasteDataDialog.value = {
          show: false,
        }
        jumpToEdit(fid, aid)
      }, 300)
      // return {}
    })
  })
}

// function init() {
//   getListData(1)
// }
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .header-options {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: auto;
  }
}
.btns {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}
.searchinput {
  width: 240px;
  margin-right: 20px;
}
.version {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.btn-text {
  margin-right: 10px;
}
</style>
