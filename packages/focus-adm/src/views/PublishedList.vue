<template>
  <!-- 已发布列表 -->
  <div class="publish-pending-list">
    <TableList :status="status" ref="tabelRef">
      <!-- <template #option="scope">
        <el-button type="primary" @click="startPublish(scope.data.id)">查看详情</el-button>
      </template> -->
    </TableList>
  </div>
</template>

<script setup lang="ts">
import { EnumListStatus } from '@/service/reviewService'
import TableList from '../components/TableList.vue'
import { ref, defineEmits } from 'vue'
import { publishService } from '@/service/reviewService'
import { ElMessage } from 'element-plus'

const reviewId = ref(0)
const tabelRef = ref<any>(null)
const isLoading = ref(false)
const status = ref([EnumListStatus.published])
function startPublish(fid: number) {
  console.log('🚀 ~ file: PublishList.vue:29 ~ startPublish ~ fid:', fid)
  reviewId.value = fid
}

function closeEdit() {
  reviewId.value = 0
}
</script>

<style scoped lang="scss"></style>
