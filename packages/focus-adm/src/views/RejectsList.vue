<template>
  <!-- 作废配置列表 -->
  <div class="preview-list">
    <TableList :status="status" ref="tabelRef">
      <template #option="scope">
        <el-button type="primary" @click="copyConfig(scope.data)" :disabled="isLoading">复制</el-button>
      </template>
    </TableList>
  </div>
</template>

<script setup lang="ts">
import { EnumListStatus } from '@/service/reviewService'
import TableList from '../components/TableList.vue'
import { ref, defineEmits, computed } from 'vue'
import { reviewService, publishService } from '@/service/reviewService'
import { ElMessage } from 'element-plus'
import DiffTable from '@/components/DiffTable.vue'
const status = [
  EnumListStatus.rejectReview,
  EnumListStatus.rejectPublish,
  EnumListStatus.revokeReview,
  EnumListStatus.revokePublish,
]
const reviewId = ref(0)
const tabelRef = ref<any>(null)
const isLoading = ref(false)
const reviewStatus = ref(0)
const canReview = ref(false)

function startReview(fid: number, status: number, applyReviewVer: number) {
  console.log('🚀 ~ file: ReviewList.vue:29 ~ startReview ~ fid:', fid)
  reviewId.value = fid
  reviewStatus.value = status
  canReview.value = !applyReviewVer
}

function closeEdit() {
  reviewId.value = 0
}

function copyConfig(data: any) {
  console.log('🚀 ~ file: RejectsList.vue:69 ~ copyConfig ~ data:', data)
  isLoading.value = true
  reviewService.getReviewData(data.id).then((res: any) => {
    console.log('🚀 ~ file: DiffTable.vue:19 ~ reviewService.getReviewData ~ res:', res)
    const { curCustomConfig = {}, reviewConfig = {} } = res
    const { extraInfo = {} } = data
    const dataPaste = {
      ...reviewConfig,
      configVer: `V${extraInfo.customerVer ? extraInfo.customerVer + 1 : 1}.${extraInfo.ver}`,
    }
    console.log('🚀 ~ reviewService.getReviewData ~ dataPaste:', dataPaste)
    localStorage.setItem('FOCUS_V3_COPYALL', JSON.stringify(dataPaste))
    ElMessage.success('复制成功！')
    isLoading.value = false
  })
}
</script>

<style scoped lang="scss"></style>
