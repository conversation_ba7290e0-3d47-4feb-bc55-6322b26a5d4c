<template>
  <!-- 待发布列表 -->
  <div class="publish-pending-list">
    <TableList :status="status" ref="tabelRef" :devOwnerAuditStatus="1">
      <template #option="scope">
        <el-button
          type="primary"
          @click="startPublish(scope.data.id, scope.data.reviewUser, scope.data.status, scope.data.applyUser)"
          >查看详情</el-button
        >
      </template>
    </TableList>
  </div>

  <!-- 发布对比弹窗 -->
  <el-dialog
    v-model="showDiffDialog"
    title="发布详情"
    width="90%"
    :before-close="closeEdit"
    align-center
    modal-class="review-modal"
  >
    <DiffTable :reviewId="reviewId" :key="reviewId" v-if="reviewId" :pageType="'review'"></DiffTable>

    <template #footer>
      <el-button @click="closeEdit" :disabled="isLoading">取消</el-button>
      <el-button type="danger" @click="rejectPublish" :disabled="isLoading || !canClickBtn">拒绝发布</el-button>
      <el-button type="primary" @click="acceptPublish" :disabled="isLoading || !canClickBtn">发布对客</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { EnumListStatus } from '@/service/reviewService'
import TableList from '../components/TableList.vue'
import { ref, defineEmits, computed } from 'vue'
import { publishService } from '@/service/reviewService'
import { ElMessage } from 'element-plus'
import useUserStore from '@/store/userStore'
import DiffTable from '@/components/DiffTable.vue'

const userStore = useUserStore()
console.log('🐬 ~ userStore:', userStore.optionRoles.devReview)

const reviewId = ref(0)
const tabelRef = ref<any>(null)
const isLoading = ref(false)
const curClickReviewUser = ref('')
const curClickStatus = ref(-1)
const reviewTargetApplyUser = ref('') // 当前配置的提交复核人员
const status = ref([EnumListStatus.acceptReview, EnumListStatus.rejectPublish, EnumListStatus.publishInTime])

const canClickBtn = computed(() => {
  return (
    reviewTargetApplyUser.value === userStore.userName &&
    curClickStatus.value !== 4 &&
    curClickStatus.value !== 5 &&
    curClickReviewUser.value !== userStore.userName &&
    userStore.optionRoles.publish
  )
})

const showDiffDialog = computed(() => {
  return !!reviewId.value
})

function startPublish(fid: number, reviewUser: string, status: number, applyUser: string) {
  console.log('🚀 ~ startPublish ~ editorUser:', applyUser)
  console.log('🚀 ~ file: PublishList.vue:29 ~ startPublish ~ fid:', fid)
  reviewId.value = fid
  reviewTargetApplyUser.value = applyUser
  curClickReviewUser.value = reviewUser
  curClickStatus.value = status
}

function acceptPublish() {
  isLoading.value = true
  publishService.acceptPublish(reviewId.value, '').then((status) => {
    if (status) {
      tabelRef.value.reset()
      ElMessage.success('发布通过成功！')
      setTimeout(() => {
        closeEdit()
      }, 300)
    } else {
      console.error('出错了！')
    }
    isLoading.value = false
  })
}

function rejectPublish() {
  isLoading.value = true
  publishService.rejectPublish(reviewId.value, '').then((status) => {
    if (status) {
      tabelRef.value.reset()
      ElMessage.success('发布拒绝成功！')
      setTimeout(() => {
        closeEdit()
      }, 300)
    } else {
      console.error('出错了！')
    }
    isLoading.value = false
  })
}

function closeEdit() {
  reviewId.value = 0
  isLoading.value = false
}
</script>

<style scoped lang="scss">
.review-modal {
  overflow: hidden;
  .el-dialog__body {
    padding-top: 0;
    padding-bottom: 0;
  }
}
</style>
