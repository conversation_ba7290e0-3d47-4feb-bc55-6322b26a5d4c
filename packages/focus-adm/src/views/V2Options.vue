<template>
  <div class="v2-options">
    <div class="box">
      <el-button style="margin-right: 10px" type="primary" @click="submit" :disabled="!submitData.id || isLocking"
        >保存到预览</el-button
      >
      <div class="right">
        <el-text v-if="!nextVer" type="success">内容无改动</el-text>
        <template v-if="!isLocking">
          <el-text style="margin-right: 20px" v-if="nextVer" type="danger"
            >未保存版本：V{{ customVer }}.{{ nextVer }}</el-text
          >
          <el-text v-show="editTime && nextVer" type="danger">改动时间：{{ editTime }}</el-text>
        </template>
        <template v-if="isLocking">
          <el-text v-if="nextVer" type="info">正在保存，请稍等...</el-text>
        </template>
        <br />
        <el-text style="margin-right: 20px" type="primary">当前版本：V{{ customVer }}.{{ fidVer }} </el-text>
        <el-text v-show="time">保存时间：{{ time }}</el-text>
      </div>
    </div>

    <el-text>每次【修改组件】都需要点击“保存预览”才能在移动端查看!</el-text>
    <br />
    <el-text>每次【修改模拟数据】都要重新扫码查看最新模拟状态！</el-text>
    <!-- <div id="qrcode"></div> -->
  </div>
</template>

<script setup lang="ts">
import configService from '@/service/configService'
import { computed, onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import dayjs from 'dayjs'

const submitData = ref<any>({})
const fidVer = ref('0.0')
const time = ref('')
const router = useRouter()
const route = useRoute()
const saveMsg = ref('')
const isLocking = ref(false)
const customVer = ref(0)
const localSaveData = ref('{}')
const editTime = ref('')
const nextVer = computed(() => {
  const isSame = localSaveData.value === JSON.stringify(submitData.value)
  return submitData.value && !isSame ? fidVer.value + 1 : ''
})

onMounted(() => {
  getDataById()
  window.addEventListener(
    'message',
    (e) => {
      const { data, origin } = e
      console.log('3333')

      if (data.type === 'v3Data') {
        console.log('接收到的数据。。。', data.data)
        submitData.value = data.data || {}
        editTime.value = dayjs().format('YYYY年MM月DD日 HH:mm:ss')
      }
      // if(data &&data.type===)
      console.log('.>>>>>>>>>>><,,,,,,,,,')
    },
    false
  )
})

function getDataById() {
  if (route.query) {
    console.log('route.query......', route.query)
    configService.getConfig(route.query.fid).then((res: any) => {
      console.log('🚀 ~ file: V2Options.vue:42 ~ configService.getConfig ~ res:', res)
      const { customerVer = 0, ver = 0 } = res
      console.log('🚀 ~ file: V2Options.vue:44 ~ configService.getConfig ~ ver:', ver)
      console.log('🚀 ~ file: V2Options.vue:44 ~ configService.getConfig ~ customerVer:', customerVer)
      customVer.value = customerVer + 1
      fidVer.value = ver
    })
    console.log('🚀 ~ file: V2Options.vue:46 ~ configService.getConfig ~ fidVer.value:', fidVer.value)
  }
}

function submit() {
  saveMsg.value = '正在保存...'
  isLocking.value = true
  console.log('🚀 ~ 准备保存啦！', submitData.value)
  configService.updateConfig(submitData.value).then((res: any) => {
    console.log('🚀 ~ file: V2Options.vue:35 ~ configService.updateConfig ~ res:', res)
    const { lastUpdateTime, ver = 0 } = res
    setTimeout(() => {
      time.value = lastUpdateTime
      fidVer.value = ver
      saveMsg.value = '保存成功！'
      localSaveData.value = JSON.stringify(submitData.value)
      isLocking.value = false
    }, 5000)
  })
}
</script>

<style scoped>
html,
body {
  height: 100%;
}
.v2-options {
  margin-top: -15px;
  position: relative;
  #qrcode {
    position: absolute;
    top: 0;
    right: 0;
  }
  .box {
    width: 100%;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
}
</style>
