{"ret_code": "0000", "ret_msg": "请求成功", "token_status": "OK", "biz_no": "2405310KA0254263srjvdZKxU82wiunL", "process_type": "sync", "ret_data": {"total": 37, "list": [{"id": 44, "createBy": "tokentest", "createTime": "2024-05-31 17:41:12", "updateBy": "auto", "updateTime": "2024-05-31 17:41:12", "title": "focus权限修改181", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2024-05-31 17:41:12", "dataId": 181, "reviewUser": "auto", "reviewTime": "2024-05-31 17:41:12", "reviewDesc": "自动复核", "publishUser": "auto", "publishTime": "2024-05-31 17:41:12", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 42, "createBy": "dev", "createTime": "2024-05-31 10:20:27", "updateBy": "dev", "updateTime": "2024-05-31 17:08:56", "title": "Hzh复核测试", "reviewType": "FocusActivityConfig", "operateType": "insert", "applyUser": "dev", "applyTime": "2024-05-31 10:20:27", "dataId": 181, "reviewUser": "dev", "reviewTime": "2024-05-31 17:08:56", "reviewDesc": "", "publishUser": "", "publishTime": null, "publishDesc": "", "status": 2, "publishType": 2, "publishConfig": null}, {"id": 43, "createBy": "dev", "createTime": "2024-05-31 12:41:01", "updateBy": "dev", "updateTime": "2024-05-31 16:47:44", "title": "ceshi fuhe", "reviewType": "FocusActivityConfig", "operateType": "insert", "applyUser": "dev", "applyTime": "2024-05-31 12:41:01", "dataId": 2385, "reviewUser": "dev", "reviewTime": "2024-05-31 16:47:44", "reviewDesc": "", "publishUser": "", "publishTime": null, "publishDesc": "", "status": 3, "publishType": 1, "publishConfig": null}, {"id": 40, "createBy": "dev", "createTime": "2024-05-29 16:59:19", "updateBy": "dev", "updateTime": "2024-05-31 14:18:36", "title": "Hzh复核测试", "reviewType": "FocusActivityConfig", "operateType": "update", "applyUser": "dev", "applyTime": "2024-05-29 16:59:19", "dataId": 181, "reviewUser": "dev", "reviewTime": "2024-05-29 17:57:15", "reviewDesc": "复核通过", "publishUser": "dev", "publishTime": "2024-05-30 20:00:00", "publishDesc": "dev", "status": 1, "publishType": 2, "publishConfig": null}, {"id": 33, "createBy": "tokentest", "createTime": "2023-09-19 11:01:43", "updateBy": "auto", "updateTime": "2023-09-19 11:01:43", "title": "focus权限修改2195", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2023-09-19 11:01:43", "dataId": 2195, "reviewUser": "auto", "reviewTime": "2023-09-19 11:01:43", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2023-09-19 11:01:43", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 32, "createBy": "tokentest", "createTime": "2023-07-24 15:03:28", "updateBy": "auto", "updateTime": "2023-07-24 15:03:28", "title": "focus权限修改446", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2023-07-24 15:03:28", "dataId": 446, "reviewUser": "auto", "reviewTime": "2023-07-24 15:03:28", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2023-07-24 15:03:28", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 31, "createBy": "tokentest", "createTime": "2022-04-19 17:42:47", "updateBy": "auto", "updateTime": "2022-04-19 17:42:47", "title": "focus权限修改113", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-04-19 17:42:47", "dataId": 113, "reviewUser": "auto", "reviewTime": "2022-04-19 17:42:47", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-04-19 17:42:47", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 30, "createBy": "tokentest", "createTime": "2022-04-19 16:44:50", "updateBy": "auto", "updateTime": "2022-04-19 16:44:50", "title": "focus权限修改127", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-04-19 16:44:50", "dataId": 127, "reviewUser": "auto", "reviewTime": "2022-04-19 16:44:50", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-04-19 16:44:50", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 29, "createBy": "tokentest", "createTime": "2022-04-18 14:40:46", "updateBy": "auto", "updateTime": "2022-04-18 14:40:46", "title": "focus权限修改81", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-04-18 14:40:46", "dataId": 81, "reviewUser": "auto", "reviewTime": "2022-04-18 14:40:46", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-04-18 14:40:46", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 28, "createBy": "tokentest", "createTime": "2022-04-18 14:40:16", "updateBy": "auto", "updateTime": "2022-04-18 14:40:17", "title": "focus权限修改81", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-04-18 14:40:16", "dataId": 81, "reviewUser": "auto", "reviewTime": "2022-04-18 14:40:17", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-04-18 14:40:17", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 27, "createBy": "tokentest", "createTime": "2022-03-22 17:12:43", "updateBy": "auto", "updateTime": "2022-03-22 17:12:43", "title": "focus权限修改93", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-22 17:12:43", "dataId": 93, "reviewUser": "auto", "reviewTime": "2022-03-22 17:12:43", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-22 17:12:43", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 26, "createBy": "tokentest", "createTime": "2022-03-22 16:14:52", "updateBy": "auto", "updateTime": "2022-03-22 16:14:52", "title": "focus权限修改128", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-22 16:14:52", "dataId": 128, "reviewUser": "auto", "reviewTime": "2022-03-22 16:14:52", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-22 16:14:52", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 25, "createBy": "tokentest", "createTime": "2022-03-22 16:14:42", "updateBy": "auto", "updateTime": "2022-03-22 16:14:43", "title": "focus权限修改128", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-22 16:14:42", "dataId": 128, "reviewUser": "auto", "reviewTime": "2022-03-22 16:14:43", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-22 16:14:43", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 24, "createBy": "tokentest", "createTime": "2022-03-22 16:14:30", "updateBy": "auto", "updateTime": "2022-03-22 16:14:30", "title": "focus权限修改128", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-22 16:14:30", "dataId": 128, "reviewUser": "auto", "reviewTime": "2022-03-22 16:14:30", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-22 16:14:30", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 23, "createBy": "tokentest", "createTime": "2022-03-22 16:13:36", "updateBy": "auto", "updateTime": "2022-03-22 16:13:36", "title": "focus权限修改128", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-22 16:13:36", "dataId": 128, "reviewUser": "auto", "reviewTime": "2022-03-22 16:13:36", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-22 16:13:36", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 22, "createBy": "tokentest", "createTime": "2022-03-22 11:14:22", "updateBy": "auto", "updateTime": "2022-03-22 11:14:22", "title": "focus权限修改130", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-22 11:14:22", "dataId": 130, "reviewUser": "auto", "reviewTime": "2022-03-22 11:14:22", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-22 11:14:22", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 21, "createBy": "tokentest", "createTime": "2022-03-22 11:14:14", "updateBy": "auto", "updateTime": "2022-03-22 11:14:14", "title": "focus权限修改130", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-22 11:14:14", "dataId": 130, "reviewUser": "auto", "reviewTime": "2022-03-22 11:14:14", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-22 11:14:14", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 20, "createBy": "tokentest", "createTime": "2022-03-22 11:13:48", "updateBy": "auto", "updateTime": "2022-03-22 11:13:48", "title": "focus权限修改130", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-22 11:13:48", "dataId": 130, "reviewUser": "auto", "reviewTime": "2022-03-22 11:13:48", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-22 11:13:48", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 19, "createBy": "tokentest", "createTime": "2022-03-22 08:50:48", "updateBy": "auto", "updateTime": "2022-03-22 08:50:48", "title": "focus权限修改130", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-22 08:50:48", "dataId": 130, "reviewUser": "auto", "reviewTime": "2022-03-22 08:50:48", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-22 08:50:48", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 18, "createBy": "tokentest", "createTime": "2022-03-14 11:31:48", "updateBy": "auto", "updateTime": "2022-03-14 11:31:48", "title": "focus权限修改93", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-14 11:31:48", "dataId": 93, "reviewUser": "auto", "reviewTime": "2022-03-14 11:31:48", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-14 11:31:48", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 17, "createBy": "tokentest", "createTime": "2022-03-14 11:31:44", "updateBy": "auto", "updateTime": "2022-03-14 11:31:44", "title": "focus权限修改93", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-14 11:31:44", "dataId": 93, "reviewUser": "auto", "reviewTime": "2022-03-14 11:31:44", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-14 11:31:44", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 16, "createBy": "tokentest", "createTime": "2022-03-14 11:31:38", "updateBy": "auto", "updateTime": "2022-03-14 11:31:38", "title": "focus权限修改93", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-14 11:31:38", "dataId": 93, "reviewUser": "auto", "reviewTime": "2022-03-14 11:31:38", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-14 11:31:38", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 15, "createBy": "tokentest", "createTime": "2022-03-14 11:31:29", "updateBy": "auto", "updateTime": "2022-03-14 11:31:29", "title": "focus权限修改93", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-14 11:31:29", "dataId": 93, "reviewUser": "auto", "reviewTime": "2022-03-14 11:31:29", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-14 11:31:29", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 14, "createBy": "tokentest", "createTime": "2022-03-14 11:25:42", "updateBy": "auto", "updateTime": "2022-03-14 11:25:42", "title": "focus权限修改93", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-14 11:25:42", "dataId": 93, "reviewUser": "auto", "reviewTime": "2022-03-14 11:25:42", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-14 11:25:42", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 13, "createBy": "tokentest", "createTime": "2022-03-14 11:25:32", "updateBy": "auto", "updateTime": "2022-03-14 11:25:32", "title": "focus权限修改126", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-14 11:25:32", "dataId": 126, "reviewUser": "auto", "reviewTime": "2022-03-14 11:25:32", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-14 11:25:32", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 12, "createBy": "tokentest", "createTime": "2022-03-14 11:25:07", "updateBy": "auto", "updateTime": "2022-03-14 11:25:07", "title": "focus权限修改126", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-14 11:25:07", "dataId": 126, "reviewUser": "auto", "reviewTime": "2022-03-14 11:25:07", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-14 11:25:07", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 11, "createBy": "v_mingboliu", "createTime": "2022-03-08 11:41:36", "updateBy": "auto", "updateTime": "2022-03-08 11:41:36", "title": "focus权限修改122", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "v_mingboliu", "applyTime": "2022-03-08 11:41:36", "dataId": 122, "reviewUser": "auto", "reviewTime": "2022-03-08 11:41:36", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-08 11:41:36", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 10, "createBy": "v_mingboliu", "createTime": "2022-03-08 11:40:28", "updateBy": "auto", "updateTime": "2022-03-08 11:40:28", "title": "focus权限修改122", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "v_mingboliu", "applyTime": "2022-03-08 11:40:28", "dataId": 122, "reviewUser": "auto", "reviewTime": "2022-03-08 11:40:28", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-08 11:40:28", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 9, "createBy": "v_mingboliu", "createTime": "2022-03-07 15:43:56", "updateBy": "auto", "updateTime": "2022-03-07 15:43:56", "title": "focus权限修改107", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "v_mingboliu", "applyTime": "2022-03-07 15:43:56", "dataId": 107, "reviewUser": "auto", "reviewTime": "2022-03-07 15:43:56", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-07 15:43:56", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 8, "createBy": "tokentest", "createTime": "2022-03-07 15:41:59", "updateBy": "auto", "updateTime": "2022-03-07 15:41:59", "title": "focus权限修改107", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-07 15:41:59", "dataId": 107, "reviewUser": "auto", "reviewTime": "2022-03-07 15:41:59", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-07 15:41:59", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 7, "createBy": "v_mlingcai", "createTime": "2022-03-07 15:32:29", "updateBy": "auto", "updateTime": "2022-03-07 15:32:29", "title": "focus权限修改81", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "v_mlingcai", "applyTime": "2022-03-07 15:32:29", "dataId": 81, "reviewUser": "auto", "reviewTime": "2022-03-07 15:32:29", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-07 15:32:29", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 6, "createBy": "v_mlingcai", "createTime": "2022-03-07 15:32:16", "updateBy": "auto", "updateTime": "2022-03-07 15:32:17", "title": "focus权限修改81", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "v_mlingcai", "applyTime": "2022-03-07 15:32:16", "dataId": 81, "reviewUser": "auto", "reviewTime": "2022-03-07 15:32:17", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-07 15:32:17", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 5, "createBy": "v_mlingcai", "createTime": "2022-03-07 15:32:05", "updateBy": "auto", "updateTime": "2022-03-07 15:32:05", "title": "focus权限修改81", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "v_mlingcai", "applyTime": "2022-03-07 15:32:05", "dataId": 81, "reviewUser": "auto", "reviewTime": "2022-03-07 15:32:05", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-07 15:32:05", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 4, "createBy": "tokentest", "createTime": "2022-03-03 14:42:55", "updateBy": "auto", "updateTime": "2022-03-03 14:42:55", "title": "focus权限修改121", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-03 14:42:55", "dataId": 121, "reviewUser": "auto", "reviewTime": "2022-03-03 14:42:55", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-03 14:42:55", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 3, "createBy": "tokentest", "createTime": "2022-03-02 15:01:08", "updateBy": "auto", "updateTime": "2022-03-02 15:01:08", "title": "focus权限修改121", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "tokentest", "applyTime": "2022-03-02 15:01:08", "dataId": 121, "reviewUser": "auto", "reviewTime": "2022-03-02 15:01:08", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-03-02 15:01:08", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 2, "createBy": "v_zhenhaihong", "createTime": "2022-02-28 17:40:30", "updateBy": "auto", "updateTime": "2022-02-28 17:40:30", "title": "Focus配置权限转让", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "v_zhenhaihong", "applyTime": "2022-02-28 17:40:30", "dataId": 53, "reviewUser": "auto", "reviewTime": "2022-02-28 17:40:30", "reviewDesc": "自动审核", "publishUser": "auto", "publishTime": "2022-02-28 17:40:30", "publishDesc": "auto", "status": 1, "publishType": 1, "publishConfig": null}, {"id": 1, "createBy": "v_zhenhaihong", "createTime": "2022-02-28 17:35:19", "updateBy": "auto", "updateTime": "2022-02-28 17:35:19", "title": "Focus配置权限转让", "reviewType": "FocusActivityGroup", "operateType": "update", "applyUser": "v_zhenhaihong", "applyTime": "2022-02-28 17:35:19", "dataId": 53, "reviewUser": "auto", "reviewTime": "2022-02-28 17:35:19", "reviewDesc": "自动审核", "publishUser": "", "publishTime": null, "publishDesc": "", "status": 2, "publishType": 1, "publishConfig": null}], "pageNum": 1, "pageSize": 50, "size": 37, "startRow": 1, "endRow": 37, "pages": 1, "prePage": 0, "nextPage": 0, "isFirstPage": true, "isLastPage": true, "hasPreviousPage": false, "hasNextPage": false, "navigatePages": 8, "navigatepageNums": [1], "navigateFirstPage": 1, "navigateLastPage": 1}, "system_time": "20240531185428", "hj_biz_no": "2405310KA0254263srjvdZKxU82wiunL"}