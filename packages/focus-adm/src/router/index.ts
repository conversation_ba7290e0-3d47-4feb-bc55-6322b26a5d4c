import { createRouter, createWebHistory, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import Home from '../views/Home.vue'
import List from '../views/EditList.vue'
import { userService } from '../service/userService'
import V2Options from '../views/V2Options.vue'
import ReviewList from '../views/ReviewList.vue'
import PubLishPendingList from '../views/PublishPendingList.vue'
import PublishedList from '../views/PublishedList.vue'
import Error from '../views/Error.vue'
import RejectList from '../views/RejectsList.vue'
const routes: Array<RouteRecordRaw> = [
  {
    path: '/landing',
    name: 'landing',
    redirect: '/editList',
  },
  {
    path: '/',
    name: 'Home',
    redirect: '/editList',
  },
  {
    path: '/editList',
    name: '列表页',
    // route level code-splitting
    // this generates a separate chunk (about.[hash].js) for this route
    // which is lazy-loaded when the route is visited.
    component: List,
    meta: {
      edit: true,
    },
  },
  {
    path: '/v2options',
    name: '给v2的操作按钮',
    // route level code-splitting
    // this generates a separate chunk (about.[hash].js) for this route
    // which is lazy-loaded when the route is visited.
    component: V2Options,
    meta: {},
  },
  {
    path: '/reviewlist',
    name: '复核列表',
    component: ReviewList,
    meta: {
      title: '复核列表',
      review: true,
    },
  },
  {
    path: '/publishPendingList',
    name: '复核通过待发布',
    component: PubLishPendingList,
    meta: {
      title: '复核通过待发布',
      review: true,
      publish: true,
    },
  },
  {
    path: '/publishedList',
    name: '已发布列表',
    component: PublishedList,
    meta: {
      title: '已发布列表',
      review: true,
      publish: true,
    },
  },
  {
    path: '/rejectList',
    name: '作废配置列表',
    component: RejectList,
    meta: {
      title: '作废配置列表',
    },
  },
  {
    path: '/error',
    name: '错误页',
    component: Error,
    meta: {
      title: '错误页',
    },
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
})

router.beforeEach((to, from, next) => {
  console.log('🚀 ~ file: main.ts:11 ~ router.beforeEach ~ to, from, next:', to, from, next)
  if (to.path === '/V2Options') {
    return next()
  }
  // next()
  userService.checkUserAuth(to).then((status) => {
    console.log('🚀 ~ file: index.ts:42 ~ userService.checkUserAuth ~ status:', status)
    next()
    // 不校验权限
    // if (status) {
    //   next()
    // } else {
    //   console.error('权限出错啦！')
    //   next('/error')
    // }
  })
})

export default router
