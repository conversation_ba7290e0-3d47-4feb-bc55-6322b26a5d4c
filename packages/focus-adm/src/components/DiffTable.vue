<template>
  <div class="diff-table">
    <el-card class="module-compare" :body-class="'contain'">
      <template #header>
        <el-radio-group v-model="showTableIndex" class="nav">
          <el-radio-button label="0">页面配置</el-radio-button>
          <el-radio-button label="1">组件配置</el-radio-button>
          <el-radio-button label="2">灰度渲染</el-radio-button>
          <el-radio-button label="3">数据源</el-radio-button>
          <el-radio-button label="4">分享内容管理</el-radio-button>
          <!-- <el-radio-button label="5">弹出层</el-radio-button> -->
        </el-radio-group>
        <div class="header">
          <div class="title">字段中文</div>
          <div class="val val1">旧配置({{ oldPageVer }})</div>
          <div class="val val2">新配置({{ pageVer }})</div>
        </div>
      </template>

      <!-- 页面字段 -->
      <div class="item" v-show="showTableIndex === '0'">
        <div
          class="key-item"
          v-for="(keyItem, _index) in pageConfigs"
          :class="keyItem.isSame ? '' : 'not-same'"
          :key="_index"
        >
          <div class="title">{{ keyItem.title }}</div>
          <div class="val va1">
            <el-text>
              {{ keyItem.val1 }}
            </el-text>
          </div>
          <div class="val va2">
            <el-text>
              {{ keyItem.val2 }}
            </el-text>
          </div>
        </div>
      </div>

      <!-- 组件字段对比 -->
      <div class="item" v-for="(item, index) in moduleCompareResult" :key="index" v-show="showTableIndex === '1'">
        <div class="big-title">{{ item.bigTitle }}</div>

        <div
          class="key-item"
          v-for="(keyItem, _index) in item.compares"
          :class="keyItem.isSame ? '' : 'not-same'"
          :key="_index"
        >
          <div class="title">{{ keyItem.title }}</div>
          <div class="val va1">
            <img-box :text="keyItem.val1"></img-box>
          </div>
          <div class="val va2">
            <img-box :text="keyItem.val2"></img-box>
          </div>
        </div>
      </div>

      <!-- 灰度对比 -->
      <div class="item" v-show="showTableIndex === '2'">
        <div
          class="key-item"
          v-for="(keyItem, _index) in grayListCompareResult"
          :class="keyItem.isSame ? '' : 'not-same'"
          :key="_index"
        >
          <div class="title">{{ keyItem.bigTitle }}</div>
          <div class="val va1">
            <el-text>
              {{ keyItem.val1 }}
            </el-text>
          </div>
          <div class="val va2">
            <el-text>
              {{ keyItem.val2 }}
            </el-text>
          </div>
        </div>
      </div>

      <!-- 分享对比 -->
      <div
        class="item"
        v-show="showTableIndex === '4' && shareConfigResult.rtConner && shareConfigResult.rtConner.title"
      >
        <div class="key-item" :class="shareConfigResult.rtConner?.isSame ? '' : 'not-same'">
          <div class="title">{{ shareConfigResult.rtConner && shareConfigResult.rtConner.title }}</div>
          <div class="val va1">
            <el-text>
              {{ shareConfigResult.rtConner && shareConfigResult.rtConner.val1 }}
            </el-text>
          </div>
          <div class="val va2">
            <el-text>
              {{ shareConfigResult.rtConner && shareConfigResult.rtConner.val2 }}
            </el-text>
          </div>
        </div>
      </div>

      <div class="item" v-show="showTableIndex === '4'" v-for="(item, index) in shareConfigResult.configs" :key="index">
        <div class="big-title">{{ item.bigTitle }}</div>

        <div
          class="key-item"
          v-for="(keyItem, _index) in item.compares"
          :class="keyItem.isSame ? '' : 'not-same'"
          :key="_index"
        >
          <div class="title">{{ keyItem.title }}</div>
          <div class="val va1">
            <img-box :text="keyItem.val1"></img-box>
          </div>
          <div class="val va2">
            <img-box :text="keyItem.val2"></img-box>
          </div>
        </div>
      </div>

      <!-- 数据源对比 -->
      <div class="item" v-show="showTableIndex === '3'" v-for="(item, index) in apiListResult" :key="index">
        <div class="big-title">{{ item.bigTitle }}</div>

        <div
          class="key-item"
          v-for="(keyItem, _index) in item.compares"
          :class="keyItem.isSame ? '' : 'not-same'"
          :key="_index"
        >
          <div class="title">{{ keyItem.title }}</div>
          <div class="val va1">
            <el-text>
              {{ keyItem.val1 }}
            </el-text>
          </div>
          <div class="val va2">
            <el-text>
              {{ keyItem.val2 }}
            </el-text>
          </div>
        </div>
      </div>

      <!-- 弹出层对比 -->
      <div class="item" v-show="showTableIndex === '5'" v-for="(_item, index) in popupConfigResult" :key="index">
        <div class="big-title">{{ _item.bigTitle }}</div>

        <div class="item" v-for="(item, index) in _item.modules" :key="index" v-show="showTableIndex === '1'">
          <div class="big-title">{{ item.bigTitle }}</div>

          <div
            class="key-item"
            v-for="(keyItem, _index) in item.compares"
            :class="keyItem.isSame ? '' : 'not-same'"
            :key="_index"
          >
            <div class="title">{{ keyItem.title }}</div>
            <div class="val va1">
              <img-box :text="keyItem.val1"></img-box>
            </div>
            <div class="val va2">
              <img-box :text="keyItem.val2"></img-box>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <el-card class="preview-bar">
      <template #header> 页面预览 {{ pageVer }}</template>
      <!-- <el-button @click="changeQrcodePath">修改二维码链接</el-button> -->
      <review-q-r-code
        :fid="fid"
        v-show="showQrCode"
        :mockDatas="mockDatas"
        :pageType="pageType"
        :customUrl="customUrl"
      ></review-q-r-code>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { EnumListStatus } from '@/service/reviewService'
import TableList from '../components/TableList.vue'
import { ref, defineEmits, defineProps, onMounted, computed, effect, reactive } from 'vue'
import { reviewService } from '@/service/reviewService'
import { ElMessage } from 'element-plus'
import ReviewQRCode from './ReviewQRCode.vue'
import ImgBox from './ImgBox.vue'

const props = defineProps<{
  reviewId: number
  pageType: string
}>()
const customData = ref<any>({})
const reviewData = ref<any>({})
const showTableIndex = ref('0')
const pageConfigs = ref<any>([])
const keyMap = {}
const infoMap = ref({})
const emit = defineEmits(['updateReviewItsmMsg'])

const moduleCompareResult = ref<any[]>([])
const grayListCompareResult = ref<any[]>([])
const shareConfigResult = ref<{
  rtConner?: any
  configs?: Array<any>
}>({})

const fid = ref(0)
const showQrCode = ref(false)
const pageVer = ref('')
const oldPageVer = ref('')
const apiListResult = ref<any[]>([])
const popupConfigResult = ref<any[]>([])
const customUrl = ref('')
const mockDatas = reactive<{
  mockConditions: Array<any>
  mockGrayIds: Array<{ id: string; text: string }>
}>({
  mockConditions: [],
  mockGrayIds: [],
})

onMounted(() => {
  setTimeout(() => {
    showQrCode.value = true
  }, 500)
})
setTimeout(() => {
  getData()
}, 500)

function getData() {
  reviewService.getReviewData(props.reviewId).then((res: any) => {
    console.log('🚀 ~ file: DiffTable.vue:19 ~ reviewService.getReviewData ~ res:', res)
    const { curCustomConfig = {}, reviewConfig = {}, customVer = '--' } = res
    console.log('🚀 ~ reviewService.getReviewData ~ customVer:', customVer)
    // changeQrcodePath(reviewConfig.id)
    fid.value = reviewConfig.id
    pageVer.value = `V${(reviewConfig.customerVer || 0) + 1}.${reviewConfig.ver}`
    console.log('🚀 ~ reviewService.getReviewData ~  pageVer.value:', pageVer.value)
    oldPageVer.value = `V${customVer}`
    customData.value = parseData(curCustomConfig)
    reviewData.value = parseData(reviewConfig)

    readyCompareData(customData.value, reviewData.value)

    updateReviewMsg()
  })
}

function updateReviewMsg() {
  console.log(reviewData.value)
  const pageData = (reviewData.value.config && reviewData.value.config[0]) || {}
  customUrl.value = pageData.data.customUrl || ''
  let baseHost = 'https://m.webank.com'
  if (BUILD_MODE !== 'prod') {
    baseHost = 'https://m.test.webank.com'
  }
  let reviewLink = `${baseHost}/s/hj/focus2/preview/index.html?fid=${reviewData.value.id}&focus_review=1&mockTime=0`

  if (customUrl.value) {
    if (customUrl.value.indexOf('#/') > -1) {
      const [p, hash] = customUrl.value.split('#/')
      reviewLink = `${p}?fid=${reviewData.value.id}&focus_review=1=1&mockTime=0#/${hash}`
    } else {
      reviewLink = `${customUrl.value}?fid=${reviewData.value.id}&focus_review=1=1&mockTime=0`
    }
  }

  const pageLink = customUrl.value
    ? customUrl.value
    : `${baseHost}/s/hj/focus2/client/index.html?fid=${reviewData.value.id}`

  emit('updateReviewItsmMsg', {
    fid: reviewData.value.id,
    desc: reviewData.value.desc,
    oldPageVer: oldPageVer.value,
    pageVer: pageVer.value,
    loginType: pageData.data.needLogin === '1' ? '活动页' : '静态页',
    reviewLink,
    pageLink,
  })
}

function parseGrayList(grayList: any[], isNew?: boolean) {
  console.log('🚀 ~ parseGrayList ~ grayList:', grayList)
  if (!grayList.some((i) => i.useGrayType === '1')) {
    return {}
  }
  let result = {}
  grayList.forEach((i) => {
    const { id = '', tagIds = [] } = i

    let title = `满足通用灰度ID[${id}]`
    if (id === '0') {
      title = '未登录'
    } else if (id === '1') {
      title = '未满足通用灰度'
    }

    if (isNew) {
      mockDatas.mockGrayIds.push({
        id: id === '0' ? '0000' : id,
        text: title,
      })
    }
    let hideModules = '隐藏的组件ID有：' + (tagIds[0] && tagIds[0].hideModules && tagIds[0].hideModules.join(','))
    result[id] = {
      title,
      val: hideModules,
    }
  })
  console.log('🚀 ~ parseGrayList ~ result:', result)

  return result
}

function parseData(data: any) {
  const result: any = {}

  Object.keys(data).forEach((k: string) => {
    let d = data[k]
    if (typeof d === 'string') {
      try {
        d = JSON.parse(d)
        console.log('🚀 ~ JSONparse:', d)
      } catch (err) {
        console.log('🚀 ~ file: DiffTable.vue:60 ~ parse ~ err:', err)
      }
    }

    console.log('🚀 ~ file: DiffTable.vue:58 ~ Object.keys ~ d:', d)
    result[k] = d
  })

  return result
}

function readyCompareData(oldData: any, newData: any) {
  console.log('🚀 ~ file: DiffTable.vue:64 ~ readyCompareData ~ newData:', newData)
  console.log('🚀 ~ file: DiffTable.vue:64 ~ readyCompareData ~ oldData:', oldData)
  const {
    config = [],
    popupConfig = [],
    shareConfig = {},
    grayList = [],
    apiList = {},
    id,
    aid,
    desc,
    department,
  } = newData

  parsePageConfig(newData, oldData)

  const newConfigParsed = parseModules(config, true)
  console.log('🚀 ~ file: DiffTable.vue:106 ~ readyCompareData ~ newConfigParsed:', newConfigParsed)
  const oldConfigParsed = parseModules(oldData.config || [])
  console.log('🚀 ~ file: DiffTable.vue:108 ~ readyCompareData ~ oldConfigParsed:', oldConfigParsed)
  const compareConfigResult = parseCompareConfig(newConfigParsed, oldConfigParsed)
  moduleCompareResult.value = compareConfigResult

  grayListCompareResult.value = compareGrayList(parseGrayList(grayList, true), parseGrayList(oldData.grayList || []))

  shareConfigResult.value = compareShareConfig(shareConfig || {}, oldData.shareConfig || {})

  apiListResult.value = compareApiList(apiList || [], oldData.apiList || [])

  popupConfigResult.value = comparePopup(popupConfig || [], oldData.popupConfig || [])
}

function comparePopup(newData: any[], oldData: any[]) {
  console.log('🚀 ~ comparePopup ~ oldData:', oldData)
  console.log('🚀 ~ comparePopup ~ newData:', newData)
  const allList = new Array(10).fill(0)

  return allList.map((i, index) => {
    return {
      bigTitle: `弹出层-${index + 1}`,
      modules: [
        {
          val1: 0,
          val2: 0,
          isSame: true,
        },
      ],
    }
  })
}

function compareApiList(newData: any[], oldData: any[]) {
  console.log('🚀 ~ compareApiList ~ oldData:', oldData)
  console.log('🚀 ~ compareApiList ~ newData:', newData)
  const names = {
    _api_09: '产品列表接口',
  }
  const apiParams = {
    codes: '产品Code',
  }
  const list = new Array(oldData.length > newData.length ? oldData.length : newData.length).fill(0)

  const result: any[] = list.map((i, index) => {
    let d1 = oldData[index] || {}
    let d2 = newData[index] || {}
    const bigTitle = `接口-${index + 1}`
    const name1 = d1.apiKey ? names[d1.apiKey] : ''
    const name2 = d2.apiKey ? names[d2.apiKey] : ''
    const nameIsSame = name1 === name2
    const compares: any = []
    compares.push({
      title: '接口名',
      val1: name1,
      val2: name2,
      isSame: nameIsSame,
    })
    const params1 = d1.apiParams || {}
    const params2 = d2.apiParams || {}

    Object.keys(apiParams).forEach((k) => {
      const val1 = params1[k] || []
      const val2 = params2[k] || []
      const isSame = JSON.stringify(val1) === JSON.stringify(val2)

      compares.push({
        title: `参数-${k}`,
        val1,
        val2,
        isSame,
      })
    })

    return {
      bigTitle,
      compares,
    }
  })
  console.log('🚀 ~ constresult:any[]=list.map ~ result:', result)
  return result
}

function compareShareConfig(
  newData: {
    base:
      | {
          rtConner: '1'
        }
      | ''
    configList: Array<{
      appShareType: string
      desc: string
      imgUrl: string
      miniUserName: string
      path: string
      scenes: string
      shareImageBg: string
      shareImageHeight: string
      title: string
      mgmAidV2: string
    }>
  },
  oldData: {
    base:
      | {
          rtConner: '1'
        }
      | ''
    configList: Array<{
      appShareType: string
      desc: string
      imgUrl: string
      miniUserName: string
      path: string
      scenes: string
      shareImageBg: string
      shareImageHeight: string
      title: string
      mgmAid: string
      mgmAidV2: string
    }>
  },
) {
  console.log('🚀 ~ compareShareConfig ~ oldData:', oldData)
  console.log('🚀 ~ compareShareConfig ~ newData:', newData)
  const parseShare = (shareData: {
    base:
      | {
          rtConner: '1'
        }
      | ''
    configList: Array<{
      appShareType: string
      desc: string
      imgUrl: string
      miniUserName: string
      path: string
      scenes: string
      shareImageBg: string
      shareImageHeight: string
      title: string
      mgmAid: string
      mgmAidV2: string
    }>
  }) => {
    console.log('🚀 ~ parseShare ~ shareData:', shareData)
    const { base, configList = [] } = shareData
    const result: any = {}

    if (base && base.rtConner) {
      const title = `右上角分享`
      const val = `分享配置-${base.rtConner}`
      result.rtConner = {
        title,
        val,
      }
    } else {
      const title = `右上角分享`
      const val = `不分享`
      result.rtConner = {
        title,
        val,
      }
    }

    const configListMap: any = {
      title: '分享标题',
      desc: '分享内容',
      imgUrl: '分享图片',
      mgmAid: '分享活动ID',
      mgmAidV2: '新MGMID',
      appShareType: {
        title: 'App分享面板',
        stringMap: {
          '0': '拉起分享面板[微信好友+微信朋友圈]',
          '1': '分享H5-拉起分享面板[微信好友]',
          '2': '分享H5-拉起分享面板[微信朋友圈]',
          '3': '分享H5-直接分享[微信好友]',
        },
      },

      miniUserName: {
        title: '分享类型',
        stringMap: {
          H5: '微信H5活动页',
          'gh_acb373d81795-release': 'WE2000小程序卡片-生产包',
          'gh_acb373d81795-preview': 'WE2000小程序卡片-体验包（N环境）',
          'gh_f7447a1fcd79-preview': '零花钱小程序卡片-体验包（M1环境）',
        },
      },

      shareImageHeight: 'App图片分享-图片高度',

      shareImageBg: 'App图片分享-背景图片',
    }

    configList.forEach((i, index) => {
      Object.keys(configListMap).forEach((key) => {
        const infoVal = configListMap[key]
        const k = `${index}_${key}`
        if (typeof infoVal === 'string') {
          result[k] = {
            key,
            title: infoVal,
            val: i[key],
          }
        } else if (infoVal.stringMap) {
          const title = infoVal.title

          const data = {
            key,
            title,
            val: infoVal.stringMap[i[key]],
          }
          result[k] = data
        }
      })
    })

    console.log('🚀 ~ result:', result)
    return result
  }

  const _newData = parseShare(newData)
  console.log('🚀 ~ compareShareConfig ~ _newData:', _newData)
  const _oldData = parseShare(oldData)
  console.log('🚀 ~ compareShareConfig ~ _oldData:', _oldData)

  const list = Array.from(new Set(Object.keys(_newData).concat(Object.keys(_oldData))))
  console.log('🚀 ~ list:', list)
  let result: any = {
    rtConner: {},
    configs: [],
  }
  list.map((key) => {
    if (key === 'rtConner') {
      const d1 = _oldData[key] || {}
      const d2 = _newData[key] || {}
      const val1 = d1.val || ''
      const val2 = d2.val || ''
      const isSame = val1 === val2
      const title = d1.title || d2.title
      result.rtConner = {
        title,
        isSame,
        val1,
        val2,
      }
    } else {
      let kArr = key.split('_') || []
      const k1 = kArr[0] || 0
      const k2 = kArr[1] || ''

      if (k1 && k2) {
        const bigTitle = `分享配置-${k1 + 1}`
        if (!result.configs[k1]) {
          result.configs[k1] = {
            bigTitle,
            compares: [],
          }
        }
        const d1 = _oldData[key] || {}
        console.log('🚀 ~ list.map ~ d1:', d1)
        const d2 = _newData[key] || {}
        console.log('🚀 ~ list.map ~ d2:', d2)
        const val1 = d1.val
        const val2 = d2.val
        const title = d1.title || d2.title
        const isSame = val1 === val2
        result.configs[k1].compares.push({
          title,
          isSame,
          val1,
          val2,
        })
      }
    }
  })
  return result
}

function compareGrayList(newData = {}, oldData = {}) {
  const keys = Array.from(new Set(Object.keys(newData).concat(Object.keys(oldData))))

  const result = keys.map((k) => {
    const d1 = oldData[k] || {}
    const d2 = newData[k] || {}
    const bigTitle = d2.title || d1.title
    const val1 = d1.val
    const val2 = d2.val
    const isSame = val1 === val2
    return {
      bigTitle,
      val1,
      val2,
      isSame,
    }
  })
  return result
}

function parsePageConfig(newData: any, oldData: any) {
  const infoMap = {
    id: '配置ID(fid)',
    aid: '活动ID(aid)',
    desc: '页面描述',
    department: '页面分类',
  }

  const resultInfo: any = []

  Object.keys(infoMap).forEach((k) => {
    const data = {}
    const val1 = oldData[k] || ''
    const val2 = newData[k] || ''
    let isSame = false
    const title = infoMap[k]
    if (val1 === val2) {
      isSame = true
    }
    resultInfo.push({
      key: k,
      title,
      isSame,
      val1,
      val2,
    })
  })

  let pageDataNew = newData.config.find((i: any) => i.moduleId === 1) || {}
  pageDataNew = { ...(pageDataNew.data || {}), ...(pageDataNew.styles || {}) }
  console.log('🚀 ~ file: DiffTable.vue:189 ~ parsePageConfig ~ pageDataNew:', pageDataNew)

  let pageDataOld = (oldData.config && oldData.config.find((i: any) => i.moduleId === 1)) || {}
  pageDataOld = (pageDataOld && { ...(pageDataOld.data || {}), ...(pageDataOld.styles || {}) }) || {}
  console.log('🚀 ~ file: DiffTable.vue:191 ~ parsePageConfig ~ pageDataOld:', pageDataOld)

  const pageInfoMap = {
    pageTitle: '页面标题',
    bg: '页面背景颜色',
    needLogin: {
      title: '页面类型',
      stringMap: {
        '0': '静态页',
        '1': '活动页',
      },
    },
    autoJoinAid: '自动参与活动',
    forceLoginInApp: {
      title: '在App未登录时访问',
      stringMap: {
        '0': '不跳登录页，直接访问',
        '1': '跳登录页',
      },
    },
    setM2: {
      title: 'M2访问时建立邀请关系',
      objMap: {
        stuats: {
          title: '接受状态',
          '0': '不建立邀请关系',
          '1': '自动接收邀请关系',
        },
        mgmAid: 'MGM活动ID',
        isParent: '是否是爸妈版专用接受邀请关系',
      },
    },

    startTime: '页面可访问时间-开始',
    endTime: '页面可访问时间-结束',
  }

  Object.keys(pageInfoMap).map((key) => {
    const infoVal = pageInfoMap[key]
    if (typeof infoVal === 'string') {
      const val1 = pageDataOld[key] || ''
      const val2 = pageDataNew[key] || ''
      const isSame = val1 === val2
      resultInfo.push({
        key,
        title: infoVal,
        isSame,
        val1,
        val2,
      })
    } else {
      if (infoVal.stringMap) {
        const title = infoVal.title
        const d1 = pageDataOld[key]
        const d2 = pageDataNew[key]
        const val1 = infoVal.stringMap[d1] || ''
        const val2 = infoVal.stringMap[d2] || ''
        const isSame = val1 === val2
        const data = {
          key,
          title,
          isSame,
          val1,
          val2,
        }
        resultInfo.push(data)
      } else if (infoVal.objMap) {
        let title = infoVal.title
        let d1: any = pageDataOld[key] || ''
        let d2: any = pageDataNew[key] || ''
        if (typeof d2 === 'string') {
          try {
            d1 = JSON.parse(d1) || {}
            d2 = JSON.parse(d2) || {}
          } catch (err) {
            console.log('🚀 ~ file: DiffTable.vue:264 ~ Object.keys ~ err:', err)
          }
        }
        Object.keys(infoVal.objMap).forEach((_key) => {
          let val1 = ''
          let val2 = ''
          let isSame = false
          let t = ''
          let k = `${key}.${_key}`
          if (typeof infoVal.objMap[_key] === 'string') {
            t = `${title}_${infoVal.objMap[_key]}`
            val1 = d1[_key] || ''
            val2 = d2[_key] || ''
          } else {
            t = `${title}_${infoVal.objMap[_key].title}`
            val1 = infoVal.objMap[_key][d1] || ''
            val2 = infoVal.objMap[_key][d2] || ''
          }
          isSame = val1 === val2
          resultInfo.push({
            key: k,
            title: t,
            isSame,
            val1,
            val2,
          })
        })
      }
    }
  })

  pageConfigs.value = resultInfo
}

function parseCompareConfig(_newData: any, _oldData: any) {
  console.log('🚀 ~ parseCompareConfig ~ _newData:', _newData)
  let result: any = []
  const allModuleKeys = Array.from(new Set(Object.keys(_newData).concat(Object.keys(_oldData))))
    .map((i) => Number(i))
    .sort()
  console.log('🚀 ~ parseCompareConfig ~ allModuleKeys:', allModuleKeys)
  result = allModuleKeys.map((mId) => {
    const dataNew = _newData[mId] || {}
    console.log('🚀 ~ result=allModuleKeys.map ~ dataNew:', dataNew)
    const dataOld = _oldData[mId] || {}
    console.log('🚀 ~ file: DiffTable.vue:122 ~ result=allModuleKeys.map ~ dataOld:', dataOld)
    const keys = ['moduleId', 'moduleName', 'moduleTitle', 'compareSttyles', 'compareDatas']
    // const allKeys: any = Array.from(new Set([dataNew.compareKyes?.concat(dataOld.compareKyes || []])))
    const allKeys: any = Array.from(
      new Set([
        ...(Array.isArray(dataNew?.compareKyes) ? dataNew.compareKyes : []),
        ...(Array.isArray(dataOld?.compareKyes) ? dataOld.compareKyes : []),
      ]),
    )
    console.log('🐬 ~ result=allModuleKeys.map ~ allKeys:', allKeys)
    const compares: any = []
    let bigTitle = dataNew.moduleTitle || dataOld.moduleTitle
    allKeys.forEach((k: string) => {
      const d1 = (dataOld.compares && dataOld.compares[k]) || {}
      const d2 = (dataNew.compares && dataNew.compares[k]) || {}
      const { val: val1 = '' } = d1
      const { val: val2 = '' } = d2
      const isSame = val1 === val2
      compares.push({
        title: d1.title || d2.title || '',
        isSame,
        key: k,
        val1,
        val2,
      })
    })
    console.log('🐬 ~ result=allModuleKeys.map ~ compares:', compares)
    return {
      bigTitle,
      compares,
    }
  })
  console.log('🚀 ~ file: DiffTable.vue:144 ~ result=allModuleKeys.map ~ result:', result)
  return result
}

function parseModules(config: Array<any>, isNew?: boolean) {
  const result = {}
  config.forEach((i: any) => {
    if (i.moduleId === 1) {
      return
    }
    const compareStyles = {}
    const { moduleId, data, moduleName, pid, styles = {}, moduleType, moduleSubType } = i
    const compareDatas = {
      pid: {
        title: '父级ID',
        val: pid,
      },
    }
    console.log('🚀 ~ file: DiffTable.vue:108 ~ config.forEach ~ i:', i)
    const moduleTitle = `组件【${moduleName}】-${moduleId}`
    const condition: any = {}
    Object.keys(data).forEach((dataKey: any) => {
      let dataMap: any = {
        title: '标题',
        text: '文字',
        aid: '活动ID',
        grayTexts: '置灰文本',
        colorMode: {
          title: '使用标准文字颜色',
          stringMap: {
            '0': '不使用',
            '1': '使用',
          },
        },
        imgUrl: '图片地址',
        direction: {
          title: '子元素排列方向',
          stringMap: {
            row: '从左往右',
            column: '从上往下',
          },
        },
        flex: '子元素对齐方式',
        boxh: {
          title: '盒子高度控制',
          stringMap: {
            '0': '固定高度&宽度',
            '1': '动态高度&宽度-由子元素撑开高度&宽度',
            '2': '动态高度&固定宽度-由子元素撑开高度，宽度固定不变',
          },
        },
        dialog: '----',
      }
      const clickEvent = {
        title: '点击效果',
        fnMap: (dataKey: any) => {
          console.log('.....dataKey', dataKey)
          const { path, query = {}, jumpType, effect, popid, miniUsername, miniEnv } = dataKey || {}
          const alias = {
            effect: '触发效果',
            path: '跳转路径',
            jumpType: '跳转类型',
            query: '额外参数',
            miniUsername: '小程序原始名',
            appId: '小程序AppId',
            miniEnv: '小程序包环境',
          }
          Object.keys(alias).map((k) => {
            let val = dataKey[k]
            if (typeof val === 'object') {
              val = JSON.stringify(val)
            }

            const title = alias[k]
            const key = `clickEvent.${k}`
            if (k === 'effect') {
              const effectMaps = {
                none: '不跳转',
                jump: '跳转',
                officialAccount: '引导关注公众号',
                calendarAlarm: '日历提醒',
                popup: '展示弹出层',
              }
              val = effectMaps[dataKey[k]] || dataKey[k]
            }

            if (k === 'jumpType') {
              const jumpTypeMaps = {
                url: '跳APP网页',
                wxUrl: '跳微信网页',
                appModule: '跳APP模块',
                productCode: '跳APP产品页',
                mini: '跳小程序',
                custom: '开发自定义',
              }
              val = jumpTypeMaps[dataKey[k]] || dataKey[k]
            }

            compareDatas[key] = {
              title,
              val,
            }
          })
        },
      }
      if (moduleType === 'BtnClick') {
        dataMap = {
          ...dataMap,
          uiType: {
            title: '按钮UI类型',
            stringMap: {
              text: '文字按钮',
              img: '图片按钮',
            },
          },
          clickEvent,
        }
      }

      if (moduleType === 'BtnDownload') {
        dataMap = {
          ...dataMap,
          androidLink: '默认安卓下载链接',
          iosLink: '默认ios链接',

          links: {
            title: '渠道链接',
            fnMap: (data: string) => {
              let arr = []
              try {
                arr = JSON.parse(data) || []
              } catch (err) {
                console.log('🚀 ~ Object.keys ~ err:', err)
              }
              arr.forEach((i: { channel: string; androidLink: string; iosLink: string }, index: number) => {
                const alias = {
                  channel: '下载渠道',
                  androidLink: '安卓下载链接',
                  iosLink: 'ios下载链接',
                }

                const title = `渠道_${index + 1}_${i.channel || '无'}`
                const key = `channel.${index}.links`
                const val = `安卓渠道：${i.androidLink}；iOS渠道：${i.iosLink}`
                compareDatas[key] = {
                  title,
                  val,
                }
              })
            },
          },
        }
      }

      if (moduleType === 'BtnAcceptShare') {
        dataMap = {
          ...dataMap,
          mgmAid: '接受邀请关系MgmId',
          imgUrl1: '按钮图片地址',
          functionType: 'MGM类型',
        }
      }

      if (moduleType === 'LogoBottom') {
        dataMap = {
          ...dataMap,
          imgUrl: '图片地址',
        }
      }

      if (moduleType === 'AnchorNav') {
        dataMap = {
          ...dataMap,
          topping: {
            title: '是否置顶',
            stringMap: {
              true: '自动吸顶',
              false: '保持原位',
            },
          },
          listData: {
            title: '锚点图片',
            fnMap: () => {
              let key = `data.${dataKey}`

              title = dataMap[dataKey].title || `${key}还没翻译中文`
              val = data[dataKey]
              try {
                val = JSON.parse(val)
              } catch (err) {
                console.log('🚀 ~ Object.keys ~ err:', err)
              }
              console.log('🚀 ~ Object.keys ~ val:', val)
              val.forEach((i: any, index: number) => {
                console.log('🚀 ~ val.forEach ~ i:', i)
                let title = `锚点图片_${index + 1}`
                const { taskId, imgUrl1, imgUrl2 } = i
                const t1 = title + '_目标组件ID'
                const val1 = taskId
                const key1 = `${key}.${index}.taskId`
                compareDatas[key1] = {
                  title: t1,
                  val: val1,
                }

                const t2 = title + '_未激活图片'
                const val2 = imgUrl1
                const key2 = `${key}.${index}.imgUrl1`

                compareDatas[key2] = {
                  title: t2,
                  val: val2,
                }

                const t3 = title + '_激活图片'
                const val3 = imgUrl2
                const key3 = `${key}.${index}.imgUrl2`

                compareDatas[key3] = {
                  title: t3,
                  val: val3,
                }
              })
              console.log('.....,compare,', compareDatas)
              // data[dataKey].forEach(i=>{

              // })
            },
          },
        }
      }

      if (moduleType === 'MVideo') {
        dataMap = {
          ...dataMap,
          title: '视频标题',
          videoUrl: '视频链接',
          autoPlay: '是否自动播放',
        }
      }

      if (moduleType === 'BtnMGM') {
        dataMap = {
          ...dataMap,
          imgUrl1: '未开户按钮图片',
          imgUrl2: '已开户按钮图片',
          imgUrl3: '禁用状态按钮图片',
          configId: '分享配置ID',
          fromAid: '----',
          joinAid: '同时参与活动',
          noAccountCanShare: '未开户分享',
        }
      }

      if (moduleType === 'BtnAdding') {
        dataMap = {
          ...dataMap,
          uiType: {
            title: '按钮类型',
            stringMap: {
              text: '文字按钮',
              img: '图片按钮',
            },
          },
          joinType: {
            title: '是否可点击参与',
            stringMap: {
              joinNow: '可点击触发参与活动',
              noJoinNow: '不可以被点击触发参与活动，只能显示参与状态',
            },
          },
          text: '未参与时显示文案',
          imgUrl: '未参与时图片链接',
          isJumpNow: {
            title: '未参与跳转类型',
            '0': '立即跳转',
            '1': '下次跳转',
          },
          once: {
            title: '触发次数',
            stringMap: {
              '0': '每次点击参与按钮都触发效果',
              '1': '仅首次参与成功触发效果',
            },
          },
          success: {
            title: '参与成功后',
            fnMap: (d: string) => {
              let a: any = d
              try {
                a = JSON.parse(d)
              } catch (err) {
                console.log('🚀 ~ Object.keys ~ err:', err)
              }
              const keys = ['imgUrl', 'text', 'bg', 'font']
              const alias = {
                imgUrl: '参与成功后_按钮图片地址',
                text: '参与成功后_按钮文字',
                bg: '参与成功后_按钮背景颜色',
                font: '参与成功后_按钮文字样式',
              }
              keys.forEach((k) => {
                const val = a[k]
                const title = alias[k]
                const key = `data.success.${k}`
                compareDatas[key] = {
                  val,
                  title,
                }
              })
            },
          },
          clickEvent,
        }
      }

      if (moduleType === 'SwiperBox') {
        dataMap = {
          ...dataMap,
          uiType: {
            title: 'UI类型',
            stringMap: {
              '0': '下标点',
              '1': '顶部卡片锚点',
            },
          },
          autoPlay: {
            title: '自动滚动',
            stringMap: {
              '0': '下标点模式-开启自动滚动',
              '1': '下标点模式-关闭自动滚动',
            },
          },
          dotColor: '下标点颜色',
          cardHeight: '顶部卡片高度',
          cardWidth: '顶部卡片宽度',
          cardImgs: {
            title: '顶部卡片图片',
            fnMap: (d: any) => {
              console.log('🚀 ~ Object.keys ~ d:', d)
              if (!d) {
                return
              }
              let arr = d
              try {
                arr = JSON.parse(d) || []
              } catch (err) {
                console.log('🚀 ~ Object.keys ~ err:', err)
              }
              arr.forEach((i: any, index: number) => {
                const key1 = `data.cardImgs.${index}.imgUrl1`
                const key2 = `data.cardImgs.${index}.imgUrl2`
                const title1 = `顶部卡片图片_${index + 1}_未激活图片`
                const title2 = `顶部卡片图片_${index + 1}_已激活图片`
                const val1 = i.imgUrl1
                const val2 = i.imgUrl2
                compareDatas[key1] = {
                  title: title1,
                  val: val1,
                }
                compareDatas[key2] = {
                  title: title2,
                  val: val2,
                }
              })
            },
          },
        }
      }

      if (moduleSubType === 'conditionBox') {
        console.log('>>>>>>>>>>>>>>>>>')
        console.log(`data.${dataKey}`)
        dataMap = {
          ...dataMap,
          conditionType: {
            title: '渲染条件_条件类型',
            stringMap: {
              dc09: 'dc09指定产品Code可售',
              dc02: 'dc02开户状态',
              dc01: 'dc01关注公众号状态',
              dc07: 'dc07是否在App环境打开',
              dc06: 'dc06是否是使用iOS设备',
              dc03: 'dc03是否满足指定灰度ID',
            },
            // fnMap: (d: any) => {
            //   console.log('🚀 ~ Object.keys ~ d:', d)

            //   return d
            // },
          },
          conditionList: {
            title: '渲染条件_组件配置',
            fnMap: (d: any) => {
              let _d = d
              console.log('>>>>>>|||||', _d)
              let key = `data.${dataKey}`
              title = dataMap[dataKey].title || `${key}还没翻译中文`
              val = _d
                .map((i: any) => {
                  const { conditionId, moduleIds = [] } = i
                  let s = `${conditionId === 1 ? '不满足条件' : '满足条件'}`
                  let mod = '显示组件ID：' + moduleIds.join(',')
                  return s + mod
                })
                .join(' ; ')
              console.log('🚀 ~ Object.keys ~ val:', val)
              compareDatas[key] = {
                title,
                val,
              }
              // _d &&
            },
          },
          conditionApiKey: '----', //要废弃的参数，不展示
          extendStr: '渲染条件_附加参数',
        }
      }

      if (moduleType === 'InputShareCode') {
        dataMap = {
          ...dataMap,
          colorInputBox: '输入框颜色',
          colorPlaceHolder: '提示词颜色',
          bgBtn: '按钮背景颜色', // 支持渐变，用逗号隔开,仅限2个颜色
          colorBtnText: '按钮文本颜色',
        }
        console.log('🚀 ~ Object.keys ~ dataMap:', dataMap)
      }

      if (moduleType === 'FundCoupon') {
        dataMap = {
          ...dataMap,
          tip: '组件小知识',
          claimBtnBg: '一键领取按钮',
          popupTip: '弹窗小知识',
          listData: {
            title: '卡券列表',
            fnMap: () => {
              let key = `data.${dataKey}`

              title = dataMap[dataKey].title || `${key}还没翻译中文`
              val = data[dataKey]
              try {
                val = JSON.parse(val)
              } catch (err) {
                console.log('🚀 ~ Object.keys ~ err:', err)
              }
              console.log('🚀 ~ Object.keys ~ val:', val)
              val.forEach((i: any, index: number) => {
                console.log('🚀 ~ val.forEach ~ i:', i)
                let title = `券列表_${index + 1}`

                const titles: any = {
                  couponId: '卡券ID',
                  btnBg1: '按钮领取前',
                  btnBg2: '按钮领取后',
                  btnText2: '按钮领取后文案',
                  clickEvent: '点击跳转',
                }
                Object.keys(i).forEach((k) => {
                  let keyName = `${key}.${index}.${k}`
                  let val = i[k]
                  let t = title + '_' + titles[k]

                  if (k === 'clickEvent') {
                    let dataKey: any = i[k]
                    console.log('🚀 ~ Object.keys ~ i[k]:', i[k])

                    console.log('🚀 ~ Object.keys ~ dataKey:', dataKey)
                    const alias = {
                      effect: '触发效果',
                      path: '跳转路径',
                      jumpType: '跳转类型',
                      query: '额外参数',
                      miniUsername: '小程序原始名',
                      appId: '小程序AppId',
                      miniEnv: '小程序包环境',
                    }
                    Object.keys(alias).map((clickEventKey) => {
                      let val = dataKey[clickEventKey]
                      if (typeof val === 'object') {
                        val = JSON.stringify(val)
                      }

                      const title = t + '_' + alias[clickEventKey]
                      const key = `${keyName}.${clickEventKey}`
                      if (clickEventKey === 'effect') {
                        const effectMaps = {
                          none: '不跳转',
                          jump: '跳转',
                          officialAccount: '引导关注公众号',
                          calendarAlarm: '日历提醒',
                          popup: '展示弹出层',
                        }
                        val = effectMaps[dataKey[clickEventKey]] || dataKey[clickEventKey]
                      }

                      if (clickEventKey === 'jumpType') {
                        const jumpTypeMaps = {
                          url: '跳APP网页',
                          wxUrl: '跳微信网页',
                          appModule: '跳APP模块',
                          productCode: '跳APP产品页',
                          mini: '跳小程序',
                          custom: '开发自定义',
                        }
                        console.log('🚀 ~ Object.keys ~ dataKey[clickEventKey]:', dataKey[clickEventKey])

                        val = jumpTypeMaps[dataKey[clickEventKey]] || dataKey[clickEventKey]
                      }

                      compareDatas[key] = {
                        title,
                        val,
                      }
                    })
                  } else {
                    compareDatas[keyName] = {
                      title: t,
                      val: val,
                    }
                  }
                })
              })
              console.log('.....,compare,', compareDatas)
            },
          },
        }
      }

      let val = data[dataKey]
      let key = `data.${dataKey}`
      let title = dataMap[dataKey] || `${key}还没翻译中文`

      console.log('🚀 ~ Object.keys ~ title:', title)
      if (dataKey === 'extendStr') {
        condition.extendStr = data[dataKey]
      }

      if (title === '----') {
        return
      }

      if (typeof title === 'object' && typeof dataMap[dataKey].fnMap === 'function') {
        dataMap[dataKey].fnMap(data[dataKey])
        return
        // if (dataMap[dataKey].isListData) {
        //   return
        // } else {
        //   title = dataMap[dataKey].title || `${key}还没翻译中文`
        //   val = dataMap[dataKey].fnMap(data[dataKey])
        //   compareDatas[key] = {
        //     title,
        //     val,
        //   }
        // }
      } else {
        if (typeof title === 'object') {
          title = dataMap[dataKey].title || `${key}还没翻译中文`
          if (dataKey === 'conditionType') {
            condition.key = val
          }
          if (typeof dataMap[dataKey].stringMap === 'object') {
            val = dataMap[dataKey].stringMap[val]
            if (dataKey === 'conditionType') {
              condition.title = val
            }
          }
        }
        console.log('🚀 ~ file: DiffTable.vue:370 ~ Object.keys ~ title:', title)
        compareDatas[key] = {
          title,
          val,
        }
      }
    })

    console.log('condition--------', condition)
    if (isNew && Object.keys(condition).length) {
      mockDatas.mockConditions.push(condition)
    }

    Object.keys(styles).forEach((styleKey) => {
      const styleMaps = {
        borderRadius: '圆角',
        font: '文字样式',
        height: '高度',
        margin: '边距',
        position: {
          title: '绝对定位位置',
          stringMap: {
            none: '',
          },
        },
        width: '宽度',
        bg: '背景颜色',
        time: '组件可见时间',
      }
      let val = styles[styleKey]
      let key = `styles.${styleKey}`
      let title = styleMaps[styleKey] || `${key}还没翻译中文`
      if (typeof title === 'object') {
        title = styleMaps[styleKey].title || `${key}还没翻译中文`
        val = styleMaps[styleKey].stringMap[val]
      }
      compareStyles[key] = {
        title,
        val,
      }
    })

    result[moduleId] = {
      moduleTitle,
      moduleId,
      moduleName,
      compareKyes: Object.keys(compareStyles).concat(Object.keys(compareDatas)),
      compares: {
        ...compareDatas,
        ...compareStyles,
      },
    }
  })
  return result
}
</script>

<style scoped lang="scss">
.diff-table {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  height: 670px;
  .header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    line-height: 1.5;
    font-size: 16px;
    text-align: center;
    font-weight: bold;
    margin-top: 10px;
    margin-bottom: -10px;
    .title {
      width: 200px;
      flex: none;
      border-right: 1px solid #eee;
    }
    .val {
      width: calc(50% - 100px);
      flex: auto;
      text-align: center;
      box-sizing: border-box;
    }
    .val1 {
      border-right: 1px solid #eee;
    }
  }
  .preview-bar {
    width: 100%;
    flex: auto;
    margin-left: 20px;
  }
  .module-compare {
    width: 70%;
    height: 95%;
    flex: none;
    overflow: hidden;
    :deep(.contain) {
      height: 85%;
      width: 100%;
      box-sizing: border-box;
      overflow-y: scroll;
    }
    .item {
      border-bottom: 1px solid #eee;
      margin-bottom: 20px;
      width: 100%;
      overflow: hidden;
      &:last-child {
        border: none;
      }
      .big-title {
        font-weight: bold;
        padding: 5px;
        font-size: 18px;
        background: #eee;
      }
      .key-item {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        border-bottom: 1px solid #eee;
        line-height: 1.5;
        &.not-same {
          background: rgb(255, 151, 151);
        }

        .title {
          width: 200px;
          flex: none;
          border-right: 1px solid #eee;
        }
        .val {
          width: calc(50% - 100px);
          flex: auto;
          text-align: center;
          box-sizing: border-box;
          padding: 0 10px;
        }
        .val1 {
          border-right: 1px solid #eee;
        }
      }
    }
  }
}
</style>
