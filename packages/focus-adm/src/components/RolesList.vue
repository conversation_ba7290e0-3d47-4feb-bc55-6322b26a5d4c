<template>
  <div class="roles">
    <el-select v-model="selected" multiple placeholder="Select" style="width: 800px; margin-bottom: 20px">
      <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
  </div>
  <el-button @click="cancel">取消</el-button>
  <el-button @click="submit" type="danger">确定</el-button>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, onMounted, computed, effect, toRefs } from 'vue'
import roleService from '@/service/roleService'
import { ElMessage } from 'element-plus'

const props = defineProps<{
  fid: number
}>()
const emit = defineEmits(['close', 'updateList'])
const { fid } = toRefs(props)

const selected = ref([])

const options = ref<any[]>([])

allRolesList()

getRolesByFid()

function getRolesByFid() {
  if (fid.value) {
    roleService.getRolesByFid(fid.value).then((res: any) => {
      console.log('🚀 ~ roleService.getRolesByFid ~ res:', res)
      selected.value = res
    })
  }
}

function allRolesList() {
  roleService.getAllRoles().then((res: any) => {
    console.log('🚀 ~ roleService.getAllRoles ~ res:', res)
    options.value = res
  })
}

function cancel() {
  emit('close')
}

function submit() {
  roleService.updateRoles(fid.value, selected.value).then((id) => {
    if (id) {
      emit('close')
      ElMessage.success('修改权限成功！')
    } else {
      ElMessage.success('修改权限失败！')
    }
  })
}
</script>

<style scoped lang="scss"></style>
