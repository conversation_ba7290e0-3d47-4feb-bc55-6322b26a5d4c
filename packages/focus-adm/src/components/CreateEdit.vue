<template>
  <div class="create-time">
    <el-switch
      v-model="copyForNew"
      size="large"
      active-value="1"
      inactive-value="0"
      active-text="使用新配置"
      inactive-text="覆盖旧配置"
      v-if="copyData.title"
    />
    <el-form label-width="auto" style="max-width: 800px" @submit.prevent v-if="copyForNew === '0'">
      <el-form-item label="覆盖目标FID">
        <el-input v-model.trim="copyTargetFid" placeholder="填入页面fid" :disabled="isLoading" style="width: 200px" />
        <el-button :suffix-icon="Search" type="primary" @click="searchFid" :disabled="!copyTargetFid">搜索</el-button>
        <br />
        <el-text type="danger" style="margin-left: 20px" v-show="!copyTargetFidData.id">没有搜索到目标FID！</el-text>
        <el-card v-show="copyTargetFid && copyTargetFidData.id" style="width: 100%">
          <template #header>目标页面信息</template>

          <p>配置ID(fid): {{ copyTargetFidData.id }}</p>
          <p>活动ID(aid): {{ copyTargetFidData.aid }}</p>
          <p>描述：{{ copyTargetFidData.desc }}</p>
          <p>分类: {{ copyTargetFidData.department }}</p>
          <p>创建人员/创建时间：{{ copyTargetFidData.createBy }}/{{ copyTargetFidData.createTime }}</p>
          <p>更新人员/更新时间：{{ copyTargetFidData.updateBy }}/{{ copyTargetFidData.updateTime }}</p>
        </el-card>
        <template v-if="copyTargetFidData.desc">
          <el-checkbox v-model="checkCopyTarget"></el-checkbox
          ><el-text type="danger" style="margin-left: 20px">确认覆盖目标页面的预览版本！该操作无法撤销！</el-text>
        </template>
      </el-form-item>
    </el-form>

    <el-form label-width="auto" style="max-width: 800px" @submit.prevent v-if="copyForNew === '1'">
      <el-form-item label="渲染方式">{{ createApp ? 'App原生页面' : 'H5活动页' }} </el-form-item>
      <el-form-item label="登录方式" v-show="!createApp">
        <el-radio-group v-model="loginType" class="ml-4">
          <el-radio :label="1">活动页——有登录态，可调用接口；微信登录、App登录可访问；非微信浏览器无法访问； </el-radio>
          <el-radio :label="2">静态页——无登录态，不可调用接口；微信、App无需登录可访问；任意浏览器可访问；</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="页面分类">
        <el-input v-model.trim="department" placeholder="填入页面分类，一般可以按活动分" :disabled="isLoading" />
      </el-form-item>

      <el-form-item label="页面描述">
        <el-input v-model.trim="desc" placeholder="填入页面描述，写下这个页面是做什么用的" :disabled="isLoading" />
      </el-form-item>

      <el-form-item label="活动ID" v-show="!createApp">
        <el-input v-model.trim="aid" placeholder="填入活动id，可以不写" type="number" :disabled="isLoading" />
      </el-form-item>
    </el-form>
    <el-button @click="closeDialog" :disabled="isLoading">取消</el-button>
    <el-button @click="submitdata" type="primary" :loading="isLoading" :disabled="!canSubmit">提交</el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineEmits, defineProps, toRefs, computed, watchEffect } from 'vue'
import configService from '../service/configService'
import { ElMessage } from 'element-plus'
import { RefreshRight, Search } from '@element-plus/icons-vue'

const props = defineProps({
  createApp: Boolean,
  modelValue: {
    type: Object,
    required: false,
    default: () => {
      return {
        aid: '',
        department: '',
        desc: '',
      }
    },
  },
  val: {
    type: Object,
    required: false,
    default: () => {
      return {
        aid: '',
        department: '',
        desc: '',
      }
    },
  },
  copyData: {
    type: Object,
    required: false,
    default: () => {
      return {
        fid: '',
      }
    },
  },
})

const emit = defineEmits(['close'])
const isLoading = ref(false)
const { createApp } = toRefs(props)

const aid = ref('')
const department = ref('')
const desc = ref('')
const loginType = ref(1)
const copyForNew = ref('1')
const copyTargetFid = ref(0)
const copyTargetFidData: any = ref({})
const checkCopyTarget = ref(false)
const canSubmit = computed(() => {
  if (copyForNew.value === '1') {
    return !!(department.value && desc.value)
  } else {
    return !!copyTargetFid.value && checkCopyTarget.value
  }
})
watchEffect(() => {
  if (copyForNew.value === '1') {
    copyTargetFid.value = 0
    checkCopyTarget.value = false
    copyTargetFidData.value = {
      id: 0,
    }
  }
})
function searchFid() {
  configService
    .getConfig(copyTargetFid.value)
    .then((res: any) => {
      console.log('🚀 ~ configService.getConfig ~ res:', res)
      copyTargetFidData.value = res
    })
    .catch(() => {
      copyTargetFidData.value = {}
    })
}

function submitdata() {
  console.error(props.copyData)
  isLoading.value = true

  if (props.copyData.fid) {
    if (copyForNew.value === '1') {
      configService
        .copyNewConfig(props.copyData.fid, aid.value, department.value, desc.value, props.copyData.versionType)
        .then((fid) => {
          console.log('🚀 ~ file: CreateEdit.vue:63 ~ configService.createNewConfig ~ res:', status)
          if (!fid) {
            ElMessage.error('出错了！')
          } else {
            ElMessage.success('创建成功！正在跳转配置页面。。。')
            copyTargetFid.value = 0
            emit('close', { fid, aid: aid.value || 0 })
            isLoading.value = false
          }
        })
    } else {
      // 复制到指定fid
      configService.copyAcoverB(props.copyData.fid, copyTargetFid.value, props.copyData.versionType).then((fid) => {
        console.log('🚀 ~ file: CreateEdit.vue:63 ~ configService.createNewConfig ~ res:', status)
        if (!fid) {
          ElMessage.error('出错了！')
        } else {
          ElMessage.success('创建成功！正在跳转配置页面。。。')
          copyTargetFid.value = 0
          emit('close', { fid, aid: 0 })
          isLoading.value = false
        }
      })
    }
  } else {
    configService.createNewConfig(aid.value, department.value, desc.value, createApp.value).then((fid) => {
      console.log('🚀 ~ file: CreateEdit.vue:63 ~ configService.createNewConfig ~ res:', status)
      if (!fid) {
        ElMessage.error('出错了！')
      } else {
        ElMessage.success('创建成功！正在跳转配置页面。。。')
        emit('close', { fid, aid: aid.value || 0 })
        isLoading.value = false
      }
    })
  }
}

function closeDialog() {
  emit('close')
}
</script>

<style scoped lang="scss"></style>
