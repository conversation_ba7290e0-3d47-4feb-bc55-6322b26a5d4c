<template>
  <div class="review-qrcode">
    <el-card class="mock-ctrl" v-loading="isLoding" custom-class="loadingbox">
      <div class="item">
        <div class="title">模拟访问时间</div>
        <div class="option">
          <el-switch v-model="mockTime.status" />
        </div>
        <div class="inputer">
          <el-date-picker
            v-model="mockTime.val"
            type="datetime"
            placeholder="Pick a Date"
            format="YYYY/MM/DD HH:mm:ss"
            value-format="x"
            :disabled="!mockTime.status"
            size="small"
          />
        </div>
      </div>

      <!-- <div class="item">
        <div class="title">模拟灰度渲染</div>
        <div class="option">
          <el-switch v-model="mockGrayIds.status" :disabled="!optionsForGray.length" />
        </div>
        <div class="inputer">
          <el-popover placement="left" title="选择模拟的灰度" :width="800" trigger="click">
            <template #reference>
              <el-button class="m-2" :disabled="!mockGrayIds.status">选择模拟灰度</el-button>
            </template>

            <el-radio-group v-model="mockGrayIds.id" class="ml-4" :disabled="!mockGrayIds.status">
              <el-radio :label="item.id" v-for="(item, index) in optionsForGray" :key="index">{{ item.text }}</el-radio>
            </el-radio-group>
          </el-popover>
        </div>
      </div>

      <div class="item">
        <div class="title">模拟条件渲染</div>
        <div class="option">
          <el-switch v-model="mockCondition.status" :disabled="!mockDatas.mockConditions.length" />
        </div>
        <div class="inputer">
          <el-popover placement="left" title="选择模拟的灰度" :width="800" trigger="click">
            <template #reference>
              <el-button class="m-2" :disabled="!mockCondition.status">选择模拟条件</el-button>
            </template>
            <el-card class="box" v-for="(item, index) in mockDatas.mockConditions" :key="index">
              <el-text>{{ item.title }}{{ item.extendStr ? `[${item.extendStr}]` : '' }}</el-text>
              <el-radio-group class="ml-4" :disabled="!mockCondition.status" v-model="mockConditionData[item.key]">
                <el-radio :label="1">不满足条件</el-radio>
                <el-radio :label="2">满足条件</el-radio>
              </el-radio-group>
            </el-card>
          </el-popover>
        </div>
      </div> -->
      <el-button @click="updateQrCode" size="small" type="primary" :disabled="isLoding">更新二维码 </el-button>
    </el-card>

    <div class="qrcode">
      <div id="qrcode-wx"></div>
      <el-tooltip :content="linkWx" placement="top" class="copy">
        <el-button
          class="btn-copy"
          @click="
              (e:any) => {
                copy(e, linkWx)
              }
            "
          size="small"
          v-if="linkWx"
          >复制链接
        </el-button>
      </el-tooltip>
      <!-- <el-switch v-model="switchShowApp" active-text="App访问" inactive-text="微信访问" /> -->
      <!-- 
      <div class="right">
       

        <div
          id="qrcode-app"
          :style="{
            opacity: switchShowApp ? 1 : 0,
          }"
        ></div>
        <el-tooltip :content="linkApp" placement="top" class="copy" v-if="switchShowApp">
          <el-button
            class="btn-copy"
            @click="
              (e:any) => {
                copy(e, linkApp)
              }
            "
            size="small"
            >复制链接
          </el-button>
        </el-tooltip>
        <p class="name" v-if="switchShowApp">拉起App访问</p>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import configService from '@/service/configService'
import { defineProps, toRefs, ref, defineEmits, onMounted, watchEffect, reactive, watch, computed, Ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Base64 } from 'js-base64'
import ClipboardJS from 'clipboard'
import { ElLoading } from 'element-plus'
import Qrcode from '../utils/qrcode.js'
// const Qrcode = require('../utils/qrcode.js')
const props = defineProps<{
  fid: number
  mockDatas: {
    mockConditions: Array<any>
    mockGrayIds: Array<{ id: string; text: string }>
  }
  pageType: string
  customUrl: string
}>()

const { fid } = toRefs(props)
const isLoading = ref(false)
const mockTime = reactive({
  status: false,
  val: 0,
})

const mockGrayIds = reactive({
  status: false,
  id: '',
  text: '',
})

const mockCondition = reactive({
  status: false,
})

const optionsForGray = computed(() => {
  return props.mockDatas.mockGrayIds || []
})
const mockConditionData = reactive({})

const switchShowApp = ref(false)

const qrcodeHandler = ref<any>(null)
const qrcodeHandlerForApp = ref<any>(null)
const linkApp = ref('')
const linkWx = ref('')
const isLoding = ref(false)

watchEffect(() => {
  if (mockGrayIds.id) {
    const target: any = props.mockDatas.mockGrayIds.find((i) => i.id === mockGrayIds.id) || {}
    mockGrayIds.text = target && target.text
  }
  if (!mockGrayIds.status) {
    mockGrayIds.text = ''
    mockGrayIds.id = ''
  }
})

const pageMockData = computed(() => {
  let data: any = {}
  if (mockGrayIds.status && mockGrayIds.id) {
    data = {
      mockGrayId: {
        id: mockGrayIds.id,
      },
    }
  }
  if (mockCondition.status) {
    console.log('...mockConditionData', mockConditionData)
    const d = {}
    Object.keys(mockConditionData).map((k) => {
      console.log('🚀 ~ Object.keys ~ k:', k)
      const target = props.mockDatas.mockConditions.find((i) => i.key === k)
      console.log('🚀 ~ Object.keys ~ target:', target)
      if (target) {
        const key = target.extendStr ? `${k}__EXTENDSTR__${target.extendStr}` : target.key
        const title = target.extendStr ? `${target.title}[${target.extendStr}]` : target.title
        console.log('🚀 ~ Object.keys ~ mockCondition[k]:', mockCondition[k])
        const status = mockConditionData[k] === 1 ? '不满足' : '满足'
        d[key] = {
          val: mockConditionData[k],
          title: title + '-' + status,
        }
      }
    })
    data = {
      ...data,
      mockCondition: d,
    }
    console.log('🚀 ~ pageMockData ~ data:', data)
  }
  return data
})

watch(
  mockTime,
  (val) => {
    console.log('🚀 ~ watch ~ val:', val)
    if (!val.status) {
      val.val = 0
    }
  },
  {
    deep: true,
  }
)

function copy(e: any, pagePath: string) {
  const text = pagePath
  const clipboard = new ClipboardJS(e.target, { text: () => text })
  clipboard.on('success', (e: any) => {
    ElMessage.success({ type: 'success', message: '复制成功' })
    // 释放内存
    clipboard.off('error')
    clipboard.off('success')
    clipboard.destroy()
  })
  clipboard.on('error', (e: any) => {
    // 不支持复制
    ElMessage.error({ type: 'waning', message: '该浏览器不支持自动复制' })
    // 释放内存
    clipboard.off('error')
    clipboard.off('success')
    clipboard.destroy()
  })
  clipboard.onClick(e)
}

function updateQrCode() {
  isLoding.value = true
  let basePath = 'https://m.webank.com/s/hj/focus2/preview/index.html'
  const pageType = `focus_${props.pageType}`
  if (BUILD_MODE !== 'prod') {
    basePath = 'https://m.test.webank.com/s/hj/focus2/preview/index.html'
  }
  const mockData = pageMockData.value
  console.log('🚀 ~ changeQrcodePath ~ mockData:', mockData)

  let path = `${basePath}?fid=${props.fid}&${pageType}=1&mockTime=${mockTime.val}`
  if (props.customUrl) {
    if (props.customUrl.indexOf('#/') > -1) {
      const [p, hash] = props.customUrl.split('#/')
      path = `${p}?fid=${props.fid}&${pageType}=1&mockTime=${mockTime.val}#/${hash}`
    } else {
      path = `${props.customUrl}?fid=${props.fid}&${pageType}=1&mockTime=${mockTime.val}`
    }
  }

  linkWx.value = path
  console.log('🚀 ~ changeQrcodePath ~ path:', path)

  if (!qrcodeHandler.value) {
    console.log('11111')
    qrcodeHandler.value = new Qrcode('qrcode-wx', {
      text: path,
      width: 300,
      height: 300,
      colorDark: '#000000',
      colorLight: '#ffffff',
      correntLevel: 3,
    })
  } else {
    console.log('2222')

    qrcodeHandler.value.makeCode(path)
  }
  console.log('33333')
  isLoding.value = false

  if (!switchShowApp.value) {
    return
  }
  isLoding.value = true
  const jumpParamStr = encodeURIComponent(
    Base64.encode(
      JSON.stringify({
        iosMinVer: 380,
        androidMinVer: 3040,
        action_param: path,
      })
    )
  )
  console.log('🚀 ~ changeQrcodePath ~ jumpParamStr:', jumpParamStr)
  const pathApp = encodeURIComponent(`webank://?subType=2&moduleType=1&jumpParam=${jumpParamStr}`)

  const downloadPath = `https://m.webank.com/s/hj/op/app/common/bridge-download/index.html?y=share&c=1&op=${encodeURIComponent(
    pathApp
  )}`
  linkApp.value = downloadPath
  qrcodeHandlerForApp.value && qrcodeHandlerForApp.value.makeCode(downloadPath)

  if (!qrcodeHandlerForApp.value) {
    qrcodeHandler.value = new Qrcode('qrcode-app', {
      text: downloadPath,
      width: 200,
      height: 200,
      colorDark: '#000000',
      colorLight: '#ffffff',
    })
  } else {
    qrcodeHandlerForApp.value.makeCode(downloadPath)
  }

  isLoding.value = false
  console.log('done')
}
</script>

<style scoped lang="scss">
.review-qrcode {
  .mock-ctrl {
    width: 100%;
    .item {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      > div {
        margin-right: 20px;
        &.title {
          flex: none;
        }
      }
    }
  }

  .qrcode {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    margin-top: 20px;
  }
}
</style>
