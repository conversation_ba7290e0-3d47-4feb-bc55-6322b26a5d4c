<template>
  <div class="table-list">
    <div class="sourch-bar">
      <el-input
        v-model="searchInputData.id"
        class="searchinput"
        placeholder="精确匹配"
        clearable
        @input="handleInputChange('id')"
        @clear="reset"
      >
        <template #prepend>复核ID</template>
      </el-input>
      <el-input
        v-model="searchInputData.fid"
        class="searchinput"
        placeholder="精确匹配"
        clearable
        @input="handleInputChange('fid')"
        @clear="reset"
      >
        <template #prepend>页面配置ID(fid)</template>
      </el-input>
      <el-button type="primary" :suffix-icon="Search" @click="search" :disabled="isLoading">搜索</el-button>
    </div>
    <el-table :data="listData" style="width: 100%" height="720px" v-loading="isLoading">
      <el-table-column prop="id" label="复核ID" width="80" />
      <el-table-column prop="dataId" label="页面配置ID(fid)" width="100" />
      <el-table-column label="发布类型" width="140">
        <template #default="scope">
          <el-text v-if="scope.row.publishType === 1">立即发布</el-text>
          <el-text v-if="scope.row.publishType === 2" type="warning">定时发布</el-text>
          <br />
          <el-text type="info" v-if="scope.row.publishType === 2">{{ scope.row.publishConfig.publishTime }}</el-text>
        </template>
      </el-table-column>
      <el-table-column label="发布状态" width="178">
        <template #default="scope">
          <div>
            <el-text :type="scope.row.statusTextType">{{ scope.row.statusText }}</el-text>
          </div>
          <div v-if="scope.row.devReviewText">
            <el-text :type="scope.row.devReviewType">{{ scope.row.devReviewText }}</el-text>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="title" label="复核内容" />
      <el-table-column prop="desc" label="版本状态" width="200">
        <template #default="scope">
          <div class="version" v-if="scope.row.status === 7">
            <el-text class="mx-1" type="danger"> 撤销发布版本号：</el-text>
            <el-tag type="danger"
              >V{{ (scope.row.extraInfo.customerVer || 0) + 1 }}.{{ scope.row.extraInfo.ver }}</el-tag
            >
          </div>
          <div class="version" v-if="scope.row.status === 6">
            <el-text class="mx-1" type="danger"> 撤销复核版本号：</el-text>
            <el-tag type="danger"
              >V{{ (scope.row.extraInfo.customerVer || 0) + 1 }}.{{ scope.row.extraInfo.ver }}</el-tag
            >
          </div>
          <div class="version" v-if="scope.row.status === 3">
            <el-text class="mx-1" type="danger"> 复核拒绝版本号：</el-text>
            <el-tag type="danger"
              >V{{ (scope.row.extraInfo.customerVer || 0) + 1 }}.{{ scope.row.extraInfo.ver }}</el-tag
            >
          </div>
          <div class="version" v-if="scope.row.status === 5">
            <el-text class="mx-1" type="danger"> 发布拒绝版本号：</el-text>
            <el-tag type="danger"
              >V{{ (scope.row.extraInfo.customerVer || 0) + 1 }}.{{ scope.row.extraInfo.ver }}</el-tag
            >
          </div>

          <div
            class="version"
            v-if="scope.row.status === 0 || (scope.row.devOwnerAuditStatus === 0 && scope.row.status === 2)"
          >
            <el-text class="mx-1" type="warning"> 复核中版本号：</el-text>
            <el-tag type="warning"
              >V{{ (scope.row.extraInfo.customerVer || 0) + 1 }}.{{ scope.row.extraInfo.ver }}</el-tag
            >
          </div>

          <div class="version" v-if="scope.row.status === 2 && scope.row.devOwnerAuditStatus === 1">
            <el-tooltip class="box-item" effect="dark" content="待发布包含设置了定时发布未生效的版本" placement="top">
              <el-text class="mx-1" type="warning">
                <el-icon><Warning /></el-icon>待发布版本号：</el-text
              >
            </el-tooltip>
            <el-tag type="warning"
              >V{{ (scope.row.extraInfo.customerVer || 0) + 1 }}.{{ scope.row.extraInfo.ver }}</el-tag
            >
          </div>
          <!-- {{ scope.row.extraInfo }} -->
          <div class="version" v-if="scope.row.status === 1">
            对客版本号：<el-tag type="success"
              >V{{ `${(scope.row.extraInfo.customerVer || 0) + 1}.${scope.row.extraInfo.ver}` }}</el-tag
            >
          </div>
          <div class="version" v-if="scope.row.status !== 1">
            对客版本号快照：<el-tag type="success"
              >V{{
                scope.row.extraInfo.customerVer
                  ? `${scope.row.extraInfo.customerVer}.${
                      scope.row.extraInfo.publishReviewVer || scope.row.extraInfo.ver
                    }`
                  : '--'
              }}</el-tag
            >
          </div>
        </template>
      </el-table-column>

      <el-table-column label="更新时间/创建时间">
        <template #default="scope">
          <el-text type="primary">{{ scope.row.updateTime }}</el-text>
          <br />
          <el-text type="info">{{ scope.row.createTime }}</el-text>
        </template>
      </el-table-column>
      <el-table-column label="申请人/申请时间">
        <template #default="scope">
          <el-text type="primary">{{ scope.row.applyUser }}</el-text>
          <br />
          <el-text type="info">{{ scope.row.applyTime }}</el-text>
        </template>
      </el-table-column>

      <el-table-column label="复核人/复核时间">
        <template #default="scope">
          <el-text type="primary">{{ scope.row.reviewUser }}</el-text>
          <br />
          <el-text type="info">{{ scope.row.reviewTime }}</el-text>
        </template>
      </el-table-column>

      <el-table-column prop="reviewDesc" label="复核意见" />

      <el-table-column label="发布人/发布时间">
        <template #default="scope">
          <el-text type="primary">{{ scope.row.publishUser }}</el-text>
          <br />
          <el-text type="info">{{ scope.row.publishTime }}</el-text>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="right">
        <template #default="scope">
          <!-- 啦啦啦{{ scope.row }} -->
          <slot name="option" :data="scope.row" @onReset="reset"></slot>
        </template>
      </el-table-column>
    </el-table>

    <div class="btns">
      <el-text type="primary" style="margin-right: 20px">总计{{ totalConfigs }}条</el-text>
      <el-button
        :icon="RefreshRight"
        circle
        style="margin-right: 10px"
        type="primary"
        :disabled="isLoading"
        @click="reset()"
      />
      <el-pagination
        background
        layout="prev, pager, next"
        :current-change="currentPage"
        @change="changePage"
        :disabled="isLoading"
        :page-count="totalPages"
        :page-size="50"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ListService, EnumListStatus } from '../service/reviewService'
import { RefreshRight, Search } from '@element-plus/icons-vue'
import { toRefs, ref, defineProps, defineExpose, reactive } from 'vue'

const props = defineProps<{
  status?: Array<EnumListStatus>
  devOwnerAuditStatus?: number
}>()

const { status, devOwnerAuditStatus } = toRefs(props)

const listService = new ListService(status.value || [])

const isLoading = ref(false)
const currentPage = ref(0)
const totalPages = ref(0)
const totalConfigs = ref(0)
const listData = ref<any>([])
const searchInputData = reactive({
  fid: '',
  id: '',
  // aid: '',
  // desc: '',
  // department: '',
})
reset()

defineExpose({
  reset: () => {
    reset()
  },
})

function handleInputChange(value: string) {
  if (value === 'id') {
    searchInputData.id = searchInputData.id.replace(/\D/g, '')
  } else if (value === 'fid') {
    searchInputData.fid = searchInputData.fid.replace(/\D/g, '')
  }
}

function reset() {
  currentPage.value = 1
  getData()
}

function search() {
  currentPage.value = 1
  getData()
}

function getData() {
  isLoading.value = true
  listService
    .getListData(
      currentPage.value,
      {
        dataId: searchInputData.fid,
        id: searchInputData.id,
      },
      devOwnerAuditStatus.value,
    )
    .then((res) => {
      console.log('🚀 ~ file: TableList.vue:39 ~ listService.getListData ~ res:', res)
      const { list, pages, total } = res
      listData.value = list
      totalPages.value = pages
      totalConfigs.value = total
    })
    .finally(() => {
      isLoading.value = false
    })
}

function changePage(page: number) {
  console.log('🚀 ~ file: TableList.vue:116 ~ changePage ~ page:', page)
  currentPage.value = page
  getData()
}
</script>

<style scoped lang="scss">
.table-list {
  .btns {
    margin-top: 20px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.searchinput {
  width: 260px;
  margin-right: 20px;
}
.sourch-bar {
  padding: 10px 20px;
}
</style>
