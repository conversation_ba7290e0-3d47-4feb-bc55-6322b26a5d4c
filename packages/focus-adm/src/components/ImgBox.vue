<template>
  <div class="show-img" v-if="isImg">
    <el-image
      style="width: 200px; height: 100px; margin-right: 20px"
      :src="imgPrevewPath"
      fit="contain"
      :crossorigin="null"
    >
    </el-image>

    <el-popover placement="bottom" :title="text" :width="900" trigger="click">
      <template #reference>
        <el-button class="m-2">查看大图</el-button>
      </template>
      <el-image :src="imgPrevewPath" fit="contain" :crossorigin="null"> </el-image>
    </el-popover>
  </div>

  <el-text>
    {{ text }}
  </el-text>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, onMounted, computed, effect } from 'vue'
const props = defineProps<{
  text: string
}>()

const isImg = computed(() => {
  return /(\.png$)|(\.jpg$)|(\.gif$)/.test(props.text)
})
const imgs = computed(() => {
  return [props.text]
})

const imgPrevewPath = computed(() => {
  if (BUILD_MODE === 'prod') {
    return props.text.replace(
      /(https:\/\/www\.webankwealthcdn\.net\/s\/hjupload)|(http:\/\/dbd\.webankwealthcdn\.net\/wm-resm\/hjupload)|(https:\/\/dbd\.webankwealthcdn\.net\/wm-resm\/hjupload)|(https:\/\/www\.webankcdn\.net\/s\/hjupload)|(http:\/\/dbd\.webankcdn\.net\/wm-resm\/hjupload)|(https:\/\/dbd\.webankcdn\.net\/wm-resm\/hjupload)/g,
      'http://opms.webank.com/res/querydata/html'
    )
  }
  return props.text
})
</script>

<style scoped lang="scss">
.show-img {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
