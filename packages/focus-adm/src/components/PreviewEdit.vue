<template>
  <div class="preview-edit">
    <el-form label-width="auto" style="max-width: 800px" @submit.prevent>
      <el-form-item label="配置ID">
        {{ propsData.fid }}
      </el-form-item>

      <el-form-item label="复核内容" required>
        <el-input
          v-model="title"
          placeholder="写入要复核的内容"
          :disabled="isLoading"
          type="textarea"
          :maxLength="100"
        />
      </el-form-item>

      <el-form-item label="发布方式">
        <el-radio-group v-model="publishType" class="ml-4">
          <el-radio :label="'1'">普通发布</el-radio>
          <el-radio :label="'2'">定时发布</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="发布生效时间" v-if="publishType === '2'">
        <el-date-picker
          v-model="publishTime"
          type="datetime"
          :disabled="isLoading"
          placeholder="指定时间到了才生效"
          value-format="x"
          format="YYYY-MM-DD HH:mm:ss"
          date-format="YYYY-MM-DD"
          time-format="HH:mm:ss"
          :editable="false"
          :disabled-date="disabledDateStart"
          :disabled-minutes="disabledMinites"
          :disabled-seconds="disabledMinites"
          :disabled-hours="disabledHours"
          @change="checkTime"
        />
      </el-form-item>

      <el-form-item label="定时发布备注" v-if="publishType === '2'">
        <el-input v-model="remark" placeholder="设置定时发布的备注" :disabled="isLoading" />
      </el-form-item>
    </el-form>
    <el-button @click="closeDialog" :disabled="isLoading">取消</el-button>
    <el-button @click="submitData" type="primary" :loading="isLoading" :disabled="!title">提交</el-button>
  </div>
</template>

<script setup lang="ts">
import configService from '@/service/configService'
import { defineProps, toRefs, ref, defineEmits, computed } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps<{
  propsData: {
    fid: string
    show: boolean
  }
}>()
const emit = defineEmits(['close', 'updateList'])
const { propsData } = toRefs(props)
const isLoading = ref(false)
const title = ref('')
const publishType = ref('1')
const publishTime = ref<number | string>('')
const remark = ref('')
const nextHour = computed(() => {
  const nextH = new Date().getHours() + 1
  return nextH > 23 ? `00` : nextH > 10 ? `${nextH}` : `0${nextH}`
})

function disabledDateStart(time: any) {
  const s = time.getTime()
  // if(searchform)
  const today = new Date()
  const midnight = new Date(today.getFullYear(), today.getMonth(), today.getDate())
  return s < midnight
}

function disabledMinites() {
  return Array.from({ length: 60 }, (v, i) => i + 1)
}

function checkTime(time: number) {
  console.log('🚀 ~ checkTime ~ time:', time)
  const t = new Date(time)
  const ht = t.getHours()
  const mt = t.getMinutes()
  const st = t.getSeconds()
  console.log('🚀 ~ checkTime ~ ht:', ht)
  console.log('🚀 ~ checkTime ~ disabledHours():', disabledHours())
  if (disabledHours().indexOf(ht) > -1 || mt !== 0 || st !== 0) {
    publishTime.value = ''
  }
}

function disabledHours() {
  const time = publishTime.value
  if (time) {
    const curDay = new Date(time).getDate()
    const now = new Date()
    const h = now.getHours()
    const hs = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]
    if (curDay === now.getDate()) {
      return hs.filter((i) => i <= h)
    }
  }

  return []
}

function submitData() {
  const data = {
    title: title.value,
    dataId: propsData.value.fid,
    publishType: publishType.value,
    publishConfig: {
      publishTime: publishTime.value,
      remark: remark.value,
    },
  }
  configService.setReviewConfig(data).then((res) => {
    console.log('🚀 ~ file: PreviewEdit.vue:71 ~ configService.setReviewConfig ~ res:', res)
    if (res) {
      emit('updateList')
      ElMessage.success('提交复核成功！')
      closeDialog()
    } else {
      ElMessage.error('提交复核失败！')
    }
  })
}

function closeDialog() {
  emit('close')
}
</script>

<style scoped lang="scss">
.preview-edit {
  padding-bottom: 10px;
}
</style>
