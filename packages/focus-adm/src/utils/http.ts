import axios from 'axios'
import { ElMessage } from 'element-plus'
import { ssoService } from '@/service/userService'
const instance = axios.create({
  // baseURL: config.focus,
  timeout: 10000,
  //   withCredentials: true,
})
// 添加请求拦截器
instance.interceptors.request.use(
  function (config) {
    // 在发送请求之前做些什么
    return config
  },
  function (error) {
    // 对请求错误做些什么
    return Promise.reject(error)
  }
)

// 添加响应拦截器
instance.interceptors.response.use(
  (res) => {
    console.log(res)
    const { ret_code, ret_data } = res.data
    if (/0000$/.test(ret_code)) {
      return ret_data || {}
    } else {
      if (
        ret_code === '5001' ||
        ret_code === '5005' ||
        (ret_code === '0031' && res.config.url && res.config.url.indexOf('get_url_schema') > -1)
      ) {
        ElMessage.error(res.data.ret_msg || 'permission denied')
        // return ret_data || {}
        return Promise.reject({ ...JSON.parse(res.data), fail: true })
        // authService.logout()
      }
      console.log('httperr', res)
      ElMessage.error(res.data.ret_msg)

      return Promise.reject({ ...JSON.parse(res.data), fail: true })
    }
  },
  (err) => {
    if (err.response) {
      console.log('err.response', err.response)
      // ajax err
      if (err.response.status === 401) {
        ElMessage.error(err.response.data.msg || 'signin please')
        ssoService.redirectToSSOLogin()
      } else if (err.response.status === 403) {
        ElMessage.error(err.response.data.msg || 'permission denied')
        ssoService.redirectToSSOLogout()
      } else {
        const { detail, msg } = err.response.data
        ElMessage.error(`${err.response.status} : ${detail || msg || 'network error'}`)
      }
      new Error(err.response.data)
      return Promise.reject(err.response.data)
    } else {
      if (err.message === 'Network Error') {
        // might be SSO
        ElMessage.error('Auth fail, redirect to sso...')
        ssoService.redirectToSSOLogin()
      } else {
        ElMessage.error(err.message || 'internal error')
        return Promise.reject(err)
      }
    }
  }
)
class Http {
  instance = instance
  get(cgiObj: any, params?: any, options?: any): Promise<any> {
    console.log('🚀 ~ file: http.ts:69 ~ Http ~ get ~ params:', params)
    const { url } = cgiObj
    // console.log(options)

    return this.instance({
      method: 'get',
      url,
      params,
    })
  }
  post(cgiObj: any, data: any, options?: any): Promise<any> {
    const { url } = cgiObj
    console.log('🚀 ~ file: http.js ~ line 91 ~ Http ~ post ~ url', url)
    // console.log(options)

    return this.instance({
      method: 'post',
      url,
      data,
    })
  }
}

export default new Http()
