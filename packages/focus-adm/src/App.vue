<template>
  <el-container>
    <el-header id="nav" v-if="showHeader">
      <!-- <router-link class="link" to="/">Home</router-link> -->
      <div class="logo">
        <p class="title">FOCUS</p>
        <p>低代码活动页管理系统</p>
      </div>
      <router-link to="/editList" class="link">列表页</router-link>
      <router-link class="link" to="/reviewlist">复核列表 </router-link>
      <router-link class="link" to="/publishPendingList">待发布列表</router-link>
      <router-link class="link" to="/publishedList">已发布列表</router-link>
      <router-link class="link" to="/rejectList">作废配置列表</router-link>

      <div class="user">
        用户：{{ userstore.userName }}
        <el-tag v-if="userstore.optionRoles.edit" style="margin-right: 10px">编辑</el-tag>
        <el-tag v-if="userstore.optionRoles.review" style="margin-right: 10px">复核</el-tag>
        <el-tag v-if="userstore.optionRoles.devReview" style="margin-right: 10px">研发负责人复核</el-tag>
        <el-tag v-if="userstore.optionRoles.publish">发布</el-tag>
        <el-button type="danger" @click="logOut">登出</el-button>
      </div>
    </el-header>
    <el-main> <router-view /> </el-main>
  </el-container>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import useUserStore from './store/userStore'
import { ssoService } from './service/userService'
const userstore = useUserStore()
const router = useRouter()
const route = useRoute()
const showHeader = computed(() => {
  if (location.href.indexOf('/v2options') < 0) {
    return true
  }
  return false
})

function logOut() {
  ssoService.redirectToSSOLogout()
}
</script>

<style lang="scss" scoped>
.logo {
  line-height: 1;
  margin: 0;
  padding: 0;
  width: 300px;
  border-radius: 10px;
  background-color: #53b98b;
  text-align: center;
  padding: 5px 10px;
  color: #fff;
  cursor: default;
  user-select: none;
  transform: scale(0.8);
  p {
    margin: 0;
    padding: 0;
    letter-spacing: -1px;
  }
  p.title {
    font-size: 40px;
    font-weight: bold;
    letter-spacing: 3px;
  }
}
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
}

#nav {
  padding: 20px 20px 0 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  .user {
    width: 100%;
    flex: auto;
    text-align: right;
  }

  .link {
    font-weight: bold;
    color: #2c3e50;
    flex: none;
    width: fit-content;
    padding: 0 20px;
    &.router-link-exact-active {
      color: #42b983;
    }
  }
}
</style>
