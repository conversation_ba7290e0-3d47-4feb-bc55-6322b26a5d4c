{"name": "@focus/adm", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode dev", "build": "vue-cli-service build", "build:test": "vue-cli-service build --mode test", "build:prod": "vue-cli-service build --mode prod", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^1.1.4", "@focus/modules": "workspace:^1.0.0", "axios": "^0.26.1", "babel-eslint": "10", "clipboard": "^2.0.11", "core-js": "^3.6.5", "dayjs": "1.11.5", "element-plus": "2.5.0", "js-base64": "^3.7.7", "pinia": "^2.1.7", "qrcode-svg": "^1.1.0", "qrcodejs2": "^0.0.2", "vue": "3.4.27", "vue-router": "^4.0.0-0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "sass": "^1.26.5", "sass-loader": "^8.0.2", "typescript": "~4.1.5"}}