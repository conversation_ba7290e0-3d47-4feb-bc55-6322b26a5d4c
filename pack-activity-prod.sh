mkdir -p ./target/focus2
mkdir -p ./target/focus2-preview

cp -r ./packages/activity-pages/client-prod/* ./target/focus2/
cp -r ./packages/focus-render/build-lib/client/* ./target/focus2/

cp -r ./packages/activity-pages/client-prod/* ./target/focus2-preview/
cp -r ./packages/focus-render/build-lib/client/* ./target/focus2-preview/

rm -rf ./packages/activity-packages/client-prod
rm -rf ./packages/activity-packages/client-prod


cd ./target

# git commit hash 的前8位
commit_hash_8=${GIT_COMMIT:0:8}  
BRANCH_NAME=${GIT_BRANCH: 7}

tar zcvf ${SUBSYSTEM_NAME}_${ENV_BUILD_VER}_${BUILD_NUMBER}_focus2_activity_pages_prod_${BRANCH_NAME}_${commit_hash_8}.tar.gz focus2
tar zcvf ${SUBSYSTEM_NAME}_${ENV_BUILD_VER}_${BUILD_NUMBER}_focus2_activity_pages_preview_${BRANCH_NAME}_${commit_hash_8}.tar.gz focus2-preview

aomp-upload ${SUBSYSTEM_NAME}_${ENV_BUILD_VER}_${BUILD_NUMBER}_focus2_activity_pages_prod_${BRANCH_NAME}_${commit_hash_8}.tar.gz
aomp-upload ${SUBSYSTEM_NAME}_${ENV_BUILD_VER}_${BUILD_NUMBER}_focus2_activity_pages_preview_${BRANCH_NAME}_${commit_hash_8}.tar.gz

# rm -rf ./focus2
# rm -rf ./focus2-preview
