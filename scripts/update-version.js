const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

try {
  // 获取当前分支名称
  const branchName = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
  
  // 匹配release-日期格式的分支 (例如: release-20230921)
  const releasePattern = /^release-(\d{4})(\d{2})(\d{2})$/;
  const match = branchName.match(releasePattern);
  
  if (match) {
    const [, year, month, day] = match;
    // 版本格式: 2.年(后两位).月日 (例如: 2.23.0921)
    const version = `2.${year.slice(2)}.${month}${day}`;
    
    // 更新package.json
    const packagePath = path.resolve(__dirname, '../package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    if (packageJson.version !== version) {
      packageJson.version = version;
      fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2) + '\n');
      console.log(`Updated version to ${version}`);
    }
  }
} catch (error) {
  console.error('Error updating version:', error.message);
  process.exit(0); // 非致命错误，不中断构建流程
}