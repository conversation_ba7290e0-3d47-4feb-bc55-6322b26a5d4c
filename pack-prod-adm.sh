
mkdir -p ./target/focusv3

cp -r ./packages/focus-adm/dist/* ./target/focusv3

cd ./target/focusv3

# git commit hash 的前8位
commit_hash_8=${GIT_COMMIT:0:8}  
BRANCH_NAME=${GIT_BRANCH: 7}

tar zcvf ${SUBSYSTEM_NAME}_${ENV_BUILD_VER}_${BUILD_NUMBER}_focus_adm_v3_prod_${BRANCH_NAME}_${commit_hash_8}.tar.gz ./
aomp-upload ${SUBSYSTEM_NAME}_${ENV_BUILD_VER}_${BUILD_NUMBER}_focus_adm_v3_prod_${BRANCH_NAME}_${commit_hash_8}.tar.gz
cd ..
rm -rf ./focusv3

