**需求单/Bug单链接：**

**方案设计链接：**

**自测文档链接：**

**checklist：**

1、判空类
- [x] 解构取值：检查被解构者非空，检查解构值赋初值，最多只用一层解构，多层用可选链语法
- [x] 对象使用：检查空对象，空属性
- [x] 数组取值：检查空数组
- [x] JSX禁用短路写法：检查render方法中是否有短路写法判断渲染元素

2、标准类
- [x] JSON解析：json解析必须有try catch，推荐使用封装方法
- [x] 灰度开关添加：检查灰度登记，自测灰度内，灰度外逻辑，都要覆盖
- [x] 本地存储：LocalStorage的key常量定义在常量文件中，方便维护，并且要有更新机制
- [x] CSS中z-index：使用需遵守层级规范，不得随意使用
- [x] 第三方库的引入：遵照《[依赖库管理规范](http://docs.weoa.com/docs/8Nk6MwRVYETdxwqL#anchor-OmVa)》

3、数值计算类
- [x] 数值计算类型：金额、收益、利率、百分比 一般都为浮点类型，重点判断
- [x] 数值计算精度：如有精度要求，用Decimal等库进行计算

4、业务类
- [x] 数据获取和使用的时序：数据未完成赋值前禁止使用，特别在页面初始化阶段的网络数据
- [x] 数据对比：对象对比判断要比较具体的对象属性字段，不做全对象比较

5、优化类
- [x] 网络返回数据的公共处理：网络数据返回的格式化、校验等公共处理在service进行，以便后续可能公用
- [x] 交互事件优化：点击、滚动限频、输入防抖等通用优化
- [x] 数据绑定：尽量用数据驱动方式更改view层，不要去做DOM操作

6、公共组件库修改
- [x] 最小改动点：逻辑修改要尽量最小改动
- [x] 参数约束：新增组建参数必须明确指定参数类型，并且增加类型校验
- [x] 最少存量影响：修改尽量不要影响存量代码，比如独立逻辑添加if代码块儿
- [x] 验证场景足够有覆盖性：尽量多的验证有代表性的使用场景
